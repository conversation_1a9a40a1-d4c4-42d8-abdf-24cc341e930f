// @ts-ignore
import { SSTConfig } from 'sst';
import { MyStack } from '@shared/infra/iac/MyStack';

export default {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  config(_input) {
    return {
      name: 'turnero',
      region: 'us-east-1',
    };
  },
  async stacks(app) {
    app.setDefaultFunctionProps({
      runtime: 'nodejs20.x',
      logRetention: 'two_weeks',
      nodejs: {
        minify: true,
        format: 'cjs',
        esbuild: {
          keepNames: true,
        },
      },
    });
    await app.stack(MyStack);
  },
} satisfies SSTConfig;
