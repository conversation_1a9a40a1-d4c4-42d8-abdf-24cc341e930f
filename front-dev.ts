// Este archivo es usado por pack/dev/tsconfig.json para generar el paquete npm con los archivos de testing (npm i -D) para ser usados en el frontend
import { getCalendarCreds as _getCalendarCreds } from '@shared/infra/iac/helpers';
import { CalendarApi as _CalendarApi } from './src/modules/calendar/services/CalendarApi';
import { tomorrowPlus as _tomorrowPlus } from './src/modules/calendar/utils/utils';
import { calendar as _gCalendar } from '@googleapis/calendar';
import {
  createSlot as _createSlot,
} from './src/modules/calendar/utils/test';
import { Event as _Event } from './src/modules/calendar/domain/Event';

export namespace calendar {
  export const getCalendarCreds = _getCalendarCreds;
  export const CalendarApi = _CalendarApi;
  export const tomorrowPlus = _tomorrowPlus;
  export const gCalendar = _gCalendar;
  export const createSlot = _createSlot;
  export const Event = _Event;
}
