{"name": "@s4nt14go/book-backend", "version": "0.0.3", "description": "", "files": ["lib/**/*"], "exports": {".": {"import": {"types": "./lib/front.d.ts", "default": "./lib/front.js"}}}, "scripts": {"pack": "npm pack && npm run cp-to-front", "prepack": "npm run build", "build": "npm run clean && npm run copy-check-node-version && npm run build:esm", "clean": "rm -rf lib", "copy-check-node-version": "mkdir -p lib && cp ../check-node-version.js lib/check-node-version.js", "build:esm": "tsc -p tsconfig.json && tsc-alias -p tsconfig.json", "cp-to-front": "cp s4nt14go-book-backend-0.0.3.tgz ~/desarrollo/react/turnero-front", "preinstall": "npm run check-node-version", "check-node-version": "node lib/check-node-version.js", "view": "npm view @s4nt14go/book-backend"}, "author": "", "license": "ISC", "dependencies": {"semver": "7.5.4", "luxon": "3.4.4", "uuid": "9.0.1", "exponential-backoff": "3.1.2", "libphonenumber-js": "1.12.8"}}