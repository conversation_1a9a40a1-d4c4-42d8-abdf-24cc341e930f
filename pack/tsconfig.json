{
  "compilerOptions": {
    "lib": ["ES2022", "DOM"],
    "target": "ES2022",
    "module": "ESNext",
    "moduleResolution": "bundler",
    "outDir": "lib",

    "noImplicitAny": true,                               /* Enable error reporting for expressions and declarations with an implied 'any' type. */
    "resolveJsonModule": true,                           /* Enable importing .json files. */
    "baseUrl": "./..",
    "paths": {
      "@shared/*": ["src/shared/*"]
    },
    "checkJs": true,
    "allowJs": true,
    "declaration": true,
    "declarationMap": true,
    "allowSyntheticDefaultImports": true
  },
  "extends": "@tsconfig/node20/tsconfig.json",
  "files": ["../front.ts"],
}