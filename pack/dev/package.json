{"name": "@s4nt14go/book-backend-dev", "version": "0.0.2", "description": "", "files": ["lib/**/*"], "exports": {".": {"import": {"types": "./lib/front-dev.d.ts", "default": "./lib/front-dev.js"}}}, "scripts": {"pack": "npm pack && npm run cp-to-front", "prepack": "npm run build && npm run cp-config && npm run cp-models", "cp-config": "mkdir -p lib/src/shared/infra/database/sequelize/config && cp ../../src/shared/infra/database/sequelize/config/config.js lib/src/shared/infra/database/sequelize/config/", "cp-models": "mkdir -p lib/src/shared/infra/database/sequelize/models && cp ../../src/shared/infra/database/sequelize/models/*.js lib/src/shared/infra/database/sequelize/models/", "build": "npm run clean && npm run copy-check-node-version && npm run build:esm", "clean": "rm -rf lib", "copy-check-node-version": "mkdir -p lib && cp ../../check-node-version.js lib/check-node-version.js", "build:esm": "tsc -p tsconfig.json && tsc-alias -p tsconfig.json", "cp-to-front": "cp s4nt14go-book-backend-dev-0.0.2.tgz ~/desarrollo/react/turnero-front", "preinstall": "npm run check-node-version", "check-node-version": "node lib/check-node-version.js", "view": "npm view @s4nt14go/book-backend-dev"}, "author": "", "license": "ISC", "dependencies": {"semver": "^7.5.4", "aws-sdk": "2.1540.0", "@googleapis/calendar": "^9.7.0", "google-auth-library": "^9.7.0", "luxon": "^3.4.4", "sequelize": "^6.36.0", "pg": "^8.11.3", "dotenv": "^16.3.2"}}