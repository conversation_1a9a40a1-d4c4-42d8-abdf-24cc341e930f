# BookBot

## Node.js Version Selection

To determine which version of Node.js to adopt, I check what GitHub Actions and Lambda use.

Looking at the guide [Understanding GitHub Actions](https://docs.github.com/en/actions/learn-github-actions/understanding-github-actions), in [actions/setup-node](https://github.com/actions/setup-node), it can be seen that the [latest available release](https://github.com/actions/setup-node/releases/tag/v4.0.0) uses `node20`.

From the AWS console, I can see that the available runtime is `20.x.`

Using `nvm ls-remote --lts`, I see that the latest Node.js LTS is v20.10.0 (npm v10.2.3), so I set that:
```shell
nvm install --lts
nvm ls
nvm alias default 20
nvm use 20
```

## Project Initialization

> To skip all the following steps, you can just grab the files in the link referred at the end.

Crear `package.json`:
```json5
{
  "name": "back",
  "version": "0.0.1",
  "scripts": {}
}
```

Como el primer commit no se puede modificar, puede estar bueno comenzar comiteando este archivo inicial y así tener máxima flexibilidad en la edición de commits siguientes. Abrir carpeta en WebStorm, ejecutar en una terminal `git init` y comitearlo `feat: add initial package.json`.

> Crear `.gitignore` para excluir archivos

Instalar dependencias:
```shell
npm i -D typescript
npm i -D @tsconfig/node20 ts-node semver
npm i -D vitest
npm i -D @typescript-eslint/parser @typescript-eslint/eslint-plugin eslint
npm i -D prettier chance @types/aws-lambda
npm i luxon uuid sequelize
npm i -D @types/luxon
```

> **Nota sobre luxon**: Poner primero *setZone()* y después *startOf()*, etc.. Si ponía el *setZone()* al final, en mi notebook mostraba una hora y en la lambda deployada otra, ya que *startOf('day')* es diferente cuando las compus están en distinto uso horario <br />
> *Correcto/consistente*: `DateTime.now().setZone('America/Cordoba').startOf('day')` <br />
> *Incorrecto/inconsistente*: `DateTime.now().startOf('day').setZone('America/Cordoba')`

> En otro proyecto, `ts-node` no funcionaba y usé [tsx](https://tsx.is/watch-mode) en su lugar. `tsx` tiene watch mode: `tsx watch ./file.ts` 

Create files:
`check-node-version.js`
```javascript
const semver = require('semver');

const version = 20;
const range = `>=${version}.0.0 <${version + 1}.0.0`;
if (!semver.satisfies(process.version, range)) {
  console.log(
    `ERROR: Required node version ${version} not satisfied with current version ${process.version}.\n` +
    `Change it running: nvm use ${version} or nvm alias default ${version}`,
  );
  process.exit(1);
} else {
  console.log(
    `OK: Required node version ${version} satisfies with current version ${process.version}.`,
  );
}

```

`tsconfig.json`
```json5
{
  "compilerOptions": {
    "noImplicitAny": true,                               /* Enable error reporting for expressions and declarations with an implied 'any' type. */
    "resolveJsonModule": true,                           /* Enable importing .json files. */
    "baseUrl": "./src",
    "paths": {
      "@shared/*": ["shared/*"],
    },
  },
  "extends": "@tsconfig/node20/tsconfig.json"
}
```

Copy files: 
```text
.eslintrc
vitest.config.js
.prettierrc
vitest.unit.env.ts
vitest.e2e.env.ts
```

Add to `package.json`:
```json5
{
  "scripts": {
    "typecheck": "tsc --noEmit",
    "lint": "eslint . --ext .ts --max-warnings=0",
    "lint-and-fix": "eslint . --ext .ts --max-warnings=0 --fix",
    "prettier-check": "prettier --config .prettierrc '**/*.ts' '**/*.js' --check",
    "prettier-format": "prettier --config .prettierrc '**/*.ts' '**/*.js' --write",
    "test": "npm run test-unit",
    "test-unit": "TEST_MODE=unit vitest --run",
    "check-node-version": "node check-node-version.js",
    "preinstall": "npm run check-node-version"
  },
}
```

Initialize `git`:
```shell
git init
```

Create `.gitignore`:
```gitignore
/node_modules/
/.idea/
```

Copy core files:
```text
src/shared/core
src/shared/decorators
src/shared/events
src/shared/infra/invocation/IInvoker.ts
src/shared/utils/test.ts
src/shared/utils/utils.ts
src/shared/utils/utils.unit.ts
Los siguientes se pueden adaptar si es que no se usa GraphQL:
src/shared/infra/appsync/utils.ts
src/shared/infra/appsync/test.ts
```

Verificar que se ejecutan sin errors: `npm run check-node-version`, `npm run typecheck` y `npm run unit-test`.

Hasta aquí es una primer etapa de archivos iniciales pero no se incluye de `infra` a `appsync`, `database`, `iac`, `invocation`, ni `Controllers.ts`, así como tampoco a e2e tests. A esos archivos los voy agregando a medida que los voy necesitando porque hay que hacerles muchas modificaciones para adaptarlos al nuevo proyecto. Da como resultado este [repo](https://github.com/s4nt14go/melixubi-back/tree/4fc0132c0b04063d034aca631d84baaf69d38014)

El proyecto turnero está hecho con [SST v2](https://v2.sst.dev). Si alguna vez recibo el error:
```text
software.amazon.awssdk.services.lambda.model.TooManyRequestsException: Rate Exceeded. (Service: Lambda, Status Code: 429, Request ID: ...)
```
...puede que no se deba a un error del diseño, sino a que estoy debugeando mediante `npm start`

## Data type Date

> Otras librerías de fechas que puedo probar que son más usadas recomendadas por [Robin Wieruch](https://www.robinwieruch.de/react-libraries#time-in-react) [trends](https://npmtrends.com/date-fns-vs-dayjs-vs-luxon). También ver de reemplazar [chance](https://chancejs.com/) ya que no tiene soporte para TypeScript.

Al crear un `Date` de JavaScript, es posible que cree un `Date` inválido, por ejemplo, si se le pasa un string con una fecha inválida (`new Date('2022-02-31')`). Por eso, en el domain se usa [Dat](src/shared/core/Dat.ts) que evita fechas inválidas, con estas propiedades:
```typescript
type DatProps = {
  j: Date; // Date de JS válido
  l: DateTime; // Luxon
  s: string; // i.e.: 2024-01-25T10:49Z
  t: number;
}
```
Agregando:
* `j` Date de JS válido
* `l` Luxon para hacer cálculos de dates
* `s` el formato de string que uso a lo largo del proyecto
* `t` el `Date.getTime()` con los milisegundos desde el epoch 01/01/1970 00:00:00 UTC

A las fechas de los modelos las guardo como strings (`DatProps.s`), AppSync también las envía/recibe como strings.

Conviene guardar las fechas como `Date` en la base de datos, ya que de esa forma con sequelize se pueden hacer búsquedas con [rangos](https://sequelize.org/docs/v7/querying/operators/#range-operators) en PostgreSQL, en [BalanceRepo.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/balance/repos/BalanceRepo.ts) se pueden ver ejemplos. Sequelize guarda los timestamps autogenerados (`created_at`, `updated_at` y `deleted_at`) como `Date`.

> Info sobre timezones en [TimeReference.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/domain/TimeReference.ts)

## Sequelize

Para que devuelva los integers como integers y no como strings, agregué en [config.json](src/shared/infra/database/sequelize/config/config.js) `pg.defaults.parseInt8 = true`.

Si bien en `models` se pueden importar los atributos de los distintos modelos (por ejemplo, en [reservation.js](https://github.com/s4nt14go/bookBot/blob/master/src/shared/infra/database/sequelize/models/reservation.js) se importa [reservationOption.js](https://github.com/s4nt14go/bookBot/blob/master/src/shared/infra/database/sequelize/models/reservationOption.js)), en `migrations` no, ya que son el estado de la base de datos en un momento dato del proyecto, simplemente se copian los modelos. Tener en cuenta que cuando se importan, los campos deben tener el mismo nombre, por ejemplo ver en `reservation.js`, el campo `locId`.
 
En lugar de usar una carpeta `seeders`, pongo los datos de prueba en la carpeta `migrations` ya que los seeds necesitan estar en sincronía con el estado de la base de datos. Uso una seed al final de todas las migraciones para mantener los datos actualizados con los distintos cambios en estructura de la tabla. Hay que borrar esa seed final de la tabla `SequelizeMeta` al agregar otras migraciones para que la considere como la última. 

Al nombrar las tablas, no usar camelCase ya que no funcionan las queries sql (mediante `queryInterface.sequelize.query`), al ser case-insensitive.

Al nombrar tablas/modelos, usar singular en la carpeta `models` (por ejemplo: [reservation](https://github.com/s4nt14go/bookBot/blob/feebb6c5a4dcaf77b8a1d236a9e0fb9a79a0ef0d/src/shared/infra/database/sequelize/models/reservation.js#L73), [Reservation](https://github.com/s4nt14go/bookBot/blob/10f26cc7154809ea5b7bd42c7e5c10b8a1e4e2c6/src/shared/infra/database/sequelize/models/index.js#L26)), y plural en la carpeta `migrations` (por ejemplo: [reservations](https://github.com/s4nt14go/bookBot/blob/feebb6c5a4dcaf77b8a1d236a9e0fb9a79a0ef0d/src/shared/infra/database/sequelize/migrations/023-create-reservations.js#L6), [seed](https://github.com/s4nt14go/bookBot/blob/10f26cc7154809ea5b7bd42c7e5c10b8a1e4e2c6/src/shared/infra/database/sequelize/migrations/901-seed.js#L47), [ReservationsInDb](https://github.com/s4nt14go/bookBot/blob/1e5a742c3854a9607f7542fd2719ddd987746411/src/shared/infra/database/sequelize/migrations/Reservations.json#L2), [rmRowsAll](https://github.com/s4nt14go/bookBot/blob/10f26cc7154809ea5b7bd42c7e5c10b8a1e4e2c6/src/shared/infra/database/sequelize/migrations/rmRowsAll.sh#L12), [rmTablesAll](https://github.com/s4nt14go/bookBot/blob/10f26cc7154809ea5b7bd42c7e5c10b8a1e4e2c6/src/shared/infra/database/sequelize/migrations/rmTablesAll.sh#L12)).

En un momento al ejecutar la lambda de un use case me daba un error `Error: Dialect needs to be explicitly supplied as of v4.0.0`. Lo resolví poniendo las credenciales de CockroachDB en esa lambda mediante [MyStack.ts](src/shared/infra/iac/MyStack.ts).

> Puedo probar [Neon](https://console.neon.tech) como alternativa a [CockroachDB](https://www.cockroachlabs.com). Yan escribió un acerca de [Neon](https://theburningmonk.com/tags/neon).

### Modelado

No pongo en el dominio `updated_at`, `created_at` ni `deleted_at` ya que son manejados por Sequelize. Sólo pongo e2e tests para verificar su funcionamiento, no se testean en los unit tests.

### Sanitización de los ids

Cuando se busca con Sequelize un `id`, se envía como `string` y si el tipo de esa columna se puso como `DataTypes.UUID` (a diferencia de `DataTypes.TEXT`) y no respeta el formato de `UUID` da la excepción `SequelizeDatabaseError: could not parse "xxx" as type uuid`.

Es por ello que hay que validar las `id` que se mapean a `DataTypes.UUID` que se reciben en el controlador que se expone al exterior, es decir `AppSyncController`. Pero no para los controladores internos, como lo son `FunctionController`s y `AsynchronousController`, ya que en el caso de estos últimos los datos deberían ser válidos, no siendo necesario de sanitizar.

Una alternativa a `UUID` es `ULID` como se expone en este [artículo](https://www.linkedin.com/posts/milan-jovanovic_should-you-stop-using-uuids-uuids-guid-activity-7294978097412493312-rnfc)

### Ejemplo de update retornando la entidad actualizada

[ReservationOptionRepo](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/repos/ReservationOptionRepo.ts) se puede tomar como ejemplo para cuando se desea devolver la entidad actualizada en un update.

## Tests para verificar que los domain events triggerean los subscribers

Cuando empiezo a usar domain events, necesito que el nombre de la lambda `distributeDomainEvents` este en las env vars (ver por ejemplo: [CreateBusinessUserEvents.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/user/useCases/createBusinessUser/CreateBusinessUserEvents.ts)). Esa variable se setea en [MyStack.ts](https://github.com/s4nt14go/bookBot/blob/c1343e8a080a4a5b0cf418dea1f5d1cc45f6091a/stacks/MyStack.ts#L95) y si bien podría testearla localmente mediante `npm run start`, agregándola en `.env`, debería ensuciar el diseño poniendo `dotenv.config()`. Es por ello que los tests de los domain events se hacen directamente e2e tests con el deployado en AWS (mediante `npm run deploy`, en lugar de `npm run start`).

Existen dos formas de agregar domain events: directamente en la creación del aggregate [BusinessUser.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/user/domain/BusinessUser.ts) o a nivel del controlador [CreateReservation.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/createReservation/CreateReservation.ts).

## Configuración de los tests en WebStorm

La forma más cómoda de ejecutar los tests mediante WebStorm, es configurar las environmental variables para unit tests en el template de default ("Run" > "Edit Configurations" > "Edit configuration templates..." > "Vitest"):
```
Environmental variables: TEST_MODE=unit
```
Seleccionar "Store as project file" y comittear el archivo que se crea en la carpeta `.run`. 

Y para cada archivo `*.e2e.ts` configurar:
```
Environmental variables: TEST_MODE=e2e;AWS_PROFILE=<perfil-de-aws>
```
...y guardar esas configuraciones checkeando "Store as project file". Comitear esas configuraciones guardadas en la carpeta `.run`.

## Preferir `null` en lugar de `[]` como resultado de una función

Cuando el resultado de una función es un array vacío `[]`, mejor enviar `null` en lugar de `[]`. De esa forma, se evita que se llamen funciones siguientes con `[]` como argumento, lo que habitualmente no tiene sentido, prefiero el escape temprano por checkeo contra `null`.

También desde donde se llama a esa función se puede preguntar simplemente por `if (resultado)` en lugar de `if (resultado.length)`.

De manera que usar `[Type, ...Type[]] | null` como resultado de la función, en lugar de `Type[] | null`.

Pero los types que envío al frontend, pasan por graphql y para usarlos allí también (mediante la exportación de `@s4nt14go/book-backend`) debo tener en cuenta las limitación de graphql para que estos types funcionen bien con el proyecto fullstack.

La forma más cercana de expresar `[Type, ...Type[]] | null` en la schema de GraphQL es con `[Type!]`, pero eso admite `[]`, es decir que a esos types que son enviados al frontend tipearlos `Type[] | null`, en lugar de `[Type, ...Type[]] | null`.

También preferir `null` | `Type` en lugar de `null` | `<primitiva como string o number>`. Ya que por ejemplo, al usar primitivas permite valores que en realidad no tienen sentido. Por ejemplo, si tipeo un campo `description`: `string`, va a permitir la string vacía `''`, que en realidad no tiene sentido, ya que si no hay descripción, debería ser `null`. Con `number` se da el caso que `NaN` e `Infinity` son considerados `number`, lo que no debería ser considerado así en el proyecto.

## Errores devueltos por controladores

[Controllers.ts](https://github.com/s4nt14go/bookBot/blob/master/src/shared/infra/Controllers.ts) tiene controladores de tipo `AppSyncController` que expone la API al frontend, `FunctionController`s que son casos de uso internos entre los distintos módulos, y `AsynchronousController` que son los que se suscriben a los domain events o controladores desacoplados del controlador que lo llama, ya que el padre no necesita una respuesta, simplemente dispara el hijo.

Cuando el `execute` de `AppSyncController` no es exitoso, devuelve un `Envelope` con el campo `errors` conteniendo un array con uno o múltiples errores. Un ejemplo de múltiples errores es cuando por ejemplo [UpdateBusinessUser](https://github.com/s4nt14go/bookBot/blob/master/src/modules/user/useCases/updateBusinessUser/UpdateBusinessUser.ts) detecta errores en varios campos, como `firstname`, `lastname`, etc..

La forma en que envían este array de errores es mediante:
```text
formatErrors -> AppSyncController -> Envelope.errors(errors) -> adaptResult (configurado al llamar addResolverWithLambda en MyStack.ts)
```

De esta forma, cuando hay errores, siempre se devuelve un array `{ errors: [BaseErrorDto, ...BaseErrorDto[]] }` (aunque haya habido solo un error), para estandarizar el manejo de errores en el frontend.

Uso [Result](https://github.com/s4nt14go/bookBot/blob/master/src/shared/core/Result.ts) para los casos en los que al intentar crear un `ValueObject` se regresa sólo un error, mientras que [Result2](https://github.com/s4nt14go/bookBot/blob/master/src/shared/core/Result2.ts), es para aquellos casos que se devuelven múltiples errores.

### `FunctionController`s

Al tener todos los módulos en el mismo repo/microservicio, cuando un use case llama a otro de un módulo distinto (y desde luego esto también aplica a un use case del mismo módulo), conviene llamarlo en forma de función, no hay necesidad de introducir una lambda y tener que soportar la demora de otro componente de infra, como por ejemplo un posible cold start. Si el use case llamado puede dar error, usar `FunctionController2`, si no da error, usar `FunctionController`.

En cambio, si el use case consumido, se encuentra en otro repo/microservicio, allí sí el use case debería ser una lambda, invocada desde el controlador padre. Como este es un proyecto modular monolithic, no uso lambdas para esos controladores hijos, uso `FunctionController`s.

También, tiene sentido que sea una lambda cuando no necesitamos la respuesta del use case en forma sincrónica, sino que sólo queremos que se ejecute el use case llamado y desacoplar su ejecución del use case padre. Para eso utilizo `AsynchronousController`, que también uso para subscribirme a domain events.

Para `FunctionController`, `FunctionController2` o `AsynchronousController`, no uso el decorador `GuardUuid` ya que estos controladores son llamados de forma interna, no están expuestos al exterior por lo que los datos de entrada deberían ser correctos. El decorador `ReturnUnexpectedError` está diseñado para ser usado con `AppSyncController`s.

En algunas situaciones puede ser que un mismo case sea usado en forma interna por otro use case y también expuesto al frontend. En ese caso, se puede crear el `FunctionController` y que el `AppSyncController` simplemente retransmita su request y response. Ejemplo de [GetBusinessUserFC](https://github.com/s4nt14go/bookBot/blob/master/src/modules/user/useCases/getBusinessUserFC/index.ts) usado por [GetBusinessUser](https://github.com/s4nt14go/bookBot/blob/master/src/modules/user/useCases/getBusinessUser/index.ts) (que simplemente transmite el request y response) y [CancelReservation](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/cancelReservation/index.ts).

## GraphQL queries returning possible null result

Si bien puede parecer un wrapping innecesario, es conveniente que las queries devuelvan el resultado dentro de una propiedad `result` en el objeto que se recibe de AppSync.

Esto sirve para que en aquellos casos en los que el resultado es `null`, dejarlo en claro y que no se confunda con un error de la red. Por ejemplo, si se busca un elemento por id y no se encuentra, se devuelve `null` de esta forma:
```typescript
{
  result: null
  time  // propiedad adicional agregada a todos los objetos que se devuelven mediante @shared/core/Envelope.ts
}
```

...cuando en el caso de encontrarlo, se devuelve:
```typescript
{
  result: <found>
  time
}
```

## Siempre retornar algo de las GraphQL queries

Si bien por ejemplo, en el caso de un command se podría pensar que retornar `void` es algo perfectamente válido, a los fines de estandarizar las respuestas y facilitar su procesamiento en el frontend devolver del controller al menos `true`, el DTO quedaría:
```typescript
export type Response = true;
```
...que se condice con un retorno del controlador:
```json5
{
  status: Status.OK,
  result: true,
}
```
...y en la graphql schema:
```graphql
type OkResponse {
  result: Boolean!  # always true
  time: AWSDateTime!
}
```

Probablemente sería posible no retornar nada, pudiendo checkear en el front si la respuesta fue un OK fijándose en el `status` de la respuesta
```json5
{
  status: 'DATA_RECEIVED',
  data: null,
  errors: null,
  apolloError: null,
  time: 1730807849821
}
```
...sin embargo enviando `true` en lugar de un `void`, también va a popular `data: true` que sirve para confirmar que estuvo todo bien y no se está olvidando enviar algo.

Como en [DeleteReservationOption.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/deleteReservationOption/DeleteReservationOption.ts) por ejemplo.

Un caso particular es cuando se hace un update a una entidad, en ese caso conviene devolver la entidad actualizada, eso va a hacer que el cache de Apollo client en el frontend actualice automáticamente las queries que la requirieron.

## Uso de fragments

Para los e2e tests con GraphQL requests, conviene usar fragments para las entidades de los módulos y así evitar repetir código. Poner los fragments en el `test.ts` del módulo. Ver [balance](https://github.com/s4nt14go/bookBot/blob/master/src/modules/balance/utils/test.ts), [calendar](https://github.com/s4nt14go/bookBot/blob/master/src/modules/calendar/utils/test.ts), [reservation](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/utils/test.ts) y [user](https://github.com/s4nt14go/bookBot/blob/master/src/modules/user/utils/test.ts)

## Al actualizar entidades, devolver la entidad actualizada para que actualice el cache en el frontend

Un ejemplo de esto es [CancelReservationByBusiness](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/cancelReservationByBusiness/CancelReservationByBusiness.ts) donde retorno `ReservationDto` actualizada.

Para que ello suceda, debo poner en el [schema.graphql](https://github.com/s4nt14go/bookBot/blob/master/src/shared/infra/appsync/schema.graphql) que la `ReservationDto` que retorno, sea del mismo `__typename` que el listado quiero que se actualice. En el caso de [CancelReservationByBusiness](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/cancelReservationByBusiness/CancelReservationByBusiness.ts) quiero que actualice la query `getReservations` que retorna un listado de `ReservationWithTimestamps`, es por ello que en el schema pongo:
```graphql
type Mutation {
  ...
  cancelReservationByBusiness(...): CancelReservationByBusinessResponse!
}
type CancelReservationByBusinessResponse {
  result: CancelReservationByBusinessResult!
  time: AWSDateTime!
}
type CancelReservationByBusinessResult {
    ...
    reservation: ReservationWithTimestamps!
}
```

No sólo que de esta forma actualiza el caché del front, sino que también ejecuta el código que se ejecuta cuando se hace la query que devuelve dicha combinación de `__typename`:`id`, aunque en realidad no es que se hizo un request con esa query. Por ejemplo en el caso del request del front [CancelReservationByBusiness](https://github.com/s4nt14go/turnero-front/blob/master/src/modules/reservation/cancelReservationByBusiness/ports.input.ts) para la que el back devuelve `ReservationWithTimestamps`, no sólo se disparan los `setStore` de `zustand` esperados debido a `cancelReservationByBusiness`, sino que también se disparan los de `getReservations`. Estos son los eventos que se ven en la devtools del front solapa Redux:
```text
cancelReservationByBusiness/LAUNCHED
getReservations/DATA_RECEIVED
cancelReservationByBusiness/DATA_RECEIVED
```

Otro detalle a tener en cuenta es que a veces no alcanza con devolver el dto sino que debo incluir también las timestamps (específicamente `created_at` y `updated_at`) ya que puede hacer falta para el listado de las entidades, si por ejemplo, están ordenadas por dichas columnas.

En el `insert()` de [BalanceRepo.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/balance/repos/BalanceRepo.ts) se puede ver un ejemplo en el que se devuelve de sequelize la entidad con timestamps.

## Respuestas al front con Timestamps

Hay use cases que necesitan devolver timestamps (`created_at` y `updated_at`). Si tomamos como ejemplo [GetCustomerUsers](https://github.com/s4nt14go/bookBot/blob/master/src/modules/user/useCases/getCustomerUsers/GetCustomerUsers.ts) vemos que cuando se envían los datos al front, están envueltas las operaciones [CustomerUser](https://github.com/s4nt14go/bookBot/blob/master/src/modules/user/domain/CustomerUser.ts).assemble() (llamada por [CustomerUserRepo](https://github.com/s4nt14go/bookBot/blob/master/src/modules/user/repos/CustomerUserRepo.ts).getByIds) y [CustomerUser](https://github.com/s4nt14go/bookBot/blob/master/src/modules/user/domain/CustomerUser.ts).toDto().

Para que se retornen las timestamps es necesario agregar `...rest` a estos métodos `assemble()` y `toDto()`:
```typescript
public toDto(): CustomerUserDto {
  const { firstName, lastName, email, timezone, phone, ...rest } = this.props;

  return {
    ...rest,
    id: this.id.toString(),
    firstName: firstName.value,
    lastName: lastName.value,
    email: email.value,
    timezone: timezone.value,
    phone: phone?.value || null,
  };
}

public static assemble(dto: CustomerUserDto): CustomerUser {
  const { id, firstName, lastName, email, timezone, phone, ...rest } = dto;
  return new CustomerUser(
    {
      ...rest,
      firstName: Name.create(firstName).value,
      lastName: Name.create(lastName).value,
      email: Email.create(email).value,
      timezone: TimeZone.create(timezone).value,
      phone: phone ? Phone.create(phone).value : null,
    },
    new EntityID(id),
  );
}
``` 

## Si se devuelven entidades con valores viejos al frontend, se va a actualizar el caché del frontend con ese valor viejo

Hay que tener cuidado cuando se envían al frontend valores de entidades viejos. Esto sucedía en una versión vieja de la respuesta del request [getReservations](https://github.com/s4nt14go/bookBot/blob/6cd39db791d0dcf3c9954c7ba46eb0c877dc56ec/src/modules/reservation/useCases/getReservations/GetReservations.ts), en el que se devolvía del controlador este `ReservationDto[]`:
```typescript
type ReservationDto = {
  ...
  business: BusinessUserDto;
  customer: CustomerUserDto | ManuallyCreatedCustomerDto; // As the CustomerUser or ManuallyCreatedCustomer may change over time, we need to store a snapshot of the CustomerUser or ManuallyCreatedCustomer at the time of the reservation
  reservationOption: ReservationOptionDto; // As the ReservationOption may change over time, we need to store a snapshot of the ReservationOption at the time of the reservation
}
````
...que a su vez pasan por el [schema.graphql](https://github.com/s4nt14go/bookBot/blob/6cd39db791d0dcf3c9954c7ba46eb0c877dc56ec/src/shared/infra/appsync/schema.graphql) con la forma:
```graphql
type ReservationWithTimestamps {
  ...
  business: BusinessUser!
  customer: ReservationCustomer!
  reservationOption: ReservationOption!
}
union ReservationCustomer = Customer | ManuallyCreatedCustomer
```
Cuando en el frontend se reciban estas entidades con sus `__typename`: `BusinessUser`, `Customer`/`ManuallyCreatedCustomer` y `ReservationOption` y respectivos `id` con los valores viejos (aquellos que tenían al momento de realizar la reserva), se va a actualizar el caché del frontend con esos valores viejos. Por eso es que tuve que renombrar esos tipos en el schema:
```graphql
type ReservationWithTimestamps {
  ...
  business: BusinessUser_dontCache!
  customer: ReservationCustomer!
  reservationOption: ReservationOption_dontCache!
}
union ReservationCustomer = Customer_dontCache | ManuallyCreatedCustomer_dontCache
```

Y excluirlos del cache en la configuración de Apollo Client en el frontend:
```typescript
const client = new ApolloClient({
  cache: new InMemoryCache({
    typePolicies: {
      ManuallyCreatedCustomer_dontCache: {
        keyFields: false,
      },
      Customer_dontCache: {
        keyFields: false,
      },
      BusinessUser_dontCache: {
        keyFields: false,
      },
      ReservationOption_dontCache: {
        keyFields: false,
      },
    },
  }),
});
```

Después decidí borrar estos campos `Reservation`.`business`, `customer` y `reservationOption` ya que estaba usando un tipo de dato `JSON` de sequelize para ellos, y en el caso que tenga que modificar un campo interno, las migraciones de la bd iban a ser muy complejas. En lugar de usar `JSON`, decidí usar campos individuales simples como por ejemplo, en el caso de `customer`: `customerFirstName`, `customerLastName`, `customerEmail` y `customerPhone`. 

Commits en los que agregué `Reservation`.`customerFirstName`, `customerLastName`, `customerPhone` y `customerEmail`: [backend](https://github.com/s4nt14go/bookBot/commit/3da663271261495e9b36f6a83db68b293baa092a), [frontend](https://github.com/s4nt14go/turnero-front/commit/8807119d8c388b8b2f9e57a3f3831d4feb624849)

Commits en los que eliminé `Reservation`.`customer`: [backend](https://github.com/s4nt14go/bookBot/commit/f8c4b8affc4277a912ff37f725f283db2748d6d6), [backend2](https://github.com/s4nt14go/bookBot/commit/fafcfe363df6db6f93c4a995e72d1d6fb04607ee), [frontend](https://github.com/s4nt14go/turnero-front/commit/b6236cb150b25986dcf036199791776846b94487)

Commits en los que eliminé `Reservation`.`business`: [backend](https://github.com/s4nt14go/bookBot/commit/aae1c6bbe2478e8477be9723d9ce05e3abd3ac83), [backend2](https://github.com/s4nt14go/bookBot/commit/cd47fea982adceff9dc0600496e63a19053306d4), [frontend](https://github.com/s4nt14go/turnero-front/commit/db77f5802e521709000846928264fc40e54f41ab)

Commits en los que agregué `Reservation`.`srvName`, `srvDuration`, `srvDescription`, `srvPrice`, `locName` y `locId`: [backend](https://github.com/s4nt14go/bookBot/commit/752bfe6e5c29e50021c241a0d4c07339a7c34dd7), [frontend](https://github.com/s4nt14go/turnero-front/commit/e376d2164bcbaa6ad38532175263c4045a7d4174)

Commit en el que agregué `Reservation`.`roZone`: [backend](https://github.com/s4nt14go/bookBot/commit/4da998af9a9dfbf8308599451b98edff9274e37a)

Commits en los que eliminé `Reservation`.`reservationOption`: [backend](https://github.com/s4nt14go/bookBot/commit/c2e0f0fa8e53bbafdafc23cb469f05aa3a43174c), [frontend](https://github.com/s4nt14go/turnero-front/commit/aca70977a63bebf7bf4aaed3827ff277638aa094)

## Limitaciones en los campos de los DTOs

En forma similar a cuando se trata de variables que queremos guardar en la BD, los DTOs que son enviados al frontend pasan por GraphQL, y sólo se pueden enviar campos de tipo `string`, `number`, `boolean`, `null`, `array` o un objeto. No enviar `undefined` en ningún DTO, para que en el frontend `codegen` no agregue `undefined` en los resultados de las queries/mutations/subscriptions configurar `avoidOptionals: true` en `codegen.ts`.

Tampoco enviar enums (por ejemplo, `Action.CREATED | Action.UPDATED | Action.NO_CHANGES`), enviar `strings` en su lugar.

Por ejemplo, los siguiente campos, `action` y `nextEvents` están mal:
```typescript
// DTOs.ts
export type Response = {
  action: Action.CREATED | Action.UPDATED | Action.NO_CHANGES;
  currentCalendar: {
    id: string;
    name: string;
    nextEvents: NextEventDto[] | null | 'NO_SEARCH';
  },
}
```

El problema de `action` es que es un enum, debe ser enviado como `string`. Mientras que `nextEvents` puede ser un `array` o un `string`, y como GraphQL sólo admite un tipo o el otro, se debe cambiar a `NextEventDto[] | null`:
```typescript
// DTOs.ts
export type Response = {
  action: string;
  currentCalendar: {
    id: string;
    name: string;
    nextEvents: NextEventDto[] | null;
    nextEventsSearched: boolean;
  },
}
```

Este caso también se daba con `BaseError` que es enviado al frontend por la mutación `checkGetEventsAndSave`. Por lo que tuve que eliminar el campo `data` de tipo `unknown` y crear un error específico con un `data` tipiado con primitivas para el caso en donde requería que el error tuviera ese `data`, ver ejemplo [EveryErrors.ShouldBeADivisorOfMinutesInDay](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/domain/EveryErrors.ts)

> De hecho, las limitaciones de los DTOs teniendo que pasar por GraphQL, son mayores que las impuestas por la BD, ya que en la BD se pueden guardar objetos mediante `type: new Sequelize.JSON()`. Docs de sequelize [v6](https://sequelize.org/docs/v6/other-topics/other-data-types/#json-sqlite-mysql-mariadb-oracle-and-postgresql-only) y [v7](https://sequelize.org/docs/v7/models/data-types/#json--jsonb). Sin embargo, si un campo de la bd es `JSON` y tengo que modificar un campo interno, las migraciones de la bd son muy complejas. En lugar de usar `JSON`, usar campos individuales simples que va a ser más fácil de manipular al evolucionar el data model. El uso válido que preveo de `JSON` es para enviar data de analytics.

Es preferible enviar siempre los mismos campos en los DTOs y ponerlos `null` si para una respuesta particular no tienen razón de ser, ya que de esa forma es más fácil de manejar en el frontend. Por ejemplo, en lugar de este DTO:
```typescript
export type CheckAndSaveResponse = {
  action: string;
  currentCalendar: CurrentCalendar;
} | {
  action: string;
  currentCalendar: null;
  messageError: string;
  checkError: BaseErrorDto;
}
```

Es preferible enviar:
```typescript
export type CheckAndSaveResponse = {
  action: string;
  currentCalendar: CurrentCalendar | null;
  messageError: string | null;
  checkError: BaseErrorDto | null;
}
```

De esa forma en el frontend puedo preguntar por `if (response.messageError)`, sin recibir el error de que `messageError` no existe en `CheckAndSaveResponse`.

## "No tests were found" error de Vitest

Cuando se está throweando un error en el nivel top del test, por ejemplo por:
```typescript
// Add all process.env used:
const { getVersion } = process.env;
if (!getVersion) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}
```

Al ejecutar el test con el Run de WebStorm, no muestra el error, sino que muestra "No tests were found".<br />

Para verlo debo ejecutar vitest desde la terminal, por ejemplo mediante:<br />
`npm run test-e2e -- src/modules/versions/useCases/getVersion/GetVersion.e2e.ts`

## Types dentro de las entidades

Base type: `<Entity>UpdateInput` <br />
`<Entity>CreateInput = function(<Entity>UpdateInput)` <br />
`<Entity>Props = function(<Entity>CreateInput)` (se agregan las default props) <br />
`<Entity>Dto = function(<Entity>Props)` <br />

Ejemplos: [ReservationOption](src/modules/reservation/domain/ReservationOption.ts), [Service](https://github.com/s4nt14go/bookBot/blob/5b3a1868671066048f5f07dc142c1b88073a6292/src/modules/reservation/domain/Service.ts#L9)

Para casos simples, donde no hay diferencia entre varios de los types, es posible que sólo existan `<Entity>Dto` y `<Entity>Props`.

Ejemplo: [Calendar](src/modules/calendar/domain/Calendar.ts)

## Real & fake repos

Starting from `<Entity>Dto`, I build `<Entity>ToDb` in `<Entity>Repo`. <br />
`<Entity>InDb` is `<Entity>ToDb & Timestamps`. <br />

`<Entity>ToDb` and `<Entity>InDb` should be defined in `<Entity>Repo`.  `<Entity>RepoFake` imports `<Entity>InDb`, but at the beginning when only `<Entity>RepoFake` exists, `<Entity>InDb` can be defined in it temporarily.  

For a simple example, where `<Entity>ToDb` equals `<Entity>Dto` see [BusinessUserRepo](src/modules/user/repos/BusinessUserRepo.ts). For a case where they aren't equal, see [GeneralSettingsRepo](src/modules/reservation/repos/GeneralSettingsRepo.ts).

Another example, where `<Entity>InDb` doesn't match `<Entity>Dto` is [ReservationOptionRepo](src/modules/reservation/repos/ReservationOptionRepo.ts) and [ReservationOptionRepoFake](src/modules/reservation/repos/ReservationOptionRepoFake.ts). Moreover, in this case, I needed some auxiliary functions (mappers), so I write those functions in `ReservationOptionRepo`, make them static and use them in `ReservationOptionRepoFake` too.

The most simple example, in which only a fake repo is used: [UserRepoFake.ts](src/modules/usersExample/repos/UserRepoFake.ts)

In `<Entity>RepoFake`, `migrations/<Entities>.json` is read and inputted in the constructor, this way:
```typescript
import { ReservationsInDb as json } from '@shared/infra/database/sequelize/migrations/Reservations.json';

export class ReservationRepoFake implements IReservationRepo {
  private readonly ReservationsInDb: ReservationInDb[];

  public constructor() {
    this.ReservationsInDb = [...json];  // Load a brand new copy of the json for every fake repo
  }
```
In the unit tests, a brand new `<Entity>RepoFake` is created for each test, putting it inside `vitest.beforeEach` to avoid shared state between tests.

Ejemplo de un repo simple que no usa transactions, ni events, guardando un `Entity` en lugar de `AggregateRoot`, que no extiende de `Repository`: [AnalyticsEventRepo](https://github.com/s4nt14go/bookBot/blob/master/src/modules/analytics/repos/AnalyticsEventRepo.ts).

## Dependencias externas

Como estoy desarrollando varios módulos en un mismo proyecto, y dentro de cada módulo me resulta útil importar funcionalidades de otros módulos, las importo (en lugar de copiar y pegar, como sería el caso si estos otros módulos estén en otros proyectos), pero lo hago pasando por un archivo `external.ts` para hacerlo más explícito.

Estos archivos están presentes en los módulos:
* [reservation](src/modules/reservation/external.ts)
* [calendar](src/modules/calendar/external.ts)

Estos archivos `external.ts` son código que será deployado a producción, por lo que no deben tener archivos fakes o de tests. Para ello utilizar la carpeta `utils`. En dicha carpeta, también separar las funciones que dependen sólo del código del módulo (`utils/test.ts`), de las que dependen de otros módulos (`utils/testExternal.ts`).

Ejemplos de estos archivos de test son:
* Módulo `reservation`:
  * [test.ts](src/modules/reservation/utils/test.ts)
  * [testExternal.ts](src/modules/reservation/utils/testExternal.ts)
* Módulo `calendar`:
  * [test.ts](src/modules/calendar/utils/test.ts)
  * [testExternal.ts](src/modules/calendar/utils/testExternal.ts)

## Uso de `undefined`

Si bien en el dominio se permite el uso de `undefined` y `null`, eso trae complicaciones cuando se trata de variables que se guardan en la base de datos, ya que en la base de datos sólo existe `null`. Por ejemplo, analicemos el caso del create y update de un crud de la tabla `User`:

```typescript
type CreateDTO = {
  name: string;
  description?: string;
}

type UpdateDTO = {
  name: string;
  description?: string;
}

sequelizeModels.User.create({
  name: 'James',
  description: 'Great guy',
});
// DB state:
// { name: 'James', description: 'Great guy' }

sequelizeModels.User.update({ 
  name: 'Paul',
}, { where : { name: 'James' } });
// DB state:
// { name: 'Paul', description: 'Great guy' }
```

Al hacer `update` se actualiza el campo `name` pero no el campo `description`. Para que también se actualice `description` hay que enviar `null` en lugar de `undefined`. Para evitar estos malos entendidos, no permitir `undefined` y manejarse sólo con `null`.

Sin embargo, cuando se trata de variables intermedias que no se mapean a columnas en la base de datos, se pude usar `undefined`.

## Distintos tipos de ValueObjects

Un `ValueObject` puede ser algo simple como una primitive (por ejemplo, `string` o `number`):
```typescript
type EmailDto = string;
type EmailProps = {
  value: EmailDto;
}
// ...
class Email extends ValueObject<EmailProps, EmailDto> {
  private constructor(props: EmailProps) {
    super(props);
  }
  // ...
  public toDto(): EmailDto {
    return this.value;
  }
}
```

Un objecto compuesto de primitives:

```typescript
type TimeReferenceProps = {
  hour: number;
  minute: number;
  zone: string;
}
type TimeReferenceDto = TimeReferenceProps;
// ...
class TimeReference extends ValueObject<
  TimeReferenceProps,
  TimeReferenceDto
> {
  private constructor(props: TimeReferenceProps) {
    super(props);
  }
  // ...
  public toDto(): TimeReferenceDto {
    return this.props;
  }
}
```

O un objecto compuesto por otros ValueObjects:

```typescript
type LocationProps = {
  name: string | null;
  description: string | null;
  identifier: IdentifierT;
  showInfoWhenReserving: boolean;
};
type LocationDto = Spread<LocationProps, {
  identifier: string;
}>;
// ...
class Location extends ValueObject<LocationProps, LocationDto> {
  private constructor(props: LocationProps) {
    super(props);
  }
  // ...
  public toDto(): LocationDto {
    return {
      ...this.props,
      identifier: this.props.identifier.value,
    };
  }
}
```

Si quiero hacer editable las propiedades del value object, poner `public declare props: <VOprops>;`, lo que permite hacer por ejemplo en [Integer.ts](https://github.com/s4nt14go/bookBot/blob/master/src/shared/core/Integer.ts):
```typescript
public add(by: Integer) {
  this.props = {
    value: this.props.value + by.value,
  };
}
```

## Ejemplos para reusar ValueObjects y *Errors

Ejemplo para reusar `ValueObject`: [Description.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/domain/Description.ts) <br />
Ejemplo para reusar `*Errors`: [CancelReservationErrors.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/cancelReservation/CancelReservationErrors.ts) reusa [CancelReservationByBusinessErrors.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/cancelReservationByBusiness/CancelReservationByBusinessErrors.ts)

## Entity.create()

Entities can be created in 2 ways. One when we are fine with a random id:

```typescript
const user = User.create({
  name: 'James',
  description: 'Great guy',
});
```

> E.g.: [User](https://github.com/s4nt14go/bookBot/blob/d5b75d19f29dc101b97b8180d4035ca0bd4cf488/src/modules/usersExample/domain/User.ts#L59)

And another when we want to specify the id:

```typescript
const user = User.create({
  name: 'James',
  description: 'Great guy',
}, '123');
```

> E.g.: [Calendar](https://github.com/s4nt14go/bookBot/blob/d5b75d19f29dc101b97b8180d4035ca0bd4cf488/src/modules/calendar/domain/Calendar.ts#L33)

## Ejemplos para usar como referencia

- Creación de función para validación inicial de los argumentos del request que pueden hacerse en el frontend en real-time antes de realizar el request al backend (`errorsRT`, a diferencia de los provenientes del backend `errorsBK`): [UpdateGSreservationLimits.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/calendar/useCases/updateGSreservationLimits/UpdateGSreservationLimits.ts). Usando fields anidados: [CreateReservationOption.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/createReservationOption/CreateReservationOption.ts), [UpdateReservationOption.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/updateReservationOption/UpdateReservationOption.ts).
- Errores con traducciones: [CreateReservationErrors.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/reservation/useCases/createReservation/CreateReservationErrors.ts)
> Es necesario que el endpoint devuelva errores con el formato que espera el front (por ejemplo, `BaseErroDto`), cuando estos son mostrados en el front, si estos errores no se muestran en la interface no sería necesario, de todas formas es bueno mantener una estandarización.
- Errores custom: `Error404` en [CalendarApi.unit.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/calendar/services/CalendarApi.unit.ts) y `Error1` en [https://github.com/s4nt14go/bookBot/blob/master/test.ts](src/shared/utils/test.ts)
- Para las validaciones de los datos de entrada de los use cases, en aquellos casos en los que el usuario no ingresa los datos, si dan error al intentar crear el value object, asumir valores de default y que el flujo no falle, ya que el usuario no tiene forma de subsanar estos errores. Por ejemplo, este es el caso del envío del `timezone` y `userLng` en [CreateBusinessUser.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/user/useCases/createBusinessUser/CreateBusinessUser.ts) que sería llenado automáticamente por el frontend. Sin embargo, en el caso de [UpdateBusinessUser.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/user/useCases/updateBusinessUser/UpdateBusinessUser.ts), como el usuario llena el formulario para `timezone` y `userLng`, sí se devuelven los errores al frontend, fallando el flujo.

### Uso del contexto para pasar `sendAnalytics()` a lo largo de la aplicación

Utilicé [ContextProvider](https://github.com/s4nt14go/bookBot/blob/master/src/shared/context/ContextProvider.ts) para enviar analytics de esta forma:
```ts
const contextProvider = new ContextProvider({ invoker });
const sendAnalytics = contextProvider.sendAnalytics.bind(contextProvider);

const repo = new Repo({
  RepoModel: models.Model,
  sendAnalytics,
});
const controller = new Controller({
  repo,
  contextProvider,
});
```
En la mayoría de los casos, con poner simplemente
```ts
const { sendAnalytics } = contextProvider
```
...en lugar de:
```ts
const sendAnalytics = contextProvider.sendAnalytics.bind(contextProvider)
```
...es suficiente, sin embargo en algunos casos (como [CheckMakeChangesAndSave.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/calendar/useCases/checkMakeChangesAndSave/CheckMakeChangesAndSave.ts) y [CheckGetEventsAndSave.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/calendar/useCases/checkGetEventsAndSave/CheckGetEventsAndSave.ts)), daba el error `Context not set` al usar `sendAnalytics()`, por lo que mejor adoptar siempre:
```ts
const sendAnalytics = contextProvider.sendAnalytics.bind(contextProvider)
```

## Exponential backoff

Ejemplo del uso de la librería [exponential-backoff](https://github.com/coveooss/exponential-backoff?tab=readme-ov-file#readme) con `Promise.allSettled`:
```typescript
// pruebas.ts
import { backOff } from 'exponential-backoff';

const p1 = new Promise((resolve) => {
  setTimeout(() => {
    resolve(0);
  }, 2000);
});
const p2 = new Promise((resolve) => {
  resolve(1);
});
const p3 = () => new Promise((resolve) => {
  throw Error('Error lanzado');
});
const p4 = () => new Promise((resolve, reject) => {
  reject('Rechazada');
  // resolve('Ok');
});
const p4b2 = async () => {
  return await backOff(() => p4());
}
const main = async () => {
  const fulfilledORRejected = await Promise.allSettled([p1, p2, p3(), p4b2()]);
  console.log({fulfilledORRejected});
}

main();
```
> Ejecutar con `ts-node pruebas.ts`

En el código fue usado en:
* [CalendarApi.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/calendar/services/CalendarApi.ts)
* [LambdaInvoker.ts](https://github.com/s4nt14go/bookBot/blob/master/src/shared/infra/invocation/LambdaInvoker.ts)
* `commonBackOffOptions`  y `isRateLimitExceeded` están en [utils.ts](https://github.com/s4nt14go/bookBot/blob/master/src/shared/utils/utils.ts)

Para no utilizar transacciones en la bd (lo que bloquearía la bd), utilicé un aggregate con key compuesta `id` e `id2` [AggregateRootCK.ts](https://github.com/s4nt14go/bookBot/blob/master/src/shared/core/domain/AggregateRootCK.ts) extendido por [BalanceRow.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/balance/domain/BalanceRow.ts), que si al insertar un nuevo registro da sequelize unique error, reintenta con exponential backoff, en el use case [MoveInvolvingReservation.ts](https://github.com/s4nt14go/bookBot/blob/master/src/modules/balance/useCases/moveInvolvingReservation/MoveInvolvingReservation.ts)

## Reintento desde el frontend

Me supo dar error de timeout la lambda `getAvailabilityOfReservationOp-JipZ6WyP4hfR`, request id `d2ba5038-cbc8-4b4b-a9a2-ca664130a3fd` a las 2025-04-15T12:54:01.682Z.

Al ppio pensé en partir `GetAvailableTimes.ts` (`@calendar`) en 2 funciones, en donde en la 1era se llamara a un `GetAvailableTimesContext.ts`, para llamar a `calendarRepo.getAvailability`, `calendarRepo.getReservations` y `generalSettingsRepo.get` sólo una vez desde `GetAvailabilityOfReservationOptions.ts` (`@reservation`) y no llamar esas funciones por cada reservation option. Sin embargo al ver en el [log](https://us-east-1.console.aws.amazon.com/cloudwatch/home?region=us-east-1#logsV2:log-groups/log-group/$252Faws$252Flambda$252Fdev-turnero-MyStack-getAvailabilityOfReservationOp-JipZ6WyP4hfR/log-events/2025$252F04$252F15$252F$255B$2524LATEST$255Da6fd3a1a9ad043c7957534fe482333f9) de cloudwatch que en realidad esa parte no demora mucho, desistí:

<p align="center">
    <img src="doc/timedOutLog.png" />
</p>

Me parece mejor, detectar en el [frontend](https://github.com/s4nt14go/turnero-front/blob/master/src/pages/schedule/Schedule.tsx) que fue un error de timeout y reintentar el request.

## Exhaustividad en tests

En los tests unitarios testeo todas las posibilidades de outpus y argumentos de entradas.

En los e2e tests testeo las respuestas `OK` y errores que pueda provocar sin mockear. Me permito utilizar `chance.pickone()` en los argumentos de entrada, de manera que a lo largo de ejecuciones de los tests, se prueben distintos casos, aunque no todas las posibilidades cada vez que ejecuto los tests.

## Enviar SMS con AWS SNS

Estado inicial:
<p align="center">
    <img src="doc/SMS/SMS%20sandbox.png" />
</p>

Al agregar un número de teléfono al sandbox, se puede enviar SMS a ese número:
<p align="center">
    <img src="doc/SMS/SMS%20sandbox%20with%20verified%20phone%20number.png" />
</p>

Al clickear en "Exit SMS Sandbox", dirige a "Service quota increase":
<p align="center">
    <img src="doc/SMS/Request%20exit%20from%20sandbox.png" />
</p>

Explicación de la solicitud de salida del sandbox:
```text
To use my app, users need to confirm their phone number. A verification code is sent via SMS only when the user requests it. They have a single attempt, and the code is valid for just one minute. If they enter the correct code, their phone number is verified.
I’ve already developed the frontend for this verification process and am now starting to implement it on the backend. The frontend (still under development) is deployed at:
https://master--turnero-s4nt14go.netlify.app/profile
The app is currently in the development stage and has not been publicly launched yet.
```

Completado de la sección Request:

**Region: US East (N. Virginia)**<br />
El [precio](https://aws.amazon.com/es/sns/sms-pricing) depende del lugar a donde se envía el SMS, no el origen, por lo que elegir la AWS region que vengo usando (por lo general US East (N. Virginia) - us-east-1 es la de menor precio en general).

**Resource Type: General Limits**<br />
Tenía dudas con elegir "Dedicated SMS Short Codes" pero eso es para high-volume marketing campaigns, "General Limits" es más económico.

**New quota value: 100**<br />
100 SMS per day gives you room to properly test and launch your app. It's reasonable for a development/early launch phase. AWS prefers to see legitimate usage patterns before granting large increases. Starting smaller shows you're not planning to spam. AWS support tends to approve reasonable initial requests faster, and you can always scale up with demonstrated legitimate usage.
Daily Usage Estimate: If you have 50 new users per day = 50 SMS. Plus some failed attempts/retries = ~70-80 SMS. 100 per day gives you a comfortable buffer

Para ver el caso creado: https://support.console.aws.amazon.com/support/home#/case/history

## Entidades

Inicialmente planteé las entidades `Service` y `Location` como entidades simples compuestas de primitives, mientras que `ReservationOption` se componía de dichas entidades.

Sin embargo, es más conveniente directamente guardar una snapshot de `Service` y `Location` junto a `ReservationOption` al crearla o editarla. De manera que `Service` y `Location` pasan a ser value objects, contenidos por `ReservationOption`.

[Último commit antes de convertir a `Service` y `Location` en value objects](https://github.com/s4nt14go/bookBot/blob/c1343e8a080a4a5b0cf418dea1f5d1cc45f6091a/src/modules/reservation/domain/ReservationOption.ts) 

## Pasos para crear/modificar un nuevo use case

Primero con los unit tests modificando domain, faked repos y controlador del use case. Los faked repos leen `src/shared/infra/database/sequelize/migrations/<Entidad>s.json`

Luego con los e2e tests del controlador del use case, agregándolo a AppSync y usando repos reales. Para los repos reales es necesario agregar las migraciones de la db en `src/shared/infra/database/sequelize/migrations/<migración>.js`.

## Crear package del domain para usar en el frontend

Para crear un package que usa path aliases, instalar:
```shell
npm i -D tsc-alias
```

Copiar como modelo la carpeta `pack`, `front.ts` y `front-dev.ts`. Hay que verificar que el `target` usado en el proyecto y lo que está en `pack/tsconfig.json` coincidan. El `tsconfig.json` del proyecto, extiende de `@tsconfig/node20/tsconfig.json`, que tiene:
```json5
{
  ...
  "display": "Node 20",
  "_version": "20.1.0",
  "compilerOptions": {
    ...
    "target": "es2022",
    ...
  }
}
```
Es por ello que en `pack/tsconfig.json` puse:
```json5
{
  "compilerOptions": {
    "lib": ["ES2022", "DOM"],
    "target": "ES2022",
    "moduleResolution": "bundler",
    ...
  }
}
```

Al importar `src/modules/calendar/utils/test` en `front-dev.ts`, incluye el `require`:
```typescript
const models = require('../../../shared/infra/database/sequelize/models');
```
...a su vez, ese `index.js` tiene:
```javascript
const config = require('../config/config');
```
...por lo que en `pack/dev/package.json` incluí los scripts `cp-models` y `cp-config` que copian los archivos `.js` necesarios.

Luego realizar el siguiente flujo para generar el package.

### Flujo para generar un package y usar localmente

Primero agregar lo que se desea exportar en `front.ts` y agregar las dependencias de `package.json` que usa lo exportado a `pack/package.json`

Luego, para generar una nueva versión hay que ubicarse en la carpeta `pack`, incrementarla en `package.json` y para hacer un build local en la carpeta `lib` ejecutar:
```shell
npm run build
```

Para hacer un package local sin publicarlo:
```shell
npm run pack
```
Genera un `.tgz` que se puede descomprimir en una carpeta `package` para ver el contenido.
> Notar que `npm run pack` ejecuta `npm pack` y copia el `.tgz` a la carpeta del frontend. Por lo que no confundirse y ejecutar `npm pack` cuando lo correcto es `npm run pack`

Para instalarlo en el frontend:
```shell
npm i <ruta-al-package.tgz>
```
Luego de instalar el package en el frontend, detener servidor de React y volver a arrancar.

Para ejecutar un archivo `.ts`:
```shell
ts-node <archivo.ts>
```

De forma similar, para exportar las dependencias de desarrollo que son usadas en los tests del front, usar `front-dev.ts` y `pack/dev`.

> Al exportar luxon con `@s4nt14go/book-backend` e instalarlo en el frontend, en el frontend podía usar luxon sin necesidad de instalarlo con `npm i luxon`. Lo único que tuve que instalar fueron los types como developer dependency mediante `npm i @types/luxon -D`

> Algunas veces, luego de instalarlo en el frontend, daba error, teniendo que reiniciar el servidor de vite.

### Flujo para publicar un package en npmjs.com

Para saber cuál es la última versión publicada en npmjs.com:
```shell
npm view @s4nt14go/book-backend
```

Para publicar una nueva versión en npmjs.com, ejecutar desde la carpeta `pack`:
```shell
npm publish --access public
```

Para instalar en el frontend:
```shell
npm i @s4nt14go/book-backend@latest && npm list
```
Luego de instalar el package en el frontend, detener servidor de React y volver a arrancar. Algunas veces no instalaba la última versión publicada, que solucioné publicando una nueva versión, repitiendo todo el proceso.

### Error "The requested module backend does not provide an export named \<modulo\>" en el frontend

Cuando en el frontend sale un error del tipo:
```shell
Uncaught SyntaxError: The requested module '/node_modules/.vite/deps/@s4nt14go_book-backend.js?v=3bbbf50c' does not provide an export named 'reservation'
```
Verificar que probablemente el autocompletado de WebStorm importó mal algo del backend, como por ejemplo:
```typescript
import ReservationOptionDto = reservation.ReservationOptionDto;
```
...cuando lo correcto sería:
```typescript
type ReservationOptionDto = reservation.ReservationOptionDto;
```

### Cómo exportar el type de un enum

```typescript
export namespace reservation {
  export const CustomerType = _CustomerType;
  export type CustomerTypeT =`${_CustomerType}`;
}
```

## Explorar la API en Apollo Sandbox

Ir a https://studio.apollographql.com/sandbox, copiar la url (por ejemplo: https://6bc47rkc7zdifkjndukpb6cejy.appsync-api.us-east-1.amazonaws.com/graphql) y en la configuración agregar dentro de "Shared headers":
```dotenv
x-api-key: <api-key>
# Por ejemplo para https://6bc47rkc7zdifkjndukpb6cejy.appsync-api.us-east-1.amazonaws.com/graphql:
x-api-key: da2-7bery4xhkfbapgwj7bi6oh6374
```

## Uso de Google Calendar API

Pasos en la console de Google Cloud para crear un servicio que es el que usaremos desde la app de nodejs:
- En [console.cloud.google.com](https://console.cloud.google.com) crear un proyecto. Por ejemplo, usando <EMAIL>, creo proyecto `prueba1-s4nt14godev`
- Habilito la Google Calendar API
- Crear credencial "Cuenta de servicio" con nombre `Servicio1`, lo que genera `<EMAIL>` y guardar la clave (`prueba1--s4nt14godev-*.json`).

Pasos a realizar en el Calendar para compartir un calendario con el servicio: 
- Primero crear calendario, por ejemplo calendario `pelu`
- En la configuración de `pelu` > Compartir con personas o grupos específicos > Agregar personas y grupos > email del servicio, por ejemplo `<EMAIL>` seleccionando con Permiso para "Realizar cambios en los eventos"

En un momento me pasó que dejó de funcionar la `CalendarApi`, realicé varias cosas y pude hacela funcionar devuelta pero una cosa que noté al final es que no sé por qué en los permisos de los calendarios estaba mal puestos los emails de las cuentas de servicio, en lugar de decir `<EMAIL>`, ahora decía `<EMAIL>` (sin el `1` de `servicio1`).

Lo debo haber desconfigurado por error ya que en la solapa "Conexión" del front tambén estaba mal puesto sin el 1. Ese valor en el frontend no tiene ninguna función más que a los fines explicativos al usuario para configurar sus calendarios, ya que lo que importa es lo que está en el [Parameter Store](https://us-east-1.console.aws.amazon.com/systems-manager/parameters/%252Fturnero%252Fdev%252Fcalendar/description?region=us-east-1&tab=Table) del backend (leído por [MyStack.ts](https://github.com/s4nt14go/bookBot/blob/master/src/shared/infra/iac/MyStack.ts)), que es quien interactúa con Google Calendar API. Una forma de notar que hay algo mal en la configuración de los calendarios es que el avatar de un email inválido sale gris en lugar de azul:
<p align="center">
    <img src="doc/servicioCon1.png" width="350" />
    <img src="doc/servicioSin1.png" width="350" />
</p>

Para el debugging es útil el proyecto `calendarApi` modificando la variable `calendarId` (según qué calendario configuré con el email de la cuenta de servicio que estoy probando), y la importación de `credentials` usando la clave json creada para esa cuenta de servicio.

Otra cosa que hice fue que activé la cuenta de servicios <NAME_EMAIL> como completa. Al empezar a usar la API existe una cuota [gratuita](https://cloud.google.com/free/docs/free-cloud-features?hl=es-419) y la activación la debo hacer manualmente clickeando en un botón azul arriba a la derecha. De ser necesario esto, cuando cree el email/proyecto/servicio para producción hacerlo de una así no se interrumpe el servicio.

Creé nuevas credenciales para renovar la cuota gratuita:
- Nuevo gmail: <EMAIL>
- Proyecto en Google Cloud Console: turnero
- Habilité la Google Calendar API
- Cree cuenta de servicio `servicio1` con clave json `winged-precept-448216-r7-31510f88d470.json` (copiada en proyecto `calendarApi`).
- Actualicé [Parameter Store](https://us-east-1.console.aws.amazon.com/systems-manager/parameters/%252Fturnero%252Fdev%252Fcalendar/description?region=us-east-1&tab=Table) con los datos de la clave y redeployé backend.
- Agregué <EMAIL> con permiso "Realizar cambios en los eventos" a todos los calendarios
- Actualicé `src/modules/calendar/services/CalendarApi.e2e.ts` y copié `winged-precept-448216-r7-31510f88d470.json` para que utilice las nuevas credenciales 

Al crear el proyecto el 18/1/25 en GCC me salió esto:
<p align="center">
    <img src="doc/pruebaGratuita1.png" width="350" />
    <img src="doc/pruebaGratuita2.png" width="350" />
</p>

Para acceder a esa info, ir a console.cloud.google.com, en el avatar de arriba a la derecha seleccionar que sea el gmail correcto (por ejemplo, `<EMAIL>`), seleccionar el proyecto (`turnero`), desplegar menú lateral izquierdo, Facturación. 

## Subdomains

Si bien en las propiedades uso "?" por una cuestión de brevedad, en realidad correspondería poner "\<type> | null", por lo que se menciona en [Uso de undefined](#uso-de-undefined).

### user

**Responsibilities:**
* Handle user registration and retrieval of user data.

**Entities:**
* BusinessUser
    - Props:
        - firstName: Name
        - lastName: Name
        - email: Email
        - phone?: string
        - timezone: TimeZone

* CustomerUser
    - Props:
        - firstName: Name
        - lastName: Name
        - email: Email
        - phone?: string
        - timezone: TimeZone

* ManuallyCreatedCustomer
    - Props:
        - firstName: Name
        - lastName: Name
        - email?: Email
        - phone?: string
        - createdBy: string (business user id)

**Use cases / controllers:**
* `CreateBusinessUser({ firstName: string, lastName: string, email: string, timezone: string })`:`BusinessUserDto`
* `CreateCustomerUser({ firstName: string, lastName: string, email: string, timezone: string })`:`CustomerUserDto`
* `GetBusinessUser({ id: string })`:`BusinessUserDto`
* `GetCustomerUser({ id: string })`:`CustomerUserDto`

**Use cases / controllers TBI:**
* `UpdateAccountInfo({ userType: string, id: string, firstName: string, lastName: string, timeZone: string })`: `UPDATED | NO_CHANGES`
* `ChangePhone({ userType: string, id: string, phone: string })`: `INITIATED | NO_CHANGES`
* `ChangeEmail({ userType: string, id: string, email: string })`: `INITIATED | NO_CHANGES`
* `GetCustomers({ customerIds: string[] })`:`CustomerUserDto[]` --> para ser usado por use case GetReservations@reservation

### calendar

**Responsibilities:**
* Handle authorization check to verify the app was given access to user's Google Calendar.

**Entities:**
* Calendar
    - Dto:
        - id: string (Google calendar id)
        - name: string
        - userId: string (linked to a business user in subdomain user)
        - type: AVAILABILITY | RESERVATIONS

* GeneralSettings
    - Dto:
        - id: string (linked to a business user in subdomain user)
        - reservationLimits: reservationLimits

**Use cases / controllers:**
* ```typescript
    CheckAndSave({ userId: string, calendarId: string, type: string }): {
        action: CREATED | UPDATED | NO_CHANGES,
        name: string,
    }
  ```
  Verifies whether the app has the necessary authorization to access the user's Google Calendar. It returns the calendar name.
* ```typescript
  GetAvailableTimes({
    userId: string,
    identifier: string,
    every: number,
    from: string,
    to: string,
    timeReference: {
        hour: number,
        minute: number,
        zone: string,
  }) : [TimeWithSpotsDto]
  ```
Retrieves available times and spots from the user's availability and reservation Google Calendar for a given location, duration and time reference.
* ```typescript
  CreateReservationEvent({
    userId: string,
    identifier: string,
    start: string,
    end: string,
    name: string,
    timeZone: string,
    customer: {
        firstName: string,
        lastName: string,
        phone: string,
        email: string,
        phone: string,
  }): CREATED | UPDATED
  ```
Creates a new event in the user's reservation Google Calendar.

**Use cases / controllers TBI:**
* `DeleteCalendar({ userId: string, type: string })`: `OK | NOT_FOUND`
* `UpcomingEvents({ userId: string, type: string })`: `UpcomingEvent[] = {name?: string, identifiers: string[] | null}`
* `SaveReservationLimits({ userId: string, maxDaysAhead: number, minTimeBeforeService: number })`: `GeneralSettingsDto`
* `CancelReservation({ userId: string, eventId: string, start: string, end: string })` : `OK | NOT_FOUND`

### reservation

**Responsibilities:**
* Handle creation and retrieval of services and locations. Entities: Service, Location
* Entity Location holds the hashtag needed to detect the allowed slots for reservation which will be used when querying calendar subdomain.
* Handle creation and retrieval of reservations options, which are combinations of one service and one location. Entity: ReservationOption.
* Handle creation of new reservations based on reservations options. Entity: Reservation

La entity Reservation se usa para que el customer pueda cancelar o modificar la reserva pero como ese evento que se va a crear en el calendar puede ser borrado por el business user, puede dejar de existir en cualquier momento por lo que siempre se deberá consultar al servicio de calendar para tener datos actualizados.

**Entities:**
* GeneralSettings
    - Dto:
        - id: string (the same as the business userId)
        - cancellationUpTo: N0 (in minutes)
        - title?: string
        - welcome?: string
        - slug?: Slug

* Service (value object)
    - Props:
        - name: Name
        - every: Every
        - price?: Price
        - description?: string <br />

* Location (value object)
    - Props:
        - name?: Name
        - description?: string
        - identifier: Identifier
        - showInfoWhenReserving: boolean

* Reservation <br />
Al momento de realizar la reserva, se guarda una snapshot de los datos de `ReservationOption`, `BusinessUser` y `CustomerUser`/`ManuallyCreatedCustomer`. Ya que estos pueden ser editados o borrados.
    - Props:
        - userId (link to the business user in subdomain user)
        - business: BusinessUserDto
        - customerType: REGISTERED | MANUALLY_CREATED
        - customerId: string
        - customer: CustomerUserDto | ManuallyCreatedCustomerDto
        - start: Dat
        - eventId: string
        - roId: string
        - reservationOption: ReservationOptionDto
        // With defaults at creation:
        - cancelledBy?: CUSTOMER | BUSINESS
        - cancellationReason?: string
        - cancelledDate?: Dat

* ReservationOption
    - Props:
        - userId (link to the business user in subdomain user)
        - name: Name
        - timeReference: TimeReference
        - service: Service
        - location: Location
        - explanatoryNotes?: ExplanatoryNotes <br />
        - active: boolean

**Use cases / controllers:**

* ```typescript
  CreateReservationOption({ 
    userId: string, 
    name: string, 
    service: { 
        name: string, 
        every: number, 
        price?: number,
        description?: string, 
    }, 
    location: { 
        name?: string, 
        description?: string, 
        identifier: string, 
        showInfoWhenReserving: boolean,
    }, 
    timeReference: { 
        hour: number, 
        minute: number, 
        zone: string,
    },
    explanatoryNotes: {
      while?: string,
      after?: string,
    },
  }) : ReservationOptionDto
  ```

* `GetReservationOptions({ userId: string })`:`ReservationOptionDto[] | null`

* ```typescript
  CreateReservation({
    customerId: string,
    businessId: string, 
    start: string,
    roId: string,
    reservationOptionVersion: number,
  }) : ReservationDto
  ```

**Use cases / controllers TBI:**
* ```typescript
  UpdateReservationOption({ 
    id: string,
    idem as CreateReservationOption
  }) : ReservationOptionDto
  ```
* `DeleteReservationOption({ userId: string, id: string })`: `OK | NOT_FOUND`
* `EnableReservationOption({ userId: string, id: string })`: `OK | NOT_FOUND`
* `DisableReservationOption({ userId: string, id: string })`: `OK | NOT_FOUND`
* `GetReservations({ userId: string })`:`ReservationDto[] | null`
* `GetGeneralSettings({ userId: string })` : `GeneralSettingsDto`
* `SaveStoreInfo({ userId: string, title?: string, welcome?: string, slug: string})` : `GeneralSettingsDto`
* `SaveCancelationUpTo({ userId: string, cancellationUpTo: number})` : `GeneralSettingsDto`
* `GetLastUsedNotes({ userId: string })` : `ExplanatoryNotes`
* `CancelReservation({ id, customerId: string, businessId: string })` : `OK | NOT_FOUND`  --> llama a CancelReservation@calendar mediante un event (no en forma sincrónica), ya que aunque CancelReservation@calendar falle, no cambia nada en el dominio de reservation.

## Diferencias entre `ReservationOption.name` y `ReservationOption.Service.name`

`ReservationOption.name` debe ser única entre las opciones de reserva del usuario, de esa forma cuando creo o edito y quiero completar los campos usando una existente como plantilla, sus nombres son únicos.

`ReservationOption.Service.name` no tiene por qué ser único, ya que puede haber varios servicios con el mismo nombre, por ejemplo "Corte de pelo", que son proporcionados en distintas locations. Aunque eso puede generar confusión ya que en la selección de servicios en la agenda van a salir varios servicios con el mismo nombre, se puede poner un nombre más descriptivo que lo haga único.

`ReservationOption.name` sólo es conocido por el business, `ReservationOption.Service.name` sale en la agenda y es lo que ve el customer.