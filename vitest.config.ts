// @ts-ignore
import { defineConfig } from 'vitest/config';
import path from 'path';

const options = {} as Record<string, unknown>;
const { TEST_MODE } = process.env;
switch (TEST_MODE) {
  case 'unitNoBO':  // unit tests without backoff tests
    options.include = ['**/*.unit.ts', '**/*.unit.js'];
    options.exclude = [
      '**/CalendarApi.unit.ts',
      '**/LambdaInvoker.unit.ts',
      '**/CreateReservationByBusiness.unit.ts',
      '**/balance/utils/utils.unit.ts',
    ];
    options.setupFiles = ['./vitest.unit.env.ts'];
    break;
  case 'unit':
    options.include = ['**/*.unit.ts', '**/*.unit.js'];
    options.testTimeout = 12000;  // Had to increase from the default 5s when testing backoff
    options.setupFiles = ['./vitest.unit.env.ts'];
    break;
  case 'e2e':
    options.include = ['**/*.e2e.ts', '**/*.int.ts'];
    options.testTimeout = 25000;
    // options.testTimeout = 2147483647;
    options.setupFiles = ['./vitest.e2e.env.ts'];
    break;

  default:
    throw new Error(`Unknown TEST_MODE: ${TEST_MODE}`);
}

export default defineConfig({
  test: {
    ...options,
    coverage: {
      exclude: [
        '.sst',
        'src/shared/events',
        'src/shared/infra',
        'src/modules/*/repos',
        'src/modules/*/utils',
      ],
    },
  },
  resolve: {
    alias: [
      { find: '@shared', replacement: path.resolve(__dirname, './src/shared') },
    ],
  },
});
