{
  // To be able to use `ts-node` to run typescript files using the `paths` configuration. Do not forget to `npm i -D tsconfig-paths`
  "ts-node": {
    "require": ["tsconfig-paths/register"]
  },
  "compilerOptions": {
    "noImplicitAny": true,                               /* Enable error reporting for expressions and declarations with an implied 'any' type. */
    "resolveJsonModule": true,                           /* Enable importing .json files. */
    "baseUrl": "./src",
    "paths": {
      "@shared/*": ["shared/*"],
    },
  },
  "extends": "@tsconfig/node20/tsconfig.json"
}