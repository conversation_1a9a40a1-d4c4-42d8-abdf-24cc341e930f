import { IUserRepo } from './IUserRepo';
import { UserEmail } from '../domain/UserEmail';
import { User, UserDto } from '../domain/User';
import { Timestamps } from '@shared/infra/database/sequelize/config/Timestamps';
import { UsersInDb } from '@shared/infra/database/sequelize/migrations/UsersExample.json';

type UserToDb = UserDto;
export type UserInDb = UserToDb & Timestamps;

export class UserRepoFake implements IUserRepo {
  private UsersInDb: UserInDb[];

  public constructor() {
    this.UsersInDb = UsersInDb;
  }
  public async findByUsername(username: string): Promise<User | null> {
    const userInDb = this.UsersInDb.filter(
      (user) => user.username === username,
    )[0];
    if (!userInDb) return null;
    return User.assemble(userInDb);
  }
  public async exists(email: UserEmail): Promise<boolean> {
    const usersInDb = this.UsersInDb.filter((user) => user.email === email.value);
    return !!usersInDb.length;
  }

  public async create(user: User) {
    const now = new Date().toISOString();
    this.UsersInDb.push({
      ...user.toDto(),
      created_at: now,
      updated_at: now,
      deleted_at: null,
    });
  }
}
