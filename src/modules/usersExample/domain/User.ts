import { AggregateRoot } from '@shared/core/domain/AggregateRoot';
import { EntityID } from '@shared/core/domain/EntityID';
import { UserEmail } from './UserEmail';
import { UserCreatedEventExample as UserCreatedEvent } from './events/UserCreatedEventExample';
import { UserName } from './UserName';
import { Alias } from './Alias';
import { Spread } from '@shared/utils/utils';

export type UserInput = {
  email: UserEmail;
  username: UserName;
  alias: Ali<PERSON> | null;
  isAdmin: boolean;
};

type UserProps = UserInput & {
  isEmailVerified: boolean; // default value at creation is false
};

export type UserDto = Spread<
  UserProps,
  {
    id: string;
    email: string;
    username: string;
    alias: string | null;
    isAdmin: boolean;
  }
>;

export class User extends AggregateRoot<UserProps, UserDto> {
  private __class = this.constructor.name;

  get email(): UserEmail {
    return this.props.email;
  }

  get isEmailVerified(): boolean {
    return this.props.isEmailVerified;
  }

  get username(): UserName {
    return this.props.username;
  }

  get alias(): Alias | null {
    return this.props.alias;
  }

  get isAdmin(): boolean {
    return this.props.isAdmin;
  }

  private constructor(props: UserProps, id?: EntityID) {
    super(props, id);
  }

  public static create(input: UserInput): User {
    const user = new User({
      ...input,
      isEmailVerified: false,
    });
    user.addDomainEvent(new UserCreatedEvent(user.toDto()));
    return user;
  }

  public toDto(): UserDto {
    const { id, email, username, alias, isEmailVerified, isAdmin } = this;

    return {
      id: id.toString(),
      email: email.value,
      username: username.value,
      alias: alias === null ? null : alias.value,
      isEmailVerified,
      isAdmin,
    };
  }

  public static assemble(dto: UserDto): User {
    const { id, email, username, alias } = dto;
    return new User(
      {
        ...dto,
        email: UserEmail.create(email).value,
        username: UserName.create(username).value,
        alias: alias === null ? null : Alias.create(alias).value,
      },
      new EntityID(id),
    );
  }
}
