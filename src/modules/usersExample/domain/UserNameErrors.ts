import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';

export namespace UserNameErrors {
  export class TooShort extends BadRequest {
    public constructor(minLength: number) {
      super({ message: `It should be at least ${minLength} characters long` });
    }
  }

  export class <PERSON>Long extends BadRequest {
    public constructor(maxLength: number) {
      super({ message: `It should be at most ${maxLength} characters long` });
    }
  }
}

patch({ UserNameErrors });
