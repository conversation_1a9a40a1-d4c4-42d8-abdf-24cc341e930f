import { expect, test } from 'vitest';
import { User } from '../User';
import { Alias } from '../Alias';
import { UserName } from '../UserName';
import { UserEmail } from '../UserEmail';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

test(`UserCreatedEventExample is added to user during creation`, () => {
  // const user = createUser({});
  const user = User.create({
    email: UserEmail.create(chance.email()).value,
    username: UserName.create(
      chance.string({ alpha: true, numeric: true, length: 15 }),
    ).value,
    alias: Alias.create(chance.string({ alpha: true, numeric: true, length: 15 }))
      .value,
    isAdmin: chance.bool(),
  });

  expect(user.domainEvents.length).toBe(1);
  const domainEvent = user.domainEvents[0];
  expect(domainEvent.constructor.name).toBe('UserCreatedEventExample');
  expect(user.id.toString()).toBe(domainEvent.aggregateId);
});
