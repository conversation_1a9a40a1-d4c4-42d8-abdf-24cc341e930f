import { UserDto } from '../User';
import { DomainEventBase } from '@shared/events/DomainEventBase';
import { DomainEventTypes } from '@shared/events/DomainEventTypes';

export class UserCreatedEventExample extends DomainEventBase {
  public user;

  public constructor(user: UserDto) {
    super({
      aggregateId: user.id,
      type: DomainEventTypes.UserCreatedEventExample,
    });
    this.user = user;
  }
}
