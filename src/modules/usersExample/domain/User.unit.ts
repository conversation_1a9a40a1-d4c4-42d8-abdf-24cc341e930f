import { expect, test } from 'vitest';
import { User } from './User';
import { <PERSON><PERSON> } from './Alias';
import { UserEmail } from './UserEmail';
import { UserName } from './UserName';
import { orNull } from '@shared/utils/test';

const defaultProps = {
  isEmailVerified: false,
};

test(`create & assemble back a user with alias`, () => {
  const dto = {
    email: '<EMAIL>',
    username: 'test_username',
    alias: orNull('test_alias'),
    isAdmin: false,
  };

  const args = {
    ...dto,
    email: UserEmail.create(dto.email).value,
    username: UserName.create(dto.username).value,
    alias: dto.alias === null ? null : Alias.create(dto.alias).value,
  };
  const created = User.create(args);

  expect(created.toDto()).toMatchObject(dto);

  const assembled = User.assemble({
    ...dto,
    ...defaultProps,
    id: created.id.toString(),
  });

  expect(assembled.equals(created)).toBe(true);
});

test(`Create user without alias`, () => {
  const dto = {
    email: '<EMAIL>',
    username: 'test_username',
    alias: orNull('test_alias'),
    isAdmin: true,
  };

  const args = {
    ...dto,
    ...defaultProps,
    email: UserEmail.create(dto.email).value,
    username: UserName.create(dto.username).value,
    alias: dto.alias === null ? null : Alias.create(dto.alias).value,
  };
  const created = User.create(args);

  expect(created.toDto()).toMatchObject(dto);

  const assembled = User.assemble({
    ...dto,
    ...defaultProps,
    id: created.id.toString(),
  });

  expect(assembled.equals(created)).toBe(true);
});

test(`assemble from db`, () => {
  const inDB = {
    id: 'testId',
    email: '<EMAIL>',
    username: 'username1',
    alias: orNull('test_alias'),
    isEmailVerified: true,
    isAdmin: false,
  };

  const assembled = User.assemble(inDB);

  expect(assembled.toDto()).toMatchObject(inDB);
});
