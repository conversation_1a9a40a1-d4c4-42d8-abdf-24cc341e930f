import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { AliasErrors } from './AliasErrors';

type AliasDto = string;

type AliasProps = {
  value: AliasDto;
};

export class Alias extends ValueObject<AliasProps, AliasDto> {
  private __class = this.constructor.name;
  public static maxLength = 15;
  public static minLength = 2;

  get value(): string {
    return this.props.value;
  }

  private constructor(props: AliasProps) {
    super(props);
  }

  public static create(value: string): Result<Alias> {
    const trimmed = value.trim();
    const invalidChars = trimmed.match(/[^a-z0-9.\-_+]/gi);
    if (invalidChars)
      return Result.fail(new AliasErrors.InvalidCharacters(invalidChars));
    return Result.convertValue(trimmed)
      .ensure((value: string) => {
        return value.length >= this.minLength;
      }, new AliasErrors.TooShort(this.minLength))
      .ensure((value: string) => {
        return value.length <= this.maxLength;
      }, new AliasErrors.TooLong(this.maxLength))
      .onBoth((result) =>
        result.isSuccess
          ? Result.ok<Alias>(new Alias({ value: result.value }))
          : Result.fail(result.error!),
      );
  }

  public toDto(): AliasDto {
    return this.value;
  }
}
