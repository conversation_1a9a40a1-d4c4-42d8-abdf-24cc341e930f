import { expect, test } from 'vitest';
import { UserEmail } from './UserEmail';

test(`Creation`, () => {
  const result = UserEmail.create('<EMAIL>');

  expect(result.isSuccess).toBe(true);
  const userEmail = result.value;
  expect(userEmail.value).toBe('<EMAIL>');
});

test(`Fails with invalid email`, () => {
  const result = UserEmail.create('john@gmail.b');

  expect(result).toMatchObject({
    isFailure: true,
    error: {
      message: expect.any(String),
      type: 'CreateEmailErrors.EmailNotValid',
      status: 400,
    },
  });
});
