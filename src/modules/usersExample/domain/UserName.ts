import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { UserNameErrors } from './UserNameErrors';

interface UserNameProps {
  value: string;
}

interface UserNameDto extends UserNameProps {}

export class UserName extends ValueObject<UserNameProps, UserNameDto> {
  private __class = this.constructor.name;
  public static maxLength = 15;
  public static minLength = 2;

  get value(): string {
    return this.props.value;
  }

  private constructor(props: UserNameProps) {
    super(props);
  }

  public static create(name: string): Result<UserName> {
    const trimmed = name.trim();
    return Result.convertValue(trimmed)
      .ensure((value: string) => {
        return value.length >= this.minLength;
      }, new UserNameErrors.TooShort(this.minLength))
      .ensure((value: string) => {
        return value.length <= this.maxLength;
      }, new UserNameErrors.TooLong(this.maxLength))
      .onBoth((result) =>
        result.isSuccess
          ? Result.ok<UserName>(new UserName({ value: result.value }))
          : Result.fail(result.error!),
      );
  }

  public toDto(): UserNameDto {
    return {
      value: this.value,
    };
  }
}
