import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';

export namespace AliasErrors {
  export class TooShort extends BadRequest {
    public constructor(minLength: number) {
      super({ message: `<PERSON><PERSON> should be at least ${minLength} characters long` });
    }
  }

  export class TooLong extends BadRequest {
    public constructor(maxLength: number) {
      super({ message: `<PERSON><PERSON> should be at most ${maxLength} characters long` });
    }
  }

  export class InvalidCharacters extends BadRequest {
    public constructor(chars: string[]) {
      super({ message: `Invalid characters detected: ${chars.join(' ')}` });
    }
  }
}

patch({ AliasErrors });
