import { expect, test, describe } from 'vitest';
import { <PERSON><PERSON> } from './Alias';
import { AliasErrors } from './AliasErrors';

test(`Creation using a string`, () => {
  const result = Alias.create('test_alias');
  expect(result.isSuccess).toBe(true);
  const alias = result.value;
  expect(alias.value).toBe('test_alias');
});

describe(`Creation fails with`, () => {
  test(`a short value`, () => {
    const result = Alias.create('1');
    expect(result).toMatchObject({
      isFailure: true,
      error: {
        message: expect.any(String),
        type: 'AliasErrors.TooShort',
        status: 400,
      },
    });
  });

  test(`with a long value`, () => {
    const result = Alias.create('1234567890123456');
    expect(result).toMatchObject({
      isFailure: true,
      error: {
        message: expect.any(String),
        type: 'AliasErrors.TooLong',
        status: 400,
      },
    });
  });

  test(`with invalid characters`, () => {
    const result = Alias.create('Alias123$%^&{');
    expect(result).toMatchObject({
      isFailure: true,
      error: {
        message: expect.any(String),
        type: 'AliasErrors.InvalidCharacters',
        status: 400,
      },
    });
    const msg = (result.error as AliasErrors.InvalidCharacters).message;
    expect('$%^&{'.split('').every((item) => msg.includes(item))).toBe(true);
  });
});
