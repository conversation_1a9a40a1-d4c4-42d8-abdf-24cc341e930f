import { expect, test } from 'vitest';
import { UserName } from './UserName';

test(`Creation`, () => {
  const result = UserName.create('test_name');
  expect(result.isSuccess).toBe(true);
  const username = result.value;
  expect(username.value).toBe('test_name');
});

test(`Creation fails with a short username`, () => {
  const result = UserName.create('1');
  expect(result).toMatchObject({
    isFailure: true,
    error: {
      message: expect.any(String),
      type: 'UserNameErrors.TooShort',
      status: 400,
    },
  });
});

test(`Creation fails with a long username`, () => {
  const result = UserName.create('1234567890123456');
  expect(result).toMatchObject({
    isFailure: true,
    error: {
      message: expect.any(String),
      type: 'UserNameErrors.TooLong',
      status: 400,
    },
  });
});
