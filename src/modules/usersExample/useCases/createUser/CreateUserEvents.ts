import { DomainEvents } from '@shared/core/domain/DomainEvents';
import { UserCreatedEventExample as UserCreatedEvent } from '../../domain/events/UserCreatedEventExample';
import { IInvoker } from '@shared/infra/invocation/IInvoker';

export class CreateUserEvents {
  public static registration(invoker: IInvoker) {
    // Add all process.env used:
    const { distributeDomainEvents } = process.env;
    if (!distributeDomainEvents) {
      console.log('process.env', process.env);
      throw new Error(`Undefined env var!`);
    }

    DomainEvents.setInvoker(invoker);
    DomainEvents.register(`${distributeDomainEvents}`, UserCreatedEvent.name);
  }
}
