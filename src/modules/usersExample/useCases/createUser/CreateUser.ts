import { Request, Response } from './CreateUserDTOs';
import { CreateUserErrors } from './CreateUserErrors';
import { UserEmail } from '../../domain/UserEmail';
import { UserName } from '../../domain/UserName';
import { Result } from '@shared/core/Result';
import { User } from '../../domain/User';
import { IUserRepo } from '../../repos/IUserRepo';
import { CreateUserEvents } from './CreateUserEvents';
import { Alias } from '../../domain/Alias';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { formatErrors } from '@shared/core/AppError';
import { IInvoker } from '@shared/infra/invocation/IInvoker';
const { CREATED, CONFLICT } = Status;

export class CreateUser {
  private readonly userRepo: IUserRepo;
  public constructor(userRepo: IUserRepo, invoker: IInvoker) {
    this.userRepo = userRepo;
    CreateUserEvents.registration(invoker);
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const emailOrError = UserEmail.create(dto.email);
    const usernameOrError = UserName.create(dto.username);

    const dtoResult = Result.combine([emailOrError, usernameOrError]);

    if (dtoResult.isFailure) return formatErrors([dtoResult.error!]);
    const email = emailOrError.value;
    const username = usernameOrError.value;

    let alias = null;
    if (dto.alias !== null) {
      const aliasOrError = Alias.create(dto.alias);
      if (aliasOrError.isFailure)
        return formatErrors([aliasOrError.error!]);
      alias = aliasOrError.value;
    }

    const emailAlreadyTaken = await this.userRepo.exists(email);
    if (emailAlreadyTaken)
      return {
        status: CONFLICT,
        result: new CreateUserErrors.EmailAlreadyTaken(email.value),
      };

    const usernameAlreadyTaken = await this.userRepo.findByUsername(
      username.value,
    );
    if (usernameAlreadyTaken)
      return {
        status: CONFLICT,
        result: new CreateUserErrors.UsernameAlreadyTaken(username.value),
      };

    const user = User.create({
      email,
      username,
      alias,
      isAdmin: dto.isAdmin,
    });

    await this.userRepo.create(user);

    return { status: CREATED, result: user.toDto() };
  }
}
