import { expect, test, vi, beforeEach } from 'vitest';
vi.stubEnv('distributeDomainEvents', 'dummy');
import { CreateUser } from './CreateUser';
import { UserRepoFake } from '../../repos/UserRepoFake';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { uuidFormat } from '@shared/utils/utils';
import { orNull } from '@shared/utils/test';
import { UsersInDb } from '@shared/infra/database/sequelize/migrations/UsersExample.json';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

let userRepo, createUser: CreateUser;
beforeEach(() => {
  userRepo = new UserRepoFake();
  createUser = new CreateUser(userRepo, new LambdaInvokerFake());
});

const defaultProps = {
  isEmailVerified: false,
};

test(`User creation with alias`, async () => {
  const validData = {
    username: chance.string({ alpha: true, numeric: true, length: 15 }),
    email: chance.email(),
    alias: orNull('test_alias'),
    isAdmin: false,
  };

  const result = await createUser.executeImpl(validData);

  expect(result).toMatchObject({
    status: 201,
    result: {
      id: expect.stringMatching(uuidFormat),
      ...validData,
      ...defaultProps,
    },
  });
});

test(`User creation without alias`, async () => {
  const validData = {
    username: chance.string({ alpha: true, numeric: true, length: 15 }),
    email: chance.email(),
    alias: null,
    isAdmin: false,
  };

  const result = await createUser.executeImpl(validData);

  expect(result).toMatchObject({
    status: 201,
    result: {
      id: expect.stringMatching(uuidFormat),
      ...validData,
      ...defaultProps,
    },
  });
});

test(`User creation fails for taken email`, async () => {
  const data = {
    username: chance.string({ alpha: true, numeric: true, length: 15 }),
    email: UsersInDb[0].email,
    alias: null,
    isAdmin: true,
  };

  const result = await createUser.executeImpl(data);

  expect(result).toMatchObject({
    status: 409,
    result: {
      message: expect.any(String),
      type: 'CreateUserErrors.EmailAlreadyTaken',
      status: 409,
    },
  });
});

test(`User creation fails for taken username`, async () => {
  const data = {
    username: UsersInDb[0].username,
    email: chance.email(),
    alias: null,
    isAdmin: true,
  };

  const result = await createUser.executeImpl(data);

  expect(result).toMatchObject({
    status: 409,
    result: {
      message: expect.any(String),
      type: 'CreateUserErrors.UsernameAlreadyTaken',
      status: 409,
    },
  });
});
