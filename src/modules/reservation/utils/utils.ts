import { ManuallyCreatedCustomerDto } from '../external';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isManuallyCreatedCustomerDto(test: any): test is ManuallyCreatedCustomerDto {
  return test && test.createdBy && test.fullName;
}

export function getContactDetails(args: {
  customerPhone: string | null;
  customerEmail: string | null;
  lng: 'en' | 'es';
}) {
  const { customerPhone, customerEmail, lng } = args;
  const phone = lng === 'es'? 'teléfono' : 'phone';
  return (customerPhone || customerEmail ? `(` : '') +
    (customerPhone ? `${phone}: ${customerPhone}` : '') +
    (customerPhone && customerEmail ? `, ` : '') +
    (customerEmail ? `email: ${customerEmail}` : '') +
    (customerPhone || customerEmail ? `) ` : '');
}