// Funciones usadas en los tests que dependen de otros módulos externos.
import { BusinessUsersInDb as _BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { CustomerUsersInDb as _CustomerUsersInDb } from '@shared/infra/database/sequelize/migrations/CustomerUsers.json';
import { ManuallyCreatedCustomersInDb as _ManuallyCreatedCustomersInDb } from '@shared/infra/database/sequelize/migrations/ManuallyCreatedCustomers.json';

// Imports for createLocation() that uses external Identifier
import {
  CustomerUserDto,
  Identifier,
  IdentifierT,
  ManuallyCreatedCustomerDto,
} from '../external';
import { Location, LocationDto } from '../domain/Location';
import { orNull } from '@shared/utils/test';
import { sendAnalyticsDummy as sendAnalytics } from '@shared/utils/utils';
// Imports for getCreateResOptReq() and createReservationOption() that depend on createLocation()
import { Request as CreateReservationOptionRequest } from '../useCases/createReservationOption/CreateReservationOptionDTOs';
import {
  ReservationOption,
  ReservationOptionDto,
} from '../domain/ReservationOption';
import { Every } from '../domain/Every';
import {
  createExplanatoryNotes,
  createName,
  createService,
  createTimeReference,
  getFutureTime,
  getPossibleEvery,
} from './test';

import {
  rmEvents as _rmEvents,
} from '../../calendar/utils/utils';

import { tomorrowPlus as _tomorrowPlus } from '../../calendar/utils/utils';
import { CalendarsInDb as _CalendarsInDb } from '@shared/infra/database/sequelize/migrations/Calendars.json';

import {
  createBusinessUser as _createBusinessUser,
  createCustomerManually,
} from '../../user/utils/test';
import { createCustomerUser as _createCustomerUser } from '../../user/utils/test';

import { CalendarApi as _CalendarApi } from '../../calendar/services/CalendarApi';
import { Event as _Event } from '../../calendar/domain/Event';

import { CalendarApiErrors } from '../../calendar/services/CalendarApiErrors';

// import for CreateReservationByBusiness.unit.ts
import { Action as _Action } from '../../calendar/useCases/createReservationEvent/CreateReservationEvent';

import { BalanceRepo } from '../../balance/repos/BalanceRepo';
import { rmBalanceRow as _rmBalanceRow } from '../../balance/utils/test';
import { Movement as _Movement } from '../../balance/domain/BalanceRow';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize/models'); // If I use @shared unit tests fail

// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

export const balanceRepo = new BalanceRepo(models.BalanceRow);

export const BusinessUsersInDb = _BusinessUsersInDb;
export const CustomerUsersInDb = _CustomerUsersInDb;
export const ManuallyCreatedCustomersInDb = _ManuallyCreatedCustomersInDb;
import { Slot as _Slot } from '../../calendar/domain/Slot';
import { getLng } from '@shared/utils/utils';
import { Name } from '../domain/Name';
import { Description } from '../domain/Description';
import { CustomerType, Reservation } from '../domain/Reservation';
import { Dat } from '@shared/core/Dat';
import { CustomerUser } from '../../user/domain/CustomerUser';
import { BusinessUserRepo } from '../../user/repos/BusinessUserRepo';

export function createLocation(): {
  location: Location;
  locationDto: LocationDto;
} {
  const location = Location.create({
    name: Name.create({ value: chance.word() }).value,
    description: orNull(Description.create({ value: chance.sentence() }).value),
    identifier: Identifier.create({ value: chance.word() }, sendAnalytics).value,
    showInfoWhenReserving: chance.bool(),
  });
  return {
    location,
    locationDto: location.toDto(),
  };
}

export function getCreateResOptReq(): CreateReservationOptionRequest {
  return {
    userId: chance.guid({ version: 4 }),
    name: createName().value,
    service: createService().serviceDto,
    location: createLocation().locationDto,
    every: getPossibleEvery(),
    timeReference: createTimeReference().toDto(),
    explanatoryNotes: createExplanatoryNotes().toDto(),
    active: chance.bool(),
    lng: getLng(),
  };
}

export function createReservationOption(userId?: string): {
  reservationOption: ReservationOption;
  dto: ReservationOptionDto;
} {
  const { service } = createService();
  const { location } = createLocation();
  const reservationOption = ReservationOption.create({
    userId: userId? userId : chance.guid({ version: 4 }),
    name: createName(),
    service,
    location,
    timeReference: createTimeReference(),
    explanatoryNotes: createExplanatoryNotes(),
    active: chance.bool(),
    every: Every.create({ value: getPossibleEvery() }).value,
  });
  return { reservationOption, dto: reservationOption.toDto() };
}

export const rmEvents = _rmEvents;

export const tomorrowPlus = _tomorrowPlus;
export const CalendarsInDb = _CalendarsInDb;

export const createBusinessUser = _createBusinessUser;
export const createCustomerUser = _createCustomerUser;

export const CalendarApi = _CalendarApi;
export type CalendarApiT = _CalendarApi;
export const Event = _Event;
export const Slot = _Slot;

export const CalendarApiErrors_CalendarNotFound =
  CalendarApiErrors.CalendarNotFound;
export const CalendarApiErrors_CalendarEventNotFound =
  CalendarApiErrors.CalendarEventNotFound;
export const CalendarApiErrors_InvalidGrant = CalendarApiErrors.InvalidGrant;

export function createReservation(args?: { userId?: string, customerUser?: CustomerUser }) {
  const _args = args? args : {};
  const { userId, customerUser } = _args;
  const _userId = userId? userId : chance.guid();
  let customer: CustomerUserDto | ManuallyCreatedCustomerDto,
      customerType: CustomerType;
  if (customerUser) {
    customer = customerUser.toDto();
    customerType = CustomerType.REGISTERED;
  } else {
    customerType = chance.pickone(Object.keys(CustomerType));
    switch (customerType) {
      case CustomerType.MANUALLY_CREATED:
        customer = createCustomerManually(_userId).dto;
        break;
      case CustomerType.REGISTERED:
        customer = createCustomerUser().dto;
        break;
      default:
        throw Error(`Invalid customer type ${customerType}`);
    }
  }
  const reservationOption = createReservationOption(_userId).dto;
  const reservation = Reservation.create({
    userId: _userId,
    customerType,
    customerId: customer.id,
    customerFirstName: customer.firstName,
    customerLastName: customer.lastName,
    // @ts-expect-error phone doesn't exist in ManuallyCreatedCustomerDto
    customerPhone: customer.phone? customer.phone : null,
    // @ts-expect-error email doesn't exist in ManuallyCreatedCustomerDto
    customerEmail: customer.email? customer.email : null,
    start: Dat.create({ value: getFutureTime() }).value,
    eventId: chance.string(),
    roId: reservationOption.id,
    roZone: reservationOption.timeReference.zone,
    srvName: reservationOption.service.name,
    srvDuration: reservationOption.service.duration,
    srvDescription: reservationOption.service.description,
    srvPrice: reservationOption.service.price,
    locName: reservationOption.location.name,
    locId: reservationOption.location.identifier,
  });

  return {
    reservation,
    reservationOption,
    dto: reservation.toDto(),
    customer,
  }
}

export const Action = _Action;

export function getTestData(args: {
  initial: Dat;
  identifier: IdentifierT;
}) {
  const { initial, identifier } = args;
  const availabilityEvent1 = Event.create({
    name: 'Availability 1',
    description: `${identifier.query}:2`,
    slot: Slot.create({
      start: initial,
      end: Dat.create({ value: initial.l.plus({ minutes: 60 }) }).value,
    }).value,
  });
  const availabilityEvent2 = Event.create({
    name: 'Availability 2',
    description: `${identifier.query}:3`,
    slot: Slot.create({
      start: Dat.create({ value: initial.l.plus({ minutes: 30 }) }).value,
      end: Dat.create({ value: initial.l.plus({ minutes: 90 }) }).value,
    }).value,
  });

  return { availabilityEvent1, availabilityEvent2 };
}

export const rmBalanceRow = _rmBalanceRow;

export const Movement = _Movement;

export function getBusinessUser(args: { id: string }) {
  const { id } = args;
  const inDB = BusinessUsersInDb.find((bu) => bu.id === id);
  if (!inDB) return null;
  return BusinessUserRepo.mapDb2Dto(inDB);
}