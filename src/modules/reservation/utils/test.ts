// Funciones usadas en los tests que no dependen de otros módulos externos
import { Service, ServiceDto } from '../domain/Service';
import { UtilsClass } from '@shared/utils/UtilsClass';
import { Name } from '../domain/Name';
import { Price } from '../domain/Price';
import { ExplanatoryNotes } from '../domain/ExplanatoryNotes';
import { TimeReference } from '../domain/TimeReference';
import {
  ReservationOption,
  ReservationOptionDto,
} from '../domain/ReservationOption';
import { ReservationOptionInDb } from '../repos/ReservationOptionRepo';
import {
  dateFormat,
  orNull,
} from '@shared/utils/test';
import {
  sendAnalyticsDummy as sendAnalytics,
} from '@shared/utils/utils';
import { getTimezone } from '@shared/core/test';
import { createPositiveInt } from '@shared/infra/appsync/test';
import { Description } from '../domain/Description';
import { expect } from 'vitest';
import { Dat } from '@shared/core/Dat';
import { DateTime } from 'luxon';
import { Reservation } from '../domain/Reservation';
import { ReservationRepo } from '../repos/ReservationRepo';
import {
  ReservationsInDb,
} from '@shared/infra/database/sequelize/migrations/Reservations.json';
import { MaxDaysAhead } from '../domain/MaxDaysAhead';
import { ReservationLimits } from '../domain/ReservationLimits';
import { N0 } from '@shared/core/N0';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const GSmodel = models.GeneralSettingsForReservation;

export function getPossibleEvery() {
  const possiblesEvery = [
    1, 2, 5, 10, 15, 16, 20, 30, 45, 60, 80, 90, 120, 144, 160, 180,
  ];
  return possiblesEvery[
    chance.integer({ min: 0, max: possiblesEvery.length - 1 })
  ];
}

export function getPrice(): number {
  return chance.floating({ min: 1, fixed: 2, max: UtilsClass.MAX_ROUNDING });
}

export function createTimeReference(): TimeReference {
  return TimeReference.create({
    hour: chance.integer({ min: 0, max: 23 }),
    minute: chance.integer({ min: 0, max: 59 }),
    zone: getTimezone(),
  }).value;
}

export function createExplanatoryNotes(): ExplanatoryNotes {
  return ExplanatoryNotes.create({
    while: orNull(chance.sentence()),
    after: orNull(chance.sentence()),
  });
}

export function createService(): { service: Service; serviceDto: ServiceDto } {
  const service = Service.create({
    name: Name.create({ value: chance.word() }).value,
    description: orNull(Description.create({ value: chance.sentence()}).value),
    duration: createPositiveInt(),
    price: orNull(Price.create({ value: getPrice() }).value),
  });
  return {
    service: service,
    serviceDto: service.toDto(),
  };
}

export function createName(): Name {
  return Name.create({ value: chance.string({ alpha: true, numeric: true }) }).value;
}

export function createDescription(): Description {
  return Description.create({ value: chance.sentence() }).value;
}

export function createReservationLimits() {
  return ReservationLimits.create({
    maxDaysAhead: MaxDaysAhead.create({ value: chance.integer({ min: 1, max: MaxDaysAhead.MAX }) }).value,
    minTimeBeforeService: N0.create({ value: chance.integer({ min: 0, max: 60 }) }).value,
  }).value;
}

export function removeReservationOption(id: string) {
  return models.ReservationOption.destroy({
    where: { id },
    force: true,
  });
}

export function rmReservations(ids: string[]) {
  return models.Reservation.destroy({
    where: { id: ids },
    force: true,
  });
}

export function removeGS(id: string) {
  return models.GeneralSettingsForReservation.destroy({
    where: { id },
    force: true,
  });
}

// Copied from ReservationOptionRepo.ts but adding paranoid option
export async function getReservationOptionIncludingDeleted(args: {
  userId: string;
  id: string;
}): Promise<ReservationOption | null> {
  const { userId, id } = args;
  const found: { dataValues: ReservationOptionInDb } =
    await models.ReservationOption.findOne({
      where: {
        userId,
        id,
      },
      paranoid: false,
    });
  if (!found) return null;

  const dto = ReservationOptionRepoMapDb2Dto(found.dataValues);
  return ReservationOption.assemble(dto, sendAnalytics);
}
const ReservationOptionRepoMapDb2Dto = (
  resOptInDb: ReservationOptionInDb,
): ReservationOptionDto => {
  const {
    srvName,
    srvDescription,
    srvDuration,
    srvPrice,
    locName,
    locDescription,
    locIdentifier,
    locShowInfoWhenReserving,
    trHour,
    trMinute,
    trZone,
    noteWhile,
    noteAfter,
    ...rest
  } = resOptInDb;
  return {
    ...rest,
    service: {
      name: srvName,
      description: srvDescription,
      duration: srvDuration,
      price: srvPrice,
    },
    location: {
      name: locName,
      description: locDescription,
      identifier: locIdentifier,
      showInfoWhenReserving: locShowInfoWhenReserving,
    },
    timeReference: {
      hour: trHour,
      minute: trMinute,
      zone: trZone,
    },
    explanatoryNotes: {
      while: noteWhile,
      after: noteAfter,
    },
  };
};

export async function rmGS(id: string) {
  return GSmodel.destroy({
    where: { id },
  });
}

export const timestamps = {
  created_at: expect.stringMatching(dateFormat),
  updated_at: expect.stringMatching(dateFormat),
  deleted_at: null,
};

export const getFutureTime = (minutes?: number) => Dat.create({ value: DateTime.now().plus({ minutes: minutes? minutes : 2 })}).value.s;
export const getPastTime = () => Dat.create({ value: DateTime.now().minus({ minutes: 2 })}).value.s;

export const getReservationByIndex = (index: number)=> Reservation.assemble(
  ReservationRepo.mapDb2Dto({
    ...ReservationsInDb[index],
    start: getFutureTime(1441), // A minute later than the max cancellationUpTo in general settings
  })
);

export function createMaxDaysAhead(value?: number) {
  const v = value !== undefined ? value : chance.integer({ min: 1, max: MaxDaysAhead.MAX });
  return MaxDaysAhead.create({ value: v }).value;
}