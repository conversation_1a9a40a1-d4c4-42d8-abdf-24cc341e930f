import gql from 'graphql-tag';

export const ReservationFragment = gql`
  fragment ReservationFragment on Reservation {
    id
    userId
    start
    customerType
    cancelledBy
    cancelledDate
    cancellationReason
    cancelReservationEventErrors
    customerId
    customerFirstName
    customerLastName
    customerPhone
    customerEmail
    eventId
    roId
    roZone
    srvName
    srvDuration
    srvDescription
    srvPrice
    locName
    locId
    reminder
  }
`;
export const ReservationWithTimestampsFragment = gql`
  fragment ReservationWithTimestampsFragment on ReservationWithTimestamps {
    id
    userId
    start
    customerType
    cancelledBy
    cancelledDate
    cancellationReason
    cancelReservationEventErrors
    customerId
    customerFirstName
    customerLastName
    customerPhone
    customerEmail
    eventId
    roId
    roZone
    srvName
    srvDuration
    srvDescription
    srvPrice
    locName
    locId
    reminder
    created_at
    updated_at
    deleted_at
  }
`;

const ServiceFragment = gql`
  fragment ServiceFragment on Service {
    name
    description
    duration
    price
  }
`;
const LocationFragment = gql`
  fragment LocationFragment on Location {
    name
    description
    identifier
    showInfoWhenReserving
  }
`;
const TimeReferenceFragment = gql`
  fragment TimeReferenceFragment on TimeReference {
    hour
    minute
    zone
  }
`;
const ExplanatoryNotesFragment = gql`
  fragment ExplanatoryNotesFragment on ExplanatoryNotes {
    while
    after
  }
`;
const ReservationOptionFragment = gql`
  fragment ReservationOptionFragment on ReservationOption {
    id
    userId
    name
    service {
      ...ServiceFragment
    }
    location {
      ...LocationFragment
    }
    timeReference {
      ...TimeReferenceFragment
    }
    explanatoryNotes {
      ...ExplanatoryNotesFragment
    }
    active
    every
    version
  }
  ${ServiceFragment}
  ${LocationFragment}
  ${TimeReferenceFragment}
  ${ExplanatoryNotesFragment}
`;

export const createReservationOptionQuery = gql`
  mutation (
    $userId: ID!
    $name: String!
    $service: ServiceInput!
    $location: LocationInput!
    $timeReference: TimeReferenceInput!
    $explanatoryNotes: ExplanatoryNotesInput!
    $active: Boolean!
    $every: Int!
    $lng: String!
  ) {
    createReservationOption(
      userId: $userId
      name: $name
      service: $service
      location: $location
      timeReference: $timeReference
      explanatoryNotes: $explanatoryNotes
      active: $active
      every: $every
      lng: $lng
    ) {
      result {
        ...ReservationOptionFragment
      }
      time
    }
  }
  ${ReservationOptionFragment}
`;

const TimeWithSpotsFragment = gql`
  fragment TimeWithSpotsFragment on TimeWithSpots {
    spots
    time
  }
`;
const BaseErrorFragment = gql`
  fragment BaseErrorFragment on BaseError {
    type
    message
    status
  }
`;
export const ReservationOptionWithTimestampsFragment = gql`
  fragment ReservationOptionWithTimestampsFragment on ReservationOptionWithTimestamps {
    id
    userId
    name
    service {
      ...ServiceFragment
    }
    location {
      ...LocationFragment
    }
    timeReference {
      ...TimeReferenceFragment
    }
    explanatoryNotes {
      ...ExplanatoryNotesFragment
    }
    active
    every
    version
    created_at
    updated_at
    deleted_at
  }
  ${ServiceFragment}
  ${LocationFragment}
  ${TimeReferenceFragment}
  ${ExplanatoryNotesFragment}
`;
export const ReservationOptionWithTimestampsAndAvailabilityFragment = gql`
  fragment ReservationOptionWithTimestampsAndAvailabilityFragment on ReservationOptionWithTimestampsAndAvailability {
    id
    userId
    name
    service {
      ...ServiceFragment
    }
    location {
      ...LocationFragment
    }
    timeReference {
      ...TimeReferenceFragment
    }
    explanatoryNotes {
      ...ExplanatoryNotesFragment
    }
    active
    every
    version
    created_at
    updated_at
    deleted_at
    availabilityGot {
      ...TimeWithSpotsFragment
    }
    availabilityErrors {
      ...BaseErrorFragment
    }
  }
  ${ServiceFragment}
  ${LocationFragment}
  ${TimeReferenceFragment}
  ${ExplanatoryNotesFragment}
  ${TimeWithSpotsFragment}
  ${BaseErrorFragment}
`;

const ReservationLimitsFragment = gql`
  fragment ReservationLimitsFragment on ReservationLimits {
    maxDaysAhead
    minTimeBeforeService
  }
`;
export const GSforReservationsFragment = gql`
  fragment GSforReservationsFragment on GSforReservations {
    id
    cancellationUpTo
    phoneRequired
    maxSimultaneousReservations
    reservationLimits {
      ...ReservationLimitsFragment
    }
    reminderTemplate
    title
    welcome
    slug
  }
  ${ReservationLimitsFragment}
`;