// Este archivo no debe tener ningún código fake, ni test, ya que es lo que se deploya a producción. Usar utils/testExternal.ts para ese tipo de código importado desde otros módulos.
// region calendar
import { Identifier as _Identifier } from '../calendar/domain/Identifier';
import {
  Response as _CreateReservationEventRes,
  Request as _CreateReservationEventReq,
} from '../calendar/useCases/createReservationEvent/CreateReservationEventDTOs';
import {
  Request as _CancelReservationEventReq,
  Response as _CancelReservationEventRes,
} from '../calendar/useCases/cancelReservationEvent/CancelReservationEventDTOs';
import {
  Request as _GetAvailableTimesReq,
  Response as _GetAvailableTimesRes,
} from '../calendar/useCases/getAvailableTimes/GetAvailableTimesDTOs';
// endregion
// region user
import {
  Response as _GetCustomerAndBusinessUserRes,
  Request as _GetCustomerAndBusinessUserReq,
} from '../user/useCases/getCustomerAndBusinessUser/GetCustomerAndBusinessUserDTOs';
import {
  Response as _GetMCustomerAndBusinessUserRes,
  Request as _GetMCustomerAndBusinessUserReq,
} from '../user/useCases/getMCustomerAndBusinessUser/GetMCustomerAndBusinessUserDTOs';
import {
  Response as _GetBusinessUserFCRes,
  Request as _GetBusinessUserFCReq,
} from '../user/useCases/getBusinessUserFC/GetBusinessUserFCDTOs';
import { CustomerUserDto as _CustomerUserDto } from '../user/domain/CustomerUser';
import { ManuallyCreatedCustomerDto as _ManuallyCreatedCustomerDto } from '../user/domain/ManuallyCreatedCustomer';
import { BusinessUserCreatedEventDto as _BusinessUserCreatedEventDto } from '../user/domain/events/BusinessUserCreatedEvent';
// endregion
// region balance
import {
  Request as _IsBalanceEnoughReq,
  Response as _IsBalanceEnoughRes,
} from '../balance/useCases/isBalanceEnough/IsBalanceEnoughDTOs';
// endregion

// region calendar
export const Identifier = _Identifier;
export type IdentifierT = _Identifier;
export type CreateReservationEventReq = _CreateReservationEventReq;
export type CreateReservationEventRes = _CreateReservationEventRes;
export type CancelReservationEventReq = _CancelReservationEventReq;
export type CancelReservationEventRes = _CancelReservationEventRes;
export type GetAvailableTimesReq = _GetAvailableTimesReq;
export type GetAvailableTimesRes = _GetAvailableTimesRes;
// endregion
// region user
export type GetCustomerAndBusinessUserReq = _GetCustomerAndBusinessUserReq;
export type GetCustomerAndBusinessUserRes = _GetCustomerAndBusinessUserRes;
export type GetMCustomerAndBusinessUserReq = _GetMCustomerAndBusinessUserReq;
export type GetMCustomerAndBusinessUserRes = _GetMCustomerAndBusinessUserRes;
export type GetBusinessUserFCReq = _GetBusinessUserFCReq;
export type GetBusinessUserFCRes = _GetBusinessUserFCRes;
export type CustomerUserDto = _CustomerUserDto;
export type ManuallyCreatedCustomerDto = _ManuallyCreatedCustomerDto;
export type BusinessUserCreatedEventDto = _BusinessUserCreatedEventDto;
// endregion
// region balance
export type IsBalanceEnoughReq = _IsBalanceEnoughReq;
export type IsBalanceEnoughRes = _IsBalanceEnoughRes;
// endregion