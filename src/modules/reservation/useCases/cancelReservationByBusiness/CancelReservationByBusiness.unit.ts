import { expect, vi, it, describe, beforeEach, test } from 'vitest';
vi.stubEnv('distributeDomainEvents', 'dummy');
import { CancelReservationByBusiness } from './CancelReservationByBusiness';
import { ReservationRepoFake } from '../../repos/ReservationRepoFake';
import {
  CancelReservationEventReq,
  CancelReservationEventRes,
  GetBusinessUserFCReq,
  GetBusinessUserFCRes,
} from '../../external';
import {
  dateFormat, Error1, Error2,
  expectControllerError, 
  getAppsyncCtx,
  invokerDummy,
  mockContext,
  nonExistingId,
  orNull,
  pickLng,
} from '@shared/utils/test';
import { Result2 } from '@shared/core/Result2';
import { BaseError } from '@shared/core/AppError';
import { ReservationsInDb } from '@shared/infra/database/sequelize/migrations/Reservations.json';
import {
  BusinessUsersInDb,
  CalendarApiErrors_CalendarEventNotFound,
  CalendarApiErrors_CalendarNotFound,
  CalendarApiErrors_InvalidGrant,
  getBusinessUser,
} from '../../utils/testExternal';
import { ReservationRepo } from '../../repos/ReservationRepo';
import { FC2Handler, FCHandler } from '@shared/infra/Controllers';
import { Reservation } from '../../domain/Reservation';
import {
  getFutureTime,
  getReservationByIndex,
} from '../../utils/test';
import { ContextProvider } from '@shared/context/ContextProvider';
import { BusinessUserRepo } from '../../../user/repos/BusinessUserRepo';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const lng = pickLng();
const reason = orNull(chance.sentence());

const getReservationByIds = (args: {
  userId: string;
  id: string;
}): Reservation | null => {
  const { id, userId } = args;
  const inDB = ReservationsInDb.find((res) => res.id === id && res.userId === userId);
  if (!inDB) return null;
  return Reservation.assemble(
    ReservationRepo.mapDb2Dto({
      ...inDB,
      start: getFutureTime(),
    })
  );
};
const getInput = (reservation: Reservation) => ({
  id: reservation.id.toString(),
  businessId: reservation.userId,
  reason,
  lng,
});

let reservationRepo: ReservationRepoFake,
  cancelReservationByBusiness: CancelReservationByBusiness,
  cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes>,
  getBusinessUserFC: FCHandler<GetBusinessUserFCReq, GetBusinessUserFCRes>,
  contextProvider: ContextProvider;
beforeEach(() => {
  vi.clearAllMocks();

  reservationRepo = new ReservationRepoFake();
  vi.spyOn(reservationRepo, 'get').mockImplementation(async ({userId, id}) => getReservationByIds({userId, id}));

  cancelReservationEvent = async () => {
    return Result2.ok(undefined);
  };

  getBusinessUserFC = async (args) => getBusinessUser(args);

  contextProvider = new ContextProvider({ invoker: invokerDummy });

  cancelReservationByBusiness = new CancelReservationByBusiness({
    reservationRepo,
    cancelReservationEvent,
    getBusinessUserFC,
    contextProvider,
  });
});

describe(`success when calling calendar module`, () => {
  it(`successfully cancelled the event`, async () => {

    const reservation = getReservationByIndex(0);
    const input = getInput(reservation);
    const reservationRepo = new ReservationRepoFake();
    vi.spyOn(reservationRepo, 'get').mockImplementation(async () => reservation);

    const cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes> = async (dto) => {
      const shouldBeInEventDescription = [
        reservation.customerFirstName,
        reservation.customerLastName,
        'cancel', // works for both languages: cancelled and cancelada
      ];
      if (reservation.customerPhone) shouldBeInEventDescription.push(reservation.customerPhone);
      if (reservation.customerEmail) shouldBeInEventDescription.push(reservation.customerEmail);
      if (input.reason) shouldBeInEventDescription.push(input.reason);
      shouldBeInEventDescription.forEach((data) => {
        expect(dto.addToEventDescription).toContain(data);
      });
      return Result2.ok(undefined);
    }

    const cancelReservationByBusiness = new CancelReservationByBusiness({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
    });

    const response = await cancelReservationByBusiness.execute(getAppsyncCtx(input, Object()), mockContext);

    const reservationUpdated = {
      ...reservation.toDto(),
      updated_at: expect.stringMatching(dateFormat),
      cancelledBy: 'BUSINESS',
      cancelledDate: expect.stringMatching(dateFormat),
      cancellationReason: input.reason,
      cancelReservationEventErrors: null,
    };
    expect(response).toMatchObject({
      result: {
        action: 'CANCELLED',
        reservation: reservationUpdated,
      },
    });

    const saved = await reservationRepo.get({
      id: input.id,
      userId: input.businessId,
    });
    expect(saved?.toDto()).toMatchObject(reservationUpdated);
  });

  it(`reason saved as null when ''`, async () => {
    const reservation = getReservationByIndex(0);
    const input = {
      ...getInput(reservation),
      reason: '',
    };
    const reservationRepo = new ReservationRepoFake();
    vi.spyOn(reservationRepo, 'get').mockImplementation(async () => reservation);

    const cancelReservationByBusiness = new CancelReservationByBusiness({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
    });

    const response = await cancelReservationByBusiness.execute(getAppsyncCtx(input, Object()), mockContext);

    const reservationUpdated = {
      ...reservation.toDto(),
      updated_at: expect.stringMatching(dateFormat),
      cancelledBy: 'BUSINESS',
      cancelledDate: expect.stringMatching(dateFormat),
      cancellationReason: null,
      cancelReservationEventErrors: null,
    };
    expect(response).toMatchObject({
      result: {
        action: 'CANCELLED',
        reservation: reservationUpdated,
      },
    });
  });

  const dummy = {
    calendarId: 'dummy id',
    email: 'dummy email',
    eventId: 'dummy eventId',
    lng,
    method: 'dummyMethod',
  };
  it.each([
    [
      'error CalendarApiErrors.CalendarNotFound',
      [new CalendarApiErrors_CalendarNotFound(dummy)],
    ],
    [
      'error CalendarApiErrors.CalendarEventNotFound',
      [new CalendarApiErrors_CalendarEventNotFound(dummy)],
    ],
    [
      'error CalendarApiErrors.InvalidGrant',
      [new CalendarApiErrors_InvalidGrant(dummy)],
    ],
    ['multiple errors', [new Error1(), new Error2()]],
  ])(`returned %s`, async (_title: string, errors: BaseError[]) => {

    const reservation = getReservationByIndex(0);
    const input = getInput(reservation);
    const reservationRepo = new ReservationRepoFake();
    vi.spyOn(reservationRepo, 'get').mockImplementation(async () => reservation);

    const cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes> = async () => {
      return Result2.fail(errors);
    }

    const cancelReservationByBusiness = new CancelReservationByBusiness({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
    });

    const response = await cancelReservationByBusiness.execute(getAppsyncCtx(input, Object()), mockContext);

    const reservationUpdated = {
      ...reservation.toDto(),
      updated_at: expect.stringMatching(dateFormat),
      cancelledBy: 'BUSINESS',
      cancelledDate: expect.stringMatching(dateFormat),
      cancellationReason: input.reason,
      cancelReservationEventErrors: errors.map(e => e.type),
    };
    expect(response).toMatchObject({
      result: {
        action: 'CALENDAR_ERROR',
        reservation: reservationUpdated,
      },
    });

    const saved = await reservationRepo.get({
      id: input.id,
      userId: input.businessId,
    });
    expect(saved?.toDto()).toMatchObject(reservationUpdated);
  });
});

describe(`addToEventDescription honors business' lng`, () => {
  test(`en (REGISTERED customer type case)`, async () => {
    const reservation = ReservationsInDb[2];  // ReservationsInDb[2].userId = BusinessUsersInDb[0].id = 00ad8920-c36d-11ee-8adf-3f66c88fcbdd y BusinessUsersInDb[0].lng = en
    const input = {
      id: reservation.id,
      businessId: reservation.userId,
      reason,
      lng: 'es',  // Even sending 'es' lng from the client, it will use business' lng
    };

    const cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes> = async (dto) => {
      const shouldBeInEventDescription = [
        reservation.customerFirstName,
        reservation.customerLastName,
        reservation.customerPhone,
        reservation.customerEmail,
        'cancelled',
      ];
      if (input.reason) shouldBeInEventDescription.push(input.reason);
      shouldBeInEventDescription.forEach((data) => {
        expect(dto.addToEventDescription).toContain(data);
      });
      return Result2.ok(undefined);
    }

    const cancelReservationByBusiness = new CancelReservationByBusiness({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
    });

    await cancelReservationByBusiness.execute(getAppsyncCtx(input, Object()), mockContext);
  });
  test(`es (MANUALLY_CREATED customer type case)`, async () => {
    const reservation = ReservationsInDb[1];  // ReservationsInDb[1].userId = BusinessUsersInDb[1].id = 01a82ed0-c39a-11ee-8adf-3f66c88fcbdd y BusinessUsersInDb[1].lng = es
    const getBusinessUserFC = async () => BusinessUserRepo.mapDb2Dto(BusinessUsersInDb[1]);
    const input = {
      id: reservation.id,
      businessId: reservation.userId,
      reason,
      lng: 'en',  // Even sending 'en' lng from the client, it will use business' lng
    };

    const cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes> = async (dto) => {
      const shouldBeInEventDescription = [
        reservation.customerFirstName,
        reservation.customerLastName,
        'cancelada',
      ];
      if (input.reason) shouldBeInEventDescription.push(input.reason);
      shouldBeInEventDescription.forEach((data) => {
        expect(dto.addToEventDescription).toContain(data);
      });
      return Result2.ok(undefined);
    }

    const cancelReservationByBusiness = new CancelReservationByBusiness({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
    });

    await cancelReservationByBusiness.execute(getAppsyncCtx(input, Object()), mockContext);
  });
});

describe(`fails when`, () => {
  test(`the reservation isn't found`, async () => {
    const reservation = getReservationByIndex(0);
    const input = getInput(reservation);

    const response = await cancelReservationByBusiness.executeImpl({
      ...input,
      id: nonExistingId,
    });
    expectControllerError({
      response,
      code: 404,
      error: 'CancelReservationByBusinessErrors.ReservationNotFound',
    });
  });
  test(`the reservation isn't cancellable`, async () => {
    const errors = [new Error1(), new Error2()];
    const reservation = getReservationByIndex(0);
    vi.spyOn(reservation, 'isCancellable').mockImplementation(() => Result2.fail(errors));
    const input = getInput(reservation);

    const reservationRepo = new ReservationRepoFake();
    vi.spyOn(reservationRepo, 'get').mockImplementation(async () => reservation);

    const cancelReservationByBusiness = new CancelReservationByBusiness({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
    });

    const response = await cancelReservationByBusiness.execute(getAppsyncCtx(input, Object()), mockContext);
    expect(response.errors).toEqual(errors);
  });
  test(`business isn't found`, async () => {
    const reservation = getReservationByIndex(0);
    const input = getInput(reservation);

    const getBusinessUserFC = async () => null;

    const cancelReservationByBusiness = new CancelReservationByBusiness({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
    });

    const response = await cancelReservationByBusiness.execute(getAppsyncCtx(input, Object()), mockContext);

    expect(response.errors).toEqual(expect.arrayContaining([
      expect.objectContaining({
        type: 'CancelReservationByBusinessErrors.BusinessUserNotFound',
        message: expect.any(String),
        status: 404,
      }),
    ]));
  });

  test(`cancelByBusiness errors`, async () => {
    const errors = [new Error1(), new Error2()];
    const reservation = getReservationByIndex(0);
    vi.spyOn(reservation, 'cancelByBusiness').mockImplementation(() => Result2.fail(errors));
    const input = getInput(reservation);
    const reservationRepo = new ReservationRepoFake();
    vi.spyOn(reservationRepo, 'get').mockImplementation(async () => reservation);

    const cancelReservationByBusiness = new CancelReservationByBusiness({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
    });

    const response = await cancelReservationByBusiness.execute(getAppsyncCtx(input, Object()), mockContext);

    expect(response.errors).toEqual(errors);
  });
});
