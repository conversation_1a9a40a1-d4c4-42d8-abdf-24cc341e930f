import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { StatusError } from '@shared/core/Status';
import { getLng, OptionalLng } from '@shared/utils/utils';

export namespace CancelReservationByBusinessErrors {
  export class ReservationNotFound extends BaseError {
    public constructor(args: { id: string } & OptionalLng) {
      const { id, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.reservationNotFound(id),
        status: StatusError.NOT_FOUND,
      });
    }
  }
  export class BusinessUserNotFound extends BaseError {
    public constructor(args: { id: string } & OptionalLng) {
      const { id, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.businessUserNotFound(id),
        status: StatusError.NOT_FOUND,
      });
    }
  }
}

const trans = {
  en: {
    reservationNotFound(id: string) {
      return `Reservation not found for id ${id}`;
    },
    reservationAlreadyCancelled(id: string) {
      return `Reservation ${id} is already cancelled`;
    },
    businessUserNotFound(id: string) {
      return `Business user not found for id ${id}`;
    },
  },
  es: {
    reservationNotFound(id: string) {
      return `Reserva no encontrada para el id ${id}`;
    },
    reservationAlreadyCancelled(id: string) {
      return `La reserva ${id} ya está cancelada`;
    },
    businessUserNotFound(id: string) {
      return `Negocio no encontrado para id ${id}`;
    },
  },
};

patch({ CancelReservationByBusinessErrors });
