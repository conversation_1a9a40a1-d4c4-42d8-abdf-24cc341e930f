import { DomainEvents } from '@shared/core/domain/DomainEvents';
import { CancelReservationByBusinessEvent } from '../../domain/events/CancelReservationByBusinessEvent';
import { IInvoker } from '@shared/infra/invocation/IInvoker';

export class CancelReservationByBusinessEvents {
  public static registration(invoker: IInvoker) {
    // Add all process.env used:
    const { distributeDomainEvents } = process.env;
    if (!distributeDomainEvents) {
      console.log('process.env', process.env);
      throw new Error(`Undefined env var!`);
    }

    DomainEvents.setInvoker(invoker);
    DomainEvents.register(
      `${distributeDomainEvents}`,
      CancelReservationByBusinessEvent.name,
    );
  }
}