import { Request, Response, Action } from './CancelReservationByBusinessDTOs';
import { CancelReservationByBusinessErrors } from './CancelReservationByBusinessErrors';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { IReservationRepo } from '../../repos/IReservationRepo';
import {
  CancelReservationEventReq,
  CancelReservationEventRes,
} from '../../external';
import { Dat } from '@shared/core/Dat';
import { formatErrors } from '@shared/core/AppError';
import {
  AppSync<PERSON>ontroller,
  FC2Handler,
  FCHandler,
} from '@shared/infra/Controllers';
import { getLng } from '@shared/utils/utils';
import { getContactDetails } from '../../utils/utils';
import {
  CancelReservationByBusinessEvents,
} from './CancelReservationByBusinessEvents';
import {
  CancelReservationByBusinessEvent,
} from '../../domain/events/CancelReservationByBusinessEvent';
import { CustomerType } from '../../domain/Reservation';
import { GetBusinessUserFCReq, GetBusinessUserFCRes } from '../../external';
import { ContextProvider } from '@shared/context/ContextProvider';

// CancelReservationByBusiness use case is used by BusinessUsers. dto.businessId should be taken from auth data.
export class CancelReservationByBusiness extends AppSyncController<
  Request,
  Response
> {
  private readonly cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes>;
  private readonly getBusinessUserFC: FCHandler<GetBusinessUserFCReq, GetBusinessUserFCRes>;
  private readonly reservationRepo: IReservationRepo;
  public constructor(args: {
    reservationRepo: IReservationRepo;
    cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes>;
    getBusinessUserFC: FCHandler<GetBusinessUserFCReq, GetBusinessUserFCRes>;
    contextProvider: ContextProvider;
  }) {
    const {
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
    } = args;
    super({ contextProvider });
    this.reservationRepo = reservationRepo;
    this.cancelReservationEvent = cancelReservationEvent;
    this.getBusinessUserFC = getBusinessUserFC;
    CancelReservationByBusinessEvents.registration(contextProvider.invoker())
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const {
      id,
      businessId,
      reason: _reason,
      lng: _lng,
    } = dto;

    let reason: string | null = null;
    if (_reason !== null) {
      let trimmed = _reason.trim();
      trimmed = trimmed.replace(/\s+/g, ' ');
      if (trimmed.length > 0)
        reason = trimmed;
    }

    const lng = getLng(_lng);

    const reservation = await this.reservationRepo.get({ id, userId: businessId });
    if (!reservation)
      return formatErrors([
        new CancelReservationByBusinessErrors.ReservationNotFound({ id, lng }),
      ]);

    const isCancellableOrErrors = reservation.isCancellable(lng);
    if (isCancellableOrErrors.isFailure)
      return formatErrors(isCancellableOrErrors.errors!);

    const businessUser = await this.getBusinessUserFC({ id: businessId });
    if (!businessUser)
      return formatErrors([
        new CancelReservationByBusinessErrors.BusinessUserNotFound({ id: businessId, lng }),
      ]);
    const businessLng = getLng(businessUser.lng);

    const {
      eventId,
      start,
      customerType,
      customerFirstName,
      customerLastName,
      customerPhone,
      customerEmail,
      roZone,
      srvDuration,
      locId,
    } = reservation;
    const end = start.l.plus({ minutes: srvDuration });

    const cancelledDate = Dat.create().value;
    const cancelledDateText = cancelledDate.l
      .setZone(roZone)
      .startOf('second')
      .toISO({ suppressMilliseconds: true });

    let addToEventDescription;
    const customerFullName =
      `${customerFirstName? customerFirstName : ''}` +
      `${customerFirstName && customerLastName? ' ' : ''}` +
      `${customerLastName? customerLastName : ''}`;

    if (customerType === CustomerType.REGISTERED) {

      const contactDetails = getContactDetails({
        customerPhone,
        customerEmail,
        lng: businessLng,
      });

      switch (businessLng) {
        case 'es': {
          addToEventDescription =
            `Reserva de ${customerFullName} ` +
            contactDetails +
            `cancelada por el negocio a las ${cancelledDateText}.`;
          if (reason)
            addToEventDescription += ` Razón: ${reason}`;
          break;
        }
        case 'en': {
          addToEventDescription =
            `${customerFullName}'s ` +
            contactDetails +
            `reservation cancelled by business at ${cancelledDateText}.`;
          if (reason)
            addToEventDescription += ` Reason: ${reason}`;
          break;
        }
      }

    } else if (customerType === CustomerType.MANUALLY_CREATED) {
      switch (businessLng) {
        case 'es':
          addToEventDescription =
            `Reserva de ${customerFullName} cancelada por el negocio a las ${cancelledDateText}.`;
          if (reason)
            addToEventDescription += ` Razón: ${reason}`;
          break;
        case 'en':
          addToEventDescription =
            `${customerFullName}'s reservation cancelled by business at ${cancelledDateText}.`;
          if (reason)
            addToEventDescription += ` Reason: ${reason}`;
          break;
      }
    } else {
      // This should never happen, as customerType is validated in this.reservationRepo.get, so it shouldn't be invalid.
    }

    // Call synchronously CancelReservationEvent in module calendar
    let result = await this.cancelReservationEvent({
      userId: businessId,
      eventId,
      identifier: locId,
      start: start.s,
      end: Dat.create({ value: end }).value.s,
      addToEventDescription: addToEventDescription!,
      lng: businessLng,
    })

    const { isSuccess, isFailure, errors } = result;
    if (isFailure) {
      const msg = 'CancelReservationEvent failed';
      console.log(msg, { errors, reservation });
      await this.contextProvider.sendAnalytics({
        msg,
        errors,
        reservation,
      });
    }

    result = reservation.cancelByBusiness({
      cancelledDate,
      reason,
      cancelReservationEventErrors: errors?.map(e => e.toDto().type) as [string, ...string[]]  ?? null,
      lng,
    });
    if (result.isFailure)
      return formatErrors(result.errors!);

    reservation.addEvent(new CancelReservationByBusinessEvent(reservation.toDto()));
    const updated = await this.reservationRepo.update(reservation);

    return {
      status: Status.OK,
      result: {
        action: isSuccess
          ? Action.CANCELLED
          : Action.CALENDAR_ERROR, // Even when there was an error when calling calendar module, it's taken as an OK, since this can happen for a bunch of reason, e.g. because of the user deleted the event in the reservation calendar.
        reservation: updated, // returning the updated reservation, will update the reservation in the client
      },
    };
  }
}