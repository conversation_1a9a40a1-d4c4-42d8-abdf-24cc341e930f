import { beforeAll, expect, it, vi, describe, test } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { Dat } from '@shared/core/Dat';
import {
  dateFormat,
  expectErrorAppSync,
  orNull,
  pickLng,
} from '@shared/utils/test';
import {
  CustomerUserDto,
  Identifier,
  ManuallyCreatedCustomerDto,
} from '../../external';
import { ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import {
  ReservationOptionInDb,
  ReservationOptionRepo,
} from '../../repos/ReservationOptionRepo';
import {
  rmReservations,
} from '../../utils/test';
import { getCalendarCreds } from '@shared/infra/iac/helpers';
import {
  CalendarsInDb,
  rmEvents,
  tomorrowPlus,
  Event,
  Slot,
  CalendarApi,
  CalendarApiT,
  balanceRepo,
  rmBalanceRow,
  Movement,
} from '../../utils/testExternal';
import { calendar } from '@googleapis/calendar';
import { ReservationRepo } from '../../repos/ReservationRepo';
import { CustomerType, Reservation } from '../../domain/Reservation';
import {
  createCustomerUser,
  createManuallyCreatedCustomer,
} from '../../../user/utils/test';
import { ReservationsInDb } from '@shared/infra/database/sequelize/migrations/Reservations.json';
import { Request } from './CancelReservationByBusinessDTOs';
import { ReservationWithTimestampsFragment } from '../../utils/fragments';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');

// Add all process.env used:
const { PROJECT, STAGE } = process.env;
if (!PROJECT || !STAGE) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}

const sendAnalytics = vi.fn();

let reservationApi: CalendarApiT, reservationRepo: ReservationRepo;
beforeAll(async () => {
  const { calendar_clientEmail, calendar_clientKey } = await getCalendarCreds({
    name: PROJECT,
    stage: STAGE,
  });
  vi.stubEnv('calendar_clientEmail', calendar_clientEmail);
  vi.stubEnv('calendar_clientKey', calendar_clientKey);
  reservationRepo = new ReservationRepo(models.Reservation);
  reservationApi = new CalendarApi({
    google: calendar({ version: 'v3' }),
    sendAnalytics,
  });
  reservationApi.setCalendarId(CalendarsInDb[9].id); // CalendarsInDb[9].userId = BusinessUsersInDb[7].id & CalendarsInDb[9].type = 'R'
});

const appsync = new AppSyncClient();
const lng = pickLng();
const businessLng = pickLng();

const query = gql`
  mutation ($id: ID!, $businessId: ID!, $reason: String, $lng: String!) {
    cancelReservationByBusiness(
      id: $id
      businessId: $businessId
      reason: $reason
      lng: $lng
    ) {
      result {
        action
        reservation {
          ...ReservationWithTimestampsFragment
        }
      }
      time
    }
  }
  ${ReservationWithTimestampsFragment}
`;

const timestamps = {
  created_at: expect.stringMatching(dateFormat),
  updated_at: expect.stringMatching(dateFormat),
  deleted_at: null,
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const { created_at, updated_at, deleted_at, ...woutTimestamps } = ReservationOptionsInDb[13];  // ReservationOptionsInDb[13].userId = BusinessUsersInDb[7].id 07a82ed0-c39a-11ee-8adf-3f66c88fcbdd
const reservationOptionDto = ReservationOptionRepo.mapDb2Dto(woutTimestamps as ReservationOptionInDb);
const start = Dat.create({ value: tomorrowPlus().l.startOf('hour') }).value;
const end = Dat.create({
  value:
    start.l.plus({minutes: reservationOptionDto.service.duration}),
}).value;
const identifier = Identifier.create(
  { value: reservationOptionDto.location.identifier },
  sendAnalytics,
).value;

describe(`cancels a reservation event`, () => {
  it.each([
    [CustomerType.REGISTERED, createCustomerUser().dto],
    [CustomerType.MANUALLY_CREATED, createManuallyCreatedCustomer().dto],
  ])(
    `for a %s customer`,
    async (
      customerType: CustomerType,
      customer: CustomerUserDto | ManuallyCreatedCustomerDto,
    ) => {
      // First create a reservation event
      const reservationEvent = Event.create({
        name: 'Reservation',
        description: `${identifier.query}:2`,
        slot: Slot.create({
          start,
          end,
        }).value,
      });
      const resInsertedOrError =
        await reservationApi.insertEvent({
          event: reservationEvent,
          lng: pickLng(),
        });
      expect(resInsertedOrError.isSuccess).toBe(true);
      const resInserted = resInsertedOrError.value;

      // Second create a reservation in repo

      const userId = reservationOptionDto.userId;  // 07a82ed0-c39a-11ee-8adf-3f66c88fcbdd
      const reservation = Reservation.create({
        userId,
        customerType,
        customerFirstName: customer.firstName,
        customerLastName: customer.lastName,
        // @ts-expect-error phone doesn't exist in ManuallyCreatedCustomerDto
        customerPhone: customer.phone? customer.phone : null,
        // @ts-expect-error email doesn't exist in ManuallyCreatedCustomerDto
        customerEmail: customer.email? customer.email : null,
        customerId: customer.id,
        start,
        eventId: resInserted.id,
        roId: reservationOptionDto.id,
        roZone: reservationOptionDto.timeReference.zone,
        srvName: reservationOptionDto.service.name,
        srvDuration: reservationOptionDto.service.duration,
        srvDescription: reservationOptionDto.service.description,
        srvPrice: reservationOptionDto.service.price,
        locName: reservationOptionDto.location.name,
        locId: reservationOptionDto.location.identifier,
      });
      await reservationRepo.create(reservation);

      // Send request
      const input = {
        id: reservation.id.toString(),
        businessId: userId,
        reason: orNull(chance.sentence()),
        lng,
        businessLng,
      };
      const received = await appsync.send<Request>({
        query,
        variables: input,
      });

      expect(received.status).toBe(200);
      const json = await received.json();
      if (!json.data) {
        console.log('CancelReservationByBusiness.e2e.ts 1 json', JSON.stringify(json, null, 2));
      }
      const response = json.data.cancelReservationByBusiness;
      if (!response)
        console.log('CancelReservationByBusiness.e2e.ts 2 json', json);

      const { result } = response;
      if (result.action !== 'CANCELLED') console.log('CancelReservationByBusiness.e2e.ts 3 result', result);
      expect(result.action).toEqual('CANCELLED');
      expect(result.reservation).toEqual({
        ...reservation.toDto(),
        ...timestamps,
        start: reservation.start.j.toJSON(),
        cancelledBy: 'BUSINESS',
        cancellationReason: input.reason,
        cancelledDate: expect.stringMatching(dateFormat),
        cancelReservationEventErrors: null,
      });

      // Wait for the side effect to be applied
      await new Promise(resolve => setTimeout(resolve, 3000));
      // Side effect on @balance
      const balanceRows = await balanceRepo.list(userId);
      const matches = balanceRows.filter(row => {
        return row.reservationId === reservation.id.toString() &&
          row.movement === Movement.CANCELLATION_BY_BUSINESS;
      });
      expect(matches).toHaveLength(1);

      // Clean up
      await rmReservations([reservation.id.toString()]);
      await rmEvents({
        inserted: [{ value: { id: resInserted.id } }],
        calendarApi: reservationApi,
      });
      await rmBalanceRow({ userId, index: 1 });
      await rmBalanceRow({
        userId: matches[0].userId,
        index: matches[0].index.value,
      });
    },
  );

  test(`although an error was received from calendar module`, async () => {
    // Skip creating a reservation event in calendar
    // Create a reservation in repo
    const customerType = chance.pickone([
      CustomerType.REGISTERED,
      CustomerType.MANUALLY_CREATED,
    ]);
    let customer: CustomerUserDto | ManuallyCreatedCustomerDto;
    switch (customerType) {
      case 'REGISTERED':
        customer = createCustomerUser().dto;
        break;
      case 'MANUALLY_CREATED':
        customer = createManuallyCreatedCustomer().dto;
        break;
      default:
        throw new Error(`Invalid customerType: ${customerType}`);
    }

    const userId = reservationOptionDto.userId;
    const reservation = Reservation.create({
      userId: reservationOptionDto.userId,
      customerType,
      customerFirstName: customer.firstName,
      customerLastName: customer.lastName,
      // @ts-expect-error phone doesn't exist in ManuallyCreatedCustomerDto
      customerPhone: customer.phone? customer.phone : null,
      // @ts-expect-error email doesn't exist in ManuallyCreatedCustomerDto
      customerEmail: customer.email? customer.email : null,
      customerId: customer.id,
      start,
      eventId: 'nonExistent',
      roId: reservationOptionDto.id,
      roZone: reservationOptionDto.timeReference.zone,
      srvName: reservationOptionDto.service.name,
      srvDuration: reservationOptionDto.service.duration,
      srvDescription: reservationOptionDto.service.description,
      srvPrice: reservationOptionDto.service.price,
      locName: reservationOptionDto.location.name,
      locId: reservationOptionDto.location.identifier,
    });
    await reservationRepo.create(reservation);

    // Send request
    const input = {
      id: reservation.id.toString(),
      businessId: reservation.userId,
      reason: orNull(chance.sentence()),
      lng,
      businessLng,
    };
    const received = await appsync.send<Request>({
      query,
      variables: input,
    });

    expect(received.status).toBe(200);
    const json = await received.json();
    if (!json.data) console.log('CancelReservationByBusiness.e2e.ts 4 json', json);
    const response = json.data.cancelReservationByBusiness;
    if (!response) console.log('CancelReservationByBusiness.e2e.ts 5 json', json);

    const { result } = response;
    expect(result.action).toEqual('CALENDAR_ERROR');

    const cancelReservationEventError = 'CancelReservationEventErrors.WhenGettingEvent';
    expect(result.reservation).toEqual({
      ...reservation.toDto(),
      ...timestamps,
      start: reservation.start.j.toJSON(),
      cancelledBy: 'BUSINESS',
      cancellationReason: input.reason,
      cancelledDate: expect.stringMatching(dateFormat),
      cancelReservationEventErrors: [cancelReservationEventError],
    });

    // Wait for the side effect to be applied
    await new Promise(resolve => setTimeout(resolve, 3000));
    // Side effect on @balance
    const balanceRows = await balanceRepo.list(userId);
    const matches = balanceRows.filter(row => {
      return row.reservationId === reservation.id.toString() &&
        row.movement === Movement.CANCELLATION_BY_BUSINESS;
    });
    expect(matches).toHaveLength(1);

    // Clean up
    await rmReservations([reservation.id.toString()]);
  });
});

describe(`fails when the reservation`, () => {
  it(`was already cancelled`, async () => {
    const reservation = ReservationsInDb[3]; // Already cancelled
    const input = {
      id: reservation.id.toString(),
      businessId: reservation.userId,
      reason: orNull(chance.sentence()),
      lng,
      businessLng,
    };
    const received = await appsync.send<Request>({
      query,
      variables: input,
    });

    expect(received.status).toBe(200);
    expectErrorAppSync({
      response: await received.json(),
      query: 'cancelReservationByBusiness',
      error: 'ReservationErrors.AlreadyCancelled',
      status: 409,
    });
  });
  it(`isn't found`, async () => {
    const input = {
      businessId: ReservationsInDb[0].userId,
      id: ReservationsInDb[1].id,
      reason: orNull(chance.sentence()),
      lng,
      businessLng,
    };
    const received = await appsync.send<Request>({
      query,
      variables: input,
    });

    expect(received.status).toBe(200);
    expectErrorAppSync({
      response: await received.json(),
      query: 'cancelReservationByBusiness',
      error: 'CancelReservationByBusinessErrors.ReservationNotFound',
      status: 404,
    });
  });
});
