import { GetAvailabilityOfReservationOptions } from './GetAvailabilityOfReservationOptions';
import { ReservationOptionRepo } from '../../repos/ReservationOptionRepo';
import { ReturnUnexpectedError } from '@shared/decorators/ReturnUnexpectedError';
import { GuardUuid } from '@shared/decorators/GuardUuid';
import { getAvailableTimesHandlerCreator } from '../../externalUseCases';
import { GeneralSettingsRepo } from '../../repos/GeneralSettingsRepo';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });
const sendAnalytics = contextProvider.sendAnalytics.bind(contextProvider);

const reservationOptionRepo = new ReservationOptionRepo({
  ReservationOptionModel: models.ReservationOption,
  sendAnalytics,
});
const gsRepo = new GeneralSettingsRepo(models.GeneralSettingsForReservation);
const controller = new GetAvailabilityOfReservationOptions({
  reservationOptionRepo,
  getAvailableTimes: getAvailableTimesHandlerCreator(sendAnalytics),
  gsRepo,
  contextProvider,
});

const decorated1 = new GuardUuid({ controller, uuids: ['userId'] });
const decorated2 = new ReturnUnexpectedError({
  wrapee: decorated1,
  contextProvider: new ContextProvider({ invoker }),
});
export const handler = decorated2.execute.bind(decorated2);