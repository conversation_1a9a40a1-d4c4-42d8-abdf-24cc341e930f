import { expect, it, vi, beforeAll } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import {
  ReservationOptionsInDb,
} from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { Request } from './GetAvailabilityOfReservationOptionsDTOs';
import {
  resOpt12expected,
  resOpt1expected,
  resOpt2expected,
} from '../getReservationOptions/test';
import { Dat } from '@shared/core/Dat';
import { tomorrowPlus } from '../../../calendar/utils/utils';
import { CalendarApi } from '../../../calendar/services/CalendarApi';
import { getCalendarCreds } from '@shared/infra/iac/helpers';
import { calendar } from '@googleapis/calendar';
import {
  CalendarsInDb,
} from '@shared/infra/database/sequelize/migrations/Calendars.json';
import {
  timestamps,
} from '../../utils/test';
import { Identifier } from '../../../calendar/domain/Identifier';
import {
  getTestData,
  Event,
  Slot,
  rmEvents,
} from '../../utils/testExternal';
import {
  expectErrorAppSync,
  pickLng,
} from '@shared/utils/test';
import {
  sendAnalyticsDummy as sendAnalytics,
} from '@shared/utils/utils';
import {
  ReservationOptionWithTimestampsAndAvailabilityFragment,
} from '../../utils/fragments';

const appsync = new AppSyncClient();

const query = gql`
  query ($userId: ID!, $lng: String!) {
    getAvailabilityOfReservationOptions(lng: $lng, userId: $userId) {
      result {
        ...ReservationOptionWithTimestampsAndAvailabilityFragment
      }
      time
    }
  }
  ${ReservationOptionWithTimestampsAndAvailabilityFragment}
`;

// Add all process.env used:
const {PROJECT, STAGE} = process.env;
if (!PROJECT || !STAGE) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}

let availabilityApi: CalendarApi, reservationApi: CalendarApi;
beforeAll(async () => {
  const {calendar_clientEmail, calendar_clientKey} = await getCalendarCreds({
    name: PROJECT,
    stage: STAGE,
  });
  vi.stubEnv('calendar_clientEmail', calendar_clientEmail);
  vi.stubEnv('calendar_clientKey', calendar_clientKey);
  availabilityApi = new CalendarApi({
    google: calendar({version: 'v3'}),
    sendAnalytics,
  });
  availabilityApi.setCalendarId(CalendarsInDb[4].id); // calendar "availability 5"
  reservationApi = new CalendarApi({
    google: calendar({version: 'v3'}),
    sendAnalytics,
  });
  reservationApi.setCalendarId(CalendarsInDb[5].id);  // calendar "reservations 5"
});

const identifier = Identifier.create({value: 'loc1'}, sendAnalytics).value;
const userId = ReservationOptionsInDb[6].userId;  // 05a82ed0-c39a-11ee-8adf-3f66c88fcbdd user added into db with the reservation options as in GetAvailableTimes.e2e.ts to reuse its code. 05a82ed0-c39a-11ee-8adf-3f66c88fcbdd uses calendars "availability 5" and "reservations 5"
const lng = pickLng();

it(`returns available times with spots`, async () => {
    const initial = Dat.create({value: tomorrowPlus().l.startOf('hour')}).value;
    const {availabilityEvent1, availabilityEvent2} = getTestData({
      initial,
      identifier,
    });

    // First create 2 availability events with 2 and 3 spots
    const availInserted1 = await availabilityApi.insertEvent({
      event: availabilityEvent1,
      lng,
    }); // Como preparación de este test necesito tener acceso a escritura del availability calendar, pero en la app de producción sólo necesito acceso de lectura
    expect(availInserted1.isSuccess).toBe(true);
    const availInserted2 = await availabilityApi.insertEvent({
      event: availabilityEvent2,
      lng,
    });
    expect(availInserted2.isSuccess).toBe(true);

    let received = await appsync.send<Request>({
      query,
      variables: {
        userId,
        lng,
      },
    });

    expect(received.status).toBe(200);
    let json = await received.json();
    let response = json.data.getAvailabilityOfReservationOptions;
    if (!response) console.log('json', json);

    expect(response.result.length).toBe(2); // 06a6f6a4-f5a8-4266-a4ab-8b0b8915f347 (Res Opt 1). Reservation option 12a6f6a4-f5a8-4266-a4ab-8b0b8915f347 is filtered out as it uses loc2 for which we haven't created availability events.

    const i = initial.l;
    const every = ReservationOptionsInDb[6].every;
    expect(response.result).toEqual([
      {
        ...resOpt1expected,
        ...timestamps,
        id: '06a6f6a4-f5a8-4266-a4ab-8b0b8915f347',
        userId,
        availabilityGot: expect.arrayContaining([
          expect.objectContaining({
            time: Dat.create({value: i}).value.s, // 0
            spots: 2,
          }),
          expect.objectContaining({
            time: Dat.create({value: i.plus({minutes: every})}).value.s, // 10
            spots: 2,
          }),
          expect.objectContaining({
            time: Dat.create({value: i.plus({minutes: 2 * every})}).value.s, // 20
            spots: 2,
          }),
          expect.objectContaining({
            time: Dat.create({value: i.plus({minutes: 3 * every})}).value.s, // 30
            spots: 5,
          }),
          expect.objectContaining({
            time: Dat.create({value: i.plus({minutes: 4 * every})}).value.s, // 40
            spots: 5,
          }),
          expect.objectContaining({
            time: Dat.create({value: i.plus({minutes: 5 * every})}).value.s, // 50
            spots: 3,
          }),
          expect.objectContaining({
            time: Dat.create({value: i.plus({minutes: 6 * every})}).value.s, // 60
            spots: 3,
          }),
          expect.objectContaining({
            time: Dat.create({value: i.plus({minutes: 7 * every})}).value.s, // 70
            spots: 3,
          }),
        ]),
        availabilityErrors: null,
      },
      {
        ...resOpt12expected,
        ...timestamps,
        id: '12a6f6a4-f5a8-4266-a4ab-8b0b8915f347',
        userId,
        availabilityGot: null,
        availabilityErrors: null,
      },
    ]);

    const duration = ReservationOptionsInDb[6].srvDuration; // 20
    // Second create 2 reservations events with 1 and 3 spots taken
    const reservation1 = Event.create({
      name: 'Reservation 1',
      description: `${identifier.query}:1`,
      slot: Slot.create({
        start: Dat.create({value: i.plus({minutes: duration + 1})}).value, // 21
        end: Dat.create({value: initial.l.plus({minutes: 45})}).value,
      }).value,
    });
    const reservation2 = Event.create({
      name: 'Reservation 2',
      description: `${identifier.query}:3`,
      slot: Slot.create({
        start: Dat.create({value: i.plus({minutes: 40})}).value,
        end: Dat.create({value: i.plus({minutes: 65})}).value,
      }).value,
    });
    const resInserted1 = await reservationApi.insertEvent({
      event: reservation1,
      lng,
    });
    expect(resInserted1.isSuccess).toBe(true);
    const resInserted2 = await reservationApi.insertEvent({
      event: reservation2,
      lng,
    });
    expect(resInserted2.isSuccess).toBe(true);

    received = await appsync.send<Request>({
      query,
      variables: {
        userId,
        lng,
      },
    });

    expect(received.status).toBe(200);
    json = await received.json();
    response = json.data.getAvailabilityOfReservationOptions;
    console.log('GetAvailabilityOfReservationOptions.e2e.ts 1', json);

    expect(response.result).toEqual([
      {
        ...resOpt1expected,
        ...timestamps,
        id: '06a6f6a4-f5a8-4266-a4ab-8b0b8915f347',
        userId,
        availabilityGot: expect.arrayContaining([
          expect.objectContaining({
            time: Dat.create({value: i}).value.s, // 0
            spots: 2,
          }),
          expect.objectContaining({
            time: Dat.create({value: i.plus({minutes: every})}).value.s, // 10
            spots: 1,
          }),
          expect.objectContaining({
            time: Dat.create({value: i.plus({minutes: 2 * every})}).value.s, // 20
            spots: 1,
          }),
          expect.objectContaining({
            time: Dat.create({value: i.plus({minutes: 3 * every})}).value.s, // 30
            spots: 1,
          }),
          expect.objectContaining({
            time: Dat.create({value: i.plus({minutes: 4 * every})}).value.s, // 40
            spots: 1,
          }),
          expect.objectContaining({
            time: Dat.create({value: i.plus({minutes: 7 * every})}).value.s, // 70
            spots: 3,
          }),
        ]),
        availabilityErrors: null,
      },
      {
        ...resOpt12expected,
        ...timestamps,
        id: '12a6f6a4-f5a8-4266-a4ab-8b0b8915f347',
        userId,
        availabilityGot: null,
        availabilityErrors: null,
      },
    ]);

    // Clean up
    await rmEvents({
      inserted: [availInserted1, availInserted2],
      calendarApi: availabilityApi,
    });
    await rmEvents({
      inserted: [resInserted1, resInserted2],
      calendarApi: reservationApi,
    });

  });

it(`returns null when there are no spots available`, async () => {
    const initial = Dat.create({value: tomorrowPlus(60 * 24).l.startOf('hour')}).value;
    const {availabilityEvent1, availabilityEvent2} = getTestData({
      initial,
      identifier,
    });

    // First create 2 availability events with 2 and 3 spots
    const availInserted1 = await availabilityApi.insertEvent({
      event: availabilityEvent1,
      lng,
    }); // Como preparación de este test necesito tener acceso a escritura del availability calendar, pero en la app de producción sólo necesito acceso de lectura
    expect(availInserted1.isSuccess).toBe(true);
    const availInserted2 = await availabilityApi.insertEvent({
      event: availabilityEvent2,
      lng,
    });
    expect(availInserted2.isSuccess).toBe(true);

    // Second create 2 reservations events taking all spots
    const i = initial.l;
    const reservation1 = Event.create({
      name: 'Reservation 1',
      description: `${identifier.query}:2`,
      slot: Slot.create({
        start: initial, // 0
        end: Dat.create({value: i.plus({minutes: 15})}).value,
      }).value,
    });
    const reservation2 = Event.create({
      name: 'Reservation 2',
      description: `${identifier.query}:5`,
      slot: Slot.create({
        start: Dat.create({value: i.plus({minutes: 19})}).value,
        end: Dat.create({value: i.plus({minutes: 71})}).value,
      }).value,
    });
    const resInserted1 = await reservationApi.insertEvent({
      event: reservation1,
      lng,
    });
    if (!resInserted1.isSuccess) console.log('GetAvailabilityOfReservationOptions.e2e.ts resInserted1', resInserted1);
    expect(resInserted1.isSuccess).toBe(true);
    const resInserted2 = await reservationApi.insertEvent({
      event: reservation2,
      lng,
    });
    expect(resInserted2.isSuccess).toBe(true);

    const received = await appsync.send<Request>({
      query,
      variables: {
        userId,
        lng,
      },
    });

    expect(received.status).toBe(200);
    const json = await received.json();
    const response = json.data.getAvailabilityOfReservationOptions;
    if (!response) console.log('GetAvailabilityOfReservationOptions.e2e.ts 2', json);

    expect(response.result).toEqual([
      {
        ...resOpt1expected,
        ...timestamps,
        id: '06a6f6a4-f5a8-4266-a4ab-8b0b8915f347',
        userId,
        availabilityGot: null,
        availabilityErrors: null,
      },
      {
        ...resOpt12expected,
        ...timestamps,
        id: '12a6f6a4-f5a8-4266-a4ab-8b0b8915f347',
        userId,
        availabilityGot: null,
        availabilityErrors: null,
      },
    ]);

    // Clean up
    await rmEvents({
      inserted: [availInserted1, availInserted2],
      calendarApi: availabilityApi,
    });
    await rmEvents({
      inserted: [resInserted1, resInserted2],
      calendarApi: reservationApi,
    });

  });

it(`fills availabilityError property when GetAvailableTimes invocation errors`, async () => {

  const userId = ReservationOptionsInDb[1].userId;  // 01a82ed0-c39a-11ee-8adf-3f66c88fcbdd user doesn't have valid calendars in Calendars.json so the error comes from: GetAvailableTimes @ calendar -> CalendarsSrv.getAvailableTimes  -> AvailabilitySrv.getChunks -> CalendarApi.listEvents -> isExceptionForCalendarNotFound -> CalendarApiErrors.CalendarNotFound
  const received = await appsync.send<Request>({
    query,
    variables: {
      userId,
      lng,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.getAvailabilityOfReservationOptions;
  if (!response) console.log('json', json);

  expect(response.result.length).toBe(1); // "Res Opt 2"

  expect(response.result).toEqual([
    {
      ...resOpt2expected,
      ...timestamps,
      availabilityGot: null,
      availabilityErrors: [
        {
          message: expect.any(String),
          status: 404,
          type: 'CalendarApiErrors.CalendarNotFound',
        },
      ],
    },
  ]);

});

it(`fails when user doesn't have active reservation options`, async () => {
  const userId = ReservationOptionsInDb[11].userId;  // aaa82ed0-c39a-11ee-8adf-3f66c88fcbdd
  const received = await appsync.send<Request>({
    query,
    variables: {
      userId,
      lng,
    },
  });

  expect(received.status).toBe(200);
  expectErrorAppSync({
    response: await received.json(),
    error: 'GetAvailabilityOfReservationOptionsErrors.NoActiveReservationOptions',
    status: 404,
    query: 'getAvailabilityOfReservationOptions',
  })
});