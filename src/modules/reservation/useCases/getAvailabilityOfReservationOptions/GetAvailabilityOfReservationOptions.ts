import {
  Request,
  ReservationOptionWithAvailabilityDto,
  Response,
} from './GetAvailabilityOfReservationOptionsDTOs';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { IReservationOptionRepo } from '../../repos/IReservationOptionRepo';
import { AppSyncController, FC2Handler } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';
import { formatErrors } from '@shared/core/AppError';
import {
  GetAvailableTimesReq,
  GetAvailableTimesRes,
} from '../../external';
import { DateTime } from 'luxon';
import {
  GetAvailabilityOfReservationOptionsErrors,
} from './GetAvailabilityOfReservationOptionsErrors';
import { getLng } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { IGeneralSettingsRepo } from '../../repos/IGeneralSettingsRepo';
import { MaxDaysAhead } from '../../domain/MaxDaysAhead';

export class GetAvailabilityOfReservationOptions extends AppSyncController<Request, Response> {
  private readonly reservationOptionRepo: IReservationOptionRepo;
  private readonly getAvailableTimes: FC2Handler<GetAvailableTimesReq, GetAvailableTimesRes>;
  private readonly gsRepo: IGeneralSettingsRepo;

  public static readonly MAX_ACTIVE_ROs_TIER1 = 20;
  public static readonly MAX_TIMES_PER_RO_TIER1 = 1000;
  public static readonly MAX_ACTIVE_ROs_TIER2 = 40;
  public static readonly MAX_TIMES_PER_RO_TIER2 = 500;

  public constructor(args: {
    reservationOptionRepo: IReservationOptionRepo;
    getAvailableTimes: FC2Handler<GetAvailableTimesReq, GetAvailableTimesRes>;
    gsRepo: IGeneralSettingsRepo;
    contextProvider: IContextProvider;
  }) {
    const {reservationOptionRepo, getAvailableTimes, gsRepo, contextProvider} = args;
    super({ contextProvider });
    this.reservationOptionRepo = reservationOptionRepo;
    this.getAvailableTimes = getAvailableTimes;
    this.gsRepo = gsRepo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const {userId, lng: _lng} = dto;
    const lng = getLng(_lng);

    const reservationOptions = await this.reservationOptionRepo.list(userId);

    if (!reservationOptions)  // Even though this may not be considered an error and a null response instead of an error could be more appropriate, with an error we can provide an explanatory message to the user and differentiate it from the case when there aren't any available spots. The same happens for GetAvailabilityOfReservationOptionsErrors.NoActiveReservationOptions
      return formatErrors([new GetAvailabilityOfReservationOptionsErrors.NoReservationOptions(lng)]);

    const active = reservationOptions.filter((resOpt) => resOpt.active);

    if (!active.length)
      return formatErrors([new GetAvailabilityOfReservationOptionsErrors.NoActiveReservationOptions(lng)]);

    const { MAX_ACTIVE_ROs_TIER1, MAX_TIMES_PER_RO_TIER1, MAX_ACTIVE_ROs_TIER2, MAX_TIMES_PER_RO_TIER2 } = GetAvailabilityOfReservationOptions;
    const maxTimesPerRO =
      active.length <= MAX_ACTIVE_ROs_TIER1 ? MAX_TIMES_PER_RO_TIER1 :
        active.length <= MAX_ACTIVE_ROs_TIER2 ? MAX_TIMES_PER_RO_TIER2 : 0;

    if (!maxTimesPerRO)
      return formatErrors([new GetAvailabilityOfReservationOptionsErrors.TooManyActiveReservationOptions({ qty: active.length, lng, max: MAX_ACTIVE_ROs_TIER2 })]);

    const gs = await this.gsRepo.get(userId);
    if (!gs)
      return formatErrors([new GetAvailabilityOfReservationOptionsErrors.UserGSnotFound(lng)]);

    const { maxDaysAhead, minTimeBeforeService } = gs.reservationLimits;

    const promises = [];
    for (const resOpt of active) {
      promises.push(this.getAvailableTimes(
          {
            userId,
            identifier: resOpt.location.identifier.value,
            every: resOpt.every.value,
            duration: resOpt.service.duration.value,
            timeReference: resOpt.timeReference.toDto(),
            from: DateTime.now().plus({minutes: 5}).toJSON(), // The available reservation times will remain valid for at least 5 minutes, preventing error ReservationError.ServiceStartTimeInPast
            to: DateTime.now().plus({days: MaxDaysAhead.MAX}).toJSON(),
            lng,
            maxTimes: maxTimesPerRO,
            roId: resOpt.id.toString(), // Included to track the reservation option in case of error. LambdaInvoker logs payload.
            maxDaysAhead: maxDaysAhead.value,
            minTimeBeforeService: minTimeBeforeService.value,
          })
      );
    }

    const fulfilledORRejected = await Promise.allSettled(promises);

    const dtos: ReservationOptionWithAvailabilityDto[] | null = [];
    for (let i = 0; i < fulfilledORRejected.length; i++) {
      if (fulfilledORRejected[i].status === 'fulfilled') {
        // @ts-expect-error - typings not working for fulfilledORRejected[i].value
        const fulfilled = fulfilledORRejected[i].value as unknown as Result2<GetAvailableTimesRes>;
        if (fulfilled.isSuccess) {
          dtos[i] = {
            ...active[i].toDto(),
            availabilityGot: fulfilled.value,
            availabilityErrors: null,
          };
        } else {
          const msg = 'GetAvailableTimes failed';
          const resOpt = active[i].toDto();
          const availabilityErrors = fulfilled.errors!.map(e => e.toDto());
          console.log(msg, {  resOpt, availabilityErrors });
          await this.contextProvider.sendAnalytics({
            msg,
            resOpt,
            availabilityErrors,
          });
          dtos[i] = {
            ...resOpt,
            availabilityErrors,
            availabilityGot: null,
          };
        }
      } else {
        const msg = 'GetAvailableTimes invocation rejected';
        const resOpt = active[i].toDto();
        console.log(msg, { rejected: fulfilledORRejected[i], resOpt });
        await this.contextProvider.sendAnalytics({
          msg,
          resOpt,
          fulfilledORRejected: fulfilledORRejected[i],
        });
        dtos[i] = {
          ...resOpt,
          availabilityErrors: [new GetAvailabilityOfReservationOptionsErrors.InvocationRejected({
            resOptName: active[i].name.value,
            // @ts-expect-error - typings not working for fulfilledORRejected[i].reason
            reason: fulfilledORRejected[i].reason,
          })],
          availabilityGot: null,
        };
      }
    }

    for (const resOpt of dtos) {
      if (resOpt.availabilityErrors)
        console.log(`GetAvailabilityOfReservationOptions returning error for ${resOpt.name}`, resOpt.availabilityErrors);
      else if (resOpt.availabilityGot)
        console.log(`GetAvailabilityOfReservationOptions returning ${resOpt.availabilityGot.length} availabilityGot for ${resOpt.name}`, resOpt.availabilityGot);
      else {
        const msg = `GetAvailabilityOfReservationOptions without error nor availabilityGot for ${resOpt.name}`;
        console.log(msg, resOpt);
        await this.contextProvider.sendAnalytics({
          msg,
          resOpt,
        });
      }
    }

    return {
      status: Status.OK,
      result: dtos,
    };
  }
}
