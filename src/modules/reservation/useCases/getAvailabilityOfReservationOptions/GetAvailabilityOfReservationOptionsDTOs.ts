import { ReservationOptionDto } from '../../domain/ReservationOption';
import { BaseErrorDto } from '@shared/core/AppError';
import { GetAvailableTimesRes } from '../../external';

export type Request = {
  userId: string; // Taken from auth data
  lng: string;
};

export type ReservationOptionWithAvailabilityDto = ReservationOptionDto & {
  availabilityGot: GetAvailableTimesRes;
  availabilityErrors: BaseErrorDto[] | null;
};

export type Response = ReservationOptionWithAvailabilityDto[] | null;
