import { patch } from '@shared/core/utils';
import { BadRequest, BaseError, NotFound } from '@shared/core/AppError';
import { PossibleLngs } from '@shared/utils/utils';
import { Status } from '@shared/core/Status';

export namespace GetAvailabilityOfReservationOptionsErrors {
  export class NoReservationOptions extends NotFound {
    public constructor(lng: PossibleLngs) {
      const t = trans[lng];
      super({ message: t.noReservationOptions });
    }
  }
  export class NoActiveReservationOptions extends NotFound {
    public constructor(lng: PossibleLngs) {
      const t = trans[lng];
      super({ message: t.noActiveReservationOptions });
    }
  }
  export class TooManyActiveReservationOptions extends BadRequest {
    public constructor(args: { qty: number, lng: PossibleLngs, max: number }) {
      const { qty, lng, max } = args;
      const t = trans[lng];
      super({ message: t.tooManyActiveReservationOptions({ qty, max }) });
    }
  }
  export class UserGSnotFound extends NotFound {
    public constructor(lng: PossibleLngs) {
      const t = trans[lng];
      super({ message: t.userGSnotFound });
    }
  }
  export class InvocationRejected extends BaseError {
    public constructor(args: { resOptName: string; reason: string }) {
      const { resOptName, reason } = args;
      super({
        message: `Invocación a GetAvailableTimes rechazada para opción de reserva "${resOptName}". Razón: ${reason}`,
        status: Status.BAD_GATEWAY,
      });
    }
  }
}

const trans = {
  en: {
    noReservationOptions: `No reservation options`,
    tooManyActiveReservationOptions(args: { qty: number; max: number }) {
      const { qty, max } = args;
      return `There are ${qty} active reservation options that exceed the maximum of ${max}.`;
    },
    noActiveReservationOptions: `No active reservation options`,
    userGSnotFound: `User general settings not found.`,
  },
  es: {
    noReservationOptions: `No hay opciones de reserva`,
    tooManyActiveReservationOptions(args: { qty: number; max: number }) {
      const { qty, max } = args;
      return `Hay ${qty} opciones de reserva activas que superan al máximo de ${max}.`;
    },
    noActiveReservationOptions: `No hay opciones de reserva activas`,
    userGSnotFound: `No se encontraron las opciones generales del usuario.`,
  },
};

patch({ GetAvailabilityOfReservationOptionsErrors });
