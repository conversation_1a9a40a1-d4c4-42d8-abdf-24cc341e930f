import { vi, it, expect, beforeEach } from 'vitest';
import { ContextProvider } from '@shared/context/ContextProvider';
import {
  GetAvailabilityOfReservationOptions,
} from './GetAvailabilityOfReservationOptions';
import { ReservationOptionRepoFake } from '../../repos/ReservationOptionRepoFake';
import { ReservationOptionRepo } from '../../repos/ReservationOptionRepo';
import {
  ReservationOptionsInDb,
} from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { GetAvailableTimesReq, GetAvailableTimesRes } from '../../external';
import { DateTime } from 'luxon';
import { BadRequest } from '@shared/core/AppError';
import {
  invokerDummy as invoker,
  mockContext,
  nonExistingId,
  pickLng,
} from '@shared/utils/test';
import { createReservationOption } from '../../utils/testExternal';
import { ReservationOption } from '../../domain/ReservationOption';
import { Dat } from '@shared/core/Dat';
import { Response } from './GetAvailabilityOfReservationOptionsDTOs';
import { Result2 } from '@shared/core/Result2';
import { FC2Handler } from '@shared/infra/Controllers';
import { GeneralSettingsRepoFake } from '../../repos/GeneralSettingsRepoFake';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

let reservationOptionRepo: ReservationOptionRepoFake,
    gsRepo: GeneralSettingsRepoFake,
    contextProvider: ContextProvider;
const lng = pickLng();

beforeEach(() => {
  reservationOptionRepo = new ReservationOptionRepoFake();
  gsRepo = new GeneralSettingsRepoFake();
  contextProvider = new ContextProvider({ invoker });
  contextProvider.set(mockContext);
  vi.spyOn(contextProvider, 'sendAnalytics').mockResolvedValue();
});

it(`returns availability for reservation options`, async () => {

  const
    spots1 = chance.natural(),
    spots2 = chance.natural(),
    spots3 = chance.natural(),
    time1 = DateTime.now().plus({day: chance.natural({max: 5})}).toJSON(),
    time2 = DateTime.now().plus({day: chance.natural({max: 5})}).toJSON(),
    time3 = DateTime.now().plus({day: chance.natural({max: 5})}).toJSON();
  let numberOfCalls = 0;

  const getAvailableTimes: FC2Handler<GetAvailableTimesReq, GetAvailableTimesRes> = async () => {
    switch (numberOfCalls) {
      case 0:
        numberOfCalls++;
        // First invocation (for ReservationOptionsInDb[0]) to GetAvailableTimes gives two times with availability.
        return Result2.ok([
          {
            time: time1,
            spots: spots1,
          },
          {
            time: time2,
            spots: spots2,
          },
        ]);

      case 1:
        numberOfCalls++;
        // Second invocation (for ReservationOptionsInDb[3]) to GetAvailableTimes gives one time with availability.
        return Result2.ok([
          {
            time: time3,
            spots: spots3,
          },
        ]);

      default:
        throw Error(`More than 2 invokes?`);
    }
  };

  const controller = new GetAvailabilityOfReservationOptions({
    reservationOptionRepo,
    getAvailableTimes,
    gsRepo,
    contextProvider,
  });

  const response = await controller.executeImpl({
    userId: ReservationOptionsInDb[0].userId,
    lng,
  });

  expect((response.result as Response)!.length).toBe(2);
  expect(response).toEqual({
    status: 200,
    result: expect.arrayContaining([
      {
        ...ReservationOptionRepo.mapDb2Dto(ReservationOptionsInDb[0]),
        availabilityGot: [
          {
            time: time1,
            spots: spots1,
          },
          {
            time: time2,
            spots: spots2,
          },
        ],
        availabilityErrors: null,
      },
      {
        ...ReservationOptionRepo.mapDb2Dto(ReservationOptionsInDb[3]),
        availabilityGot: [
          {
            time: time3,
            spots: spots3,
          },
        ],
        availabilityErrors: null,
      },
    ]),
  });

  // Verify sendAnalytics was not called for successful responses
  expect(contextProvider.sendAnalytics).not.toHaveBeenCalled();
});

it(`returns null when there are no available spots`, async () => {

  let numberOfCalls = 0;
  const getAvailableTimes: FC2Handler<GetAvailableTimesReq, GetAvailableTimesRes> = async () => {
    switch (numberOfCalls) {
      case 0:
        numberOfCalls++;
        // First invocation (for ReservationOptionsInDb[0]) to GetAvailableTimes gives two times with availability.
        return Result2.ok(null);

      case 1:
        numberOfCalls++;
        // Second invocation (for ReservationOptionsInDb[3]) to GetAvailableTimes gives one time with availability.
        return Result2.ok(null);

      default:
        throw Error(`More than 2 invokes?`);
    }
  };

  const controller = new GetAvailabilityOfReservationOptions({
    reservationOptionRepo,
    getAvailableTimes,
    gsRepo,
    contextProvider,
  });

  const response = await controller.executeImpl({
    userId: ReservationOptionsInDb[0].userId,
    lng,
  });

  expect((response.result as Response)!.length).toBe(2);
  expect(response).toEqual({
    status: 200,
    result: expect.arrayContaining([
      {
        ...ReservationOptionRepo.mapDb2Dto(ReservationOptionsInDb[0]),
        availabilityGot: null,
        availabilityErrors: null,
      },
      {
        ...ReservationOptionRepo.mapDb2Dto(ReservationOptionsInDb[3]),
        availabilityGot: null,
        availabilityErrors: null,
      },
    ]),
  });

  // Verify sendAnalytics was called for null responses
  expect(contextProvider.sendAnalytics).toHaveBeenCalledTimes(2);
});

it(`fills availabilityError when the GetAvailableTimes is fulfilled with errors`, async () => {

  class Error1 extends BadRequest {
    public constructor() {
      super({message: 'error 1'});
    }
  }

  const error1 = new Error1();

  class Error2 extends BadRequest {
    public constructor() {
      super({message: 'error 2'});
    }
  }

  const error2 = new Error2();

  let numberOfCalls = 0;
  const getAvailableTimes: FC2Handler<GetAvailableTimesReq, GetAvailableTimesRes> = async () => {
    switch (numberOfCalls) {
      case 0:
        numberOfCalls++;
        // First invocation (for ReservationOptionsInDb[0]) to GetAvailableTimes gives two times with availability.
        return Result2.fail([error1]);

      case 1:
        numberOfCalls++;
        // Second invocation (for ReservationOptionsInDb[3]) to GetAvailableTimes gives one time with availability.
        return Result2.fail([error2]);

      default:
        throw Error(`More than 2 invokes?`);
    }
  };

  const controller = new GetAvailabilityOfReservationOptions({
    reservationOptionRepo,
    getAvailableTimes,
    gsRepo,
    contextProvider,
  });

  const response = await controller.executeImpl({
    userId: ReservationOptionsInDb[0].userId,
    lng,
  });

  expect((response.result as Response)!.length).toBe(2);
  expect(response).toEqual({
    status: 200,
    result: expect.arrayContaining([
      {
        ...ReservationOptionRepo.mapDb2Dto(ReservationOptionsInDb[0]),
        availabilityGot: null,
        availabilityErrors: [error1.toDto()],
      },
      {
        ...ReservationOptionRepo.mapDb2Dto(ReservationOptionsInDb[3]),
        availabilityGot: null,
        availabilityErrors: [error2.toDto()],
      },
    ]),
  });

  // Verify sendAnalytics was called for error responses
  expect(contextProvider.sendAnalytics).toHaveBeenCalledTimes(2);
});

it(`fills availabilityError when the invocation is rejected`, async () => {

  let numberOfCalls = 0;
  const getAvailableTimes: FC2Handler<GetAvailableTimesReq, GetAvailableTimesRes> = () =>
    new Promise((_resolve, reject) => {
      switch (numberOfCalls) {
        case 0:
          numberOfCalls++;
          // First invocation (for ReservationOptionsInDb[0]) to GetAvailableTimes
          reject('reason 1');
          break;

        case 1:
          numberOfCalls++;
          // Second invocation (for ReservationOptionsInDb[3]) to GetAvailableTimes
          reject('reason 2');
          break;

        default:
          throw Error(`More than 2 invokes?`);
      }
    });

  const controller = new GetAvailabilityOfReservationOptions({
    reservationOptionRepo,
    getAvailableTimes,
    gsRepo,
    contextProvider,
  });

  const response = await controller.executeImpl({
    userId: ReservationOptionsInDb[0].userId,
    lng,
  });

  expect((response.result as Response)!.length).toBe(2);
  expect(response).toEqual({
    status: 200,
    result: expect.arrayContaining([
      {
        ...ReservationOptionRepo.mapDb2Dto(ReservationOptionsInDb[0]),
        availabilityGot: null,
        availabilityErrors: [{
          message: expect.stringMatching(new RegExp(
            `${ReservationOptionsInDb[0].name}.*reason 1`,
          ),),
          status: 502,
          type: 'GetAvailabilityOfReservationOptionsErrors.InvocationRejected',
        }],
      },
      {
        ...ReservationOptionRepo.mapDb2Dto(ReservationOptionsInDb[3]),
        availabilityGot: null,
        availabilityErrors: [{
          message: expect.stringMatching(new RegExp(
            `${ReservationOptionsInDb[3].name}.*reason 2`,
          ),),
          status: 502,
          type: 'GetAvailabilityOfReservationOptionsErrors.InvocationRejected',
        }],
      },
    ]),
  });

  // Verify sendAnalytics was called for rejected promises
  expect(contextProvider.sendAnalytics).toHaveBeenCalledTimes(2);
});

it(`returns NoReservationOptions error when there are no reservation options`, async () => {
  const getAvailableTimes: FC2Handler<GetAvailableTimesReq, GetAvailableTimesRes> = async () => {
    return Result2.ok(null);
  };

  const controller = new GetAvailabilityOfReservationOptions({
    reservationOptionRepo,
    getAvailableTimes,
    gsRepo,
    contextProvider,
  });
  const response = await controller.executeImpl({
    userId: nonExistingId,
    lng,
  });

  expect(response).toEqual({
    status: 404,
    result: [{
      message: expect.any(String),
      status: 404,
      type: 'GetAvailabilityOfReservationOptionsErrors.NoReservationOptions',
    }],
  });

  // Verify sendAnalytics was not called for this error case
  expect(contextProvider.sendAnalytics).not.toHaveBeenCalled();
});

it(`returns NoActiveReservationOptions error when there are no active reservation options`, async () => {
  const getAvailableTimes: FC2Handler<GetAvailableTimesReq, GetAvailableTimesRes> = async () => {
    return Result2.ok(null);
  };

  const controller = new GetAvailabilityOfReservationOptions({
    reservationOptionRepo,
    getAvailableTimes,
    gsRepo,
    contextProvider,
  });
  const response = await controller.executeImpl({
    userId: 'aaa82ed0-c39a-11ee-8adf-3f66c88fcbdd',
    lng,
  });

  expect(response).toEqual({
    status: 404,
    result: [{
      message: expect.any(String),
      status: 404,
      type: 'GetAvailabilityOfReservationOptionsErrors.NoActiveReservationOptions',
    }],
  });

  // Verify sendAnalytics was not called for this error case
  expect(contextProvider.sendAnalytics).not.toHaveBeenCalled();
});

it(`calls GetAvailableTimes with maxTimes=1000 when there are 20 reservation options`, async () => {

  const reservationOptionRepo = new ReservationOptionRepoFake();

  const twentyActiveROs: ReservationOption[] = [];
  while (twentyActiveROs.filter((resOpt) => resOpt.active).length < 20) {
    twentyActiveROs.push(createReservationOption().reservationOption);
  }

  vi.spyOn(reservationOptionRepo, 'list').mockImplementation(
    async () => twentyActiveROs as [ReservationOption, ...ReservationOption[]]
  );

  let numberOfCalls = 0;
  let maxTimesError = false;
  const getAvailableTimes: FC2Handler<GetAvailableTimesReq, GetAvailableTimesRes> = async (dto) => {
    numberOfCalls++;
    if (dto.maxTimes !== 1000)
      maxTimesError = true;
    return Result2.ok([
      {
        time: Dat.create().value.s,
        spots: chance.integer({min: 1, max: 99999999}),
      },
    ]);
  };

  const controller = new GetAvailabilityOfReservationOptions({
    reservationOptionRepo,
    getAvailableTimes,
    gsRepo,
    contextProvider,
  });

  await controller.executeImpl({
    userId: ReservationOptionsInDb[0].userId,
    lng,
  });

  expect(numberOfCalls).toBe(20);
  expect(maxTimesError).toBe(false);
});

it(`calls GetAvailableTimes with maxTimes=500 when there are more than 20 reservation options`, async () => {

  const reservationOptionRepo = new ReservationOptionRepoFake();

  const moreThan20activeROs: ReservationOption[] = [];
  const moreThan20 = 21;
  while (moreThan20activeROs.filter((resOpt) => resOpt.active).length < moreThan20) {
    moreThan20activeROs.push(createReservationOption().reservationOption);
  }

  vi.spyOn(reservationOptionRepo, 'list').mockImplementation(
    async () => moreThan20activeROs as [ReservationOption, ...ReservationOption[]]
  );

  let numberOfCalls = 0;
  let maxTimesError = false;
  const getAvailableTimes: FC2Handler<GetAvailableTimesReq, GetAvailableTimesRes> = async (dto) => {
    numberOfCalls++;
    if (dto.maxTimes !== 500)
      maxTimesError = true;
    return Result2.ok([
      {
        time: Dat.create().value.s,
        spots: chance.integer({min: 1, max: 99999999}),
      },
    ]);
  };

  const controller = new GetAvailabilityOfReservationOptions({
    reservationOptionRepo,
    getAvailableTimes,
    gsRepo,
    contextProvider,
  });

  await controller.executeImpl({
    userId: ReservationOptionsInDb[0].userId,
    lng,
  });

  expect(numberOfCalls).toBe(21);
  expect(maxTimesError).toBe(false);
});

it(`returns error TooManyActiveReservationOptions when there are more than 40 reservation options`, async () => {

  const reservationOptionRepo = new ReservationOptionRepoFake();

  const moreThan40activeROs: ReservationOption[] = [];
  const moreThan40 = 41;
  while (moreThan40activeROs.filter((resOpt) => resOpt.active).length < moreThan40) {
    moreThan40activeROs.push(createReservationOption().reservationOption);
  }

  vi.spyOn(reservationOptionRepo, 'list').mockImplementation(
    async () => moreThan40activeROs as [ReservationOption, ...ReservationOption[]]
  );

  let getAvailableTimesCalled = false;
  const getAvailableTimes: FC2Handler<GetAvailableTimesReq, GetAvailableTimesRes> = async () => {
    getAvailableTimesCalled = true;
    return Result2.ok(null);
  };

  const controller = new GetAvailabilityOfReservationOptions({
    reservationOptionRepo,
    getAvailableTimes,
    gsRepo,
    contextProvider,
  });

  const response = await controller.executeImpl({
    userId: ReservationOptionsInDb[0].userId,
    lng,
  });

  expect(getAvailableTimesCalled).toBe(false);
  expect(response).toEqual({
    status: 400,
    result: [{
      message: expect.any(String),
      status: 400,
      type: 'GetAvailabilityOfReservationOptionsErrors.TooManyActiveReservationOptions',
    }],
  });

  // Verify sendAnalytics was not called for this error case
  expect(contextProvider.sendAnalytics).not.toHaveBeenCalled();
});

it(`returns error UserGSnotFound when general settings aren't found`, async () => {

  const gsRepo = new GeneralSettingsRepoFake();
  vi.spyOn(gsRepo, 'get').mockImplementation(
    async () => null
  );

  const controller = new GetAvailabilityOfReservationOptions({
    reservationOptionRepo,
    getAvailableTimes: vi.fn(),
    gsRepo,
    contextProvider,
  });

  const response = await controller.executeImpl({
    userId: ReservationOptionsInDb[0].userId,
    lng,
  });

  expect(response).toEqual({
    status: 404,
    result: [{
      message: expect.any(String),
      status: 404,
      type: 'GetAvailabilityOfReservationOptionsErrors.UserGSnotFound',
    }],
  });

  // Verify sendAnalytics was not called for this error case
  expect(contextProvider.sendAnalytics).not.toHaveBeenCalled();
});