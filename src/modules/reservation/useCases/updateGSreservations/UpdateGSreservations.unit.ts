import { expect, describe, test, it } from 'vitest';
import { validateReq } from './UpdateGSreservations';
import { getLng } from '@shared/utils/utils';
import { ReminderTemplate } from '../../domain/ReminderTemplate';
import { N0 } from '@shared/core/N0';
import { PositiveInt } from '@shared/core/PositiveInt';
import { ReservationLimits } from '../../domain/ReservationLimits';

// Success case and error gs not found are tested in e2e tests

describe(`validateReq`, () => {

  const valid = {
    cancellationUpTo: 0,
    maxSimultaneousReservations: 1,
    maxDaysAhead: 20,
    minTimeBeforeService: 10,
    reminderTemplate: `Hello { firstName }!`,
    lng: getLng(),
  };

  it(`should return a result with the components if the request is valid`, () => {
    const { errors, cancellationUpTo, maxSimultaneousReservations, reservationLimits, reminderTemplate } = validateReq(valid);
    expect(errors).toBeNull();
    expect(cancellationUpTo).toBeInstanceOf(N0);
    expect(maxSimultaneousReservations).toBeInstanceOf(PositiveInt);
    expect(reservationLimits).toBeInstanceOf(ReservationLimits);
    expect(reminderTemplate).toBeInstanceOf(ReminderTemplate);
  })

  const cancellationUpToError =
    expect.objectContaining({
      field: 'cancellationUpTo',
      message: expect.any(String),
      status: 400,
      type: expect.any(String),
    });
  const maxSimultaneousReservationsError =
    expect.objectContaining({
      field: 'maxSimultaneousReservations',
      message: expect.any(String),
      status: 400,
      type: expect.any(String),
    });
  const maxDaysAheadError =
    expect.objectContaining({
      field: 'maxDaysAhead',
      message: expect.any(String),
      status: 400,
      type: expect.any(String),
    });
  const minTimeBeforeServiceError =
    expect.objectContaining({
      field: 'minTimeBeforeService',
      message: expect.any(String),
      status: 400,
      type: expect.any(String),
    });
  const reminderTemplateError =
    expect.objectContaining({
      field: 'reminderTemplate',
      message: expect.any(String),
      status: 400,
      type: expect.stringContaining('ReminderTemplateErrors.'),
    });

  test.each([
    [
      `cancellationUpTo isn't valid`,
      { ...valid, cancellationUpTo: -1 },
      [cancellationUpToError],
    ],
    [
      `maxSimultaneousReservations isn't valid`,
      { ...valid, maxSimultaneousReservations: 0 },
      [maxSimultaneousReservationsError],
    ],
    [
      `maxDaysAhead isn't valid`,
      { ...valid, maxDaysAhead: 0 },
      [ maxDaysAheadError ],
    ],
    [
      `minTimeBeforeService isn't valid`,
      { ...valid, minTimeBeforeService: -1 },
      [ minTimeBeforeServiceError ],
    ],
    [
      `reservationLimits isn't valid`,
      { ...valid, maxDaysAhead: 1, minTimeBeforeService: 60 * 24 },
      [ maxDaysAheadError, minTimeBeforeServiceError ],
    ],
    [
      `reminderTemplate isn't valid`,
      { ...valid, reminderTemplate: 'A'.repeat(ReminderTemplate.MAX_LENGTH + 1) },
      [ reminderTemplateError ],
    ],
  ])(`%s`, async (_title, input, errors) => {
    const { errors: validationErrors } = validateReq(input);

    expect(validationErrors).toEqual(
      expect.arrayContaining(errors),
    );
  });
});