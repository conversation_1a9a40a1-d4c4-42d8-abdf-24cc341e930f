import { UpdateGSreservations } from './UpdateGSreservations';
import { ReturnUnexpectedError } from '@shared/decorators/ReturnUnexpectedError';
import { GeneralSettingsRepo } from '../../repos/GeneralSettingsRepo';
import { GuardUuid } from '@shared/decorators/GuardUuid';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });

const repo = new GeneralSettingsRepo(models.GeneralSettingsForReservation);
const controller = new UpdateGSreservations({
  repo,
  contextProvider,
});

const decorated1 = new GuardUuid({ controller, uuids: ['userId'] });
const decorated2 = new ReturnUnexpectedError({
  wrapee: decorated1,
  contextProvider: new ContextProvider({ invoker }),
});
export const handler = decorated2.execute.bind(decorated2);
