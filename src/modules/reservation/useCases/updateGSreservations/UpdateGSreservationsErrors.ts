import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { Status } from '@shared/core/Status';

export namespace UpdateGSreservationsErrors {
  export class NotFound extends BaseError {
    public constructor() {
      super({
        message: `Opciones generales de reserva no encontradas`,
        status: Status.NOT_FOUND,
      });
    }
  }
}

patch({ UpdateGSreservationsErrors });
