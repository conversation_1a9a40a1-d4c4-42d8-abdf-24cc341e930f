import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Request, Response } from './UpdateGSreservationsDTOs';
import { Status } from '@shared/core/Status';
import { IGeneralSettingsRepo } from '../../repos/IGeneralSettingsRepo';
import { N0 } from '@shared/core/N0';
import { BaseError, formatErrors } from '@shared/core/AppError';
import { UpdateGSreservationsErrors } from './UpdateGSreservationsErrors';
import { getLng } from '@shared/utils/utils';
import { PositiveInt } from '@shared/core/PositiveInt';
import { ReservationLimits } from '../../domain/ReservationLimits';
import { MaxDaysAhead } from '../../domain/MaxDaysAhead';
import { ReminderTemplate } from '../../domain/ReminderTemplate';

export class UpdateGSreservations extends AppSyncController<
  Request,
  Response
> {
  private repo: IGeneralSettingsRepo;

  public constructor(args: {
    repo: IGeneralSettingsRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {

    const { cancellationUpTo, maxSimultaneousReservations, reservationLimits, errors, reminderTemplate } = validateReq({
      cancellationUpTo: dto.cancellationUpTo,
      maxSimultaneousReservations: dto.maxSimultaneousReservations,
      maxDaysAhead: dto.maxDaysAhead,
      minTimeBeforeService: dto.minTimeBeforeService,
      reminderTemplate: dto.reminderTemplate,
      lng: dto.lng,
    });

    if (errors) return formatErrors(errors);

    const userId = dto.userId;
    const gs = await this.repo.get(userId);
    if (!gs)
      return formatErrors([new UpdateGSreservationsErrors.NotFound()]);

    gs.update({
      ...gs.props,
      phoneRequired: dto.phoneRequired,
      cancellationUpTo,
      maxSimultaneousReservations,
      reservationLimits,
      reminderTemplate,
    });

    await this.repo.update(gs);

    return {
      result: gs.toDto(),
      status: Status.OK,
    };
  }
}

// Exported to be used in frontend and unit test
export function validateReq(args: {
  cancellationUpTo: number;
  maxSimultaneousReservations: number;
  maxDaysAhead: number;
  minTimeBeforeService: number;
  reminderTemplate: string;
  lng: string;
}): {
  errors: [BaseError, ...BaseError[]];
  cancellationUpTo: null;
  maxSimultaneousReservations: null;
  reservationLimits: null;
  reminderTemplate: null;
} | {
  errors: null;
  cancellationUpTo: N0;
  maxSimultaneousReservations: PositiveInt;
  reservationLimits: ReservationLimits;
  reminderTemplate: ReminderTemplate;
} {
  const {
    cancellationUpTo: _cancellationUpTo,
    maxSimultaneousReservations: _maxSimultaneousReservations,
    maxDaysAhead: _maxDaysAhead,
    minTimeBeforeService: _minTimeBeforeService,
    reminderTemplate: _reminderTemplate,
    lng: _lng,
  } = args;
  const lng = getLng(_lng);

  let errors: BaseError[] = [];
  const cancellationUpToOrErrors = N0.create({ value: _cancellationUpTo, lng });
  if (cancellationUpToOrErrors.isFailure) {
    const _errors = cancellationUpToOrErrors.errors!;
    for (const error of _errors) error.setField('cancellationUpTo');
    errors = errors.concat(cancellationUpToOrErrors.errors!);
  }

  const maxSimultaneousReservationsOrErrors = PositiveInt.create({ value: _maxSimultaneousReservations, lng });
  if (maxSimultaneousReservationsOrErrors.isFailure) {
    const _errors = maxSimultaneousReservationsOrErrors.errors!;
    for (const error of _errors) error.setField('maxSimultaneousReservations');
    errors = errors.concat(maxSimultaneousReservationsOrErrors.errors!);
  }

  let reservationLimitsComponentsErrors: BaseError[] = [];

  const maxDaysAheadOrErrors = MaxDaysAhead.create({ value: _maxDaysAhead, lng });
  if (maxDaysAheadOrErrors.isFailure) {
    const _errors = maxDaysAheadOrErrors.errors!;
    for (const error of _errors) error.setField('maxDaysAhead');
    reservationLimitsComponentsErrors = reservationLimitsComponentsErrors.concat(maxDaysAheadOrErrors.errors!);
  }

  const minTimeBeforeServiceOrErrors = N0.create({ value: _minTimeBeforeService, lng });
  if (minTimeBeforeServiceOrErrors.isFailure) {
    const _errors = minTimeBeforeServiceOrErrors.errors!;
    for (const error of _errors) error.setField('minTimeBeforeService');
    reservationLimitsComponentsErrors = reservationLimitsComponentsErrors.concat(minTimeBeforeServiceOrErrors.errors!);
  }

  let reservationLimitsOrError;
  if (!reservationLimitsComponentsErrors.length) {
    reservationLimitsOrError = ReservationLimits.create({
      maxDaysAhead: maxDaysAheadOrErrors.value,
      minTimeBeforeService: minTimeBeforeServiceOrErrors.value,
      lng,
    });
    if (reservationLimitsOrError.isFailure) {
      const error = reservationLimitsOrError.error!.setField('maxDaysAhead');
      errors = errors.concat(error, error.clone('minTimeBeforeService'));
    }
  } else {
    errors = errors.concat(reservationLimitsComponentsErrors);
  }

  const reminderTemplateOrErrors = ReminderTemplate.create({ value: _reminderTemplate, lng });
  if (reminderTemplateOrErrors.isFailure) {
    const _errors = reminderTemplateOrErrors.errors!;
    for (const error of _errors) error.setField('reminderTemplate');
    errors = errors.concat(reminderTemplateOrErrors.errors!);
  }

  if (errors.length) return {
    errors: errors as [BaseError, ...BaseError[]],
    cancellationUpTo: null,
    maxSimultaneousReservations: null,
    reservationLimits: null,
    reminderTemplate: null,
  };

  const cancellationUpTo = cancellationUpToOrErrors.value;
  const maxSimultaneousReservations = maxSimultaneousReservationsOrErrors.value;
  const reservationLimits = reservationLimitsOrError!.value;
  const reminderTemplate = reminderTemplateOrErrors.value;

  return {
    cancellationUpTo,
    maxSimultaneousReservations,
    reservationLimits,
    reminderTemplate,
    errors: null,
  };
}