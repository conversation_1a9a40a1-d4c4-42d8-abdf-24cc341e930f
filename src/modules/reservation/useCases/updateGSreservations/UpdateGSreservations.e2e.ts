import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { dateFormat, nonExistingId, pickLng } from '@shared/utils/test';
import { removeGS } from '../../utils/test';
import { GeneralSettingsRepo } from '../../repos/GeneralSettingsRepo';
import { GeneralSettings } from '../../domain/GeneralSettings';
import { Request } from './UpdateGSreservationsDTOs';
import { MAX_GRAPHQL_INT } from '@shared/infra/appsync/utils';
import { MaxDaysAhead } from '../../domain/MaxDaysAhead';
import { GSforReservationsFragment } from '../../utils/fragments';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');

const appsync = new AppSyncClient();
const repo = new GeneralSettingsRepo(models.GeneralSettingsForReservation);

const query = gql`
  mutation (
    $userId: ID!
    $cancellationUpTo: Int! 
    $maxSimultaneousReservations: Int! 
    $phoneRequired: Boolean!
    $maxDaysAhead: Int!
    $minTimeBeforeService: Int!
    $reminderTemplate: String!
    $lng: String!
  ) {
    updateGSreservations(
      userId: $userId
      cancellationUpTo: $cancellationUpTo
      maxSimultaneousReservations: $maxSimultaneousReservations
      phoneRequired: $phoneRequired
      maxDaysAhead: $maxDaysAhead
      minTimeBeforeService: $minTimeBeforeService
      reminderTemplate: $reminderTemplate
      lng: $lng
    ) {
      result {
        ...GSforReservationsFragment
      }
      time
    }
  }
  ${GSforReservationsFragment}
`;

const userId = chance.guid({ version: 4 });
const maxDaysAhead = chance.integer({ min: 1, max: MaxDaysAhead.MAX });
const input = {
  userId,
  cancellationUpTo: chance.integer({ min: 0, max: 60 }),
  maxSimultaneousReservations: chance.integer({ min: 1, max: MAX_GRAPHQL_INT }),
  phoneRequired: chance.bool(),
  maxDaysAhead,
  minTimeBeforeService: chance.integer({ min: 0, max: maxDaysAhead * 60 * 24 - 1 }),
  reminderTemplate: chance.sentence(),
  lng: pickLng(),
};

it(`updates gs`, async () => {
  const initialGs = GeneralSettings.createDefault({ id: userId, lng: input.lng });

  // First create a new GS
  await repo.create(initialGs);

  const received = await appsync.send<Request>({
    query,
    variables: input,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.updateGSreservations;
  expect(response).toEqual({
    result: expect.objectContaining({
      ...initialGs.toDto(),
      cancellationUpTo: input.cancellationUpTo,
      maxSimultaneousReservations: input.maxSimultaneousReservations,
      phoneRequired: input.phoneRequired,
      reservationLimits: {
        maxDaysAhead: input.maxDaysAhead,
        minTimeBeforeService: input.minTimeBeforeService,
      },
      reminderTemplate: input.reminderTemplate,
    }),
    time: expect.any(String),
  });

  await removeGS(userId);
});

it(`fails when general settings aren't found`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      ...input,
      userId: nonExistingId,
    },
  });

  expect(received.status).toBe(200);

  expect(await received.json()).toMatchObject({
    data: null,
    errors: [
      {
        errorType: 'UpdateGSreservationsErrors.NotFound',
        message: expect.any(String),
        errorInfo: {
          time: expect.stringMatching(dateFormat),
          errors: [
            {
              message: expect.any(String),
              status: 404,
              type: 'UpdateGSreservationsErrors.NotFound',
            },
          ],
        },
      },
    ],
  });
});
