import { GeneralSettingsDto } from '../../domain/GeneralSettings';

export type Request = {
  cancellationUpTo: GeneralSettingsDto['cancellationUpTo'];
  maxSimultaneousReservations: GeneralSettingsDto['maxSimultaneousReservations'];
  phoneRequired: GeneralSettingsDto['phoneRequired'];
  maxDaysAhead: GeneralSettingsDto['reservationLimits']['maxDaysAhead'];
  minTimeBeforeService: GeneralSettingsDto['reservationLimits']['minTimeBeforeService'];
  reminderTemplate: GeneralSettingsDto['reminderTemplate'];
  userId: string;
  lng: string;
};

export type Response = GeneralSettingsDto;
