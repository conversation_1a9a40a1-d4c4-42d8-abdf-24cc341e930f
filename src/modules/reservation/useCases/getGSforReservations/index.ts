import { GetGSforReservations } from './GetGSforReservations';
import { ReturnUnexpectedError } from '@shared/decorators/ReturnUnexpectedError';
import { GeneralSettingsRepo } from '../../repos/GeneralSettingsRepo';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });

const repo = new GeneralSettingsRepo(models.GeneralSettingsForReservation);
const controller = new GetGSforReservations({
  repo,
  contextProvider,
});

const decorated = new ReturnUnexpectedError({
  wrapee: controller,
  contextProvider: new ContextProvider({ invoker }),
});
export const handler = decorated.execute.bind(decorated);
