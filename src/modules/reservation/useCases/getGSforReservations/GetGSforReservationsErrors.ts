import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { StatusError } from '@shared/core/Status';

export namespace GetGSforReservationsErrors {
  export class UserNotFound extends BaseError {
    public constructor(id: string) {
      super({
        message: `Opciones generales de reserva para negocio con id "${id}" no encontrado`,
        status: StatusError.NOT_FOUND,
      });
    }
  }
}

patch({ GetGSforReservationsErrors });
