import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { GeneralSettingsForReservationsInDb } from '@shared/infra/database/sequelize/migrations/GeneralSettingsForReservations.json';
import { dateFormat, expectErrorAppSync, nonExistingId } from '@shared/utils/test';
import { Request } from './GetGSforReservationsDTOs';
import { GSforReservationsFragment } from '../../utils/fragments';

const appsync = new AppSyncClient();

const query = gql`
  query ($userId: ID!) {
    getGSforReservations(userId: $userId) {
      result {
        ...GSforReservationsFragment
      }
      time
    }
  }
  ${GSforReservationsFragment}
`;

it('gets general settings', async () => {
  const userId = GeneralSettingsForReservationsInDb[1].id;
  const received = await appsync.send<Request>({
    query,
    variables: {
      userId,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.getGSforReservations;
  const { cancellationUpTo, title, welcome, slug, phoneRequired } =
    GeneralSettingsForReservationsInDb[1];
  expect(response).toMatchObject({
    result: {
      id: userId,
      cancellationUpTo,
      title,
      welcome,
      slug,
      phoneRequired,
    },
    time: expect.stringMatching(dateFormat),
  });
});

it('returns error when user not found', async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      userId: nonExistingId,
    },
  });

  expect(received.status).toBe(200);
  const response = await received.json();

  expectErrorAppSync({
    response,
    error: 'GetGSforReservationsErrors.UserNotFound',
    query: 'getGSforReservations',
    status: 404,
  });
});
