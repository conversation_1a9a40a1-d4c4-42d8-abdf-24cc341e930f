import { it, expect } from 'vitest';
import { GetGSforReservations } from './GetGSforReservations';
import { GeneralSettingsRepoFake } from '../../repos/GeneralSettingsRepoFake';
import { GeneralSettingsForReservationsInDb } from '@shared/infra/database/sequelize/migrations/GeneralSettingsForReservations.json';
import {
  expectControllerError,
  invokerDummy as invoker,
} from '@shared/utils/test';
import { ContextProvider } from '@shared/context/ContextProvider';

const repo = new GeneralSettingsRepoFake();
const contextProvider = new ContextProvider({ invoker });
const getGSforReservation = new GetGSforReservations({
  repo,
  contextProvider,
});

it('gets general settings for reservation', async () => {
  const userId = GeneralSettingsForReservationsInDb[0].id;
  const response = await getGSforReservation.executeImpl({ userId });

  // Expected result:
  const { id, cancellationUpTo, title, welcome, slug, phoneRequired } =
    GeneralSettingsForReservationsInDb[0];
  expect(response).toMatchObject({
    status: 200,
    result: {
      id,
      cancellationUpTo,
      title,
      welcome,
      slug,
      phoneRequired,
    },
  });
});

it('returns error when business not found', async () => {
  const response = await getGSforReservation.executeImpl({ userId: 'not-found' });

  expectControllerError({
    response,
    code: 404,
    error: 'GetGSforReservationsErrors.UserNotFound',
  });
});
