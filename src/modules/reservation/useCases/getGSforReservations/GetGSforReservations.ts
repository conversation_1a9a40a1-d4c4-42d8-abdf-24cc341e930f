import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Request, Response } from './GetGSforReservationsDTOs';
import { Status } from '@shared/core/Status';
import { IGeneralSettingsRepo } from '../../repos/IGeneralSettingsRepo';
import { formatErrors } from '@shared/core/AppError';
import { GetGSforReservationsErrors } from './GetGSforReservationsErrors';

export class GetGSforReservations extends AppSyncController<Request, Response> {
  private repo: IGeneralSettingsRepo;

  public constructor(args: {
    repo: IGeneralSettingsRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(event: Request): ControllerResult<Response> {
    const { userId } = event;
    const gs = await this.repo.get(userId);
    if (!gs)
      return formatErrors([new GetGSforReservationsErrors.UserNotFound(userId)]);

    return {
      result: gs.toDto(),
      status: Status.OK,
    };
  }
}
