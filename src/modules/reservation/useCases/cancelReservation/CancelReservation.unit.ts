import { expect, vi, it, describe, beforeEach, test } from 'vitest';
vi.stubEnv('distributeDomainEvents', 'dummy');
import { CancelReservation } from './CancelReservation';
import { ReservationRepoFake } from '../../repos/ReservationRepoFake';
import {
  CancelReservationEventReq,
  CancelReservationEventRes,
  GetBusinessUserFCReq,
  GetBusinessUserFCRes,
} from '../../external';
import {
  dateFormat,
  Error1,
  Error2,
  expectControllerError,
  getAppsyncCtx,
  invokerDummy,
  mockContext,
  nonExistingId,
  orNull,
  pickLng,
} from '@shared/utils/test';
import { Result2 } from '@shared/core/Result2';
import { BaseError } from '@shared/core/AppError';
import {
  ReservationsInDb,
} from '@shared/infra/database/sequelize/migrations/Reservations.json';
import {
  BusinessUsersInDb,
  CalendarApiErrors_CalendarEventNotFound,
  CalendarApiErrors_CalendarNotFound,
  CalendarApiErrors_InvalidGrant,
  getBusinessUser,
} from '../../utils/testExternal';
import { getLng } from '@shared/utils/utils';
import { FC2Handler, FCHandler } from '@shared/infra/Controllers';
import { Reservation } from '../../domain/Reservation';
import {
  getFutureTime,
  getReservationByIndex,
} from '../../utils/test';
import { ReservationRepo } from '../../repos/ReservationRepo';
import { GeneralSettingsRepoFake } from '../../repos/GeneralSettingsRepoFake';
import { N0 } from '@shared/core/N0';
import { GeneralSettings } from '../../domain/GeneralSettings';
import { GeneralSettingsRepo as repo } from '../../repos/GeneralSettingsRepo';
import { GeneralSettingsForReservationsInDb } from '@shared/infra/database/sequelize/migrations/GeneralSettingsForReservations.json';
import { ContextProvider } from '@shared/context/ContextProvider';
import { BusinessUserRepo } from '../../../user/repos/BusinessUserRepo';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const lng = getLng();
const reason = orNull(chance.sentence());

const getReservationByIds = (args: {
  customerId: string;
  id: string;
}): Reservation | null => {
  const { id, customerId } = args;
  const inDB = ReservationsInDb.find((res) => res.id === id && res.customerId === customerId);
  if (!inDB) return null;
  return Reservation.assemble(
    ReservationRepo.mapDb2Dto({
      ...inDB,
      start: getFutureTime(1441), // A minute later than the max cancellationUpTo in general settings
    })
  );
};
const getInput = (reservation: Reservation) => ({
  id: reservation.id.toString(),
  customerId: reservation.customerId,
  reason,
  lng,
});

let reservationRepo: ReservationRepoFake,
  cancelReservation: CancelReservation,
  cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes>,
  getBusinessUserFC: FCHandler<GetBusinessUserFCReq, GetBusinessUserFCRes>,
  gsRepo: GeneralSettingsRepoFake,
  contextProvider: ContextProvider;
beforeEach(() => {
  vi.clearAllMocks();

  reservationRepo = new ReservationRepoFake();
  vi.spyOn(reservationRepo, 'getByCustomer').mockImplementation(async ({customerId, id}) => getReservationByIds({customerId, id}));

  cancelReservationEvent = async () => {
    return Result2.ok(undefined);
  };

  getBusinessUserFC = async (args) => getBusinessUser(args);

  gsRepo = new GeneralSettingsRepoFake();

  contextProvider = new ContextProvider({ invoker: invokerDummy });

  cancelReservation = new CancelReservation({
    reservationRepo,
    cancelReservationEvent,
    getBusinessUserFC,
    contextProvider,
    gsRepo,
  });
});

describe(`success when calling calendar module`, () => {
  it(`successfully cancelled the event`, async () => {

    const reservation = getReservationByIndex(0);
    const input = getInput(reservation);
    const reservationRepo = new ReservationRepoFake();
    vi.spyOn(reservationRepo, 'getByCustomer').mockImplementation(async () => reservation);

    const cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes> = async (dto) => {
      [
        reservation.customerFirstName,
        reservation.customerLastName,
        reservation.customerPhone,
        reservation.customerEmail,
        'phone', // ReservationsInDb[0].business.lng = 'en'
        input.reason ? input.reason : '',
      ].forEach((data) => {
        expect(dto.addToEventDescription).toContain(data);
      });
      return Result2.ok(undefined);
    };

    const cancelReservation = new CancelReservation({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
      gsRepo,
    });

    const response = await cancelReservation.execute(getAppsyncCtx(input, Object()), mockContext);

    expect(response).toMatchObject({
      result: {
        ...reservation.toDto(),
        cancelReservationEventErrors: null,
        cancellationReason: input.reason,
        cancelledBy: 'CUSTOMER',
        cancelledDate: expect.stringMatching(dateFormat),
        updated_at: expect.stringMatching(dateFormat),
      },
    });

    const saved = await reservationRepo.getByCustomer({
      id: input.id,
      customerId: input.customerId,
    });
    expect(saved?.toDto()).toMatchObject({
      cancelledBy: 'CUSTOMER',
      cancellationReason: input.reason,
      cancelledDate: expect.stringMatching(dateFormat),
      cancelReservationEventErrors: null,
    });
  });

  it(`reason saved as null when ''`, async () => {
    const reservation = getReservationByIndex(0);
    const input = {
      ...getInput(reservation),
      reason: '',
    };
    const reservationRepo = new ReservationRepoFake();
    vi.spyOn(reservationRepo, 'getByCustomer').mockImplementation(async () => reservation);

    const cancelReservation = new CancelReservation({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
      gsRepo,
    });

    await cancelReservation.execute(getAppsyncCtx(input, Object()), mockContext);

    const saved = await reservationRepo.getByCustomer({
      id: input.id,
      customerId: input.customerId,
    });
    expect(saved?.toDto()).toMatchObject({
      cancellationReason: null,
    });
  });

  const dummy = {
    calendarId: 'dummy id',
    email: 'dummy email',
    eventId: 'dummy eventId',
    lng: pickLng(),
    method: 'dummyMethod',
  };
  it.each([
    [
      'error CalendarApiErrors.CalendarNotFound',
      [new CalendarApiErrors_CalendarNotFound(dummy)],
    ],
    [
      'error CalendarApiErrors.CalendarEventNotFound',
      [new CalendarApiErrors_CalendarEventNotFound(dummy)],
    ],
    [
      'error CalendarApiErrors.InvalidGrant',
      [new CalendarApiErrors_InvalidGrant(dummy)],
    ],
    ['multiple errors', [new Error1(), new Error2()]],
  ])(`returned %s`, async (_title: string, errors: BaseError[]) => {

    const reservation = getReservationByIndex(0);
    const input = getInput(reservation);
    const reservationRepo = new ReservationRepoFake();
    vi.spyOn(reservationRepo, 'getByCustomer').mockImplementation(async () => reservation);

    const cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes> = async () => {
      return Result2.fail(errors);
    };

    const cancelReservation = new CancelReservation({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
      gsRepo,
    });

    await cancelReservation.execute(getAppsyncCtx(input, Object()), mockContext);

    const saved = await reservationRepo.getByCustomer({
      id: input.id,
      customerId: input.customerId,
    });
    expect(saved?.toDto()).toMatchObject({
      cancelledBy: 'CUSTOMER',
      cancellationReason: input.reason,
      cancelledDate: expect.stringMatching(dateFormat),
      cancelReservationEventErrors: errors.map(e => e.type),
    });
  });
});

describe(`addToEventDescription honors business's lng`, () => {
  test(`es`, async () => {
    const reservation = ReservationsInDb[1];  // ReservationsInDb[1].userId = BusinessUsersInDb[1].id
    const input = {
      id: reservation.id,
      customerId: reservation.customerId,
      reason,
      lng: 'en',  // Even sending 'en' lng from the client, it will use business' lng
    };

    const getBusinessUserFC = async () => BusinessUserRepo.mapDb2Dto(BusinessUsersInDb[1]);  // BusinessUsersInDb[1].lng = es

    const cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes> = async (dto) => {
      [
        reservation.customerFirstName,
        reservation.customerLastName,
        reservation.customerPhone,
        reservation.customerEmail,
        'teléfono',
        input.reason ? input.reason : '',
      ].forEach((data) => {
        expect(dto.addToEventDescription).toContain(data);
      });
      return Result2.ok(undefined);
    }


    const cancelReservation = new CancelReservation({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
      gsRepo,
    });

    await cancelReservation.execute(getAppsyncCtx(input, Object()), mockContext);
  });
  test(`en`, async () => {
    const reservation = ReservationsInDb[0];
    const input = {
      id: reservation.id,
      customerId: reservation.customerId,
      reason,
      lng: 'es',  // Even sending 'es' lng from the client, it will use business' lng
    };

    const cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes> = async (dto) => {
      [
        reservation.customerFirstName,
        reservation.customerLastName,
        reservation.customerPhone,
        reservation.customerEmail,
        'phone', // ReservationsInDb[0].business.lng = en
        input.reason ? input.reason : '',
      ].forEach((data) => {
        expect(dto.addToEventDescription).toContain(data);
      });
      return Result2.ok(undefined);
    }

    const cancelReservation = new CancelReservation({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
      gsRepo,
    });

    await cancelReservation.execute(getAppsyncCtx(input, Object()), mockContext);
  });
});

describe(`fails when`, () => {
  test(`the reservation isn't found`, async () => {
    const reservation = getReservationByIndex(0);
    const input = getInput(reservation);

    const response = await cancelReservation.executeImpl({
      ...input,
      id: nonExistingId,
    });
    expectControllerError({
      response,
      code: 404,
      error: 'CancelReservationErrors.ReservationNotFound',
    });
  });
  test(`the reservation isn't cancellable`, async () => {
    const error1 = new Error1();
    const reservation = getReservationByIndex(0);
    vi.spyOn(reservation, 'isCancellable').mockImplementation(() => Result2.fail([error1]));
    const input = getInput(reservation);
    const reservationRepo = new ReservationRepoFake();
    vi.spyOn(reservationRepo, 'getByCustomer').mockImplementation(async () => reservation);

    const cancelReservation = new CancelReservation({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
      gsRepo,
    });

    const response = await cancelReservation.execute(getAppsyncCtx(input, Object()), mockContext);

    expect(response.errors).toEqual(expect.arrayContaining([
      expect.objectContaining({
        type: error1.type,
        message: expect.any(String),
        status: error1.status,
      }),
    ]));
  });
  test(`customer type isn't REGISTERED`, async () => {
    const reservation = getReservationByIndex(1); // Customer type for ReservationsInDb[1] is MANUALLY_CREATED
    const input = getInput(reservation);

    const response = await cancelReservation.execute(getAppsyncCtx(input, Object()), mockContext);

    expect(response.errors).toEqual(expect.arrayContaining([
      expect.objectContaining({
        type: 'CancelReservationErrors.InvalidCustomerType',
        message: expect.any(String),
        status: 400,
      }),
    ]));
  });
  test(`business isn't found`, async () => {
    const reservation = getReservationByIndex(0);
    const input = getInput(reservation);

    const getBusinessUserFC = async () => null;

    const cancelReservation = new CancelReservation({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
      gsRepo,
    });

    // Set context before executing
    contextProvider.set(mockContext);

    const response = await cancelReservation.execute(getAppsyncCtx(input, Object()), mockContext);

    expect(response.errors).toEqual(expect.arrayContaining([
      expect.objectContaining({
        type: 'CancelReservationErrors.BusinessUserNotFound',
        message: expect.any(String),
        status: 404,
      }),
    ]));
  });
  test(`cancelByCustomer errors`, async () => {
    const error1 = new Error1();
    const reservation = getReservationByIndex(0);
    vi.spyOn(reservation, 'cancelByCustomer').mockImplementation(() => Result2.fail([error1]));
    const input = getInput(reservation);
    const reservationRepo = new ReservationRepoFake();
    vi.spyOn(reservationRepo, 'getByCustomer').mockImplementation(async () => reservation);

    const cancelReservation = new CancelReservation({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
      gsRepo,
    });

    const response = await cancelReservation.execute(getAppsyncCtx(input, Object()), mockContext);

    expect(response.errors).toEqual(expect.arrayContaining([
      expect.objectContaining({
        type: error1.type,
        message: expect.any(String),
        status: error1.status,
      }),
    ]));
  });

  test(`cancellation is too late based on general settings`, async () => {
    const reservation = getReservationByIndex(0);
    const input = getInput(reservation);
    const reservationRepo = new ReservationRepoFake();
    vi.spyOn(reservationRepo, 'getByCustomer').mockImplementation(async () => reservation);

    vi.spyOn(gsRepo, 'get').mockImplementation(async () => {
      const gsInDb = GeneralSettingsForReservationsInDb.filter((gs) => gs.id === reservation.userId);

      const gs = GeneralSettings.assemble(repo.mapDb2Dto(gsInDb[0]));
      const now = new Date().getTime();
      const cancellationUpTo = Math.ceil((reservation.start.t - now + 1000 * 60) / 1000 / 60);  // The required cancellation notice is 1 minute more than the time remaining until the reservation
      gs.update({
        ...gs.props,
        cancellationUpTo: N0.create({ value: cancellationUpTo }).value,
      })
      return gs;
    });

    const cancelReservation = new CancelReservation({
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
      gsRepo,
    });

    const response = await cancelReservation.execute(getAppsyncCtx(input, Object()), mockContext);

    expect(response.errors).toEqual(expect.arrayContaining([
      expect.objectContaining({
        type: 'CancelReservationErrors.CancellationTooLate',
        message: expect.any(String),
        status: 403,
      }),
    ]));
  });
});
