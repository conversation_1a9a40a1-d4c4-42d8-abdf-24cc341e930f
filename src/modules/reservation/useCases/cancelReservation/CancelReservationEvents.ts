import { DomainEvents } from '@shared/core/domain/DomainEvents';
import { CancelReservationByCustomerEvent } from '../../domain/events/CancelReservationByCustomerEvent';
import { IInvoker } from '@shared/infra/invocation/IInvoker';

export class CancelReservationEvents {
  public static registration(invoker: IInvoker) {
    // Add all process.env used:
    const { distributeDomainEvents } = process.env;
    if (!distributeDomainEvents) {
      console.log('process.env', process.env);
      throw new Error(`Undefined env var!`);
    }

    DomainEvents.setInvoker(invoker);
    DomainEvents.register(
      `${distributeDomainEvents}`,
      CancelReservationByCustomerEvent.name,
    );
  }
}
