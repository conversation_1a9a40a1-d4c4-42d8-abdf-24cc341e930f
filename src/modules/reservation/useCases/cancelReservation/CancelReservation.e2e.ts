import { beforeAll, expect, it, vi, describe } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { Dat } from '@shared/core/Dat';
import {
  dateFormat,
  expectErrorAppSync,
  expectErrorAppSyncContaining,
  orNull,
  pickLng,
} from '@shared/utils/test';
import { Identifier } from '../../external';
import { ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { ReservationOptionRepo } from '../../repos/ReservationOptionRepo';
import {
  rmReservations,
} from '../../utils/test';
import { getCalendarCreds } from '@shared/infra/iac/helpers';
import {
  CalendarsInDb,
  rmEvents,
  tomorrowPlus,
  Event,
  Slot,
  CalendarApi,
  CalendarApiT,
  balanceRepo,
  rmBalanceRow,
  Movement,
} from '../../utils/testExternal';
import { calendar } from '@googleapis/calendar';
import { ReservationRepo } from '../../repos/ReservationRepo';
import { CustomerType, Reservation } from '../../domain/Reservation';
import { createCustomerUser } from '../../../user/utils/test';
import { ReservationsInDb } from '@shared/infra/database/sequelize/migrations/Reservations.json';
import { getLng } from '@shared/utils/utils';
import { Request } from './CancelReservationDTOs';
import { ReservationWithTimestampsFragment } from '../../utils/fragments';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');

const sendAnalytics = vi.fn();

// Add all process.env used:
const { PROJECT, STAGE } = process.env;
if (!PROJECT || !STAGE) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}

let reservationApi: CalendarApiT, reservationRepo: ReservationRepo;
beforeAll(async () => {
  const { calendar_clientEmail, calendar_clientKey } = await getCalendarCreds({
    name: PROJECT,
    stage: STAGE,
  });
  vi.stubEnv('calendar_clientEmail', calendar_clientEmail);
  vi.stubEnv('calendar_clientKey', calendar_clientKey);
  reservationRepo = new ReservationRepo(models.Reservation);
  reservationApi = new CalendarApi({
    google: calendar({ version: 'v3' }),
    sendAnalytics,
  });
  reservationApi.setCalendarId(CalendarsInDb[6].id); // CalendarsInDb[6].userId = BusinessUsersInDb[6].id & CalendarsInDb[6].type = 'R'
});

const appsync = new AppSyncClient();

const query = gql`
  mutation ($id: ID!, $customerId: ID!, $reason: String, $lng: String!) {
    cancelReservation(id: $id, customerId: $customerId, reason: $reason, lng: $lng) {
      result {
        ...ReservationWithTimestampsFragment
      }
      time
    }
  }
  ${ReservationWithTimestampsFragment}
`;

const reservationOptionDto = ReservationOptionRepo.mapDb2Dto(ReservationOptionsInDb[7]); // ReservationOptionsInDb[7].userId = BusinessUsersInDb[6].id 06a82ed0-c39a-11ee-8adf-3f66c88fcbdd

const start = Dat.create({ value: tomorrowPlus(60).l.startOf('hour') }).value;
const end = Dat.create({
  value:
    start.l.plus({minutes: reservationOptionDto.service.duration}),
}).value;
const identifier = Identifier.create(
  { value: reservationOptionDto.location.identifier },
  sendAnalytics,
).value;

it(`cancels a reservation event`, async () => {
  // First create a reservation event
  const reservationEvent = Event.create({
    name: 'Reservation',
    description: `${identifier.query}:2`,
    slot: Slot.create({
      start,
      end,
    }).value,
  });
  const resInsertedOrError = await reservationApi.insertEvent({
    event: reservationEvent,
    lng: pickLng(),
  });
  expect(resInsertedOrError.isSuccess).toBe(true);
  const resInserted = resInsertedOrError.value;

  // Second create a reservation in repo

  const customer = createCustomerUser().dto;
  const userId = reservationOptionDto.userId;  // 06a82ed0-c39a-11ee-8adf-3f66c88fcbdd
  const reservation = Reservation.create({
    userId,
    customerType: CustomerType.REGISTERED,
    customerFirstName: customer.firstName,
    customerLastName: customer.lastName,
    customerPhone: customer.phone,
    customerEmail: customer.email,
    customerId: customer.id,
    start,
    eventId: resInserted.id,
    roId: reservationOptionDto.id,
    roZone: reservationOptionDto.timeReference.zone,
    srvName: reservationOptionDto.service.name,
    srvDuration: reservationOptionDto.service.duration,
    srvDescription: reservationOptionDto.service.description,
    srvPrice: reservationOptionDto.service.price,
    locName: reservationOptionDto.location.name,
    locId: reservationOptionDto.location.identifier,
  });
  await reservationRepo.create(reservation);

  // Send request
  const input = {
    id: reservation.id.toString(),
    customerId: reservation.customerId,
    reason: orNull(chance.sentence()),
    lng: pickLng(),
  };
  const received = await appsync.send<Request>({
    query,
    variables: input,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log('CancelReservation.e2e.ts 1 json', json);
  const response = json.data.cancelReservation;
  if (!response) console.log('CancelReservation.e2e.ts 2 json', json);

  const dto = reservation.toDto();
  const timestamps = {
    created_at: expect.stringMatching(dateFormat),
    updated_at: expect.stringMatching(dateFormat),
    deleted_at: null,
  }
  expect(response).toEqual({
    result: {
      ...dto,
      ...timestamps,
      start: reservation.start.j.toJSON(),
      cancellationReason: input.reason,
      cancelledBy: 'CUSTOMER',
      cancelledDate: expect.stringMatching(dateFormat),
    },
    time: expect.stringMatching(dateFormat),
  });

  // Check reservation cancellation was saved on repo
  const cancelledReservation = await reservationRepo.getByCustomer({
    id: reservation.id.toString(),
    customerId: reservation.customerId,
  });
  if (!cancelledReservation) throw new Error('Cancelled reservation not found');
  expect(cancelledReservation.toDto()).toMatchObject({
    cancelledBy: 'CUSTOMER',
    cancellationReason: input.reason,
    cancelledDate: expect.stringMatching(dateFormat),
    cancelReservationEventErrors: null,
  });

  // Side effect on @balance
  // Wait for the side effect to be applied
  await new Promise(resolve => setTimeout(resolve, 3000));
  const balanceRows = await balanceRepo.list(userId);
  const matches = balanceRows.filter(row => {
    return row.reservationId === reservation.id.toString() &&
      row.movement === Movement.CANCELLATION_BY_CUSTOMER;
  });
  if (matches.length !== 1) console.log(`No cancelled by customer movement found for ${userId} at ${new Date().toJSON()}`);
  expect(matches).toHaveLength(1);

  // Clean up
  await rmReservations([reservation.id.toString()]);
  await rmEvents({
    inserted: [{ value: { id: resInserted.id } }],
    calendarApi: reservationApi,
  });
  await rmBalanceRow({ userId, index: 1 });
});

describe(`fails when the reservation`, () => {
  it(`was already cancelled`, async () => {
    const reservation = ReservationsInDb[3]; // Already cancelled
    const input = {
      id: reservation.id.toString(),
      customerId: reservation.customerId,
      reason: orNull(chance.sentence()),
      lng: getLng(),
    };
    const received = await appsync.send<Request>({
      query,
      variables: input,
    });

    expect(received.status).toBe(200);
    expectErrorAppSyncContaining({
      response: await received.json(),
      errorContaining: 'ReservationErrors.',
      status: 409,
    });
  });
  it(`isn't found`, async () => {
    const input = {
      customerId: ReservationsInDb[0].userId,
      id: ReservationsInDb[1].id,
      reason: orNull(chance.sentence()),
      lng: getLng(),
    };
    const received = await appsync.send<Request>({
      query,
      variables: input,
    });

    expect(received.status).toBe(200);
    expectErrorAppSync({
      response: await received.json(),
      query: 'cancelReservation',
      error: 'CancelReservationErrors.ReservationNotFound',
      status: 404,
    });
  });
});
