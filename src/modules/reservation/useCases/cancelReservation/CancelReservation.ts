import { Request, Response } from './CancelReservationDTOs';
import { CancelReservationErrors } from './CancelReservationErrors';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { IReservationRepo } from '../../repos/IReservationRepo';
import {
  CancelReservationEventReq,
  CancelReservationEventRes,
} from '../../external';
import { Dat } from '@shared/core/Dat';
import { formatErrors } from '@shared/core/AppError';
import {
  AppSync<PERSON><PERSON><PERSON>er,
  FC2Handler,
  FCHandler,
} from '@shared/infra/Controllers';
import { getLng } from '@shared/utils/utils';
import { getContactDetails } from '../../utils/utils';
import { CancelReservationEvents } from './CancelReservationEvents';
import {
  CancelReservationByCustomerEvent,
} from '../../domain/events/CancelReservationByCustomerEvent';
import { CustomerType } from '../../domain/Reservation';
import { GetBusinessUserFCReq, GetBusinessUserFCRes } from '../../external';
import { IGeneralSettingsRepo } from '../../repos/IGeneralSettingsRepo';
import { ContextProvider } from '@shared/context/ContextProvider';

// CancelReservation use case is used by CustomerUsers. dto.customerId should be taken from auth data.
export class CancelReservation extends AppSyncController<Request, Response> {
  private readonly cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes>;
  private readonly getBusinessUserFC: FCHandler<GetBusinessUserFCReq, GetBusinessUserFCRes>;
  private readonly reservationRepo: IReservationRepo;
  private readonly gsRepo: IGeneralSettingsRepo;
  public constructor(args: {
    reservationRepo: IReservationRepo;
    cancelReservationEvent: FC2Handler<CancelReservationEventReq, CancelReservationEventRes>;
    getBusinessUserFC: FCHandler<GetBusinessUserFCReq, GetBusinessUserFCRes>;
    contextProvider: ContextProvider;
    gsRepo: IGeneralSettingsRepo;
  }) {
    const {
      reservationRepo,
      cancelReservationEvent,
      getBusinessUserFC,
      contextProvider,
      gsRepo,
    } = args;
    super({ contextProvider });
    this.reservationRepo = reservationRepo;
    this.cancelReservationEvent = cancelReservationEvent;
    this.getBusinessUserFC = getBusinessUserFC;
    this.gsRepo = gsRepo;
    CancelReservationEvents.registration(contextProvider.invoker());
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { id, customerId, reason: _reason, lng: _lng } = dto;
    const lng = getLng(_lng);

    let reason = null;
    if (_reason) {
      const trimmed = _reason.trim();
      if (trimmed.length > 0) reason = trimmed;
    }

    const reservation = await this.reservationRepo.getByCustomer({
      id,
      customerId,
    });
    if (!reservation)
      return formatErrors([new CancelReservationErrors.ReservationNotFound({ id, lng })]);

    const isCancellableOrErrors = reservation.isCancellable(lng);
    if (isCancellableOrErrors.isFailure)
      return formatErrors(isCancellableOrErrors.errors!);

    const {
      userId,
      eventId,
      start,
      customerType,
      customerFirstName,
      customerLastName,
      customerPhone,
      customerEmail,
      srvDuration,
      roZone,
      locId,
    } = reservation;
    const end = start.l.plus({ minutes: srvDuration });

    // This cancellation is only done by CustomerUsers (not ManuallyCreatedCustomer)
    if (customerType !== CustomerType.REGISTERED) {
      await this.contextProvider.sendAnalytics({
        e: 'CancelReservationErrors.InvalidCustomerType',
        customerType,
      });
      return formatErrors([
        new CancelReservationErrors.InvalidCustomerType({
          invalid: customerType,
          lng,
        }),
      ]);
    }

    const gs = await this.gsRepo.get(userId);
    if (!gs)
      return formatErrors([new CancelReservationErrors.UserGSnotFound(lng)]);

    const cancellationUpTo = gs.cancellationUpTo.value;
    if (start.t - new Date().getTime() < cancellationUpTo * 60 * 1000)
      return formatErrors([
        new CancelReservationErrors.CancellationTooLate({ cancellationUpTo, lng }),
      ]);

    const businessUser = await this.getBusinessUserFC({ id: userId });
    if (!businessUser)
      return formatErrors([
        new CancelReservationErrors.BusinessUserNotFound({ id: userId, lng }),
      ]);
    const businessLng = getLng(businessUser.lng);

    const fullName = `${customerFirstName} ${customerLastName}`;

    const cancelledDate = Dat.create().value;
    const cancelledDateText = cancelledDate.l
      .setZone(roZone)
      .startOf('second')
      .toISO({ suppressMilliseconds: true });

    let addToEventDescription;
    const contactDetails = getContactDetails({
      customerPhone,
      customerEmail,
      lng: businessLng,
    });
    switch (businessLng) {
      case 'es': {
        addToEventDescription =
          `${fullName} ` +
          contactDetails +
          `canceló su reserva a las ${cancelledDateText}.`;
        if (reason)
          addToEventDescription += ` Razón: ${reason}`;
        break;
      }
      case 'en': {
        addToEventDescription =
          `${fullName} ` +
          contactDetails +
          `cancelled their reservation at ${cancelledDateText}.`;
        if (reason)
          addToEventDescription += ` Reason: ${reason}`;
        break;
      }
    }

    // Call synchronously CancelReservationEvent in module calendar
    let result = await this.cancelReservationEvent({
      userId,
      eventId,
      identifier: locId,
      start: start.s,
      end: Dat.create({ value: end }).value.s,
      addToEventDescription,
      lng: businessLng,
    });

    const { isFailure, errors } = result;
    if (isFailure) {
      const msg = `CancelReservationEvent failed`;
      console.log(msg, { errors, reservation });
      await this.contextProvider.sendAnalytics({
        msg,
        errors,
        reservation,
      });
    }

    result = reservation.cancelByCustomer({
      cancelledDate,
      reason,
      cancelReservationEventErrors: errors?.map(e => e.toDto().type) as [string, ...string[]]  ?? null,
      lng,
    });
    if (result.isFailure)
      return formatErrors(result.errors!);

    reservation.addEvent(new CancelReservationByCustomerEvent(reservation.toDto()));
    const updated = await this.reservationRepo.update(reservation);

    return {
      status: Status.OK,
      result: updated,
    };
  }
}
