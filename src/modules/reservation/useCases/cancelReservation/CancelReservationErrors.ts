import { patch } from '@shared/core/utils';
import {
  CancelReservationByBusinessErrors,
} from '../cancelReservationByBusiness/CancelReservationByBusinessErrors';
import { getLng, OptionalLng, PossibleLngs } from '@shared/utils/utils';
import { BaseError, NotFound } from '@shared/core/AppError';
import { StatusError } from '@shared/core/Status';

export namespace CancelReservationErrors {
  export class ReservationNotFound extends CancelReservationByBusinessErrors.ReservationNotFound {
    public constructor(args: { id: string } & OptionalLng) {
      super(args);
    }
  }
  export class InvalidCustomerType extends BaseError {
    public constructor(args: { invalid: string } & OptionalLng) {
      const { invalid, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.invalidCustomerType(invalid),
        status: StatusError.BAD_REQUEST,
      });
    }
  }
  export class BusinessUserNotFound extends CancelReservationByBusinessErrors.BusinessUserNotFound {
    public constructor(args: { id: string } & OptionalLng) {
      super(args);
    }
  }
  export class UserGSnotFound extends NotFound {
    public constructor(lng: PossibleLngs) {
      const t = trans[lng];
      super({ message: t.userGSnotFound });
    }
  }
  export class CancellationTooLate extends BaseError {
    public constructor(args: { cancellationUpTo: number } & OptionalLng) {
      const { cancellationUpTo, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.cancellationTooLate(cancellationUpTo),
        status: StatusError.FORBIDDEN,
      });
    }
  }
}

const trans = {
  en: {
    invalidCustomerType(invalid: string) {
      return `Invalid customer type for this endpoint: ${invalid}`;
    },
    userGSnotFound: `User general settings not found.`,
    cancellationTooLate(minutes: number) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      if (hours > 0) {
        return remainingMinutes > 0
          ? `Cancellation is not allowed less than ${hours} hour${hours > 1 ? 's' : ''} and ${remainingMinutes} minute${remainingMinutes > 1 ? 's' : ''} before the reservation.`
          : `Cancellation is not allowed less than ${hours} hour${hours > 1 ? 's' : ''} before the reservation.`;
      }
      return `Cancellation is not allowed less than ${minutes} minute${minutes > 1 ? 's' : ''} before the reservation.`;
    },
  },
  es: {
    invalidCustomerType(invalid: string) {
      return `Tipo de cliente no válido para este endpoint: ${invalid}`;
    },
    userGSnotFound: `No se encontraron las opciones generales del usuario.`,
    cancellationTooLate(minutes: number) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = minutes % 60;
      if (hours > 0) {
        return remainingMinutes > 0
          ? `No se permite cancelar con menos de ${hours} hora${hours > 1 ? 's' : ''} y ${remainingMinutes} minuto${remainingMinutes > 1 ? 's' : ''} antes de la reserva.`
          : `No se permite cancelar con menos de ${hours} hora${hours > 1 ? 's' : ''} antes de la reserva.`;
      }
      return `No se permite cancelar con menos de ${minutes} minuto${minutes > 1 ? 's' : ''} antes de la reserva.`;
    },
  },
};

patch({ CancelReservationErrors });
