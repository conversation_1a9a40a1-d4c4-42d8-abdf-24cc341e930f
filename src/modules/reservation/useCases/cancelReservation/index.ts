import { CancelReservation } from './CancelReservation';
import { ReservationRepo } from '../../repos/ReservationRepo';
import { ReturnUnexpectedError } from '@shared/decorators/ReturnUnexpectedError';
import { GuardUuid } from '@shared/decorators/GuardUuid';
import {
  cancelReservationEventHandlerCreator,
} from '../../externalUseCases';
import setHooks from '@shared/infra/database/sequelize/hooks';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import {
  getBusinessUserFChandler,
} from '../../externalUseCases';
import { GeneralSettingsRepo } from '../../repos/GeneralSettingsRepo';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

setHooks();
const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });
const sendAnalytics = contextProvider.sendAnalytics.bind(contextProvider);

const reservationRepo = new ReservationRepo(models.Reservation);
const gsRepo = new GeneralSettingsRepo(models.GeneralSettingsForReservation);

const controller = new CancelReservation({
  reservationRepo,
  cancelReservationEvent: cancelReservationEventHandlerCreator(sendAnalytics),
  getBusinessUserFC: getBusinessUserFChandler,
  contextProvider,
  gsRepo,
});

const decorated1 = new GuardUuid({ controller, uuids: ['id', 'customerId'] });
const decorated2 = new ReturnUnexpectedError({
  wrapee: decorated1,
  contextProvider: new ContextProvider({ invoker }),
});
export const handler = decorated2.execute.bind(decorated2);
