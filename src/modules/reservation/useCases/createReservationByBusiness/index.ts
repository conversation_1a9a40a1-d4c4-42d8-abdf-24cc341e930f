import { CreateReservationByBusiness } from './CreateReservationByBusiness';
import { ReservationRepo } from '../../repos/ReservationRepo';
import { ReservationOptionRepo } from '../../repos/ReservationOptionRepo';
import { ReturnUnexpectedError } from '@shared/decorators/ReturnUnexpectedError';
import { ReservationOptionSrv } from '../../services/ReservationOptionSrv';
import { GuardUuid } from '@shared/decorators/GuardUuid';
import {
  getMCustomerAndBusinessUserHandler,
  isBalanceEnoughHandler,
} from '../../externalUseCases';
import {
  createReservationEventHandlerCreator,
} from '../../externalUseCases';
import setHooks from '@shared/infra/database/sequelize/hooks';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import { GeneralSettingsRepo } from '../../repos/GeneralSettingsRepo';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

setHooks();
const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });
const sendAnalytics = contextProvider.sendAnalytics.bind(contextProvider);

const reservationOptionRepo = new ReservationOptionRepo({
  ReservationOptionModel: models.ReservationOption,
  sendAnalytics,
});
const reservationRepo = new ReservationRepo(models.Reservation);
const reservationOptionSrv = new ReservationOptionSrv(reservationOptionRepo);
const gsRepo = new GeneralSettingsRepo(models.GeneralSettingsForReservation);
const controller = new CreateReservationByBusiness({
  reservationRepo,
  reservationOptionSrv,
  isBalanceEnough: isBalanceEnoughHandler,
  createReservationEvent: createReservationEventHandlerCreator(sendAnalytics),
  getMCustomerAndBusinessUser: getMCustomerAndBusinessUserHandler,
  contextProvider,
  gsRepo,
});

const decorated1 = new GuardUuid({
  controller,
  uuids: ['customerId', 'businessId', 'roId'],
});
const decorated2 = new ReturnUnexpectedError({
  wrapee: decorated1,
  contextProvider: new ContextProvider({ invoker }),
});
export const handler = decorated2.execute.bind(decorated2);
