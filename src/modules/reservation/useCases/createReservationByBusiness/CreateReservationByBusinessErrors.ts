import { patch } from '@shared/core/utils';
import { OptionalLng } from '@shared/utils/utils';
import {
  CreateReservationErrors,
} from '../createReservation/CreateReservationErrors';

export namespace CreateReservationByBusinessErrors {
  export class StartIsInvalid extends CreateReservationErrors.StartIsInvalid {
    public constructor(args: { start: string } & OptionalLng) {
      super(args);
    }
  }
  export class NoBalance extends CreateReservationErrors.NoBalance {
    public constructor(args: OptionalLng) {
      super(args);
    }
  }
  export class UserGSnotFound extends CreateReservationErrors.UserGSnotFound {
    public constructor(args: OptionalLng) {
      super(args);
    }
  }
}

patch({ CreateReservationByBusinessErrors });