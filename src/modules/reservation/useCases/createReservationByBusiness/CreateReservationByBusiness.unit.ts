import { expect, test, vi, it, describe, beforeEach } from 'vitest';
vi.stubEnv('distributeDomainEvents', 'dummy');
import { CreateReservationByBusiness } from './CreateReservationByBusiness';
import { ReservationRepoFake } from '../../repos/ReservationRepoFake';
import { ReservationOptionRepoFake } from '../../repos/ReservationOptionRepoFake';
import { ReservationOptionRepo } from '../../repos/ReservationOptionRepo';
import {
  CreateReservationEventReq,
  CreateReservationEventRes,
  GetMCustomerAndBusinessUserReq,
  GetMCustomerAndBusinessUserRes,
  IsBalanceEnoughReq,
  IsBalanceEnoughRes,
} from '../../external';
import { Action, ManuallyCreatedCustomersInDb } from '../../utils/testExternal';
import {
  Error1,
  Error2,
  expectControllerError,
  expectControllerError<PERSON>ield,
  expectControllerMultipleErrors,
  invokerDummy as invoker,
  pickLng,
} from '@shared/utils/test';
import {
  uuidFormat,
  sendAnalyticsDummy as sendAnalytics,
} from '@shared/utils/utils';
import { Dat } from '@shared/core/Dat';
import { ApiResults } from '../../../calendar/services/CalendarApiFake';
import { Result } from '@shared/core/Result';
import { CustomerType } from '../../domain/Reservation';
import { ReservationOptionSrv } from '../../services/ReservationOptionSrv';
import { BusinessUsersInDb } from '../../utils/testExternal';
import { ReservationOption } from '../../domain/ReservationOption';
import { IReservationOptionRepo } from '../../repos/IReservationOptionRepo';
import { IReservationOptionSrv } from '../../services/IReservationOptionSrv';
import { ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { Result2 } from '@shared/core/Result2';
import { FC2Handler, FCHandler } from '@shared/infra/Controllers';
import { ControllerResult } from '@shared/core/ControllerResult';
import { reservation } from '../../../../../front';
import CreateReservationByBusinessResponse = reservation.CreateReservationByBusinessResponse;
import { createEventRetrieved } from '../../../calendar/utils/test';
import { GeneralSettingsRepoFake } from '../../repos/GeneralSettingsRepoFake';
import { IGeneralSettingsRepo } from '../../repos/IGeneralSettingsRepo';
import { ContextProvider } from '@shared/context/ContextProvider';
import { BusinessUserRepo } from '../../../user/repos/BusinessUserRepo';

const reservationRepo = new ReservationRepoFake();

// BusinessUsersInDb se saca de BusinessUserRepoFake, es igual a BusinessUserDto
const business = BusinessUserRepo.mapDb2Dto(BusinessUsersInDb[0]);
// ReservationOptionsInDb[0].userId = BusinessUsersInDb[0].id
// ReservationOptionsInDb no es igual a ReservationOptionDto, necesito procesarlo con ReservationOptionRepo.mapDb2Dto para obtener ReservationOptionDto
const reservationOption = ReservationOptionRepo.mapDb2Dto(
  ReservationOptionsInDb[0],
);
// CustomerUsersInDb es igual a ManuallyCreatedCustomerDto
const customer = ManuallyCreatedCustomersInDb[6]; // ManuallyCreatedCustomersInDb[6] created by BusinessUsersInDb[0]

const mockedGEventId = 'mockedGEventId';
const mockedGEvent = {
  ...createEventRetrieved().toDto(),
  id: mockedGEventId,
};
const input = {
  customerId: customer.id,
  businessId: business.id,
  // @ts-expect-error
  start: ApiResults.listSuccess1.data.items[2].start.dateTime,
  roId: reservationOption.id,
  roVersion: reservationOption.version,
  lng: pickLng(),
};

let reservationOptionRepo: IReservationOptionRepo,
  reservationOptionSrv: IReservationOptionSrv,
  createReservationByBusiness: CreateReservationByBusiness,
  isBalanceEnough: FCHandler<IsBalanceEnoughReq, IsBalanceEnoughRes>,
  getMCustomerAndBusinessUser: FC2Handler<GetMCustomerAndBusinessUserReq, GetMCustomerAndBusinessUserRes>,
  createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes>,
  gsRepo: IGeneralSettingsRepo,
  contextProvider: ContextProvider;
beforeEach(() => {
  vi.clearAllMocks();
  reservationOptionRepo = new ReservationOptionRepoFake();
  gsRepo = new GeneralSettingsRepoFake();

  reservationOptionSrv = new ReservationOptionSrv(reservationOptionRepo);
  const mockCheckResOpt = () =>
    new Promise<Result<ReservationOption>>((resolve) => {
      resolve(Result.ok(ReservationOption.assemble(reservationOption, sendAnalytics)));
    });
  vi.spyOn(reservationOptionSrv, 'checkResOpt').mockImplementation(
    mockCheckResOpt,
  );
  getMCustomerAndBusinessUser = async () => {
    return Result2.ok({
      customer,
      business,
    });
  }
  createReservationEvent = async () => {
    return Result2.ok({
      action: Action.CREATED,
      event: mockedGEvent,
    });
  }

  isBalanceEnough = async () => {
    return true;
  }
  
  contextProvider = new ContextProvider({ invoker });

  createReservationByBusiness = new CreateReservationByBusiness({
    reservationRepo,
    reservationOptionSrv,
    isBalanceEnough,
    getMCustomerAndBusinessUser,
    createReservationEvent,
    contextProvider,
    gsRepo,
  });
});

const okResponse: Awaited<ControllerResult<CreateReservationByBusinessResponse>> = {
  status: 201,
  result: {
    id: expect.stringMatching(uuidFormat),
    userId: input.businessId,
    customerType: CustomerType.MANUALLY_CREATED,
    customerId: customer.id,
    customerFirstName: customer.firstName,
    customerLastName: customer.lastName,
    customerPhone: null,
    customerEmail: null,
    start: Dat.create({ value: input.start }).value.s,
    eventId: mockedGEventId,
    roId: reservationOption.id,
    roZone: reservationOption.timeReference.zone,
    srvName: reservationOption.service.name,
    srvDuration: reservationOption.service.duration,
    srvDescription: reservationOption.service.description,
    srvPrice: reservationOption.service.price,
    locName: reservationOption.location.name,
    locId: reservationOption.location.identifier,
    cancelReservationEventErrors: null,
    cancelledBy: null,
    cancellationReason: null,
    cancelledDate: null,
    reminder: 'NOT_SENT',
  },
};

describe('success', () => {
  it(`happy path with business's lng "en"`, async () => {
    const createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes> = async (dto) => {
      expect(new RegExp(`${customer.fullName}'s`).test(dto.addToEventDescription)).toBe(true); // BusinessUsersInDb[0].lng = en
      return Result2.ok({
        action: Action.CREATED,
        event: mockedGEvent,
      });
    }

    const createReservationByBusiness = new CreateReservationByBusiness({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      getMCustomerAndBusinessUser,
      createReservationEvent,
      contextProvider,
      gsRepo,
    });
    const response = await createReservationByBusiness.executeImpl({
      ...input,
      lng: 'es',  // even sending 'es' lng from the client, it will use business' lng
    });
    expect(response).toMatchObject(okResponse);
  });

  test(`addToEventDescription honors "es" business's lng`, async () => {
    const business = BusinessUserRepo.mapDb2Dto(BusinessUsersInDb[1]);
    // ReservationOptionsInDb[1].userId = BusinessUsersInDb[1].id
    const reservationOption = ReservationOptionsInDb[1];
    const customer = ManuallyCreatedCustomersInDb[0]; // ManuallyCreatedCustomersInDb[0] created by BusinessUsersInDb[1]
    const input2 = {
      ...input,
      businessId: business.id,
      roId: reservationOption.id,
      roVersion: reservationOption.version,
      lng: 'en',  // even sending 'en' lng from the client, it will use business' lng
    };

    const getMCustomerAndBusinessUser: FC2Handler<GetMCustomerAndBusinessUserReq, GetMCustomerAndBusinessUserRes> = async () => {
      return Result2.ok({
        customer,
        business,
      });
    }

    const createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes> = async (dto) => {
      expect(new RegExp(`para ${customer.fullName}`).test(dto.addToEventDescription)).toBe(true); // BusinessUsersInDb[0].lng = en
      return Result2.ok({
        action: Action.CREATED,
        event: mockedGEvent,
      });
    }

    const createReservationByBusiness = new CreateReservationByBusiness({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      getMCustomerAndBusinessUser,
      createReservationEvent,
      contextProvider,
      gsRepo,
    });

    await createReservationByBusiness.executeImpl(input2);
  });

  describe(`addToEventDescription honors reservation option timezone`, () => {
    it(`case America/Cordoba: GMT-3`, async () => {
      const createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes> = async (dto: CreateReservationEventReq) => {
        expect(dto.addToEventDescription).toContain('-03:00'); // ReservationOptionsInDb[0].trZone = "America/Cordoba"
        return Result2.ok({
          action: Action.CREATED,
          event: mockedGEvent,
        });
      }

      const createReservationByBusiness = new CreateReservationByBusiness({
        reservationRepo,
        reservationOptionSrv,
        isBalanceEnough,
        getMCustomerAndBusinessUser,
        createReservationEvent,
        contextProvider,
        gsRepo,
      });

      await createReservationByBusiness.executeImpl(input);
    });

    it(`case Africa/Johannesburg: GMT+2`, async () => {
      const reservationOptionSrv = new ReservationOptionSrv(reservationOptionRepo);
      const mockCheckResOpt = () =>
        new Promise<Result<ReservationOption>>((resolve) => {
          const reservationOptionDto = ReservationOptionRepo.mapDb2Dto(
            ReservationOptionsInDb[14], // ReservationOptionsInDb[14].trZone = "Africa/Johannesburg"
          );
          resolve(Result.ok(ReservationOption.assemble(reservationOptionDto, sendAnalytics)));
        });
      vi.spyOn(reservationOptionSrv, 'checkResOpt').mockImplementation(
        mockCheckResOpt,
      );

      const createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes> = async (dto: CreateReservationEventReq) => {
        expect(dto.addToEventDescription).toContain('+02:00');
        return Result2.ok({
          action: Action.CREATED,
          event: mockedGEvent,
        });
      }

      const createReservationByBusiness = new CreateReservationByBusiness({
        reservationRepo,
        reservationOptionSrv,
        isBalanceEnough,
        getMCustomerAndBusinessUser,
        createReservationEvent,
        contextProvider,
        gsRepo,
      });

      await createReservationByBusiness.executeImpl(input);
    });
  });

});

describe(`fails`, () => {
  test(`when start is not a valid date-time string`, async () => {
    const response = await createReservationByBusiness.executeImpl({
      ...input,
      start: 'invalid date',
    });
    expectControllerErrorField({
      response,
      code: 400,
      type: 'CreateReservationByBusinessErrors.StartIsInvalid',
      errorField: 'start',
    });
  });

  test(`when no balance remains`, async () => {
    const isBalanceEnough = async () => {
      return false;
    }

    const createReservationByBusiness = new CreateReservationByBusiness({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      getMCustomerAndBusinessUser,
      createReservationEvent,
      contextProvider,
      gsRepo,
    });

    const response = await createReservationByBusiness.executeImpl(input);
    expectControllerError({
      response,
      code: 403,
      error: 'CreateReservationByBusinessErrors.NoBalance',
    });
  });

  const error1 = new Error1();
  const error2 = new Error2();
  test(`when ReservationOptionSrv fails`, async () => {
    const reservationOptionSrv = new ReservationOptionSrv(reservationOptionRepo);
    const mockCheckResOpt = () =>
      new Promise<Result<ReservationOption>>((resolve) => {
        resolve(Result.fail(error1));
      });
    vi.spyOn(reservationOptionSrv, 'checkResOpt').mockImplementation(
      mockCheckResOpt,
    );

    const createReservationByBusiness = new CreateReservationByBusiness({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      createReservationEvent,
      getMCustomerAndBusinessUser,
      contextProvider,
      gsRepo,
    });

    const response = await createReservationByBusiness.executeImpl(input);
    expectControllerError({
      response,
      code: error1.status,
      error: error1.type,
    });
  });

  test(`when calling GetMCustomerAndBusinessUser fails`, async () => {
    const errors = [error1, error2];
    const getMCustomerAndBusinessUser: FC2Handler<GetMCustomerAndBusinessUserReq, GetMCustomerAndBusinessUserRes> = async () => {
      return Result2.fail(errors);
    }

    const createReservationByBusiness = new CreateReservationByBusiness({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      createReservationEvent,
      getMCustomerAndBusinessUser,
      contextProvider,
      gsRepo,
    });

    const response = await createReservationByBusiness.executeImpl(input);
    expectControllerMultipleErrors({
      response,
      errors,
    });
  });

  test(`when user general settings are not found`, async () => {
    const gsRepo = new GeneralSettingsRepoFake();
    vi.spyOn(gsRepo, 'get').mockImplementation(
      async () => null
    );
    const createReservationByBusiness = new CreateReservationByBusiness({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      createReservationEvent,
      getMCustomerAndBusinessUser,
      contextProvider,
      gsRepo,
    });
    const response = await createReservationByBusiness.executeImpl(input);
    expectControllerError({
      response,
      code: 404,
      error: 'CreateReservationByBusinessErrors.UserGSnotFound',
    });
  })

  test(`when calling CreateReservationEvent fails`, async () => {
    const createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes> = async () => {
      return Result2.fail([error1]);
    }

    const createReservationByBusiness = new CreateReservationByBusiness({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      createReservationEvent,
      getMCustomerAndBusinessUser,
      contextProvider,
      gsRepo,
    });

    const response = await createReservationByBusiness.executeImpl(input);
    expectControllerError({
      response,
      code: error1.status,
      error: error1.type,
    });
  });
});
