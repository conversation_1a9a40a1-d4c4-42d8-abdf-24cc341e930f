import { beforeAll, expect, it, vi } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { Dat } from '@shared/core/Dat';
import {
  dateFormat,
  expectErrorAppSyncContaining,
  pickLng,
} from '@shared/utils/test';
import {
  rmTimestamps,
  uuidFormat,
  sendAnalyticsDummy as sendAnalytics,
} from '@shared/utils/utils';
import { Identifier } from '../../external';
import { ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import {
  ReservationOptionInDb,
  ReservationOptionRepo,
} from '../../repos/ReservationOptionRepo';
import {
  rmReservations,
} from '../../utils/test';
import { getCalendarCreds } from '@shared/infra/iac/helpers';
import {
  CalendarsInDb,
  ManuallyCreatedCustomersInDb,
  rmEvents,
  tomorrowPlus,
  Event,
  Slot,
  CalendarApi,
  CalendarApiT,
  balanceRepo,
  rmBalanceRow,
  Movement,
} from '../../utils/testExternal';
import { calendar } from '@googleapis/calendar';
import { Request } from './CreateReservationByBusinessDTOs';
import { ReservationFragment } from '../../utils/fragments';

// Add all process.env used:
const { PROJECT, STAGE } = process.env;
if (!PROJECT || !STAGE) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}

let availabilityApi: CalendarApiT, reservationApi: CalendarApiT;
beforeAll(async () => {
  const { calendar_clientEmail, calendar_clientKey } = await getCalendarCreds({
    name: PROJECT,
    stage: STAGE,
  });
  vi.stubEnv('calendar_clientEmail', calendar_clientEmail);
  vi.stubEnv('calendar_clientKey', calendar_clientKey);
  availabilityApi = new CalendarApi({
    google: calendar({ version: 'v3' }),
    sendAnalytics,
  });
  availabilityApi.setCalendarId(CalendarsInDb[0].id); // CalendarsInDb[0].userId = BusinessUsersInDb[0].id & CalendarsInDb[0].type = 'A'
  reservationApi = new CalendarApi({
    google: calendar({ version: 'v3' }),
    sendAnalytics,
  });
  reservationApi.setCalendarId(CalendarsInDb[1].id); // CalendarsInDb[1].userId = BusinessUsersInDb[0].id & CalendarsInDb[1].type = 'R'
});

const appsync = new AppSyncClient();

const query = gql`
  mutation (
    $businessId: ID!
    $start: AWSDateTime!
    $roId: ID!
    $roVersion: Int!
    $customerId: ID!
    $lng: String!
  ) {
    createReservationByBusiness(
      businessId: $businessId
      start: $start
      roId: $roId
      roVersion: $roVersion
      customerId: $customerId
      lng: $lng
    ) {
      result {
        ...ReservationFragment
      }
      time
    }
  }
  ${ReservationFragment}
`;

const initial = Dat.create({ value: tomorrowPlus(120).l.startOf('hour') }).value;
const reservationOptionDto = ReservationOptionRepo.mapDb2Dto(
  rmTimestamps(ReservationOptionsInDb[0]) as ReservationOptionInDb,  // ReservationOptionsInDb[0].userId = BusinessUsersInDb[0].id
)
const customer = ManuallyCreatedCustomersInDb[1]; // ManuallyCreatedCustomersInDb[1].createdBy = BusinessUsersInDb[0].id
const userId = reservationOptionDto.userId;  // ReservationOptionsInDb[0].userId = 00ad8920-c36d-11ee-8adf-3f66c88fcbdd
const input = {
  businessId: userId,
  start: initial.s,
  roId: reservationOptionDto.id,
  customerId: customer.id,
  roVersion: reservationOptionDto.version,
  lng: pickLng(),
};
const identifier = Identifier.create({ value: reservationOptionDto.location.identifier }, sendAnalytics).value;

it(`creates reservation event`, async () => {
  const availabilityEvent1 = Event.create({
    name: 'Availability 1',
    description: `${identifier.query}:2`,
    slot: Slot.create({
      start: initial,
      end: Dat.create({ value: initial.l.plus({ minutes: reservationOptionDto.service.duration }) })
        .value,
    }).value,
  });
  const availInserted1 = await availabilityApi.insertEvent({
    event: availabilityEvent1,
    lng: pickLng(),
  }); // Como preparación de este test necesito tener acceso a escritura del availability calendar, pero en la app de producción sólo necesito acceso de lectura
  expect(availInserted1.isSuccess).toBe(true);

  const received = await appsync.send<Request>({
    query,
    variables: input,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log('CreateReservationByBusiness.e2e 1 json', json);
  const response = json.data.createReservationByBusiness;
  if (!response) console.log('CreateReservationByBusiness.e2e 2 json', json);

  expect(response).toEqual({
    result: {
      id: expect.stringMatching(uuidFormat),
      userId,
      customerId: input.customerId,
      customerType: 'MANUALLY_CREATED',
      customerFirstName: customer.firstName,
      customerLastName: customer.lastName,
      customerPhone: null,
      customerEmail: null,
      start: Dat.create({ value: input.start }).value.s,
      eventId: expect.any(String),
      roId: expect.stringMatching(uuidFormat),
      roZone: reservationOptionDto.timeReference.zone,
      srvName: reservationOptionDto.service.name,
      srvDuration: reservationOptionDto.service.duration,
      srvDescription: reservationOptionDto.service.description,
      srvPrice: reservationOptionDto.service.price,
      locName: reservationOptionDto.location.name,
      locId: reservationOptionDto.location.identifier,
      cancelledBy: null,
      cancelledDate: null,
      cancellationReason: null,
      cancelReservationEventErrors: null,
      reminder: 'NOT_SENT',
    },
    time: expect.stringMatching(dateFormat),
  });

  // Side effect on @balance
  const balanceRows = await balanceRepo.list(userId);
  const matches = balanceRows.filter(row => {
    return row.reservationId === response.result.id.toString() &&
      row.movement === Movement.RESERVATION_BY_BUSINESS;
  });
  expect(matches).toHaveLength(1);

  await rmReservations([response.result.id]);
  await rmEvents({
    inserted: [{ value: { id: response.result.eventId } }],
    calendarApi: reservationApi,
  });
  await rmEvents({
    inserted: [availInserted1],
    calendarApi: availabilityApi,
  });
  await rmBalanceRow({userId, index: 12})
});

it(`fails when there are no spots available`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      ...input,
      start: initial.l.plus({ minutes: 1 }).toISO()!,
    },
  });

  expect(received.status).toBe(200);
  const response = await received.json();
  expectErrorAppSyncContaining({
    response,
    errorContaining: 'CreateReservationEventErrors.',
    status: 404,
  });
});
