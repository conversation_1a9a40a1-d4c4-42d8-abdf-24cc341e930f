import { DomainEvents } from '@shared/core/domain/DomainEvents';
import { ReservationByBusinessEvent } from '../../domain/events/ReservationByBusinessEvent';
import { IInvoker } from '@shared/infra/invocation/IInvoker';

export class CreateReservationByBusinessEvents {
  public static registration(invoker: IInvoker) {
    // Add all process.env used:
    const { distributeDomainEvents } = process.env;
    if (!distributeDomainEvents) {
      console.log('process.env', process.env);
      throw new Error(`Undefined env var!`);
    }

    DomainEvents.setInvoker(invoker);
    DomainEvents.register(
      `${distributeDomainEvents}`,
      ReservationByBusinessEvent.name,
    );
  }
}