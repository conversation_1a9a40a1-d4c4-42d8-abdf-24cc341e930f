import { Request, Response } from './CreateReservationByBusinessDTOs';
import {
  CreateReservationByBusinessErrors,
} from './CreateReservationByBusinessErrors';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { IReservationRepo } from '../../repos/IReservationRepo';
import {
  AppSync<PERSON>ontroller,
  FC2Handler,
  FCHandler,
} from '@shared/infra/Controllers';
import {
  CreateReservationEventReq,
  CreateReservationEventRes,
  GetMCustomerAndBusinessUserReq,
  GetMCustomerAndBusinessUserRes,
  IsBalanceEnoughReq,
  IsBalanceEnoughRes,
} from '../../external';
import {
  CustomerType,
  Reservation,
} from '../../domain/Reservation';
import { Dat } from '@shared/core/Dat';
import { formatErrors } from '@shared/core/AppError';
import { DateTime } from 'luxon';
import { IReservationOptionSrv } from '../../services/IReservationOptionSrv';
import { getLng } from '@shared/utils/utils';
import {
  ReservationByBusinessEvent,
} from '../../domain/events/ReservationByBusinessEvent';
import {
  CreateReservationByBusinessEvents,
} from './CreateReservationByBusinessEvents';
import { IGeneralSettingsRepo } from '../../repos/IGeneralSettingsRepo';
import { ContextProvider } from '@shared/context/ContextProvider';

// CreateReservationByBusiness use case is used by BusinessUsers. dto.businessId should be taken from auth data.
export class CreateReservationByBusiness extends AppSyncController<
  Request,
  Response
> {
  private readonly reservationRepo: IReservationRepo;
  private readonly reservationOptionSrv: IReservationOptionSrv;
  private readonly isBalanceEnough: FCHandler<IsBalanceEnoughReq, IsBalanceEnoughRes>;
  private readonly getMCustomerAndBusinessUser: FC2Handler<GetMCustomerAndBusinessUserReq, GetMCustomerAndBusinessUserRes>;
  private readonly createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes>;
  private readonly gsRepo: IGeneralSettingsRepo;

  public constructor(args: {
    reservationRepo: IReservationRepo;
    reservationOptionSrv: IReservationOptionSrv;
    isBalanceEnough: FCHandler<IsBalanceEnoughReq, IsBalanceEnoughRes>;
    getMCustomerAndBusinessUser: FC2Handler<GetMCustomerAndBusinessUserReq, GetMCustomerAndBusinessUserRes>;
    createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes>;
    contextProvider: ContextProvider;
    gsRepo: IGeneralSettingsRepo;
  }) {
    const {
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      getMCustomerAndBusinessUser,
      createReservationEvent,
      contextProvider,
      gsRepo,
    } = args;
    super({ contextProvider });
    this.reservationRepo = reservationRepo;
    this.reservationOptionSrv = reservationOptionSrv;
    this.isBalanceEnough = isBalanceEnough;
    this.getMCustomerAndBusinessUser = getMCustomerAndBusinessUser;
    this.createReservationEvent = createReservationEvent;
    CreateReservationByBusinessEvents.registration(contextProvider.invoker());
    this.gsRepo = gsRepo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const {
      customerId,
      businessId,
      start: _start,
      roId,
      roVersion,
      lng: _lng,
    } = dto;
    const lng = getLng(_lng);

    const startOrError = Dat.create({value: _start, lng});
    if (startOrError.isFailure)
      return formatErrors([
        new CreateReservationByBusinessErrors.StartIsInvalid({start: _start, lng}).setField('start'),
      ]);
    const start = startOrError.value;

    // Check if business has enough balance
    const isEnough = await this.isBalanceEnough({ userId: businessId, requiredBalance: 1 });
    if (!isEnough)
      return formatErrors([new CreateReservationByBusinessErrors.NoBalance({lng})]);

    const checkedOrError = await this.reservationOptionSrv.checkResOpt({
      id: roId,
      version: roVersion,
      businessId,
      lng,
    });
    if (checkedOrError.isFailure)
      return formatErrors([checkedOrError.error!]);
    const reservationOptionDto = checkedOrError.value.toDto();

    const usersOrErrors = await this.getMCustomerAndBusinessUser({
      customerId,
      businessId,
    });
    if (usersOrErrors.isFailure)
      return formatErrors(usersOrErrors.errors!);

    const users = usersOrErrors.value;
    const { business, customer } = users;

    const {fullName} = customer;

    const reservationTime = DateTime.now()
      .setZone(reservationOptionDto.timeReference.zone)
      .startOf('second')
      .toISO({suppressMilliseconds: true});

    let addToEventDescription;
    const businessLng = getLng(business.lng);
    switch (businessLng) {
      case 'es':
        addToEventDescription =
          `Reserva para ${fullName} tomada por el negocio a las ${reservationTime}`;
        break;
      case 'en':
        addToEventDescription =
          `${fullName}'s reservation taken by business at ${reservationTime}`;
        break;
    }

    const gs = await this.gsRepo.get(business.id);
    if (!gs)
      return formatErrors([new CreateReservationByBusinessErrors.UserGSnotFound({ lng })]);
    const { maxDaysAhead, minTimeBeforeService } = gs.reservationLimits;

    // Call synchronously CreateReservationEvent in module calendar
    const end = start.l.plus({
      minutes: reservationOptionDto.service.duration,
    });
    const creationOrErrors = await this.createReservationEvent({
      userId: businessId,
      identifier: reservationOptionDto.location.identifier,
      start: start.s,
      end: Dat.create({value: end}).value.s,
      name: reservationOptionDto.name,
      addToEventDescription,
      lng: businessLng,
      maxDaysAhead: maxDaysAhead.value,
      minTimeBeforeService: minTimeBeforeService.value,
    });
    if (creationOrErrors.isFailure)
      return formatErrors(creationOrErrors.errors!);

    const creation = creationOrErrors.value;
    console.log('creation', creation);

    const reservation = Reservation.create({
      userId: businessId,
      customerType: CustomerType.MANUALLY_CREATED,
      customerId,
      customerFirstName: customer.firstName,
      customerLastName: customer.lastName,
      customerPhone: null,
      customerEmail: null,
      start,
      eventId: creation.event.id,
      roId: roId,
      roZone: reservationOptionDto.timeReference.zone,
      srvName: reservationOptionDto.service.name,
      srvDuration: reservationOptionDto.service.duration,
      srvDescription: reservationOptionDto.service.description,
      srvPrice: reservationOptionDto.service.price,
      locName: reservationOptionDto.location.name,
      locId: reservationOptionDto.location.identifier,
    });

    const _dto = reservation.toDto();
    reservation.addEvent(new ReservationByBusinessEvent(_dto));

    await this.reservationRepo.create(reservation);

    return {status: Status.CREATED, result: _dto};
  }
}