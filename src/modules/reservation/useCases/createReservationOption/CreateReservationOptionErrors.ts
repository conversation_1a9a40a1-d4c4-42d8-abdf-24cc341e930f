import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { Status } from '@shared/core/Status';
import { OptionalLng, getLng, PossibleLngs } from '@shared/utils/utils';

export namespace CreateReservationOptionErrors {
  export class NameAlreadyTaken extends BaseError {
    public constructor(args: { name: string } & OptionalLng) {
      const { name, lng } = args;
      const t = trans[getLng(lng)]
      super({ message: `${t.name} "${name}" ${t.taken}`, status: Status.CONFLICT });
    }
  }
  export class MaxActiveROsReached extends BaseError {
    public constructor(args: { lng: PossibleLngs, max: number }) {
      const { lng, max } = args;
      const t = trans[getLng(lng)]
      super({ message: t.maxActiveROsReached(max), status: Status.FORBIDDEN });
    }
  }
}

const trans = {
  en: {
    name: 'Name',
    taken: 'is already taken',
    maxActiveROsReached(max: number) {
      return `Maximum number of active reservation options (${max}) reached.`;
    },
  },
  es: {
    name: 'Nombre',
    taken: 'ya está en uso',
    maxActiveROsReached(max: number) {
      return `Maximum number of active reservation options (${max}) reached.`;
    },
  },
};

patch({ CreateReservationOptionErrors });
