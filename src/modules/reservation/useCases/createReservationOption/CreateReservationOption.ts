import { Request, Response } from './CreateReservationOptionDTOs';
import { CreateReservationOptionErrors } from './CreateReservationOptionErrors';
import { ReservationOption } from '../../domain/ReservationOption';
import { IReservationOptionRepo } from '../../repos/IReservationOptionRepo';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { BaseError, formatErrors } from '@shared/core/AppError';
import { AppSyncController } from '@shared/infra/Controllers';
import { getLng } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { Name } from '../../domain/Name';
import { Service } from '../../domain/Service';
import { Location } from '../../domain/Location';
import { TimeReference } from '../../domain/TimeReference';
import { ExplanatoryNotes } from '../../domain/ExplanatoryNotes';
import { Every } from '../../domain/Every';
import { PositiveInt } from '@shared/core/PositiveInt';
import { Price } from '../../domain/Price';
import { Result } from '@shared/core/Result';
import { Description } from '../../domain/Description';
import { Identifier } from '../../external';
import {
  GetAvailabilityOfReservationOptions,
} from '../getAvailabilityOfReservationOptions/GetAvailabilityOfReservationOptions';
import { ContextProvider } from '@shared/context/ContextProvider';
import { IContextProvider } from '@shared/context/IContextProvider';

export class CreateReservationOption extends AppSyncController<Request, Response> {
  private readonly repo: IReservationOptionRepo;
  protected readonly contextProvider: ContextProvider;

  public constructor(args: {
    repo: IReservationOptionRepo;
    contextProvider: ContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
    this.contextProvider = contextProvider;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { lng: _lng, userId, ...rest } = dto;
    const lng = getLng(_lng);
    const vosOrErrors = validateReq({ ...rest, lng }, this.contextProvider.sendAnalytics);
    if (vosOrErrors.isFailure)
      return formatErrors(vosOrErrors.errors!);

    const { name, service, location, timeReference, explanatoryNotes, every } =
      vosOrErrors.value;

    const errors: BaseError[] = [];
    const existing = await this.repo.list(userId);
    if (existing) {
      if (existing.find((ro) => ro.name.equals(name))) {
        errors.push(new CreateReservationOptionErrors.NameAlreadyTaken({ name: name.value, lng }).setField('name'));
      }
      if (dto.active) {
        const currentlyActive = existing.filter((ro) => ro.active);
        const MAX = GetAvailabilityOfReservationOptions.MAX_ACTIVE_ROs_TIER2;
        if (currentlyActive.length >= MAX) {
          errors.push(new CreateReservationOptionErrors.MaxActiveROsReached({ lng, max: MAX }).setField('active'));
        }
      }
    }

    if (errors.length) {
      return formatErrors(errors);
    }

    const reservationOption = ReservationOption.create({
      ...dto,
      name,
      service,
      location,
      timeReference,
      explanatoryNotes,
      every,
    });

    await this.repo.create(reservationOption);

    return { status: Status.CREATED, result: reservationOption.toDto() };
  }
}

// Exported for use in UpdateReservationOption use case and frontend
export function validateReq(dto: Omit<Request, 'userId'>, sendAnalytics: IContextProvider['sendAnalytics']): Result2<{
  name: Name;
  service: Service;
  location: Location;
  timeReference: TimeReference;
  explanatoryNotes: ExplanatoryNotes;
  every: Every;
}> {
  const errors: BaseError[] = [];
  const lng = getLng(dto.lng);

  const nameOrError = Name.create({ value: dto.name, lng });
  if (nameOrError.isFailure)
    errors.push((nameOrError.error!).setField('name'));

  const timeReferenceOrErrors = TimeReference.create({ ...dto.timeReference, lng });
  if (timeReferenceOrErrors.isFailure)
    for (const e of timeReferenceOrErrors.errors!)
      errors.push(e.prefixField('timeReference'));

  const everyOrErrors = Every.create({ value: dto.every, lng });
  if (everyOrErrors.isFailure) {
    if (!everyOrErrors.errors) throw Error(`everyOrErrors.errors is not defined when in failure?`);
    for (let i = 0; i < everyOrErrors.errors.length; i++) {
      errors.push((everyOrErrors.errors[i]).setField(`every.${i}`));
    }
  }

  const srvDurationOrErrors = PositiveInt.create({ value: dto.service.duration, lng });
  if (srvDurationOrErrors.isFailure) {
    if (!srvDurationOrErrors.errors) throw Error(`srvDurationOrErrors.errors is not defined when in failure?`);
    for (let i = 0; i < srvDurationOrErrors.errors.length; i++) {
      errors.push((srvDurationOrErrors.errors[i]).setField(`service.duration.${i}`));
    }
  }

  const srvPriceOrError =
    dto.service.price !== null
      ? Price.create({ value: dto.service.price, lng })
      : Result.ok(null);
  if (srvPriceOrError.isFailure)
    errors.push((srvPriceOrError.error!).setField('service.price'));

  const srvNameOrError = Name.create({ value: dto.service.name, lng });
  if (srvNameOrError.isFailure)
    errors.push((srvNameOrError.error!).setField('service.name'));
  let srvDescription = null;
  if (dto.service.description !== null) {
    const srvDescriptionOrError = Description.create({ value: dto.service.description, lng });
    if (srvDescriptionOrError.isFailure)
      errors.push((srvDescriptionOrError.error!).setField('service.description'));
    else srvDescription = srvDescriptionOrError.value;
  }

  const locIdentifierOrErrors = Identifier.create({ value: dto.location.identifier, lng }, sendAnalytics);
  if (locIdentifierOrErrors.isFailure) {
    if (!locIdentifierOrErrors.errors) throw Error(`locIdentifierOrErrors.errors is not defined when in failure?`);
    for (let i = 0; i < locIdentifierOrErrors.errors.length; i++) {
      errors.push((locIdentifierOrErrors.errors[i]).setField(`location.identifier.${i}`));
    }
  }
  const locNameOrError = Name.create({ value: dto.location.name, lng });
  if (locNameOrError.isFailure)
    errors.push((locNameOrError.error!).setField('location.name'));
  let locDescription = null;
  if (dto.location.description !== null) {
    const locDescriptionOrError = Description.create({ value: dto.location.description, lng });
    if (locDescriptionOrError.isFailure)
      errors.push((locDescriptionOrError.error!).setField('location.description'));
    else locDescription = locDescriptionOrError.value;
  }

  if (errors.length) {
    return Result2.fail(errors);
  }

  const name = nameOrError.value;
  const timeReference = timeReferenceOrErrors.value;
  const every = everyOrErrors.value;

  const srvDuration = srvDurationOrErrors.value;
  const srvPrice = srvPriceOrError.value;
  const srvName = srvNameOrError.value;
  const service = Service.create({
    name: srvName,
    duration: srvDuration,
    price: srvPrice,
    description: srvDescription,
  });

  const locName = locNameOrError.value;
  const locIdentifier = locIdentifierOrErrors.value;
  const location = Location.create({
    name: locName,
    description: locDescription,
    identifier: locIdentifier,
    showInfoWhenReserving: dto.location.showInfoWhenReserving,
  });

  const explanatoryNotes = ExplanatoryNotes.create(dto.explanatoryNotes);

  return Result2.ok({
    name,
    service,
    location,
    timeReference,
    explanatoryNotes,
    every,
  });
}
