import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import {
  expectErrorAppSync,
} from '@shared/utils/test';
import {
  getLng,
  uuidFormat,
  sendAnalyticsDummy as sendAnalytics,
} from '@shared/utils/utils';
import {
  removeReservationOption,
} from '../../utils/test';
import { ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { ReservationOptionRepo } from '../../repos/ReservationOptionRepo';
import { createReservationOption } from '../../utils/testExternal';
import { Request } from './CreateReservationOptionDTOs';
import { createReservationOptionQuery } from '../../utils/fragments';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');

const appsync = new AppSyncClient();
const repo = new ReservationOptionRepo({
  ReservationOptionModel: models.ReservationOption,
  sendAnalytics,
});

const input = {
  ...createReservationOption().dto,
  lng: getLng(),
};

it(`creates reservation option`, async () => {
  const received = await appsync.send<Request>({
    query: createReservationOptionQuery,
    variables: input,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log(json);
  const response = json.data.createReservationOption;
  if (!response) console.log('json', json);

  const id = response.result.id;
  const saved = await repo.get({ userId: input.userId, id });

  if (!saved) throw Error('ReservationOption not saved');
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { lng, ...rest } = input;
  expect(saved.toDto()).toEqual({
    ...rest,
    id: expect.stringMatching(uuidFormat),
    created_at: expect.any(Date),
    updated_at: expect.any(Date),
    deleted_at: null,
  });

  await removeReservationOption(id);
});

it(`fails when ReservationOption.name is already taken`, async () => {
  const received = await appsync.send<Request>({
    query: createReservationOptionQuery,
    variables: {
      ...input,
      userId: ReservationOptionsInDb[0].userId,
      name: ReservationOptionsInDb[2].name,
    },
  });

  expect(received.status).toBe(200);
  const response = await received.json();

  expectErrorAppSync({
    response,
    error: 'CreateReservationOptionErrors.NameAlreadyTaken',
    query: 'createReservationOption',
    status: 409,
    field: 'name',
  });
});
