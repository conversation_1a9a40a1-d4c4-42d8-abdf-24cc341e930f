import { expect, test, vi, describe, beforeEach, it } from 'vitest';
import { CreateReservationOption, validateReq } from './CreateReservationOption';
import { ReservationOptionRepoFake } from '../../repos/ReservationOptionRepoFake';
import {
  uuidFormat,
  sendAnalyticsDummy as sendAnalytics,
} from '@shared/utils/utils';
import {
  expectControllerErrorContaining,
  expectControllerErrorField,
  expectErrorField,
  expectErrorFieldContaining,
  invokerDummy as invoker,
} from '@shared/utils/test';
import { ContextProvider } from '@shared/context/ContextProvider';
import {
  createReservationOption,
  getCreateResOptReq,
} from '../../utils/testExternal';
import { IReservationOptionRepo } from '../../repos/IReservationOptionRepo';
import { ReservationOption } from '../../domain/ReservationOption';
import { Name } from '../../domain/Name';
import {
  GetAvailabilityOfReservationOptions,
} from '../getAvailabilityOfReservationOptions/GetAvailabilityOfReservationOptions';

let repo: IReservationOptionRepo,
  controller: CreateReservationOption;
beforeEach(() => {
  repo = new ReservationOptionRepoFake();
  const contextProvider = new ContextProvider({ invoker });
  controller = new CreateReservationOption({ repo, contextProvider });
});

test(`Reservation Option creation`, async () => {
  const input = getCreateResOptReq();
  const result = await controller.executeImpl(input);

  expect(result).toMatchObject({
    status: 201,
    result: {
      ...input,
      id: expect.stringMatching(uuidFormat),
      service: input.service,
      location: input.location,
    },
  });
});

describe(`fails when`, () => {
  test(`validateReq errors`, async () => {
    const input = {
      ...getCreateResOptReq(),
      // Force name error
      name: '',
    };


    const contextProvider = new ContextProvider({ invoker });
    const controller = new CreateReservationOption({ repo, contextProvider });
    const response = await controller.executeImpl(input);

    expectControllerErrorContaining({
      response,
      errorContaining: 'Name',
      code: 400,
    });
  });

  test(`name is already taken`, async () => {
    const repo = new ReservationOptionRepoFake();

    const req = getCreateResOptReq();
    const mockList = () =>
      new Promise<[ReservationOption, ...ReservationOption[]]>((resolve) => {
        const ro = createReservationOption().reservationOption;
        ro.update({
          ...ro.props,
          name: Name.create({ value: req.name }).value,
        });
        resolve([ro]);
      });

    vi.spyOn(repo, 'list').mockImplementation(mockList);

    const contextProvider = new ContextProvider({ invoker });
    const controller = new CreateReservationOption({ repo, contextProvider });

    const response = await controller.executeImpl(req);

    expectControllerErrorField({
      response,
      errorField: 'name',
      type: 'CreateReservationOptionErrors.NameAlreadyTaken',
      code: 409,
    });
  });

  test(`max active reservation options are reached`, async () => {
    const repo = new ReservationOptionRepoFake();

    const mockList = () =>
      new Promise<[ReservationOption, ...ReservationOption[]]>((resolve) => {
        const ros: ReservationOption[] = [];
        while (ros.filter((ro) => ro.props.active).length < GetAvailabilityOfReservationOptions.MAX_ACTIVE_ROs_TIER2) {
          ros.push(createReservationOption().reservationOption);
        }
        resolve(ros as [ReservationOption, ...ReservationOption[]]);
      });

    vi.spyOn(repo, 'list').mockImplementation(mockList);

    const contextProvider = new ContextProvider({ invoker });
    const controller = new CreateReservationOption({ repo, contextProvider });

    const response = await controller.executeImpl({
      ...getCreateResOptReq(),
      active: true,
    });

    expectControllerErrorField({
      response,
      errorField: 'active',
      type: 'CreateReservationOptionErrors.MaxActiveROsReached',
      code: 403,
    });
  });
});

describe('validateReq', () => {

  it('should return a Result with the components if the request is valid', () => {
    const input = getCreateResOptReq();
    const result = validateReq(input, sendAnalytics);

    expect(result.isSuccess).toBe(true);
    const components = result.value;
    expect(components.name.value).toEqual(input.name);
    expect(components.every.value).toEqual(input.every);
    expect(components.service.toDto()).toMatchObject(input.service);
    expect(components.location.toDto()).toMatchObject(input.location);
    expect(components.timeReference.toDto()).toMatchObject(input.timeReference);
    expect(components.explanatoryNotes.toDto()).toMatchObject(
      input.explanatoryNotes,
    );
  });
  describe(`fails when`, () => {
    test(`name is invalid`, async () => {
      const result = validateReq({
        ...getCreateResOptReq(),
        name: '',
      }, sendAnalytics);

      expectErrorField({
        result,
        errorField: 'name',
        code: 400,
      });
    });

    test(`every is invalid`, async () => {
      const result = validateReq({
        ...getCreateResOptReq(),
        every: 7,
      }, sendAnalytics);

      expect(result.isFailure).toBe(true);
      if (!result.errors) throw Error(`result.errors is not defined when in failure?`);
      for (const error of result.errors) {
        expect(error).toMatchObject({
          field: expect.stringContaining('every.'),
          type: expect.any(String),
          message: expect.any(String),
          status: 400,
        });
      }
    });

    test(`time reference is invalid`, async () => {
      const validData = getCreateResOptReq();
      const result = validateReq({
        ...validData,
        timeReference: {
          ...validData.timeReference,
          hour: 24,
        },
      }, sendAnalytics);

      expectErrorFieldContaining({
        result,
        errorFieldContaining: 'timeReference',
        code: 400,
      });
    });

    test(`Service.duration is invalid`, async () => {
      const validData = getCreateResOptReq();
      const result = validateReq({
        ...validData,
        service: {
          ...validData.service,
          duration: 0,
        },
      }, sendAnalytics);

      expectErrorFieldContaining({
        result,
        errorFieldContaining: 'service.duration',
        code: 400,
      });
    });

    test(`Service.price is invalid`, async () => {
      const validData = getCreateResOptReq();
      const result = validateReq({
        ...validData,
        service: {
          ...validData.service,
          price: -1,
        },
      }, sendAnalytics);

      expectErrorField({
        result,
        errorField: 'service.price',
        code: 400,
      });
    });

    test(`Location.identifier is invalid`, async () => {
      const validData = getCreateResOptReq();
      const reponse = validateReq({
        ...validData,
        location: {
          ...validData.location,
          identifier: '',
        },
      }, sendAnalytics);

      expectErrorFieldContaining({
        result: reponse,
        errorFieldContaining: 'location.identifier',
        code: 400,
      });
    });

    test(`Service.name is invalid`, async () => {
      const validData = getCreateResOptReq();
      const result = validateReq({
        ...validData,
        service: {
          ...validData.service,
          name: '',
        },
      }, sendAnalytics);

      expectErrorField({
        result,
        errorField: 'service.name',
        code: 400,
      });
    });

    test(`Service.description is invalid`, async () => {
      const validData = getCreateResOptReq();
      const result = validateReq({
        ...validData,
        service: {
          ...validData.service,
          description: '',
        },
      }, sendAnalytics);

      expectErrorField({
        result,
        errorField: 'service.description',
        code: 400,
      });
    });
  });

  it('returns multiple errors', () => {
    const validData = getCreateResOptReq();
    const result = validateReq({
      ...validData,
      name: '',
      timeReference: {
        ...validData.timeReference,
        hour: 24,
      },
      service: {
        ...validData.service,
        price: -1,
        name: '',
        description: '',
      },
    }, sendAnalytics);

    expect(result.errors?.length).toBe(5);
  });
});
