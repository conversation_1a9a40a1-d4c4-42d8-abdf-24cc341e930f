import { BadRequest } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { getLng, OptionalLng } from '@shared/utils/utils';
import {
  CancelReservationByBusinessErrors,
} from '../cancelReservationByBusiness/CancelReservationByBusinessErrors';

export namespace SetReminderErrors {
  export class ReservationNotFound extends CancelReservationByBusinessErrors.ReservationNotFound {
    public constructor(args: { id: string } & OptionalLng) {
      super(args);
    }
  }

  export class InvalidReminderStatus extends BadRequest {
    public constructor(args: { invalid: string } & OptionalLng) {
      const { invalid, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.invalidReminderStatus(invalid),
      });
    }
  }
}

const trans = {
  en: {
    invalidReminderStatus(status: string) {
      return `Invalid reminder status: ${status}`;
    },
  },
  es: {
    invalidReminderStatus(status: string) {
      return `Estado de recordatorio inválido: ${status}`;
    },
  },
};

patch({ SetReminderErrors });
