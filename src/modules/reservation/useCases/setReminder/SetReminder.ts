import { Request, Response } from './SetReminderDTOs';
import { SetReminderErrors } from './SetReminderErrors';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { IReservationRepo } from '../../repos/IReservationRepo';
import { formatErrors } from '@shared/core/AppError';
import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';
import { getLng } from '@shared/utils/utils';
import { Reservation } from '../../domain/Reservation';

export class SetReminder extends AppSyncController<
  Request,
  Response
> {
  private readonly repo: IReservationRepo;
  public constructor(args: {
    repo: IReservationRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const {
      businessId,
      reservationId,
      newStatus: _newStatus,
      lng: _lng,
    } = dto;

    const lng = getLng(_lng);

    const newStatus = Reservation.isReminderStatus(_newStatus);
    if (!newStatus)
      return formatErrors([
        new SetReminderErrors.InvalidReminderStatus({ invalid: _newStatus, lng }).setField('newStatus'),
      ]);

    const reservation = await this.repo.get({
      id: reservationId,
      userId: businessId,
    });
    if (!reservation)
      return formatErrors([
        new SetReminderErrors.ReservationNotFound({ id: reservationId, lng }),
      ]);

    reservation.setReminder(newStatus);

    const updated = await this.repo.update(reservation);

    return {
      status: Status.OK,
      result: updated,
    };
  }
}