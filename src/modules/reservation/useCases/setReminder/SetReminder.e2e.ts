import { expect, it, test, describe } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { expectErrorAppSync, nonExistingId, pickLng } from '@shared/utils/test';
import { ReminderStatus } from '../../domain/Reservation';
import { Request } from './SetReminderDTOs';
import { ReservationsInDb } from '@shared/infra/database/sequelize/migrations/Reservations.json';
import { ReservationWithTimestampsFragment } from '../../utils/fragments';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const appsync = new AppSyncClient();

const query = gql`
  mutation ($reservationId: ID!, $businessId: ID!, $newStatus: String!, $lng: String!) {
    setReminder(reservationId: $reservationId, businessId: $businessId, newStatus: $newStatus, lng: $lng) {
      result {
        ...ReservationWithTimestampsFragment
      }
      time
    }
  }
  ${ReservationWithTimestampsFragment}
`;

// We'll need a valid reservation ID and business ID for testing
// For E2E tests, we should use existing data in the database
// This is typically defined in a JSON file like ReservationsInDb.json
// For now, we'll use placeholder values that should be replaced with actual values
const variables = {
  reservationId: ReservationsInDb[0].id,
  businessId: ReservationsInDb[0].userId,
  newStatus: chance.pickone([ReminderStatus.NOT_SENT, ReminderStatus.SENT, ReminderStatus.CONFIRMED]),
  lng: pickLng(),
};

it(`updates reservation reminder status`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.setReminder;
  if (!response) console.log('SetReminder.e2e.ts json', json);
  
  expect(response.result.reminder).toBe(variables.newStatus);
  expect(response.result.id).toBe(variables.reservationId);
});

describe(`fails when`, () => {
  test(`reminder status is invalid`, async () => {
    const invalidStatus = 'INVALID_STATUS';

    const received = await appsync.send<Request>({
      query,
      variables: {
        ...variables,
        newStatus: invalidStatus,
      },
    });

    expect(received.status).toBe(200);
    expectErrorAppSync({
      response: await received.json(),
      error: 'SetReminderErrors.InvalidReminderStatus',
      query: 'setReminder',
      status: 400,
      field: 'newStatus',
    });
  });

  test(`reservation isn't found`, async () => {

    const received = await appsync.send<Request>({
      query,
      variables: {
        ...variables,
        reservationId: nonExistingId,
      },
    });

    expect(received.status).toBe(200);
    expectErrorAppSync({
      response: await received.json(),
      error: 'SetReminderErrors.ReservationNotFound',
      query: 'setReminder',
      status: 404,
    });
  });
});
