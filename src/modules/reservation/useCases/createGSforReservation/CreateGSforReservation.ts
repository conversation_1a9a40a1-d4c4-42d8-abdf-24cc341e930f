import { AsynchronousController } from '@shared/infra/Controllers';
import { Request } from './CreateGSforReservationDTOs';
import { IGeneralSettingsRepo } from '../../repos/IGeneralSettingsRepo';
import { GeneralSettings } from '../../domain/GeneralSettings';
import { getLng } from '@shared/utils/utils';
import { IContextProvider } from '@shared/context/IContextProvider';

export class CreateGSforReservation extends AsynchronousController<Request> {
  private repo: IGeneralSettingsRepo;

  public constructor(args: {
    repo: IGeneralSettingsRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(event: Request) {
    await this.repo.create(GeneralSettings.createDefault({
      id: event.aggregateId,
      lng: getLng(event.user.lng),
    }));
  }
}
