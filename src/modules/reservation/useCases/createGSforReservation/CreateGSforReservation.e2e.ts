import { expect, test } from 'vitest';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Request } from './CreateGSforReservationDTOs';
import { DomainEventTypes } from '@shared/events/DomainEventTypes';
import { createBusinessUser } from '../../utils/testExternal';
import { GeneralSettingsRepo } from '../../repos/GeneralSettingsRepo';
import { rmGS } from '../../utils/test';
import { Lambda } from '@aws-sdk/client-lambda';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const { createGSforReservation } = process.env;
if (!createGSforReservation) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}

const lambdaInvoker = new LambdaInvoker(new Lambda({}));
const repo = new GeneralSettingsRepo(models.GeneralSettingsForReservation);

const { BusinessUserCreatedEvent } = DomainEventTypes;

test(`initializes data for new user`, async () => {
  const id = chance.guid({ version: 4 });
  const request: Request = {
    dateTimeOccurred: '2024-02-13T23:42Z',
    type: BusinessUserCreatedEvent,
    aggregateId: id,
    user: createBusinessUser().dto,
  };

  await lambdaInvoker.invokeSync<Request>(
    request,
    createGSforReservation,
  );

  const saved = await repo.get(id);
  expect(saved).not.toBeNull();

  await rmGS(id);
});
