import { CreateGSforReservation } from './CreateGSforReservation';
import { GeneralSettingsRepo } from '../../repos/GeneralSettingsRepo';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });

const repo = new GeneralSettingsRepo(models.GeneralSettingsForReservation);
const controller = new CreateGSforReservation({
  repo,
  contextProvider,
});

export const handler = controller.execute.bind(controller);
