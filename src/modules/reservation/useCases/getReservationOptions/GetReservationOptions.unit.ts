import { expect, beforeEach, it } from 'vitest';
import { GetReservationOptions } from './GetReservationOptions';
import { IReservationOptionRepo } from '../../repos/IReservationOptionRepo';
import { ReservationOptionRepoFake } from '../../repos/ReservationOptionRepoFake';
import { ReservationOptionDto } from '../../domain/ReservationOption';
import { resOpt1expected, resOpt3expected, resOpt4expected } from './test';
import { ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { ContextProvider } from '@shared/context/ContextProvider';
import { invokerDummy as invoker } from '@shared/utils/test';

let reservationOptionRepo: IReservationOptionRepo,
  getReservationOptions: GetReservationOptions;
beforeEach(() => {
  reservationOptionRepo = new ReservationOptionRepoFake();
  const contextProvider = new ContextProvider({ invoker });
  getReservationOptions = new GetReservationOptions({
    repo: reservationOptionRepo,
    contextProvider,
  });
});

it(`succeeds`, async () => {
  const response = await getReservationOptions.executeImpl({
    userId: ReservationOptionsInDb[0].userId,
  });

  expect(response.status).toBe(200);

  expect((response.result as ReservationOptionDto[]).length).toBe(3);

  expect(response.result).toEqual(
    expect.arrayContaining([
      expect.objectContaining(resOpt1expected),
      expect.objectContaining(resOpt3expected),
      expect.objectContaining(resOpt4expected),
    ]),
  );
});

// Return of null when no reservation options are found are tested in e2e
