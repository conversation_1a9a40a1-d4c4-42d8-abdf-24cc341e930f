import { Request, Response } from './GetReservationOptionsDTOs';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { IReservationOptionRepo } from '../../repos/IReservationOptionRepo';
import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';

export class GetReservationOptions extends AppSyncController<Request, Response> {
  private readonly repo: IReservationOptionRepo;
  public constructor(args: {
    repo: IReservationOptionRepo;
    contextProvider: IContextProvider;
  }) {
    const { contextProvider, repo } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const reservationOptions = await this.repo.list(dto.userId);

    return {
      status: Status.OK,
      result: !reservationOptions
        ? null
        : reservationOptions.map((res) => res.toDto()),
    };
  }
}
