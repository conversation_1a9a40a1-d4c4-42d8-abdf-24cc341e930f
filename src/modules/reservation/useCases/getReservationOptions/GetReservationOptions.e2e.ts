import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { resOpt1expected, resOpt3expected, resOpt4expected } from './test';
import { nonExistingId } from '@shared/utils/test';
import { Request } from './GetReservationOptionsDTOs';
import { timestamps } from '../../utils/test';
import { ReservationOptionWithTimestampsFragment } from '../../utils/fragments';

const appsync = new AppSyncClient();

const query = gql`
  query ($userId: ID!) {
    getReservationOptions(userId: $userId) {
      result {
        ...ReservationOptionWithTimestampsFragment
      }
      time
    }
  }
  ${ReservationOptionWithTimestampsFragment}
`;

it(`gets reservation options`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: { userId: ReservationOptionsInDb[0].userId },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.getReservationOptions;
  if (!response) console.log('json', json);

  expect(response.result.length).toBe(3);
  expect(response.result).toEqual([
    {
      ...resOpt1expected,
      ...timestamps,
    },
    {
      ...resOpt3expected,
      ...timestamps,
    },
    {
      ...resOpt4expected,
      ...timestamps,
    },
  ]);
});

it(`gets null when no reservation options are found`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      userId: nonExistingId,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.getReservationOptions;

  expect(response.result).toBeNull();
});
