import { ReservationOptionsInDb as _ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { ReservationOptionDto } from '../../domain/ReservationOption';
import { ReservationOptionInDb } from '../../repos/ReservationOptionRepo';

const ReservationOptionsInDb: ReservationOptionInDb[] = _ReservationOptionsInDb;

const resOpt1 = ReservationOptionsInDb[0];
const resOpt2 = ReservationOptionsInDb[1];
const resOpt3 = ReservationOptionsInDb[2];
const resOpt4 = ReservationOptionsInDb[3];
const resOpt12 = ReservationOptionsInDb[12];

const {
  srvName: srvName1,
  srvDescription: srvDescription1,
  srvDuration: srvDuration1,
  srvPrice: srvPrice1,

  locName: locName1,
  locDescription: locDescription1,
  locIdentifier: locIdentifier1,
  locShowInfoWhenReserving: locShowInfoWhenReserving1,

  trHour: hour1,
  trMinute: minute1,
  trZone: zone1,

  noteWhile: while1,
  noteAfter: after1,
  ...resOpt1clean
} = resOpt1;

const {
  srvName: srvName2,
  srvDescription: srvDescription2,
  srvDuration: srvDuration2,
  srvPrice: srvPrice2,

  locName: locName2,
  locDescription: locDescription2,
  locIdentifier: locIdentifier2,
  locShowInfoWhenReserving: locShowInfoWhenReserving2,

  trHour: hour2,
  trMinute: minute2,
  trZone: zone2,

  noteWhile: while2,
  noteAfter: after2,
  ...resOpt2clean
} = resOpt2;

const {
  srvName: srvName3,
  srvDescription: srvDescription3,
  srvDuration: srvDuration3,
  srvPrice: srvPrice3,

  locName: locName3,
  locDescription: locDescription3,
  locIdentifier: locIdentifier3,
  locShowInfoWhenReserving: locShowInfoWhenReserving3,

  trHour: hour3,
  trMinute: minute3,
  trZone: zone3,

  noteWhile: noteWhile3,
  noteAfter: noteAfter3,
  ...resOpt3clean
} = resOpt3;

const {
  srvName: srvName4,
  srvDescription: srvDescription4,
  srvDuration: srvDuration4,
  srvPrice: srvPrice4,

  locName: locName4,
  locDescription: locDescription4,
  locIdentifier: locIdentifier4,
  locShowInfoWhenReserving: locShowInfoWhenReserving4,

  trHour: hour4,
  trMinute: minute4,
  trZone: zone4,

  noteWhile: noteWhile4,
  noteAfter: noteAfter4,
  ...resOpt4clean
} = resOpt4;
const {
  srvName: srvName12,
  srvDescription: srvDescription12,
  srvDuration: srvDuration12,
  srvPrice: srvPrice12,

  locName: locName12,
  locDescription: locDescription12,
  locIdentifier: locIdentifier12,
  locShowInfoWhenReserving: locShowInfoWhenReserving12,

  trHour: hour12,
  trMinute: minute12,
  trZone: zone12,

  noteWhile: while12,
  noteAfter: after12,
  ...resOpt12clean
} = resOpt12;

export const resOpt1expected: ReservationOptionDto = {
  ...resOpt1clean,
  service: {
    name: srvName1,
    description: srvDescription1,
    duration: srvDuration1,
    price: srvPrice1,
  },
  location: {
    name: locName1,
    description: locDescription1,
    identifier: locIdentifier1,
    showInfoWhenReserving: locShowInfoWhenReserving1,
  },
  timeReference: {
    hour: hour1,
    minute: minute1,
    zone: zone1,
  },
  explanatoryNotes: {
    while: while1,
    after: after1,
  },
};
export const resOpt2expected: ReservationOptionDto = {
  ...resOpt2clean,
  service: {
    name: srvName2,
    description: srvDescription2,
    duration: srvDuration2,
    price: srvPrice2,
  },
  location: {
    name: locName2,
    description: locDescription2,
    identifier: locIdentifier2,
    showInfoWhenReserving: locShowInfoWhenReserving2,
  },
  timeReference: {
    hour: hour2,
    minute: minute2,
    zone: zone2,
  },
  explanatoryNotes: {
    while: while2,
    after: after2,
  },
};
export const resOpt3expected: ReservationOptionDto = {
  ...resOpt3clean,
  service: {
    name: srvName3,
    description: srvDescription3,
    duration: srvDuration3,
    price: srvPrice3,
  },
  location: {
    name: locName3,
    description: locDescription3,
    identifier: locIdentifier3,
    showInfoWhenReserving: locShowInfoWhenReserving3,
  },
  timeReference: {
    hour: hour3,
    minute: minute3,
    zone: zone3,
  },
  explanatoryNotes: {
    while: noteWhile3,
    after: noteAfter3,
  },
};
export const resOpt4expected: ReservationOptionDto = {
  ...resOpt4clean,
  service: {
    name: srvName4,
    description: srvDescription4,
    duration: srvDuration4,
    price: srvPrice4,
  },
  location: {
    name: locName4,
    description: locDescription4,
    identifier: locIdentifier4,
    showInfoWhenReserving: locShowInfoWhenReserving4,
  },
  timeReference: {
    hour: hour4,
    minute: minute4,
    zone: zone4,
  },
  explanatoryNotes: {
    while: noteWhile4,
    after: noteAfter4,
  },
};
export const resOpt12expected: ReservationOptionDto = {
  ...resOpt12clean,
  service: {
    name: srvName12,
    description: srvDescription12,
    duration: srvDuration12,
    price: srvPrice12,
  },
  location: {
    name: locName12,
    description: locDescription12,
    identifier: locIdentifier12,
    showInfoWhenReserving: locShowInfoWhenReserving12,
  },
  timeReference: {
    hour: hour12,
    minute: minute12,
    zone: zone12,
  },
  explanatoryNotes: {
    while: while12,
    after: after12,
  },
};
