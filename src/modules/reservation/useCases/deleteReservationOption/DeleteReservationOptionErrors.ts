import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { StatusError } from '@shared/core/Status';

export namespace DeleteReservationOptionErrors {
  export class ReservationOptionNotFound extends BaseError {
    public constructor(args: { id: string; userId: string }) {
      const { id, userId } = args;
      super({
        message: `No reservation option found with id ${id} for user ${userId}`,
        status: StatusError.NOT_FOUND,
      });
    }
  }
}

patch({ DeleteReservationOptionErrors });
