import { Request, Response } from './DeleteReservationOptionDTOs';
import { IReservationOptionRepo } from '../../repos/IReservationOptionRepo';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { formatErrors } from '@shared/core/AppError';
import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';
import { DeleteReservationOptionErrors } from './DeleteReservationOptionErrors';

export class DeleteReservationOption extends AppSyncController<Request, Response> {
  private readonly repo: IReservationOptionRepo;
  public constructor(args: {
    repo: IReservationOptionRepo;
    contextProvider: IContextProvider;
  }) {
    super({ contextProvider: args.contextProvider });
    this.repo = args.repo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { id, userId } = dto;
    const resOpt = await this.repo.get({ userId, id });
    if (!resOpt)
      return formatErrors([
        new DeleteReservationOptionErrors.ReservationOptionNotFound({
          userId,
          id,
        }),
      ]);

    await this.repo.delete(resOpt);

    return {
      result: true,
      status: Status.OK,
    };
  }
}
