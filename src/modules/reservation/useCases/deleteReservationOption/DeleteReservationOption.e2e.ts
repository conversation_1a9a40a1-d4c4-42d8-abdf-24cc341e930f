import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import {
  getReservationOptionIncludingDeleted,
  removeReservationOption,
} from '../../utils/test';
import { dateFormat } from '@shared/utils/test';
import { ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { createReservationOption } from '../../utils/testExternal';
import { Request } from './DeleteReservationOptionDTOs';
import { Request as CreateReservationOptionRequest } from './../createReservationOption/CreateReservationOptionDTOs';
import { getLng } from '@shared/utils/utils';
import { createReservationOptionQuery } from '../../utils/fragments';

export const query = gql`
  mutation ($userId: ID!, $id: ID!) {
    deleteReservationOption(userId: $userId, id: $id) {
      result
      time
    }
  }
`;

it(`deletes a reservation option`, async () => {
  const appsync = new AppSyncClient();
  // First create a new reservation option
  let received = await appsync.send<CreateReservationOptionRequest>({
    query: createReservationOptionQuery,
    variables: {
      ...createReservationOption().dto,
      lng: getLng(),
    },
  });

  expect(received.status).toBe(200);
  let json = await received.json();
  const created = json.data.createReservationOption.result;
  if (!created) console.log('DeleteReservationOption.e2e.ts created:', json);

  // Now delete the location
  received = await appsync.send<Request>({
    query,
    variables: {
      userId: created.userId,
      id: created.id,
    },
  });

  expect(received.status).toBe(200);
  json = await received.json();
  const deleted = json.data.deleteReservationOption;
  expect(deleted).toEqual({
    result: true,
    time: expect.stringMatching(dateFormat),
  });

  const got = await getReservationOptionIncludingDeleted({
    id: created.id,
    userId: created.userId,
  });

  if (!got)
    throw Error(
      `DeleteReservationOption.e2e.ts: deleted location should exist because of paranoid option.`,
    );

  // @ts-expect-error
  expect(got.props.deleted_at).toEqual(expect.any(Date));

  await removeReservationOption(created.id);
});

it(`errors if reservation option doesn't exist`, async () => {
  const appsync = new AppSyncClient();

  const received = await appsync.send<Request>({
    query,
    variables: {
      userId: ReservationOptionsInDb[0].userId,
      id: ReservationOptionsInDb[1].id,
    },
  });

  expect(received.status).toBe(200);
  expect(await received.json()).toMatchObject({
    data: null,
    errors: [
      {
        errorType: 'DeleteReservationOptionErrors.ReservationOptionNotFound',
        message: expect.any(String),
        errorInfo: {
          time: expect.stringMatching(dateFormat),
          errors: [
            {
              message: expect.any(String),
              status: 404,
              type: 'DeleteReservationOptionErrors.ReservationOptionNotFound',
            },
          ],
        },
      },
    ],
  });
});
