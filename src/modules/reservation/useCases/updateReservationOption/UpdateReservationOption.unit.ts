import { expect, test, vi, describe, it } from 'vitest';
import { ContextProvider } from '@shared/context/ContextProvider';
import { UpdateReservationOption } from './UpdateReservationOption';
import { ReservationOptionRepoFake } from '../../repos/ReservationOptionRepoFake';
import {
  dateFormat,
  expectControllerError,
  expectControllerErrorContaining,
  expectControllerErrorField,
  invokerDummy as invoker,
  nonExistingId,
  pickLng,
} from '@shared/utils/test';
import {
  createReservationOption,
  getCreateResOptReq,
} from '../../utils/testExternal';
import {
  ReservationOptionsInDb,
} from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { getLng } from '@shared/utils/utils';
import { ReservationOption } from '../../domain/ReservationOption';
import {
  GetAvailabilityOfReservationOptions,
} from '../getAvailabilityOfReservationOptions/GetAvailabilityOfReservationOptions';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const repo = new ReservationOptionRepoFake();
const contextProvider = new ContextProvider({ invoker });
const controller = new UpdateReservationOption({repo, contextProvider });

describe('success', () => {
  test(`Reservation Option update`, async () => {
    // First create a new Reservation Option
    const {reservationOption: created} = createReservationOption();
    await repo.create(created);

    // Then update it
    const update = {
      ...created.toDto(),
      userId: created.userId,
      id: created.id.toString(),
      lng: getLng(),
    };
    const result = await controller.executeImpl(update);

    expect(result.status).toBe(200);
    const saved = await repo.get({userId: update.userId, id: update.id});
    expect(saved?.toDto()).toMatchObject(update);
  });

  it(`doesn't fail if max active reservation options is reached but there is no change in the quantity of active reservation options`, async () => {
    const repo = new ReservationOptionRepoFake();
    const userId = chance.guid();
    const ros: ReservationOption[] = [];
    let activeRO: ReservationOption;
    while (
      // create new reservation options until the max active reservation options is reached
      ros.filter((ro) => ro.active).length < GetAvailabilityOfReservationOptions.MAX_ACTIVE_ROs_TIER2
    ) {
      const ro = createReservationOption(userId).reservationOption;
      ros.push(ro);
      if (ro.active) activeRO = ro;
    }

    const mockList = () =>
      new Promise<[ReservationOption, ...ReservationOption[]]>((resolve) => {
        resolve(ros as [ReservationOption, ...ReservationOption[]]);
      });

    vi.spyOn(repo, 'list').mockImplementation(mockList);
    const contextProvider = new ContextProvider({ invoker });
    const controller = new UpdateReservationOption({repo, contextProvider });

    const response = await controller.executeImpl({
      ...activeRO!.toDto(),
      lng: pickLng(),
      active: true,
    });

    expect(response).toEqual({
      status: 200,
      result: {
        ...activeRO!.toDto(),
        updated_at: expect.stringMatching(dateFormat),
      },
    })
  });
});

describe(`fails when`, () => {
  test(`validateReq errors`, async () => {
    const input = {
      ...getCreateResOptReq(),
      id: ReservationOptionsInDb[0].id,
      // Force name error
      name: '',
    };

    const contextProvider = new ContextProvider({ invoker });
    const controller = new UpdateReservationOption({repo, contextProvider });

    const response = await controller.executeImpl(input);

    expectControllerErrorContaining({
      response,
      errorContaining: 'Name',
      code: 400,
    });
  });

  test(`user doesn't have any reservation options`, async () => {

    const response = await controller.executeImpl({
      ...getCreateResOptReq(),
      id: chance.guid(),
      userId: nonExistingId,
    });

    expectControllerError({
      response,
      error: 'UpdateReservationOptionErrors.NoROsFound',
      code: 404,
    });
  });

  test(`Reservation option isn't found`, async () => {
    const response = await controller.executeImpl({
      ...getCreateResOptReq(),
      userId: ReservationOptionsInDb[0].userId,
      id: nonExistingId,
    });

    expectControllerError({
      response,
      error: 'UpdateReservationOptionErrors.NotFound',
      code: 404,
    });
  });

  test(`Reservation option name is already taken`, async () => {
    const response = await controller.executeImpl({
      ...getCreateResOptReq(),
      userId: ReservationOptionsInDb[0].userId,
      id: ReservationOptionsInDb[0].id,
      name: ReservationOptionsInDb[2].name, // ReservationOptionsInDb[2].userId === ReservationOptionsInDb[0].userId
    });

    expectControllerErrorField({
      response,
      errorField: 'name',
      code: 409,
      type: 'UpdateReservationOptionErrors.NameAlreadyTaken',
    });
  });

  test(`max active reservation options is reached`, async () => {
    const repo = new ReservationOptionRepoFake();
    const userId = chance.guid();
    const ros: ReservationOption[] = [];
    let inactiveRO: ReservationOption;
    while (
      // create new reservation options until the max active reservation options is reached
    ros.filter((ro) => ro.active).length < GetAvailabilityOfReservationOptions.MAX_ACTIVE_ROs_TIER2 ||
    // at least create one inactive reservation option
    !ros.some((ro) => !ro.active)
      ) {
      const ro = createReservationOption(userId).reservationOption;
      ros.push(ro);
      if (!ro.active) inactiveRO = ro;
    }

    const mockList = () =>
      new Promise<[ReservationOption, ...ReservationOption[]]>((resolve) => {
        resolve(ros as [ReservationOption, ...ReservationOption[]]);
      });

    vi.spyOn(repo, 'list').mockImplementation(mockList);
    const contextProvider = new ContextProvider({ invoker });
    const controller = new UpdateReservationOption({repo, contextProvider });

    const response = await controller.executeImpl({
      ...inactiveRO!.toDto(),
      lng: pickLng(),
      active: true,
    });

    expectControllerErrorField({
      response,
      errorField: 'active',
      code: 403,
      type: 'UpdateReservationOptionErrors.MaxActiveROsReached',
    });
  });
});
