import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { Status } from '@shared/core/Status';
import { getLng, OptionalLng, PossibleLngs } from '@shared/utils/utils';

export namespace UpdateReservationOptionErrors {
  export class NotFound extends BaseError {
    public constructor(lng?: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({ message: t.notFound, status: Status.NOT_FOUND });
    }
  }
  export class NoROsFound extends BaseError {
    public constructor(lng?: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({ message: t.notROsFound, status: Status.NOT_FOUND });
    }
  }
  export class NameAlreadyTaken extends BaseError {
    public constructor(args: { name: string } & OptionalLng) {
      const { name, lng } = args;
      const t = trans[getLng(lng)];
      super({ message: t.nameAlreadyTaken(name), status: Status.CONFLICT });
    }
  }
  export class MaxActiveROsReached extends BaseError {
    public constructor(args: { lng: PossibleLngs, max: number }) {
      const { lng, max } = args;
      const t = trans[getLng(lng)]
      super({ message: t.maxActiveROsReached(max), status: Status.FORBIDDEN });
    }
  }
}

const trans = {
  en: {
      notROsFound: `No reservation options found.`,
      notFound: `Reservation option not found.`,
      nameAlreadyTaken(v: string) {
        return `Name "${v}" is already taken.`;
      },
      maxActiveROsReached(max: number) {
        return `Maximum number of active reservation options (${max}) reached.`;
      },
    },
  es: {
    notROsFound: `No se encontraron opciones de reserva.`,
    notFound: `Opción de reserva no encontrada.`,
    nameAlreadyTaken(v: string) {
      return `El nombre "${v}" ya está en uso.`;
    },
    maxActiveROsReached(max: number) {
      return `Se alcanzó el máximo (${max}) de opciones de reserva activas.`;
    },
  },
};

patch({ UpdateReservationOptionErrors });
