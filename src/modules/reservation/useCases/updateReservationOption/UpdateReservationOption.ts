import { Request, Response } from './UpdateReservationOptionDTOs';
import { IReservationOptionRepo } from '../../repos/IReservationOptionRepo';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { BaseError, formatErrors } from '@shared/core/AppError';
import { AppSyncController } from '@shared/infra/Controllers';
import { UpdateReservationOptionErrors } from './UpdateReservationOptionErrors';
import { getLng } from '@shared/utils/utils';
import { validateReq } from '../createReservationOption/CreateReservationOption';
import {
  GetAvailabilityOfReservationOptions,
} from '../getAvailabilityOfReservationOptions/GetAvailabilityOfReservationOptions';
import { ContextProvider } from '@shared/context/ContextProvider';

export class UpdateReservationOption extends AppSyncController<Request, Response> {
  private readonly repo: IReservationOptionRepo;
  protected readonly contextProvider: ContextProvider;
  public constructor(args: {
    repo: IReservationOptionRepo;
    contextProvider: ContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
    this.contextProvider = contextProvider;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { lng: _lng, userId, id } = dto;
    const lng = getLng(_lng);
    const vosOrErrors = validateReq({ ...dto, lng }, this.contextProvider.sendAnalytics);
    if (vosOrErrors.isFailure)
      return formatErrors(vosOrErrors.errors!);

    const { name, service, location, timeReference, explanatoryNotes, every } =
      vosOrErrors.value;

    const errors: BaseError[] = [];
    const existing = await this.repo.list(userId);
    if (!existing)
      return formatErrors([new UpdateReservationOptionErrors.NoROsFound(lng)]);

    const currentRObeingUpdated = existing.find((ro) => ro.id.toString() === id);
    if (!currentRObeingUpdated)
      return formatErrors([new UpdateReservationOptionErrors.NotFound(lng)]);

    const sameName = existing.filter((ro) => ro.name.equals(name));
    if (sameName.length && sameName.some((ro) => !ro.id.equals(currentRObeingUpdated.id))) {
      errors.push(new UpdateReservationOptionErrors.NameAlreadyTaken({ name: name.value, lng }).setField('name'));
    }
    if (dto.active && !currentRObeingUpdated.active) {
      const currentlyActive = existing.filter((ro) => ro.active);
      const MAX = GetAvailabilityOfReservationOptions.MAX_ACTIVE_ROs_TIER2;
      if (currentlyActive.length >= MAX) {
        errors.push(new UpdateReservationOptionErrors.MaxActiveROsReached({ lng, max: MAX }).setField('active'));
      }
    }

    if (errors.length) {
      return formatErrors(errors);
    }

    currentRObeingUpdated.update({
      ...dto,
      name,
      service,
      location,
      timeReference,
      explanatoryNotes,
      every,
    });

    return { status: Status.OK, result: await this.repo.update({
        reservationOption: currentRObeingUpdated,
      }) };
  }
}
