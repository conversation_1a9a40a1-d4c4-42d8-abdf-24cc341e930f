import { describe, expect, it, test } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { expectErrorAppSync } from '@shared/utils/test';
import { removeReservationOption } from '../../utils/test';
import { ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { ReservationOptionRepo } from '../../repos/ReservationOptionRepo';
import { createReservationOption } from '../../utils/testExternal';
import { Request } from './UpdateReservationOptionDTOs';
import { getLng } from '@shared/utils/utils';
import {
  createReservationOptionQuery,
  ReservationOptionWithTimestampsFragment,
} from '../../utils/fragments';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');

const lng = getLng();
const appsync = new AppSyncClient();
const sendAnalytics = async () => {};
const repo = new ReservationOptionRepo({
  ReservationOptionModel: models.ReservationOption,
  sendAnalytics,
});

const query = gql`
  mutation (
    $id: ID!
    $userId: ID!
    $name: String!
    $service: ServiceInput!
    $location: LocationInput!
    $timeReference: TimeReferenceInput!
    $explanatoryNotes: ExplanatoryNotesInput!
    $active: Boolean!
    $every: Int!
    $lng: String!
  ) {
    updateReservationOption(
      id: $id
      userId: $userId
      name: $name
      service: $service
      location: $location
      timeReference: $timeReference
      explanatoryNotes: $explanatoryNotes
      active: $active
      every: $every
      lng: $lng
    ) {
      result {
        ...ReservationOptionWithTimestampsFragment
      }
      time
    }
  }
  ${ReservationOptionWithTimestampsFragment}
`;

it(`updates reservation option`, async () => {
  // First create a new reservation option
  const createInput = {
    ...createReservationOption().dto,
    lng,
  };
  let received = await appsync.send<Request>({
    query: createReservationOptionQuery,
    variables: createInput,
  });
  console.log('createInput', createInput);

  expect(received.status).toBe(200);
  let json = await received.json();
  const created = json.data.createReservationOption.result;
  if (!created) console.log('created json', json);
  // Then update it
  const updateInput = createReservationOption().dto;
  const updateVariables = {
    ...updateInput,
    id: created.id,
    userId: created.userId,
    lng,
  };
  console.log('updateVariables', updateVariables);
  received = await appsync.send<Request>({
    query,
    variables: updateVariables,
  });

  expect(received.status).toBe(200);
  json = await received.json();
  if (!json.data.updateReservationOption) console.log('received', json);
  const updated = json.data.updateReservationOption.result;
  if (!updated) console.log('updated json', json);

  const saved = await repo.get({ userId: created.userId, id: created.id });

  if (!saved) throw Error('ReservationOption updated not saved');
  const savedDto = saved.toDto();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { lng: _lng, ...rest } = updateVariables;
  expect(savedDto).toMatchObject({
    ...rest,
    version: 2,
    created_at: expect.any(Date),
    updated_at: expect.any(Date),
    deleted_at: null,
  });

  await removeReservationOption(saved.id.toString());
});

describe(`fails when`, () => {
  test(`Reservation option isn't found`, async () => {
    const received = await appsync.send<Request>({
      query,
      variables: {
        ...createReservationOption().dto,
        userId: ReservationOptionsInDb[0].userId,
        id: ReservationOptionsInDb[1].id,
        lng,
      },
    });

    expect(received.status).toBe(200);
    const response = await received.json();

    expectErrorAppSync({
      response,
      error: 'UpdateReservationOptionErrors.NotFound',
      query: 'updateReservationOption',
      status: 404,
    });
  });
  test(`Reservation option name is already taken`, async () => {
    const received = await appsync.send<Request>({
      query,
      variables: {
        ...createReservationOption().dto,
        userId: ReservationOptionsInDb[0].userId,
        id: ReservationOptionsInDb[0].id,
        name: ReservationOptionsInDb[2].name, // ReservationOptionsInDb[2].userId === ReservationOptionsInDb[0].userId
        lng,
      },
    });

    expect(received.status).toBe(200);
    const response = await received.json();

    expectErrorAppSync({
      response,
      error: 'UpdateReservationOptionErrors.NameAlreadyTaken',
      query: 'updateReservationOption',
      status: 409,
      field: 'name',
    });
  });
});