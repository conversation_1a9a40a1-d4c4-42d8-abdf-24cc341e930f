import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import {
  expectErrorAppSync,
  nonExistingId,
  orNull,
  pickLng,
} from '@shared/utils/test';
import { removeGS } from '../../utils/test';
import { GeneralSettingsRepo } from '../../repos/GeneralSettingsRepo';
import { GeneralSettings } from '../../domain/GeneralSettings';
import { Response as fetchResponse } from 'node-fetch';
import { GeneralSettingsForReservationsInDb } from '@shared/infra/database/sequelize/migrations/GeneralSettingsForReservations.json';
import { Request } from './UpdateGSstoreInfoDTOs';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');

const appsync = new AppSyncClient();
const repo = new GeneralSettingsRepo(models.GeneralSettingsForReservation);

const query = gql`
  mutation (
    $userId: ID!
    $title: String
    $welcome: String
    $slug: String
    $lng: String!
  ) {
    updateGSstoreInfo(
      userId: $userId
      title: $title
      welcome: $welcome
      slug: $slug
      lng: $lng
    ) {
      result
      time
    }
  }
`;

const userId = chance.guid({ version: 4 });
const initialGs = GeneralSettings.createDefault({ id: userId, lng: pickLng() });
const input = {
  userId,
  title: orNull(chance.sentence()),
  welcome: orNull(chance.sentence()),
  slug: orNull(
    chance.string({
      length: 8,
      casing: 'lower',
      alpha: true,
      numeric: true,
    }),
  ),
  lng: pickLng(),
};
type inputT = typeof input;

it(`success`, async () => {
  // First create a new GS
  await repo.create(initialGs);

  // Succeeds with random data
  let received = await appsync.send<Request>({
    query,
    variables: input,
  });

  await expectResult('OK', received);
  await expectSaved(userId, input);

  // Succeeds when the slug is already in use by the same user
  input.title = orNull(chance.sentence());
  input.welcome = orNull(chance.sentence());
  received = await appsync.send<Request>({
    query,
    variables: input,
  });

  await expectResult('OK', received);
  await expectSaved(userId, input);

  // Detects when slug is already taken
  input.title = orNull(chance.sentence());
  input.welcome = orNull(chance.sentence());
  received = await appsync.send<Request>({
    query,
    variables: {
      ...input,
      slug: GeneralSettingsForReservationsInDb[0].slug,
    },
  });

  await expectResult('SLUG_NOT_UNIQUE', received);
  await expectSaved(userId, input);

  await removeGS(userId);

  async function expectResult(result: string, received: fetchResponse) {
    expect(received.status).toBe(200);
    const json = await received.json();
    if (!json.data) console.log('UpdateGSstoreInfo json', json);
    const response = json.data.updateGSstoreInfo;
    expect(response).toEqual({
      result,
      time: expect.any(String),
    });
  }

  async function expectSaved(userId: string, input: inputT) {
    const saved = await repo.get(userId);

    if (!saved) throw Error(`GS updated wasn't found in db`);
    const savedDto = saved.toDto();
    expect(savedDto).toMatchObject({
      ...initialGs.toDto(),
      title: input.title,
      welcome: input.welcome,
      slug: input.slug,
      created_at: expect.any(Date),
      updated_at: expect.any(Date),
    });
  }
});

it(`fails when general settings aren't found`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      ...input,
      userId: nonExistingId,
    },
  });

  expect(received.status).toBe(200);
  const response = await received.json();

  expectErrorAppSync({
    response,
    error: 'UpdateGSstoreInfoErrors.NotFound',
    query: 'updateGSstoreInfo',
    status: 404,
  });
});
