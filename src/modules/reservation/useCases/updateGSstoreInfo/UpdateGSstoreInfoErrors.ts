import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { Status } from '@shared/core/Status';
import { OptionalLng, getLng } from '@shared/utils/utils';

export namespace UpdateGSstoreInfoErrors {
  export class NotFound extends BaseError {
    public constructor(args: { userId: string } & OptionalLng) {
      const { userId, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];

      super({
        message: `${t.gsForUser} "${userId}" ${t.notFound}`,
        status: Status.NOT_FOUND,
      });
    }
  }
}

type Key = keyof typeof trans.en & keyof typeof trans.es;
const trans = {
  en: {
    gsForUser: 'General settings for reservations for user',
    notFound: 'not found.',
  },
  es: {
    gsForUser: 'Configuración general de reservas para el usuario',
    notFound: 'no encontrada.',
  },
};

patch({ UpdateGSstoreInfoErrors });
