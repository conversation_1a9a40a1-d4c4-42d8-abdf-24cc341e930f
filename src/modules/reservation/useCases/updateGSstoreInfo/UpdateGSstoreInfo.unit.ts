import { it, expect, describe, beforeEach } from 'vitest';
import { UpdateGSstoreInfo } from './UpdateGSstoreInfo';
import { GeneralSettingsRepoFake } from '../../repos/GeneralSettingsRepoFake';
import {
  expectControllerErrorContaining,
  expectControllerMultipleErrors,
  nonExistingId,
  orNull,
  pickLng,
} from '@shared/utils/test';
import { GeneralSettingsForReservationsInDb } from '@shared/infra/database/sequelize/migrations/GeneralSettingsForReservations.json';
import { SlugErrors } from '@shared/core/SlugErrors';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

let repo: GeneralSettingsRepoFake, controller: UpdateGSstoreInfo, contextProvider: ContextProvider;
beforeEach(() => {
  repo = new GeneralSettingsRepoFake();
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  controller = new UpdateGSstoreInfo({
    repo,
    contextProvider,
  });
});

const valid = {
  userId: GeneralSettingsForReservationsInDb[0].id,
  title: orNull(chance.sentence()),
  welcome: orNull(chance.sentence()),
  slug: orNull(
    chance.string({ length: 8, casing: 'lower', alpha: true, numeric: true }),
  ),
  lng: pickLng(),
};

describe(`success`, () => {
  it(`random data`, async () => {
    const response = await controller.executeImpl(valid);

    expect(response).toEqual({
      status: 200,
      result: 'OK',
    });
  });

  it(`detects when slug is already taken`, async () => {
    const input = {
      userId: GeneralSettingsForReservationsInDb[1].id,
      title: orNull(chance.sentence()),
      welcome: orNull(chance.sentence()),
      slug: GeneralSettingsForReservationsInDb[0].slug,
      lng: pickLng(),
    };

    const response = await controller.executeImpl(input);

    expect(response).toEqual({
      status: 200,
      result: 'SLUG_NOT_UNIQUE',
    });
  });

  it(`succeeds when the slug is the already in use by the same user`, async () => {
    const input = {
      userId: GeneralSettingsForReservationsInDb[1].id,
      title: orNull(chance.sentence()),
      welcome: orNull(chance.sentence()),
      slug: GeneralSettingsForReservationsInDb[1].slug,
      lng: pickLng(),
    };

    const response = await controller.executeImpl(input);

    expect(response).toEqual({
      status: 200,
      result: 'OK',
    });
  });
});

describe(`failure`, () => {
  const { lng } = valid;
  it.each([
    ['empty string', '', [new SlugErrors.EmptyNotAllowed(lng).setField('slug')]],
    ['%', '%', [
      new SlugErrors.InvalidChars({ invalidChars: ['%'], lng }).setField('slug'),
      new SlugErrors.ShouldStartWithAletterOrNumber(lng).setField('slug'),
      new SlugErrors.ShouldEndWithAletterOrNumber(lng).setField('slug'),
    ]],
  ])(`invalid slug: %s`, async (title, value, errors) => {
    const response = await controller.executeImpl({
      ...valid,
      slug: value,
    });

    expectControllerMultipleErrors({
      response,
      errors,
    });
  });
  it(`doesn't find the user`, async () => {
    const response = await controller.executeImpl({
      ...valid,
      userId: nonExistingId,
    });

    expectControllerErrorContaining({
      response,
      errorContaining: 'UpdateGSstoreInfoErrors.NotFound',
      code: 404,
    });
  });
});
