import { AppSyncController } from '@shared/infra/Controllers';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Request, Response } from './UpdateGSstoreInfoDTOs';
import { Status } from '@shared/core/Status';
import { IGeneralSettingsRepo } from '../../repos/IGeneralSettingsRepo';
import { formatErrors } from '@shared/core/AppError';
import { UpdateGSstoreInfoErrors } from './UpdateGSstoreInfoErrors';
import { Slug } from '@shared/core/Slug';
import { getLng } from '@shared/utils/utils';
import { IContextProvider } from '@shared/context/IContextProvider';

export class UpdateGSstoreInfo extends AppSyncController<Request, Response> {
  private repo: IGeneralSettingsRepo;

  public constructor(args: {
    repo: IGeneralSettingsRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { title, welcome, slug: _slug, userId, lng: _lng } = dto;
    const lng = getLng(_lng);

    let slug: Slug | null = null;
    if (_slug !== null) {
      const slugOrError = Slug.create({ slug: _slug, lng });
      if (slugOrError.isFailure)
        return formatErrors(slugOrError.errors!.map((e) => e.setField('slug')));
      slug = slugOrError.value;
    }

    const gs = await this.repo.get(userId);
    if (!gs)
      return formatErrors([new UpdateGSstoreInfoErrors.NotFound({ userId, lng })]);

    let slugIsAvailable = true;
    if (slug)
      slugIsAvailable = await this.repo.isSlugAvailable({
        userIdAsking: userId,
        slug,
      });

    let result: Response;
    if (slugIsAvailable) {
      gs.update({
        ...gs.props,
        title,
        welcome,
        slug,
      });
      result = 'OK';
    } else {
      gs.update({
        ...gs.props,
        title,
        welcome,
      });
      result = 'SLUG_NOT_UNIQUE';
    }
    await this.repo.update(gs);

    return { status: Status.OK, result };
  }
}
