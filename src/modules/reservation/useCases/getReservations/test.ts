import { ReservationDto } from '../../domain/Reservation';
import { ReservationRepo } from '../../repos/ReservationRepo';
import { ReservationsInDb } from '@shared/infra/database/sequelize/migrations/Reservations.json';
import { Dat } from '@shared/core/Dat';

const res1 = ReservationsInDb[0];
const res3 = ReservationsInDb[2];
const res4 = ReservationsInDb[3];
const res5 = ReservationsInDb[4];
const res6 = ReservationsInDb[5];
const res8 = ReservationsInDb[7];
const res9 = ReservationsInDb[8];
export const res1expected: ReservationDto = ReservationRepo.mapDb2Dto({
  ...res1,
  cancelledDate: res1.cancelledDate
    ? Dat.create({ value: res1.cancelledDate }).value.s
    : res1.cancelledDate,
});
export const res3expected: ReservationDto = ReservationRepo.mapDb2Dto({
  ...res3,
  cancelledDate: res3.cancelledDate
    ? Dat.create({ value: res3.cancelledDate }).value.s
    : res3.cancelledDate,
});
export const res4expected: ReservationDto = ReservationRepo.mapDb2Dto({
  ...res4,
  cancelledDate: res4.cancelledDate
    ? Dat.create({ value: res4.cancelledDate }).value.s
    : res4.cancelledDate,
});
export const res5expected: ReservationDto = ReservationRepo.mapDb2Dto({
  ...res5,
  cancelledDate: res5.cancelledDate
    ? Dat.create({ value: res5.cancelledDate }).value.s
    : res5.cancelledDate,
});
export const res6expected: ReservationDto = ReservationRepo.mapDb2Dto({
  ...res6,
  cancelledDate: res6.cancelledDate
    ? Dat.create({ value: res6.cancelledDate }).value.s
    : res6.cancelledDate,
});
export const res8expected: ReservationDto = ReservationRepo.mapDb2Dto({
  ...res8,
  cancelledDate: res8.cancelledDate
    ? Dat.create({ value: res8.cancelledDate }).value.s
    : res8.cancelledDate,
});
export const res9expected: ReservationDto = ReservationRepo.mapDb2Dto({
  ...res9,
  cancelledDate: res9.cancelledDate
    ? Dat.create({ value: res9.cancelledDate }).value.s
    : res9.cancelledDate,
});
