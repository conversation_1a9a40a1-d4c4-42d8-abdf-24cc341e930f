import { Request, Response } from './GetReservationsDTOs';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { IReservationRepo } from '../../repos/IReservationRepo';
import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';

export class GetReservations extends AppSyncController<Request, Response> {
  private readonly repo: IReservationRepo;

  public constructor(args: {
    repo: IReservationRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const reservations = await this.repo.list(dto.userId);

    return {
      status: Status.OK,
      result: !reservations
        ? null
        : reservations.map((res) => res.toDto()),
    };
  }
}
