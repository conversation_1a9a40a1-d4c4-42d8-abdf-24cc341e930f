import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { ReservationsInDb } from '@shared/infra/database/sequelize/migrations/Reservations.json';
import { dateFormat, nonExistingId } from '@shared/utils/test';
import { Request } from './GetReservationsDTOs';
import { ReservationDto } from '../../domain/Reservation';
import { ReservationRepo } from '../../repos/ReservationRepo';
import { Dat } from '@shared/core/Dat';
import { ReservationWithTimestampsFragment } from '../../utils/fragments';

const appsync = new AppSyncClient();

const query = gql`
  query ($userId: ID!) {
    getReservations(userId: $userId) {
      result {
        ...ReservationWithTimestampsFragment
      }
      time
    }
  }
  ${ReservationWithTimestampsFragment}
`;

const timestamps = {
  created_at: expect.stringMatching(dateFormat),
  updated_at: expect.stringMatching(dateFormat),
  deleted_at: null,
};

it(`gets reservations`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: { userId: ReservationsInDb[1].userId },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log('GetReservations 1', json);
  const response = json.data.getReservations;
  if (!response) console.log('json', json);

  expect(response.result.length).toBe(1);
  const res = ReservationsInDb[1];
  const resExpected: ReservationDto = ReservationRepo.mapDb2Dto({
    ...res,
    start: expect.stringMatching(dateFormat),
    cancelledDate: res.cancelledDate
      ? Dat.create({ value: res.cancelledDate }).value.s
      : res.cancelledDate,
  });
  expect(response.result).toEqual([  // if it throws an error and I can't tell what the error is, it's because it occurs in a nested object's property
    {
      ...resExpected,
      ...timestamps,
    },
  ]);
});

it(`gets null when no reservations are found`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      userId: nonExistingId,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log('GetReservations 2', json);
  const response = json.data.getReservations;

  expect(response.result).toBeNull();
});
