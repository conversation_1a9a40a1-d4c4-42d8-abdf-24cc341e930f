import { expect, beforeEach, it } from 'vitest';
import { GetReservations } from './GetReservations';
import { IReservationRepo } from '../../repos/IReservationRepo';
import { ReservationRepoFake } from '../../repos/ReservationRepoFake';
import { ReservationDto } from '../../domain/Reservation';
import { ReservationsInDb } from '@shared/infra/database/sequelize/migrations/Reservations.json';
import {
  res1expected,
  res3expected,
  res4expected,
  res5expected,
  res6expected,
  res8expected,
  res9expected,
} from './test';
import {
  dateFormat,
  nonExistingId,
  invokerDummy as invoker,
} from '@shared/utils/test';
import { ContextProvider } from '@shared/context/ContextProvider';

let repo: IReservationRepo, getReservations: GetReservations;
beforeEach(() => {
  repo = new ReservationRepoFake();
  const contextProvider = new ContextProvider({ invoker });
  getReservations = new GetReservations({
    repo,
    contextProvider,
  });
});

it(`succeeds`, async () => {
  const response = await getReservations.executeImpl({
    userId: ReservationsInDb[0].userId,
  });

  expect(response.status).toBe(200);

  expect((response.result as ReservationDto[]).length).toBe(7);

  expect(response.result).toEqual(
    expect.arrayContaining([
      expect.objectContaining({
        ...res1expected,
        start: expect.stringMatching(dateFormat),
      }),
      expect.objectContaining({
        ...res3expected,
        start: expect.stringMatching(dateFormat),
      }),
      expect.objectContaining({
        ...res4expected,
        start: expect.stringMatching(dateFormat),
      }),
      expect.objectContaining({
        ...res5expected,
        start: expect.stringMatching(dateFormat),
      }),
      expect.objectContaining({
        ...res6expected,
        start: expect.stringMatching(dateFormat),
      }),
      expect.objectContaining({
        ...res8expected,
        start: expect.stringMatching(dateFormat),
      }),
      expect.objectContaining({
        ...res9expected,
        start: expect.stringMatching(dateFormat),
      }),
    ]),
  );
});

it(`returns null when doesn't find any`, async () => {
  const response = await getReservations.executeImpl({
    userId: nonExistingId,
  });

  expect(response.status).toBe(200);
  expect(response.result).toBeNull();
});
