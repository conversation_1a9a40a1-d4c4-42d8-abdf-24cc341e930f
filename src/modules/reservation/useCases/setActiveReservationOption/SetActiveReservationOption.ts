import { Request, Response } from './SetActiveReservationOptionDTOs';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { IReservationOptionRepo } from '../../repos/IReservationOptionRepo';
import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';
import { formatErrors } from '@shared/core/AppError';
import { SetActiveReservationOptionErrors } from './SetActiveReservationOptionErrors';
import { getLng } from '@shared/utils/utils';
import {
  GetAvailabilityOfReservationOptions,
} from '../getAvailabilityOfReservationOptions/GetAvailabilityOfReservationOptions';

export class SetActiveReservationOption extends AppSyncController<
  Request,
  Response
> {
  private readonly repo: IReservationOptionRepo;
  public constructor(args: {
    repo: IReservationOptionRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { userId, id, active, lng: _lng } = dto;
    const lng = getLng(_lng);
    const reservationOptions = await this.repo.list(userId);
    if (!reservationOptions)
      return formatErrors([new SetActiveReservationOptionErrors.NoROs(lng)]);

    const reservationOption = reservationOptions.find(ro => ro.id.toString() === id);

    if (!reservationOption)
      return formatErrors([new SetActiveReservationOptionErrors.NotFound(lng)]);

    if (!reservationOption.active && active) {
      const existingActiveROs = reservationOptions.filter(ro => ro.active);
      const MAX = GetAvailabilityOfReservationOptions.MAX_ACTIVE_ROs_TIER2;
      if (existingActiveROs.length >= MAX) {
        return formatErrors([new SetActiveReservationOptionErrors.MaxActiveROsReached({ max: MAX, lng })]);
      }
    }

    reservationOption.setActive(active);

    return {
      status: Status.OK,
      result: await this.repo.update({
        reservationOption,
        skip_updated_at: true,  // the updated_at timestamp won't be modified. This is used in the setActiveReservationOption use case to prevent altering the order of the list of reservation options on the frontend, as the list is sorted by updated_at. Without this, activating or deactivating a reservation option, would move it to the top of the list, which is weird.
      }),
    };
  }
}
