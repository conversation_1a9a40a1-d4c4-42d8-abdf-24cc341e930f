import { expect, beforeEach, it, test, describe } from 'vitest';
import { ContextProvider } from '@shared/context/ContextProvider';
import { SetActiveReservationOption } from './SetActiveReservationOption';
import { IReservationOptionRepo } from '../../repos/IReservationOptionRepo';
import { ReservationOptionRepoFake } from '../../repos/ReservationOptionRepoFake';
import { createReservationOption } from '../../utils/testExternal';
import {
  expectControllerError,
  invokerDummy as invoker,
  nonExistingId,
  pickLng,
} from '@shared/utils/test';
import { ReservationOption } from '../../domain/ReservationOption';
import {
  GetAvailabilityOfReservationOptions,
} from '../getAvailabilityOfReservationOptions/GetAvailabilityOfReservationOptions';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

let repo: IReservationOptionRepo,
  setActiveReservationOption: SetActiveReservationOption;
beforeEach(async () => {
  repo = new ReservationOptionRepoFake();
  const contextProvider = new ContextProvider({ invoker });
  setActiveReservationOption = new SetActiveReservationOption({
    repo,
    contextProvider,
  });
});

it(`succeeds`, async () => {
  // First create a reservation option
  const initialRO = createReservationOption().reservationOption;
  await repo.create(initialRO);
  const input = {
    id: initialRO.id.toString(),
    userId: initialRO.userId,
    active: !initialRO.active,
    lng: pickLng(),
  };

  const response = await setActiveReservationOption.executeImpl(input);
  expect(response).toMatchObject({
    status: 200,
    result: {
      ...initialRO.toDto(),
      active: input.active,
    },
  });
});

describe(`fails when`, async () => {
  test(`there are no reservation options`, async () => {
    const initialRO = createReservationOption().reservationOption;
    const input = {
      id: initialRO.id.toString(),
      userId: initialRO.userId,
      active: !initialRO.active,
      lng: pickLng(),
    };

    const response = await setActiveReservationOption.executeImpl({
      ...input,
      userId: nonExistingId,
    });
    expectControllerError({
      response,
      error: 'SetActiveReservationOptionErrors.NoROs',
      code: 404,
    });
  });
  test(`the reservation option doesn't exist`, async () => {
    // First create a reservation option for a user
    const initialRO = createReservationOption().reservationOption;
    await repo.create(initialRO);
    const input = {
      id: initialRO.id.toString(),
      userId: initialRO.userId,
      active: !initialRO.active,
      lng: pickLng(),
    };

    const response = await setActiveReservationOption.executeImpl({
      ...input,
      id: nonExistingId,
    });
    expectControllerError({
      response,
      error: 'SetActiveReservationOptionErrors.NotFound',
      code: 404,
    });
  });
  test(`max active reservation options reached`, async () => {
    // Create an inactive reservation option
    const userId = chance.guid({ version: 4 });
    const inactiveRO = createReservationOption(userId).reservationOption;
    inactiveRO.setActive(false);
    const input = {
      id: inactiveRO.id.toString(),
      userId: inactiveRO.userId,
      active: !inactiveRO.active,
      lng: pickLng(),
    };
    await repo.create(inactiveRO);
    // Create MAX_ACTIVE_ROs_TIER2 active reservation options
    const ROs: ReservationOption[] = [];
    while (ROs.filter(ro => ro.active).length < GetAvailabilityOfReservationOptions.MAX_ACTIVE_ROs_TIER2) {
      const RO = createReservationOption(userId).reservationOption;
      await repo.create(RO);
      ROs.push(RO);
    }

    const response = await setActiveReservationOption.executeImpl(input);
    expectControllerError({
      response,
      error: 'SetActiveReservationOptionErrors.MaxActiveROsReached',
      code: 400,
    });
  });
});
