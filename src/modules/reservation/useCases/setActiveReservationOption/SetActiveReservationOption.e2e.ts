import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import {
  expectErrorAppSync,
  pickLng,
} from '@shared/utils/test';
import {
  sendAnalyticsDummy as sendAnalytics,
} from '@shared/utils/utils';
import {
  ReservationOptionRepo,
} from '../../repos/ReservationOptionRepo';
import { createReservationOption } from '../../utils/testExternal';
import {
  removeReservationOption,
} from '../../utils/test';
import { ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { Request } from './SetActiveReservationOptionDTOs';
import { ReservationOptionWithTimestampsFragment } from '../../utils/fragments';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');

const appsync = new AppSyncClient();
const repo = new ReservationOptionRepo({
  ReservationOptionModel: models.ReservationOption,
  sendAnalytics,
});

const query = gql`
  mutation ($id: ID!, $userId: ID!, $active: Boolean!, $lng: String!) {
    setActiveReservationOption(id: $id, userId: $userId, active: $active, lng: $lng) {
      result {
        ...ReservationOptionWithTimestampsFragment
      }
      time
    }
  }
  ${ReservationOptionWithTimestampsFragment}
`;

it(`sets ReservationOption.active`, async () => {
  // First create a reservation option
  const initial = createReservationOption().reservationOption;
  const initialInRepo = await repo.create(initial);
  const initialUpdatedAt = initialInRepo.dataValues.updated_at.getTime();

  const input = {
    ...initial.toDto(),
    active: !initial.toDto().active,
  };
  const received = await appsync.send<Request>({
    query,
    variables: {
      ...input,
      lng: pickLng(),
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.setActiveReservationOption;
  if (!response) console.log('json', json);
  expect(response.result).toMatchObject(input);

  const finalInRepo = await repo.get({ userId: initial.userId, id: initial.id.toString() });
  const finalUpdatedAt =
    // @ts-expect-error updated_at is returned by repo.get
    finalInRepo!.props.updated_at.getTime();

  expect(finalUpdatedAt).toBe(initialUpdatedAt);  // Activating or deactivating doesn't change updated_at

  removeReservationOption(initial.id.toString());
});

it(`fails when reservation option isn't found`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      id: ReservationOptionsInDb[0].id,
      userId: ReservationOptionsInDb[1].userId,
      active: chance.bool(),
      lng: pickLng(),
    },
  });

  expect(received.status).toBe(200);
  expectErrorAppSync({
    response: await received.json(),
    error: 'SetActiveReservationOptionErrors.NotFound',
    query: 'setActiveReservationOption',
    status: 404,
  });
});
