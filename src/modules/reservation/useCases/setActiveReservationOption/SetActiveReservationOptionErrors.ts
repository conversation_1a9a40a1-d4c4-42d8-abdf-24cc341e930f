import {
  BadRequest,
  NotFound as _NotFound,
} from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { getLng, PossibleLngs } from '@shared/utils/utils';

export namespace SetActiveReservationOptionErrors {
  export class NotFound extends _NotFound {
    public constructor(lng: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({ message: t.notFound });
    }
  }
  export class NoROs extends _NotFound {
    public constructor(lng: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({ message: t.notFound });
    }
  }
  export class MaxActiveROsReached extends BadRequest {
    public constructor(args: { max: number, lng: PossibleLngs }) {
      const { max, lng } = args;
      const t = trans[getLng(lng)];
      super({ message: t.maxActiveROsReached(max) });
    }
  }
}

const trans = {
  en: {
    notFound: `Reservation option not found.`,
    noROs: `There are no reservation options.`,
    maxActiveROsReached: (max: number) => `Maximum quantity (${max}) of active reservation options reached.`,
  },
  es: {
    notFound: `Opción de reserva no encontrada.`,
    noROs: `No hay opciones de reserva.`,
    maxActiveROsReached: (max: number) => `Máxima cantidad (${max}) de opciones de reserva activas alcanzada.`,
  },
};

patch({ SetActiveReservationOptionErrors });
