import { beforeAll, expect, it, vi } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { Dat } from '@shared/core/Dat';
import {
  dateFormat,
  expectErrorAppSync,
  pickLng,
} from '@shared/utils/test';
import {
  sendAnalyticsDummy as sendAnalytics,
} from '@shared/utils/utils';
import { getLng, rmTimestamps, uuidFormat } from '@shared/utils/utils';
import { Identifier } from '../../external';
import {
  ReservationOptionsInDb,
} from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import {
  ReservationOptionInDb,
  ReservationOptionRepo,
} from '../../repos/ReservationOptionRepo';
import {
  rmReservations,
} from '../../utils/test';
import { getCalendarCreds } from '@shared/infra/iac/helpers';
import {
  balanceRepo,
  CalendarApi,
  CalendarApiT,
  CalendarsInDb,
  CustomerUsersInDb,
  Event,
  Movement,
  rmBalanceRow,
  rmEvents,
  Slot,
  tomorrowPlus,
} from '../../utils/testExternal';
import { calendar } from '@googleapis/calendar';
import { Request } from './CreateReservationDTOs';
import { ReservationFragment } from '../../utils/fragments';

// Add all process.env used:
const {PROJECT, STAGE} = process.env;
if (!PROJECT || !STAGE) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}

let availabilityApi: CalendarApiT, reservationApi: CalendarApiT;
beforeAll(async () => {
  const {calendar_clientEmail, calendar_clientKey} = await getCalendarCreds({
    name: PROJECT,
    stage: STAGE,
  });
  vi.stubEnv('calendar_clientEmail', calendar_clientEmail);
  vi.stubEnv('calendar_clientKey', calendar_clientKey);
  availabilityApi = new CalendarApi({
    google: calendar({version: 'v3'}),
    sendAnalytics,
  });
  availabilityApi.setCalendarId(CalendarsInDb[7].id);
  reservationApi = new CalendarApi({
    google: calendar({version: 'v3'}),
    sendAnalytics,
  });
  reservationApi.setCalendarId(CalendarsInDb[8].id);
});

const appsync = new AppSyncClient();

const query = gql`
  mutation (
    $businessId: ID!
    $start: AWSDateTime!
    $roId: ID!
    $roVersion: Int!
    $customerId: ID!
    $lng: String!
  ) {
    createReservation(
      businessId: $businessId
      start: $start
      roId: $roId
      roVersion: $roVersion
      customerId: $customerId
      lng: $lng
    ) {
      result {
        ...ReservationFragment
      }
      time
    }
  }
  ${ReservationFragment}
`;

const initial = Dat.create({value: tomorrowPlus(60).l.startOf('hour')}).value;
const reservationOptionDto = ReservationOptionRepo.mapDb2Dto(
  rmTimestamps(ReservationOptionsInDb[10]) as ReservationOptionInDb,  // ReservationOptionsInDb[10].userId = 02a82ed0-c39a-11ee-8adf-3f66c88fcbdd
)
const userId = reservationOptionDto.userId;
const input = {
  businessId: userId,
  start: initial.s,
  roId: reservationOptionDto.id,
  customerId: CustomerUsersInDb[0].id,
  roVersion: reservationOptionDto.version,
  lng: getLng(),
};
const identifier = Identifier.create({value: reservationOptionDto.location.identifier}, sendAnalytics).value;

it(`creates reservation event`, async () => {
  const availabilityEvent1 = Event.create({
    name: 'Availability 1',
    description: `${identifier.query}:2`,
    slot: Slot.create({
      start: initial,
      end: Dat.create({value: initial.l.plus({minutes: reservationOptionDto.service.duration})})
        .value,
    }).value,
  });
  const availInserted1 = await availabilityApi.insertEvent({
    event: availabilityEvent1,
    lng: pickLng(),
  }); // Como preparación de este test necesito tener acceso a escritura del availability calendar, pero en la app de producción sólo necesito acceso de lectura
  expect(availInserted1.isSuccess).toBe(true);

  const received = await appsync.send<Request>({
    query,
    variables: {
      ...input,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log(`CreateReservation.e2e json 1:`, json)
  const response = json.data.createReservation;
  if (!response) console.log(`CreateReservation.e2e json 2:`, json);

  expect(response).toEqual({
    result: {
      id: expect.stringMatching(uuidFormat),
      userId,
      customerId: input.customerId,
      customerType: 'REGISTERED',
      customerFirstName: CustomerUsersInDb[0].firstName,
      customerLastName: CustomerUsersInDb[0].lastName,
      customerPhone: CustomerUsersInDb[0].phone,
      customerEmail: CustomerUsersInDb[0].email,
      start: Dat.create({value: input.start}).value.s,
      eventId: expect.any(String),
      roId: expect.stringMatching(uuidFormat),
      roZone: reservationOptionDto.timeReference.zone,
      srvName: reservationOptionDto.service.name,
      srvDuration: reservationOptionDto.service.duration,
      srvDescription: reservationOptionDto.service.description,
      srvPrice: reservationOptionDto.service.price,
      locName: reservationOptionDto.location.name,
      locId: reservationOptionDto.location.identifier,
      cancelledBy: null,
      cancelledDate: null,
      cancellationReason: null,
      cancelReservationEventErrors: null,
      reminder: 'NOT_SENT',
    },
    time: expect.stringMatching(dateFormat),
  });

  // Side effect on @balance
  const balanceRows = await balanceRepo.list(userId);
  const matches = balanceRows.filter(row => {
    return row.reservationId === response.result.id.toString() &&
      row.movement === Movement.RESERVATION_BY_CUSTOMER;
  });
  expect(matches).toHaveLength(1);

  await rmReservations([response.result.id]);
  await rmEvents({
    inserted: [{value: {id: response.result.eventId}}],
    calendarApi: reservationApi,
  });
  await rmEvents({
    inserted: [availInserted1],
    calendarApi: availabilityApi,
  });
  await rmBalanceRow({userId, index: 1});
});

it(`fails when the customer doesn't have a phone`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      // GeneralSettingsForReservationsInDb[0].phoneRequired = true, GeneralSettingsForReservationsInDb[0].id = BusinessUsersInDb[0].id
      ...input,
      // CustomerUsersInDb[2].phone = null
      customerId: CustomerUsersInDb[2].id,
    },
  });

  expect(received.status).toBe(200);
  expectErrorAppSync({
    response: await received.json(),
    error: 'CreateReservationErrors.CustomerWithoutPhone',
    query: 'createReservation',
    status: 403,
  });
});
