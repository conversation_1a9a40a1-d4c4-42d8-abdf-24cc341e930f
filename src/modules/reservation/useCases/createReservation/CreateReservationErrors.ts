import { BadRequest, BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { StatusError } from '@shared/core/Status';
import { getLng, OptionalLng } from '@shared/utils/utils';

export namespace CreateReservationErrors {
  export class StartIsInvalid extends BadRequest {
    public constructor(args: { start: string } & OptionalLng) {
      const { start, lng } = args;
      const t = trans[getLng(lng)];
      super({ message: t.startIsInvalid(start) });
    }
  }
  export class NoBalance extends BaseError {
    public constructor(args: OptionalLng) {
      const { lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.noBalance,
        status: StatusError.FORBIDDEN,
      });
    }
  }
  export class CustomerWithoutPhone extends BaseError {
    public constructor(args: OptionalLng) {
      const { lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.customerWithoutPhone,
        status: StatusError.FORBIDDEN,
      });
    }
  }
  export class UserGSnotFound extends BaseError {
    public constructor(args: OptionalLng) {
      const { lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.userGSnotFound,
        status: StatusError.NOT_FOUND,
      });
    }
  }
  export class MaxPendingReservationsReached extends BaseError {
    public constructor(args: { max: number } & OptionalLng) {
      const { max, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.maxPendingReservationsReached(max),
        status: StatusError.FORBIDDEN,
      });
    }
  }
}

const trans = {
  en: {
    startIsInvalid(v: string) {
      return `Start field is invalid: ${v}.`;
    },
    businessNotFound(v: string) {
      return `Business with id ${v} not found.`;
    },
    customerNotFound(v: string) {
      return `Customer with id ${v} not found.`;
    },
    customerWithoutPhone: `This business requires the customer to configure their phone to make a reservation.`,
    noBalance: `No balance remaining.`,
    userGSnotFound: `User general settings not found.`,
    maxPendingReservationsReached(max: number) {
      return `You have reached the maximum number of pending reservations (${max}).`;
    },
  },
  es: {
    startIsInvalid(v: string) {
      return `El campo de inicio es inválido: ${v}.`;
    },
    businessNotFound(v: string) {
      return `Negocio con id ${v} no encontrado.`;
    },
    customerNotFound(v: string) {
      return `Cliente con id ${v} no encontrado.`;
    },
    customerWithoutPhone: `Este negocio requiere que el cliente configure su teléfono para realizar una reserva.`,
    noBalance: `No queda balance.`,
    userGSnotFound: `Opciones generales del usuario no encontradas.`,
    maxPendingReservationsReached(max: number) {
      return `Has alcanzado el número máximo de reservas pendientes (${max}).`;
    },
  },
};

patch({ CreateReservationErrors });
