import { expect, test, vi, describe, beforeEach } from 'vitest';
vi.stubEnv('getCustomerAndBusinessUser', 'dummy');
vi.stubEnv('createReservationEvent', 'dummy');
vi.stubEnv('distributeDomainEvents', 'dummy');
import { CreateReservation } from './CreateReservation';
import { ReservationRepoFake } from '../../repos/ReservationRepoFake';
import { ReservationOptionRepoFake } from '../../repos/ReservationOptionRepoFake';
import { ReservationOptionRepo } from '../../repos/ReservationOptionRepo';
import {
  CreateReservationEventReq,
  CreateReservationEventRes,
  GetCustomerAndBusinessUserReq,
  GetCustomerAndBusinessUserRes,
  IsBalanceEnoughReq,
  IsBalanceEnoughRes,
} from '../../external';
import {
  Error1,
  Error2,
  expectControllerError,
  expectController<PERSON>rror<PERSON>ield,
  expectControllerMultipleErrors, 
  invokerDummy as invoker,
  pickLng,
} from '@shared/utils/test';
import {
  getLng,
  uuidFormat,
  sendAnalyticsDummy as sendAnalytics,
} from '@shared/utils/utils';
import { Dat } from '@shared/core/Dat';
import { ApiResults } from '../../../calendar/services/CalendarApiFake';
import { Result } from '@shared/core/Result';
import { Result2 } from '@shared/core/Result2';
import { CustomerType } from '../../domain/Reservation';
import { ReservationOptionSrv } from '../../services/ReservationOptionSrv';
import {
  Action,
  BusinessUsersInDb,
  CustomerUsersInDb,
} from '../../utils/testExternal';
import { ReservationOption } from '../../domain/ReservationOption';
import { IReservationOptionRepo } from '../../repos/IReservationOptionRepo';
import { IReservationOptionSrv } from '../../services/IReservationOptionSrv';
import { ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { createEventRetrieved } from '../../../calendar/utils/test';
import { FC2Handler, FCHandler } from '@shared/infra/Controllers';
import { IGeneralSettingsRepo } from '../../repos/IGeneralSettingsRepo';
import { GeneralSettingsRepoFake } from '../../repos/GeneralSettingsRepoFake';
import { GeneralSettings } from '../../domain/GeneralSettings';
import { PositiveInt } from '@shared/core/PositiveInt';
import { ContextProvider } from '@shared/context/ContextProvider';
import { BusinessUserRepo } from '../../../user/repos/BusinessUserRepo';

const reservationRepo = new ReservationRepoFake();

// BusinessUsersInDb se saca de BusinessUserRepoFake, es igual a BusinessUserDto
const business = BusinessUserRepo.mapDb2Dto(BusinessUsersInDb[0]);  // GeneralSettingsForReservationsInDb[0].phoneRequired = true, GeneralSettingsForReservationsInDb[0].id = BusinessUsersInDb[0].id
// ReservationOptionsInDb[0].userId = BusinessUsersInDb[0].id
// ReservationOptionsInDb no es igual a ReservationOptionDto, necesito procesarlo con ReservationOptionRepo.mapDb2Dto para obtener ReservationOptionDto
const reservationOptionDto = ReservationOptionRepo.mapDb2Dto(
  ReservationOptionsInDb[0],
);
// CustomerUsersInDb es igual a CustomerUserDto
const customer = CustomerUsersInDb[0];  // CustomerUsersInDb[0].phone existe

const mockedGEventId = 'mockedGEventId';
const mockedGEvent = {
  ...createEventRetrieved().toDto(),
  id: mockedGEventId,
};
const input = {
  customerId: customer.id,
  businessId: business.id,
  // @ts-expect-error
  start: ApiResults.listSuccess1.data.items[2].start.dateTime,
  roId: reservationOptionDto.id,
  roVersion: reservationOptionDto.version,
  lng: getLng(),
};

let reservationOptionRepo: IReservationOptionRepo,
  reservationOptionSrv: IReservationOptionSrv,
  createReservation: CreateReservation,
  isBalanceEnough: FCHandler<IsBalanceEnoughReq, IsBalanceEnoughRes>,
  createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes>,
  getCustomerAndBusinessUser: FC2Handler<GetCustomerAndBusinessUserReq, GetCustomerAndBusinessUserRes>,
  gsRepo: IGeneralSettingsRepo,
  contextProvider: ContextProvider;
beforeEach(() => {
  vi.clearAllMocks();
  reservationOptionRepo = new ReservationOptionRepoFake();
  gsRepo = new GeneralSettingsRepoFake();
  getCustomerAndBusinessUser = async () => {
    return Result2.ok({
      customer,
      business,
    });
  }

  reservationOptionSrv = new ReservationOptionSrv(reservationOptionRepo);
  const mockCheckResOpt = () =>
    new Promise<Result<ReservationOption>>((resolve) => {
      resolve(Result.ok(ReservationOption.assemble(reservationOptionDto, sendAnalytics)));
    });
  vi.spyOn(reservationOptionSrv, 'checkResOpt').mockImplementation(
    mockCheckResOpt,
  );

  createReservationEvent = async () => {
    return Result2.ok({
      action: Action.CREATED,
      event: mockedGEvent,
    });
  };

  isBalanceEnough = async () => {
    return true;
  }

  contextProvider = new ContextProvider({ invoker });

  createReservation = new CreateReservation({
    reservationRepo,
    reservationOptionSrv,
    isBalanceEnough,
    getCustomerAndBusinessUser,
    createReservationEvent,
    gsRepo,
    contextProvider,
  });
});

const okResponse = {
  status: 201,
  result: {
    id: expect.stringMatching(uuidFormat),
    userId: input.businessId,
    customerType: CustomerType.REGISTERED,
    customerId: customer.id,
    customerFirstName: customer.firstName,
    customerLastName: customer.lastName,
    customerPhone: customer.phone,
    customerEmail: customer.email,
    start: Dat.create({ value: input.start }).value.s,
    eventId: mockedGEventId,
    roId: reservationOptionDto.id,
    roZone: reservationOptionDto.timeReference.zone,
    srvName: reservationOptionDto.service.name,
    srvDescription: reservationOptionDto.service.description,
    srvPrice: reservationOptionDto.service.price,
    srvDuration: reservationOptionDto.service.duration,
    locName: reservationOptionDto.location.name,
    locId: reservationOptionDto.location.identifier,
    cancelReservationEventErrors: null,
    cancelledBy: null,
    cancellationReason: null,
    cancelledDate: null,
    reminder: 'NOT_SENT',
  },
};

describe('success', () => {
  test(`happy path with business's lng "en"`, async () => {
    const createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes> = async (dto: CreateReservationEventReq) => {
      [customer.firstName, customer.lastName, customer.phone, customer.email, 'reserved'] // BusinessUsersInDb[0].lng = en
        .forEach(data => expect(dto.addToEventDescription).toContain(data));
      return Result2.ok({
        action: Action.CREATED,
        event: mockedGEvent,
      });
    }

    const createReservation = new CreateReservation({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      getCustomerAndBusinessUser,
      createReservationEvent,
      contextProvider,
      gsRepo,
    });

    const response = await createReservation.executeImpl({
      ...input,
      lng: 'es',  // even sending 'es' lng from the client, it will use business' lng
    });

    expect(response).toMatchObject(okResponse);
  });

  test(`addToEventDescription honors "es" business's lng`, async () => {
    const business = BusinessUserRepo.mapDb2Dto(BusinessUsersInDb[1]);  // 01a82ed0-c39a-11ee-8adf-3f66c88fcbdd Ronald Mc Wells, lng = es, phoneRequired = false
    // ReservationOptionsInDb[1].userId = BusinessUsersInDb[1].id
    const reservationOption = ReservationOptionsInDb[1];
    const input2 = {
      ...input,
      businessId: business.id,
      roId: reservationOption.id,
      roVersion: reservationOption.version,
      lng: 'en',  // even sending 'en' lng from the client, it will use business' lng
    };

    const createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes> = async (dto) => {
      [customer.firstName, customer.lastName, customer.phone, customer.email, 'reservó'] // BusinessUsersInDb[1].lng = en
        .forEach(data => expect(dto.addToEventDescription).toContain(data));
      return Result2.ok({
        action: Action.CREATED,
        event: mockedGEvent,
      });
    };

    const getCustomerAndBusinessUser = async () => {
      return Result2.ok({
        customer,
        business,
      });
    }

    const createReservation = new CreateReservation({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      getCustomerAndBusinessUser,
      createReservationEvent,
      contextProvider,
      gsRepo,
    });

    await createReservation.executeImpl(input2);
  });

  describe(`addToEventDescription honors reservation option timezone`, () => {
    test(`case America/Cordoba: GMT-3`, async () => {
      const createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes> = async (dto: CreateReservationEventReq) => {
        expect(dto.addToEventDescription).toContain('-03:00'); // ReservationOptionsInDb[0].trZone = "America/Cordoba"
        return Result2.ok({
          action: Action.CREATED,
          event: mockedGEvent,
        });
      }

      const createReservation = new CreateReservation({
        reservationRepo,
        reservationOptionSrv,
        isBalanceEnough,
        getCustomerAndBusinessUser,
        createReservationEvent,
        contextProvider,
        gsRepo,
      });

      await createReservation.executeImpl(input);
    });

    test(`case Africa/Johannesburg: GMT+2`, async () => {
      const reservationOptionSrv = new ReservationOptionSrv(reservationOptionRepo);
      const mockCheckResOpt = () =>
        new Promise<Result<ReservationOption>>((resolve) => {
          const reservationOptionDto = ReservationOptionRepo.mapDb2Dto(
            ReservationOptionsInDb[14], // ReservationOptionsInDb[14].trZone = "America/Cordoba"
          );
          resolve(Result.ok(ReservationOption.assemble(reservationOptionDto, sendAnalytics)));
        });
      vi.spyOn(reservationOptionSrv, 'checkResOpt').mockImplementation(
        mockCheckResOpt,
      );

      const createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes> = async (dto: CreateReservationEventReq) => {
        expect(dto.addToEventDescription).toContain('+02:00');
        return Result2.ok({
          action: Action.CREATED,
          event: mockedGEvent,
        });
      }

      const createReservation = new CreateReservation({
        reservationRepo,
        reservationOptionSrv,
        isBalanceEnough,
        getCustomerAndBusinessUser,
        createReservationEvent,
        contextProvider,
        gsRepo,
      });

      await createReservation.executeImpl(input);
    });
  });

  test(`customer doesn't have phone, but business doesn't require it`, async () => {
    const getCustomerAndBusinessUser: FC2Handler<GetCustomerAndBusinessUserReq, GetCustomerAndBusinessUserRes> = async () => {
      return Result2.ok({
        // GeneralSettingsForReservationsInDb[1].phoneRequired = false, id = BusinessUsersInDb[1].id
        business: BusinessUserRepo.mapDb2Dto(BusinessUsersInDb[1]),
        // CustomerUsersInDb[2].phone = null
        customer: CustomerUsersInDb[2],
      });
    };

    const reservationRepo = new ReservationRepoFake();
    const createReservation = new CreateReservation({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      createReservationEvent,
      getCustomerAndBusinessUser,
      contextProvider,
      gsRepo,
    });

    const response = await createReservation.executeImpl(input);
    expect(response.status).toBe(201);
  })

});

describe(`fails`, () => {
  test(`when start is not a valid date-time string`, async () => {
    const response = await createReservation.executeImpl({
      ...input,
      start: 'invalid date',
    });
    expectControllerErrorField({
      response,
      code: 400,
      type: 'CreateReservationErrors.StartIsInvalid',
      errorField: 'start',
    });
  });

  test(`when no balance remains`, async () => {
    const isBalanceEnough = async () => {
      return false;
    }
    const createReservation = new CreateReservation({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      getCustomerAndBusinessUser,
      createReservationEvent,
      contextProvider,
      gsRepo,
    });

    const response = await createReservation.executeImpl(input);
    expectControllerError({
      response,
      code: 403,
      error: 'CreateReservationErrors.NoBalance',
    });
  });

  const error1 = new Error1();
  const error2 = new Error2();
  test(`when ReservationOptionSrv fails`, async () => {
    const reservationOptionSrv = new ReservationOptionSrv(reservationOptionRepo);
    const mockCheckResOpt = () =>
      new Promise<Result<ReservationOption>>((resolve) => {
        resolve(Result.fail(error1));
      });
    vi.spyOn(reservationOptionSrv, 'checkResOpt').mockImplementation(
      mockCheckResOpt,
    );

    const createReservation = new CreateReservation({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      createReservationEvent,
      getCustomerAndBusinessUser,
      contextProvider,
      gsRepo,
    });

    const response = await createReservation.executeImpl(input);
    expectControllerError({
      response,
      code: error1.status,
      error: error1.type,
    });
  });

  test(`when calling GetCustomerAndBusinessUser fails`, async () => {
    const errors = [error1, error2];
    const getCustomerAndBusinessUser: FC2Handler<GetCustomerAndBusinessUserReq, GetCustomerAndBusinessUserRes> = async () => {
      return Result2.fail(errors);
    }

    const createReservation = new CreateReservation({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      createReservationEvent,
      getCustomerAndBusinessUser,
      contextProvider,
      gsRepo,
    });

    const response = await createReservation.executeImpl(input);
    expectControllerMultipleErrors({
      response,
      errors,
    });
  });

  test(`when user general settings are not found`, async () => {
    const gsRepo = new GeneralSettingsRepoFake();
    vi.spyOn(gsRepo, 'get').mockImplementation(
      async () => null
    );
    const createReservation = new CreateReservation({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      createReservationEvent,
      getCustomerAndBusinessUser,
      contextProvider,
      gsRepo,
    });
    const response = await createReservation.executeImpl(input);
    expectControllerError({
      response,
      code: 404,
      error: 'CreateReservationErrors.UserGSnotFound',
    });
  })

  test(`business requires phone, but customer hasn't configured it`, async () => {
    const getCustomerAndBusinessUser: FC2Handler<GetCustomerAndBusinessUserReq, GetCustomerAndBusinessUserRes> = async () => {
      return Result2.ok({ customer: CustomerUsersInDb[2], business });
    }

    const createReservation = new CreateReservation({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      createReservationEvent,
      getCustomerAndBusinessUser,
      contextProvider,
      gsRepo,
    });

    const response = await createReservation.executeImpl(input);
    expectControllerError({
      response,
      code: 403,
      error: 'CreateReservationErrors.CustomerWithoutPhone',
    });
  });

  test(`when calling CreateReservationEvent fails`, async () => {
    const errors = [error1, error2];

    const createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes> = async () => {
      return Result2.fail(errors);
    };

    const reservationRepo = new ReservationRepoFake();

    const createReservation = new CreateReservation({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      getCustomerAndBusinessUser,
      createReservationEvent,
      contextProvider,
      gsRepo,
    });

    const response = await createReservation.executeImpl(input);
    expectControllerMultipleErrors({
      response,
      errors,
    });
  });

  test(`when maximum number of pending reservations is reached`, async () => {

    // CustomerUsersInDb[0].id 00afe240-c39a-11ee-8adf-3f66c88fcbdd has 1 non-deleted non-canceled reservation for BusinessUsersInDb[0].id 00ad8920-c36d-11ee-8adf-3f66c88fcbdd
    const maxPendingReservations = 1;

    const gsRepo = new GeneralSettingsRepoFake();
    vi.spyOn(gsRepo, 'get').mockImplementation(
      async () => {
        const gs = GeneralSettings.createDefault({
          id: business.id,
          lng: pickLng(),
        });
        gs.update({
          ...gs.props,
          maxSimultaneousReservations: PositiveInt.create({ value: maxPendingReservations }).value,
        });
        return gs;
      }
    );

    const reservationRepo = new ReservationRepoFake();

    const createReservation = new CreateReservation({
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      getCustomerAndBusinessUser,
      createReservationEvent,
      contextProvider,
      gsRepo,
    });

    const response = await createReservation.executeImpl(input);

    expectControllerError({
      response,
      code: 403,
      error: 'CreateReservationErrors.MaxPendingReservationsReached',
    });
  });
});
