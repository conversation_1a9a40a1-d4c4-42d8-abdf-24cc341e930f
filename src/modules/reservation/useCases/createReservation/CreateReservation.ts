import { Request, Response } from './CreateReservationDTOs';
import { CreateReservationErrors } from './CreateReservationErrors';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { IReservationRepo } from '../../repos/IReservationRepo';
import {
  AppSync<PERSON>ontroller,
  FC2Handler,
  FCHandler,
} from '@shared/infra/Controllers';
import {
  CreateReservationEventReq,
  CreateReservationEventRes,
  GetCustomerAndBusinessUserReq,
  GetCustomerAndBusinessUserRes,
  IsBalanceEnoughReq,
  IsBalanceEnoughRes,
} from '../../external';
import { CustomerType, Reservation } from '../../domain/Reservation';
import { Dat } from '@shared/core/Dat';
import { formatErrors } from '@shared/core/AppError';
import { DateTime } from 'luxon';
import { IReservationOptionSrv } from '../../services/IReservationOptionSrv';
import { getLng } from '@shared/utils/utils';
import {
  ReservationByCustomerEvent,
} from '../../domain/events/ReservationByCustomerEvent';
import { CreateReservationEvents } from './CreateReservationEvents';
import { IGeneralSettingsRepo } from '../../repos/IGeneralSettingsRepo';
import { IContextProvider } from '@shared/context/IContextProvider';

// CreateReservation use case is used by CustomerUsers. dto.customerId should be taken from auth data.
export class CreateReservation extends AppSyncController<Request, Response> {
  private readonly reservationRepo: IReservationRepo;
  private readonly reservationOptionSrv: IReservationOptionSrv;
  private readonly createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes>;
  private readonly getCustomerAndBusinessUser: FC2Handler<GetCustomerAndBusinessUserReq, GetCustomerAndBusinessUserRes>;
  private readonly isBalanceEnough: FCHandler<IsBalanceEnoughReq, IsBalanceEnoughRes>;
  private readonly gsRepo: IGeneralSettingsRepo;

  public constructor(args: {
    reservationRepo: IReservationRepo;
    reservationOptionSrv: IReservationOptionSrv;
    isBalanceEnough: FCHandler<IsBalanceEnoughReq, IsBalanceEnoughRes>;
    createReservationEvent: FC2Handler<CreateReservationEventReq, CreateReservationEventRes>;
    getCustomerAndBusinessUser: FC2Handler<GetCustomerAndBusinessUserReq, GetCustomerAndBusinessUserRes>;
    gsRepo: IGeneralSettingsRepo;
    contextProvider: IContextProvider;
  }) {
    const {
      reservationRepo,
      reservationOptionSrv,
      isBalanceEnough,
      getCustomerAndBusinessUser,
      createReservationEvent,
      gsRepo,
      contextProvider,
    } = args;
    super({ contextProvider });
    this.reservationRepo = reservationRepo;
    this.reservationOptionSrv = reservationOptionSrv;
    this.isBalanceEnough = isBalanceEnough;
    this.createReservationEvent = createReservationEvent;
    this.getCustomerAndBusinessUser = getCustomerAndBusinessUser;
    CreateReservationEvents.registration(contextProvider.invoker());
    this.gsRepo = gsRepo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const {
      customerId,
      businessId,
      start: _start,
      roId,
      roVersion,
      lng: _lng,
    } = dto;
    const lng = getLng(_lng);

    const startOrError = Dat.create({ value: _start, lng });
    if (startOrError.isFailure)
      return formatErrors([new CreateReservationErrors.StartIsInvalid({ start: _start, lng }).setField('start')]);
    const start = startOrError.value;

    const isEnough = await this.isBalanceEnough({
      userId: businessId,
      requiredBalance: 1,
    });
    if (!isEnough)
      return formatErrors([new CreateReservationErrors.NoBalance({ lng })]);

    const checkedOrError = await this.reservationOptionSrv.checkResOpt({
      id: roId,
      version: roVersion,
      businessId,
      lng,
    });
    if (checkedOrError.isFailure)
      return formatErrors([checkedOrError.error!]);
    const reservationOptionDto = checkedOrError.value.toDto();

    const usersOrErrors = await this.getCustomerAndBusinessUser({
      customerId,
      businessId,
    });
    if (usersOrErrors.isFailure)
      return formatErrors(usersOrErrors.errors!);

    const users = usersOrErrors.value;
    const { business, customer } = users;

    const gs = await this.gsRepo.get(business.id);
    if (!gs)
      return formatErrors([new CreateReservationErrors.UserGSnotFound({ lng })]);

    const { firstName, lastName, phone, email } = customer;

    if (gs.phoneRequired && !phone)
      return formatErrors([new CreateReservationErrors.CustomerWithoutPhone({ lng })]);

    const pendingReservations = await this.reservationRepo.pendingReservations({
      userId: businessId,
      customerId,
    });
    const { maxSimultaneousReservations: { value: maxSimultaneousReservations } } = gs;
    if (pendingReservations >= maxSimultaneousReservations)
      return formatErrors([new CreateReservationErrors.MaxPendingReservationsReached({ max: maxSimultaneousReservations, lng })]);

    const reservationTime = DateTime.now()
      .setZone(reservationOptionDto.timeReference.zone)
      .startOf('second')
      .toISO({ suppressMilliseconds: true });

    let addToEventDescription;
    const businessLng = getLng(business.lng);
    switch (businessLng) {
      case 'es':
        addToEventDescription = `${firstName} ${lastName} (teléfono: ${phone}, email: ${email}) reservó a las ${reservationTime}`;
        break;
      case 'en':
        addToEventDescription = `${firstName} ${lastName} (phone: ${phone}, email: ${email}) reserved at ${reservationTime}`;
        break;
    }

    const { maxDaysAhead, minTimeBeforeService } = gs.reservationLimits;

    // Call synchronously CreateReservationEvent in module calendar
    const end = start.l.plus({
      minutes: reservationOptionDto.service.duration,
    });
    const creationOrErrors = await this.createReservationEvent({
      userId: businessId,
      identifier: reservationOptionDto.location.identifier,
      start: start.s,
      end: Dat.create({ value: end }).value.s,
      name: reservationOptionDto.name,
      addToEventDescription,
      lng,
      maxDaysAhead: maxDaysAhead.value,
      minTimeBeforeService: minTimeBeforeService.value,
    });
    if (creationOrErrors.isFailure)
      return formatErrors(creationOrErrors.errors!);

    const creation = creationOrErrors.value;
    console.log('creation', creation);

    const reservation = Reservation.create({
      userId: businessId,
      customerType: CustomerType.REGISTERED,
      customerId,
      customerFirstName: customer.firstName,
      customerLastName: customer.lastName,
      customerPhone: customer.phone,
      customerEmail: customer.email,
      start,
      eventId: creation.event.id,
      roId: roId,
      roZone: reservationOptionDto.timeReference.zone,
      srvName: reservationOptionDto.service.name,
      srvDuration: reservationOptionDto.service.duration,
      srvPrice: reservationOptionDto.service.price,
      srvDescription: reservationOptionDto.service.description,
      locName: reservationOptionDto.location.name,
      locId: reservationOptionDto.location.identifier,
    });

    const _dto = reservation.toDto();
    reservation.addEvent(new ReservationByCustomerEvent(_dto));

    await this.reservationRepo.create(reservation);

    return { status: Status.CREATED, result: _dto };
  }
}
