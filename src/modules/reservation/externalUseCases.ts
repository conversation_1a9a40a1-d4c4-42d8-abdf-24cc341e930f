// Si no sacaba los imports de estos handlers de ./external.ts, los unit tests daban error y la exportación de la librería @s4nt14go/book-backend daba error al usarla en el frontend
// region calendar
import {
  createHandler as _cancelReservationEventHandlerCreator,
} from '../calendar/useCases/cancelReservationEvent';
import {
  createHandler as _createReservationEventHandlerCreator,
} from '../calendar/useCases/createReservationEvent';
// Si no sacaba el import de getAvailableTimes de ./external.ts, los unit tests daban error
import {
  createHandler as _getAvailableTimesHandlerCreator,
} from '../calendar/useCases/getAvailableTimes';
// endregion
// region user
import {
  handler as _getMCustomerAndBusinessUserHandler,
} from '../user/useCases/getMCustomerAndBusinessUser';
import {
  handler as _getCustomerAndBusinessUserHandler,
} from '../user/useCases/getCustomerAndBusinessUser';
import {
  handler as _getBusinessUserFChandler,
} from '../user/useCases/getBusinessUserFC';
// endregion
// region balance
import { handler as _isBalanceEnoughHandler } from '../balance/useCases/isBalanceEnough';
// endregion

// region calendar
export const cancelReservationEventHandlerCreator = _cancelReservationEventHandlerCreator;
export const createReservationEventHandlerCreator = _createReservationEventHandlerCreator;
export const getAvailableTimesHandlerCreator = _getAvailableTimesHandlerCreator;
// endregion

// region user
export const getMCustomerAndBusinessUserHandler = _getMCustomerAndBusinessUserHandler;
export const getCustomerAndBusinessUserHandler = _getCustomerAndBusinessUserHandler;
export const getBusinessUserFChandler = _getBusinessUserFChandler;
/// endregion

// region balance
export const isBalanceEnoughHandler = _isBalanceEnoughHandler;
// endregion