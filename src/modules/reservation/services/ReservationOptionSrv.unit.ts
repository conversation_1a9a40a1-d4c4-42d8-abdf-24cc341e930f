import { describe, it, expect, test } from 'vitest';
import { ReservationOptionSrv } from './ReservationOptionSrv';
import { expectErrorResult, nonExistingId } from '@shared/utils/test';
import { ReservationOptionRepoFake } from '../repos/ReservationOptionRepoFake';
import { ReservationOptionRepo } from '../repos/ReservationOptionRepo';
import { ReservationOptionsInDb } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { getLng } from '@shared/utils/utils';

const lng = getLng();

describe('checkResOpt', () => {
  const reservationOptionRepo = new ReservationOptionRepoFake();
  const srv = new ReservationOptionSrv(reservationOptionRepo);
  const input = {
    id: ReservationOptionsInDb[0].id,
    version: ReservationOptionsInDb[0].version,
    businessId: ReservationOptionsInDb[0].userId,
    lng,
  };
  it('succeeds', async () => {
    const result = await srv.checkResOpt(input);

    expect(result.isSuccess).toBe(true);
    expect(result.value.toDto()).toMatchObject(
      ReservationOptionRepo.mapDb2Dto(ReservationOptionsInDb[0]),
    );
  });

  describe(`fails when `, () => {
    test(`reservation option is not found`, async () => {
      const result = await srv.checkResOpt({
        ...input,
        id: nonExistingId,
        lng,
      });

      expectErrorResult({
        result,
        error: 'ReservationOptionSrvErrors.ReservationOptionNotFound',
        code: 404,
      });
    });

    test(`reservation option is not active`, async () => {
      const result = await srv.checkResOpt({
        id: ReservationOptionsInDb[2].id,
        version: ReservationOptionsInDb[2].version,
        businessId: ReservationOptionsInDb[2].userId,
        lng,
      });

      expectErrorResult({
        result,
        error: 'ReservationOptionSrvErrors.ReservationOptionInactive',
        code: 400,
      });
    });

    test(`there is a version mismatch`, async () => {
      const result = await srv.checkResOpt({
        ...input,
        version: ReservationOptionsInDb[0].version + 1,
      });

      expectErrorResult({
        result,
        error: 'ReservationOptionSrvErrors.ReservationOptionVersionMismatch',
        code: 400,
      });
    });
  });
});
