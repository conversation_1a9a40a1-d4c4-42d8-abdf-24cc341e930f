import { Result } from '@shared/core/Result';
import { ReservationOptionSrvErrors } from './ReservationOptionSrvErrors';
import { IReservationOptionSrv } from './IReservationOptionSrv';
import { IReservationOptionRepo } from '../repos/IReservationOptionRepo';
import { ReservationOption } from '../domain/ReservationOption';
import { PossibleLngs } from '@shared/utils/utils';

export class ReservationOptionSrv implements IReservationOptionSrv {
  private repo: IReservationOptionRepo;

  public constructor(repo: IReservationOptionRepo) {
    this.repo = repo;
  }

  public async checkResOpt(args: {
    id: string;
    version: number;
    businessId: string;
    lng: PossibleLngs;
  }): Promise<Result<ReservationOption>> {
    const { id, version, businessId: userId, lng } = args;

    if (!this.repo) throw Error(`checkResOpt error: repo is not defined`);
    const reservationOption = await this.repo.get({
      userId,
      id,
    });
    console.log('reservationOption', reservationOption);

    if (!reservationOption)
      return Result.fail(
        new ReservationOptionSrvErrors.ReservationOptionNotFound({
          userId,
          id,
          lng,
        }),
      );

    if (!reservationOption.active)
      return Result.fail(
        new ReservationOptionSrvErrors.ReservationOptionInactive({
          roId: reservationOption.id.toString(),
          lng,
        }),
      );

    if (reservationOption.version !== version)
      return Result.fail(
        new ReservationOptionSrvErrors.ReservationOptionVersionMismatch({
          id: reservationOption.id.toString(),
          currentVersion: reservationOption.version,
          receivedVersion: version,
          lng,
        }),
      );

    return Result.ok(reservationOption);
  }
}
