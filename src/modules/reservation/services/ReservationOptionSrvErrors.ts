import { BadRequest, BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { Status } from '@shared/core/Status';
import { getLng, OptionalLng } from '@shared/utils/utils';

export namespace ReservationOptionSrvErrors {
  export class ReservationOptionNotFound extends BaseError {
    public constructor(args: { userId: string; id: string } & OptionalLng) {
      const t = trans[getLng(args.lng)];
      super({
        message: t.reservationOptionNotFound(args),
        status: Status.NOT_FOUND,
      });
    }
  }
  export class ReservationOptionInactive extends BadRequest {
    public constructor(args: { roId: string } & OptionalLng) {
      const { roId, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.reservationOptionInactive(roId),
      });
    }
  }
  export class ReservationOptionVersionMismatch extends BadRequest {
    public constructor(args: {
      id: string;
      currentVersion: number;
      receivedVersion: number;
    } & OptionalLng) {
      const t = trans[getLng(args.lng)];
      super({
        message: t.reservationOptionVersionMismatch(args),
      });
    }
  }
}

const trans = {
  en: {
    reservationOptionNotFound(args: {userId: string, id: string }) {
      const { userId, id } = args;
      return `Reservation option ${id} not found for user id ${userId}`;
    },
    reservationOptionInactive(v: string) {
      return `Reservation option ${v} is not active`;
    },
    reservationOptionVersionMismatch(args: {
      id: string;
      currentVersion: number;
      receivedVersion: number;
    }) {
      const { id, currentVersion, receivedVersion } = args;
      return `The received reservation option version was ${receivedVersion}, while the current is ${currentVersion} for reservation option id ${id}`;
    },
  },
  es: {
    reservationOptionNotFound(args: {userId: string, id: string }) {
      const { userId, id } = args;
      return `Opción de reserva ${id} no encontrada para el usuario ${userId}`;
    },
    reservationOptionInactive(v: string) {
      return `La opción de reserva ${v} no está activa`;
    },
    reservationOptionVersionMismatch(args: {
      id: string;
      currentVersion: number;
      receivedVersion: number;
    }) {
      const { id, currentVersion, receivedVersion } = args;
      return `La versión de la opción de reserva recibida fue ${receivedVersion}, mientras que la actual es ${currentVersion} para la opción de reserva ${id}`;
    },
  },
};

patch({ ReservationOptionSrvErrors });
