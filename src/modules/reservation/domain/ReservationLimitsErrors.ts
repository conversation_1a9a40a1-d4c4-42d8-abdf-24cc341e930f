import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { getLng, OptionalLng } from '@shared/utils/utils';

export namespace ReservationLimitsErrors {
  export class MinTimeBeforeServiceExceedsMaxDaysAhead extends BadRequest {
    public constructor(args: { maxDaysAhead: number } & OptionalLng) {
      const { maxDaysAhead, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.minTimeBeforeServiceExceedsMaxDaysAhead(maxDaysAhead),
      });
    }
  }
}

const trans = {
  en: {
    minTimeBeforeServiceExceedsMaxDaysAhead(daysAhead: number) {
      return `The minimum advance notice required for service provision is longer than the ${daysAhead}-day period set in which reservations can be made.`;
    },
  },
  es: {
    minTimeBeforeServiceExceedsMaxDaysAhead(daysAhead: number) {
      return `La antelación mínima requerida para la prestación del servicio excede los ${daysAhead} días configurados en los que se permite reservar.`;
    },
  },
};

patch({ ReservationLimitsErrors });
