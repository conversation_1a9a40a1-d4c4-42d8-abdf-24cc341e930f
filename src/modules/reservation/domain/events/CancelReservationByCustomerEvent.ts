import { ReservationDto } from '../Reservation';
import {
  ReservationByBusinessEvent,
  ReservationByBusinessEventDto,
} from './ReservationByBusinessEvent';
import { DomainEventTypes } from '@shared/events/DomainEventTypes';

export type CancelReservationByCustomerEventDto = ReservationByBusinessEventDto;

export class CancelReservationByCustomerEvent extends ReservationByBusinessEvent {
  public constructor(reservation: ReservationDto) {
    super(reservation);
    this.type = DomainEventTypes.CancelReservationByCustomerEvent;
  }
}