import { DomainEventTypes } from '@shared/events/DomainEventTypes';
import { ReservationDto } from '../Reservation';
import {
  DomainEventBase,
  DomainEventBaseDto,
} from '@shared/events/DomainEventBase';

export type ReservationByBusinessEventDto = DomainEventBaseDto & {
  reservation: ReservationDto;
}

export class ReservationByBusinessEvent extends DomainEventBase {
  public reservation;

  public constructor(reservation: ReservationDto) {
    super({
      aggregateId: reservation.id,
      type: DomainEventTypes.ReservationByBusinessEvent,
    });
    this.reservation = reservation;
  }

  public toDto(): ReservationByBusinessEventDto {
    return {
      ...this._toDto(),
      reservation: this.reservation,
    };
  }
}