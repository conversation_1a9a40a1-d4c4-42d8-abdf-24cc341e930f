import { ReservationDto } from '../Reservation';
import {
  ReservationByBusinessEvent,
  ReservationByBusinessEventDto,
} from './ReservationByBusinessEvent';
import { DomainEventTypes } from '@shared/events/DomainEventTypes';

export type CancelReservationByBusinessEventDto = ReservationByBusinessEventDto;

export class CancelReservationByBusinessEvent extends ReservationByBusinessEvent {
  public constructor(reservation: ReservationDto) {
    super(reservation);
    this.type = DomainEventTypes.CancelReservationByBusinessEvent;
  }
}