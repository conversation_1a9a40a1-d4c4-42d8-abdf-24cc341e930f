import { ReservationDto } from '../Reservation';
import {
  ReservationByBusinessEvent,
  ReservationByBusinessEventDto,
} from './ReservationByBusinessEvent';
import { DomainEventTypes } from '@shared/events/DomainEventTypes';

export type ReservationByCustomerEventDto = ReservationByBusinessEventDto;

export class ReservationByCustomerEvent extends ReservationByBusinessEvent {
  public constructor(reservation: ReservationDto) {
    super(reservation);
    this.type = DomainEventTypes.ReservationByCustomerEvent;
  }
}