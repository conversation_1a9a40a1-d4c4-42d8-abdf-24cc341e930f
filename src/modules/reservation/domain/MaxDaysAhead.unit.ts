import { expect, it, describe } from 'vitest';
import { MaxDaysAhead } from './MaxDaysAhead';
import {
  expectErrorsResult,
  expectErrorsResultContaining,
} from '@shared/utils/test';

it(`creates`, () => {
  const result = MaxDaysAhead.create({ value: 1 });

  expect(result).toMatchObject({
    isSuccess: true,
    value: {
      value: 1,
    },
  });
});

describe(`creation fails if value`, () => {
  it(`isn't a valid PositiveInt number`, () => {
    const result = MaxDaysAhead.create({ value: 0 });

    expectErrorsResultContaining({
      result,
      errorContaining: `PositiveIntErrors.`,
      code: 400,
    });
  });

  it(`is greater than ${MaxDaysAhead.MAX}`, () => {
    const result = MaxDaysAhead.create({ value: MaxDaysAhead.MAX + 1 });

    expectErrorsResult({
      result,
      error: `MaxDaysAheadErrors.ExceedsMax`,
      code: 400,
    });
  });
});
