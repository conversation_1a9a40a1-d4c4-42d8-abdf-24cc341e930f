import { AggregateRoot } from '@shared/core/domain/AggregateRoot';
import { EntityID } from '@shared/core/domain/EntityID';
import { Location, LocationDto } from './Location';
import { Service, ServiceDto } from './Service';
import { Name } from './Name';
import { TimeReference, TimeReferenceDto } from './TimeReference';
import { Every } from './Every';
import { Price } from './Price';
import { Identifier } from '../external';
import { Spread } from '@shared/utils/utils';
import { ExplanatoryNotes, ExplanatoryNotesDto } from './ExplanatoryNotes';
import { PositiveInt } from '@shared/core/PositiveInt';
import { Description } from './Description';
import { IContextProvider } from '@shared/context/IContextProvider';

// Para evitar que se reserve con una ReservationOption que fue updated, al reservar, se envía ReservationOption.id y también ReservationOption.version. Esto es para contemplar casos como cuando el CustomerUser o BusinessUser (al reservar para un ManuallyCreatedUser) obtiene las ReservationOptions, pero antes de que intente reservar, el BusinessUser actualiza las ReservationOptions (por ejemplo, modifica el service, precio, descripción, etc.). Si no envío la version, quien reserva cree que está reservando una cosa cuando en realidad no es así. Ver src/modules/reservation/useCases/createReservation/CreateReservation.ts y reservation/useCases/createReservationByBusiness/CreateReservationByBusiness.ts que usan checkResOpt() de reservation/services/ReservationOptionSrv.ts.

export type ReservationOptionUpdateInput = {
  name: Name;
  service: Service;
  location: Location;
  timeReference: TimeReference;
  every: Every;
  explanatoryNotes: ExplanatoryNotes;
  active: boolean;
};

type ReservationOptionCreateInput = ReservationOptionUpdateInput & {
  userId: string; // linked to business user in module user
};

type ReservationOptionProps = ReservationOptionCreateInput & {
  version: number;
};

export type ReservationOptionDto = Spread<
  ReservationOptionProps,
  {
    name: string;
    service: ServiceDto;
    location: LocationDto;
    timeReference: TimeReferenceDto;
    explanatoryNotes: ExplanatoryNotesDto;
    id: string;
    every: number;
  }
>;

export class ReservationOption extends AggregateRoot<
  ReservationOptionProps,
  ReservationOptionDto
> {
  private __class = this.constructor.name;
  public declare props: ReservationOptionProps;

  get userId(): string {
    return this.props.userId;
  }

  get active(): boolean {
    return this.props.active;
  }

  get service(): Service {
    return this.props.service;
  }

  get location(): Location {
    return this.props.location;
  }

  get name(): Name {
    return this.props.name;
  }

  get timeReference(): TimeReference {
    return this.props.timeReference;
  }

  get explanatoryNotes(): ExplanatoryNotes {
    return this.props.explanatoryNotes;
  }

  get version(): number {
    return this.props.version;
  }

  get every(): Every {
    return this.props.every;
  }

  private constructor(props: ReservationOptionProps, id?: EntityID) {
    super(props, id);
  }

  public static create(props: ReservationOptionCreateInput): ReservationOption {
    return new ReservationOption({
      ...props,
      version: 1,
    });
  }

  public update(data: ReservationOptionUpdateInput): void {
    // These are the properties that any change in them (done by a business user) will increase the version of the ReservationOption. This means that when a new reservation is trying to be booked (by a customer), the request will be rejected if there was any change in these properties in between the time the customer got the reservation options and the time they tried to book the reservation.
    const { service, timeReference, location, explanatoryNotes, every } = data;
    // name is not included because it isn't shown to the customer
    // active is not included as it's checked in the CreateReservation use case

    let increaseVersion = false;
    checkVersionedFields: {
      if (!this.props.service.equals(service)) {
        increaseVersion = true;
        break checkVersionedFields;
      }
      if (!this.props.every.equals(every)) {
        increaseVersion = true;
        break checkVersionedFields;
      }
      if (!this.props.timeReference.equals(timeReference)) {
        increaseVersion = true;
        break checkVersionedFields;
      }
      if (
        !this.props.location.identifier.equals(location.identifier) ||
        this.props.location.showInfoWhenReserving !==
          location.showInfoWhenReserving ||
        (location.showInfoWhenReserving && !this.props.location.equals(location))
      ) {
        increaseVersion = true;
        break checkVersionedFields;
      }
      if (!this.props.explanatoryNotes.equals(explanatoryNotes)) {
        increaseVersion = true;
        break checkVersionedFields;
      }
    }

    this.props = {
      ...data,
      userId: this.props.userId,
      version: increaseVersion ? this.props.version + 1 : this.props.version,
    };
  }

  public enable(): void {
    this.props.active = true;
  }

  public disable(): void {
    this.props.active = false;
  }

  public toDto(): ReservationOptionDto {
    const {
      service,
      location,
      name,
      timeReference,
      explanatoryNotes,
      every,
      ...rest
    } = this.props;

    return {
      ...rest,
      id: this.id.toString(),
      service: service.toDto(),
      location: location.toDto(),
      name: name.value,
      every: every.value,
      timeReference: timeReference.toDto(),
      explanatoryNotes: explanatoryNotes.toDto(),
    };
  }

  public static assemble(dto: ReservationOptionDto, sendAnalytics: IContextProvider['sendAnalytics']): ReservationOption {
    const {
      id,
      service: srvDto,
      location: locDto,
      name,
      timeReference,
      ...rest
    } = dto;
    const service = Service.create({
      ...srvDto,
      name: Name.create({ value: srvDto.name }).value,
      description: srvDto.description === null? null : Description.create({ value: srvDto.description }).value,
      duration: PositiveInt.create({ value: srvDto.duration }).value,
      price: srvDto.price ? Price.create({ value: srvDto.price }).value : null,
    });
    const location = Location.create({
      ...locDto,
      name: Name.create({ value: locDto.name }).value,
      description: locDto.description === null? null : Description.create({ value: locDto.description }).value,
      identifier: Identifier.create({ value: locDto.identifier }, sendAnalytics).value,
    });
    return new ReservationOption(
      {
        ...rest,
        service,
        location,
        name: Name.create({ value: name }).value,
        every: Every.create({ value: dto.every }).value,
        timeReference: TimeReference.create(timeReference).value,
        explanatoryNotes: ExplanatoryNotes.create(dto.explanatoryNotes),
      },
      new EntityID(id),
    );
  }

  public setActive(active: boolean) {
    this.props.active = active;
  }
}
