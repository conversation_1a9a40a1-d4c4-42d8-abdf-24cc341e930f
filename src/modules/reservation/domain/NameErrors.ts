import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { getLng, OptionalLng } from '@shared/utils/utils';

export namespace NameErrors {
  export class TooShort extends BadRequest {
    public constructor(args: { minLength: number; input: string } & OptionalLng) {
      const { minLength, input, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];
      super({
        message: (input === '')?
          `${t.itShouldBe} ${minLength} ${t.longButEmpty}`:
          `${t.itShouldBe} ${minLength} ${t.longButEntered} ${input}"`,
      });
    }
  }
}

type Key = keyof typeof trans.en & keyof typeof trans.es;
const trans = {
  en: {
    itShouldBe: 'It should be at least',
    longButEmpty: `characters long, but you haven't entered anything.`,
    longButEntered: 'characters long, but you entered:',
  },
  es: {
    itShouldBe: 'Debe ser de al menos',
    longButEmpty: 'caracteres, pero no has introducido nada.',
    longButEntered: 'caracteres, pero has introducido: "',
  },
};

patch({ NameErrors });
