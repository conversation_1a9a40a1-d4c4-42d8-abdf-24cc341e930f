import { expect, test } from 'vitest';
import { ExplanatoryNotes } from './ExplanatoryNotes';
import { orNull } from '@shared/utils/test';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

test(`trimming`, () => {
  const notes = ExplanatoryNotes.create({
    while: ' test - while ',
    after: ' test - after ',
  });
  expect(notes.toDto()).toMatchObject({
    while: 'test - while',
    after: 'test - after',
  });
});

test(`creation with possibly null arguments`, () => {
  const _while = orNull(chance.string());
  const after = orNull(chance.string());
  const notes = ExplanatoryNotes.create({ while: _while, after });

  expect(notes.toDto()).toMatchObject({
    while: _while,
    after,
  });
});
