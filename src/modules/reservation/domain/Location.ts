import { IdentifierT } from '../external';
import { ValueObject } from '@shared/core/domain/ValueObject';
import { Spread } from '@shared/utils/utils';
import { Name } from './Name';
import { Description } from './Description';

type LocationProps = {
  name: Name;
  description: Description | null;
  identifier: IdentifierT;
  showInfoWhenReserving: boolean;
};

export type LocationDto = Spread<
  LocationProps,
  {
    name: string;
    description: string | null;
    identifier: string;
  }
>;

export class Location extends ValueObject<LocationProps, LocationDto> {
  private __class = this.constructor.name;

  get name(): Name {
    return this.props.name;
  }

  get description(): Description | null {
    return this.props.description;
  }

  get identifier(): IdentifierT {
    return this.props.identifier;
  }

  get showInfoWhenReserving(): boolean {
    return this.props.showInfoWhenReserving;
  }

  private constructor(props: LocationProps) {
    super(props);
  }

  public static create(props: LocationProps): Location {
    return new Location(props);
  }

  public toDto(): LocationDto {
    return {
      ...this.props,
      name: this.props.name.value,
      description: this.props.description?.value ?? null,
      identifier: this.props.identifier.value,
    };
  }
}
