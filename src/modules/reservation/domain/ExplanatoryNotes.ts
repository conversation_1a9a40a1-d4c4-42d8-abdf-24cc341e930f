import { ValueObject } from '@shared/core/domain/ValueObject';

interface ExplanatoryNotesProps {
  while: string | null;
  after: string | null;
}

export type ExplanatoryNotesDto = ExplanatoryNotesProps;
export type ExplanatoryNotesInput = ExplanatoryNotesProps;

export class ExplanatoryNotes extends ValueObject<
  ExplanatoryNotesProps,
  ExplanatoryNotesDto
> {
  private __class = this.constructor.name;
  get while(): string | null {
    return this.props.while;
  }
  get after(): string | null {
    return this.props.after;
  }

  private constructor(props: ExplanatoryNotesProps) {
    super(props);
  }

  public toDto(): ExplanatoryNotesDto {
    return this.props;
  }

  public static create(args: ExplanatoryNotesInput): ExplanatoryNotes {
    return new ExplanatoryNotes({
      while: args.while?.trim() ?? null,
      after: args.after?.trim() ?? null,
    });
  }
}
