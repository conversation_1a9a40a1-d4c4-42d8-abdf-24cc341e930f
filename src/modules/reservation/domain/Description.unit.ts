import { expect, test } from 'vitest';
import { Description } from './Description';
import { Name } from './Name';

test(`Check Description is different from Name even though extend from it`, () => {
  const value = ' test  - something ';

  const result = Description.create({ value });
  expect(result.isSuccess).toBe(true);
  const description = result.value;
  expect(description.value).toBe('test - something');
  expect(description).toBeInstanceOf(Description);

  const result2 = Name.create({ value });
  const name = result2.value;

  expect(description.equals(name)).toBeFalsy();
  expect(name.equals(description)).toBeFalsy();
});
