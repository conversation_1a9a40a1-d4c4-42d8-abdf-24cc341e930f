import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { NameErrors } from './NameErrors';
import { OptionalLng } from '@shared/utils/utils';

type NameDto = string;

export type NameInput = {
  value: string;
}

export interface NameProps {
  value: NameDto;
}

export class Name extends ValueObject<NameProps, NameDto> {
  protected __class = this.constructor.name;
  public static MIN_LENGTH = 1;

  get value(): string {
    return this.props.value;
  }

  protected constructor(props: NameProps) {
    super(props);
  }

  public static create(args: NameInput & OptionalLng): Result<Name> {
    const { value, lng } = args;
    const trimmed = value.trim();
    const trimmed2 = trimmed.replace(/\s+/g, ' '); // replace duplicate spaces by just one
    return Result.convertValue(trimmed2)
      .ensure(
        (value: string) => {
          return value.length >= Name.MIN_LENGTH;
        },
        new NameErrors.TooShort({ minLength: Name.MIN_LENGTH, input: trimmed2, lng }),
      )
      .onBoth((result) =>
        result.isSuccess
          ? Result.ok<Name>(new Name({ value: result.value }))
          : Result.fail(result.error!),
      );
  }

  public toDto(): NameDto {
    return this.props.value;
  }
}
