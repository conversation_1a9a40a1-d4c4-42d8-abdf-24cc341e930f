import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { MaxDaysAhead } from './MaxDaysAhead';
import { getLng, PossibleLngs } from '@shared/utils/utils';

export namespace MaxDaysAheadErrors {
  export class ExceedsMax extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, (max: number) => string> = trans[getLng(lng)];
      super({
        message: t.exceedsMax(MaxDaysAhead.MAX),
      });
    }
  }
}

type Key = keyof typeof trans.en & keyof typeof trans.es;
const trans = {
  en: {
    exceedsMax(max: number) {
      return `The required number of days in advance cannot exceed ${max}.`;
    },
  },
  es: {
    exceedsMax(max: number) {
      return `La cantidad de días requeridas de antelación no puede ser mayor a ${max}.`;
    },
  },
};

patch({ MaxDaysAheadErrors });
