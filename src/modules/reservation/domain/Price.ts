import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { PriceErrors } from './PriceErrors';
import { UtilsClass } from '@shared/utils/UtilsClass';
import { OptionalLng } from '@shared/utils/utils';

type PriceDto = number;

interface PriceProps {
  value: PriceDto;
}

export class Price extends ValueObject<PriceProps, PriceDto> {
  private __class = this.constructor.name;
  get value(): number {
    return this.props.value;
  }

  private constructor(props: PriceProps) {
    super(props);
  }

  public static create(args: { value: number } & OptionalLng): Result<Price> {
    const { value, lng } = args;
    if (value < 0) return Result.fail(new PriceErrors.CantBeNegative(lng));

    const roundedOrError = UtilsClass.roundToTwoDecimals({ input: value, lng });
    if (roundedOrError.isFailure)
      return Result.fail(roundedOrError.error!);

    return Result.ok<Price>(new Price({ value: roundedOrError.value }));
  }

  public toDto(): PriceDto {
    return this.value;
  }
}
