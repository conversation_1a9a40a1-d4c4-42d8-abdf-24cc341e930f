import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { getLng, PossibleLngs } from '@shared/utils/utils';

export namespace PriceErrors {
  export class CantBeNegative extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({ message: t.cantBeNegative });
    }
  }
}

const trans = {
  en: {
    cantBeNegative: `Price can't be negative.`,
  },
  es: {
    cantBeNegative: `El precio no puede ser negativo.`,
  },
};

patch({ PriceErrors });
