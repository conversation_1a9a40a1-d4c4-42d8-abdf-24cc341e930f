import { AggregateRoot } from '@shared/core/domain/AggregateRoot';
import { EntityID } from '@shared/core/domain/EntityID';
import { N0 } from '@shared/core/N0';
import { Slug } from '@shared/core/Slug';
import { PossibleLngs, Spread } from '@shared/utils/utils';
import { PositiveInt } from '@shared/core/PositiveInt';
import { ReservationLimits, ReservationLimitsDto } from './ReservationLimits';
import { MaxDaysAhead } from './MaxDaysAhead';
import { ReminderTemplate } from './ReminderTemplate';

type GeneralSettingsProps = {
  cancellationUpTo: N0; // in minutes
  phoneRequired: boolean;
  maxSimultaneousReservations: PositiveInt;
  reservationLimits: ReservationLimits;
  reminderTemplate: ReminderTemplate;
  // Store information
  title: string | null;
  welcome: string | null;
  slug: Slug | null;
}

export type GeneralSettingsDto = Spread<GeneralSettingsProps, {
  id: string; // the same as the business userId
  cancellationUpTo: number;
  maxSimultaneousReservations: number;
  reservationLimits: ReservationLimitsDto;
  reminderTemplate: string;
  slug: string | null;
}>;

export class GeneralSettings extends AggregateRoot<
  GeneralSettingsProps,
  GeneralSettingsDto
> {
  private __class = this.constructor.name;
  public declare props: GeneralSettingsProps;

  get cancellationUpTo(): N0 {
    return this.props.cancellationUpTo;
  }

  get maxSimultaneousReservations(): PositiveInt {
    return this.props.maxSimultaneousReservations;
  }

  get reservationLimits(): ReservationLimits {
    return this.props.reservationLimits;
  }

  get title(): string | null {
    return this.props.title;
  }

  get welcome(): string | null {
    return this.props.welcome;
  }

  get slug(): Slug | null {
    return this.props.slug;
  }

  get phoneRequired(): boolean {
    return this.props.phoneRequired;
  }

  get reminderTemplate(): ReminderTemplate {
    return this.props.reminderTemplate;
  }

  private constructor(props: GeneralSettingsProps, id?: EntityID) {
    super(props, id);
  }

  public static createDefault(args: {id: string, lng: PossibleLngs }): GeneralSettings {
    const { id, lng } = args;
    return new GeneralSettings(
      {
        cancellationUpTo: N0.create({ value: 60 * 24 }).value, // default 24 hours
        title: null,
        welcome: null,
        slug: null,
        phoneRequired: true,
        maxSimultaneousReservations: PositiveInt.create({ value: 2 }).value,  // default 2 simultaneous reservations max
        reservationLimits: ReservationLimits.createDefault(),
        reminderTemplate: ReminderTemplate.createDefault(lng),
      },
      new EntityID(id),
    );
  }

  public update(props: GeneralSettingsProps) {
    this.props = props;
  }

  public toDto(): GeneralSettingsDto {
    return {
      ...this.props,
      slug: this.props.slug?.value ?? null,
      cancellationUpTo: this.cancellationUpTo.value,
      maxSimultaneousReservations: this.maxSimultaneousReservations.value,
      reservationLimits: this.reservationLimits.toDto(),
      reminderTemplate: this.reminderTemplate.toDto(),
      id: this.id.toString(),
    };
  }

  public static assemble(dto: GeneralSettingsDto): GeneralSettings {
    const {
      id, cancellationUpTo, maxSimultaneousReservations,
      reservationLimits: { maxDaysAhead, minTimeBeforeService },
      reminderTemplate,
      ...rest
    } = dto;

    return new GeneralSettings(
      {
        ...rest,
        cancellationUpTo: N0.create({ value: cancellationUpTo }).value,
        maxSimultaneousReservations: PositiveInt.create({ value: maxSimultaneousReservations }).value,
        reservationLimits: ReservationLimits.create({
          maxDaysAhead: MaxDaysAhead.create({ value: maxDaysAhead }).value,
          minTimeBeforeService: N0.create({ value: minTimeBeforeService }).value,
        }).value,
        slug:
          dto.slug !== null
            ? Slug.create({ slug: dto.slug }).value
            : null,
        reminderTemplate: ReminderTemplate.create({ value: reminderTemplate }).value,
      },
      new EntityID(id),
    );
  }
}
