import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result2 } from '@shared/core/Result2';
import { BaseError } from '@shared/core/AppError';
import { ReminderTemplateErrors } from './ReminderTemplateErrors';
import { OptionalLng, PossibleLngs } from '@shared/utils/utils';


export type ReminderTemplateInput = {
  value: string;
}
type ReminderTemplateProps  = ReminderTemplateInput;
type ReminderTemplateDto = ReminderTemplateProps['value'];


export class ReminderTemplate extends ValueObject<ReminderTemplateProps, ReminderTemplateDto> {
  private __class = this.constructor.name;
  public static MAX_LENGTH = 500;

  get value(): string {
    return this.props.value;
  }

  private constructor(props: ReminderTemplateProps) {
    super(props);
  }

  public static createDefault(lng: PossibleLngs): ReminderTemplate {
    switch (lng) {
      case 'es':
        return ReminderTemplate.create({
          value: `¡Hola {firstName}! Este es un recordatorio de tu cita para {service} el {day} a las {time}. Por favor, confirma si estarás presente.`,
        }).value;
      case 'en':
        return ReminderTemplate.create({
          value: `Hello {firstName}! This is a reminder for your appointment for {service} on {day} at {time}. Please confirm if you'll be attending.`,
        }).value;
    }
  }

  public static create(args: ReminderTemplateInput & OptionalLng): Result2<ReminderTemplate> {
    const { value, lng } = args;

    if (value.trim() === '')
      return Result2.ok<ReminderTemplate>(new ReminderTemplate({  value: '' }));

    const errors: BaseError[] = [];

    if (value.length > ReminderTemplate.MAX_LENGTH) {
      errors.push(new ReminderTemplateErrors.TooLong(lng));
    }

    if (errors.length > 0) {
      return Result2.fail(errors);
    }

    return Result2.ok<ReminderTemplate>(new ReminderTemplate(args));
  }

  public replaceVariables(args: {
    firstName: string | null;
    service: string;
    timeZone: string;
    start: string;
    lng: string;
  }): string {
    const { firstName: _firstName, service, timeZone, start, lng } = args;
    const firstName = _firstName ?? '';

    const day = new Intl.DateTimeFormat(lng, {
      weekday: 'long',
      day: 'numeric',
      month: 'long',
      timeZone,
    }).format(new Date(start));
    const time = new Intl.DateTimeFormat(lng, {
      hour: 'numeric',
      minute: 'numeric',
      hourCycle: 'h23',
      timeZone,
    }).format(new Date(start));

    // Replace template variables with actual values, allowing for spaces inside the curly braces
    return this.value
      .replace(/{\s*firstName\s*}/g, firstName)
      .replace(/{\s*service\s*}/g, service)
      .replace(/{\s*day\s*}/g, day)
      .replace(/{\s*time\s*}/g, time);
  }

  public toDto(): ReminderTemplateDto {
    return this.props.value;
  }

  public static assemble(dto: ReminderTemplateDto): ReminderTemplate {
    return ReminderTemplate.create({ value: dto }).value;
  }
}
