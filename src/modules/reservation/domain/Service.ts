import { Price } from './Price';
import { Name } from './Name';
import { ValueObject } from '@shared/core/domain/ValueObject';
import { Spread } from '@shared/utils/utils';
import { PositiveInt } from '@shared/core/PositiveInt';
import { Description } from './Description';

type ServiceProps = {
  name: Name;
  description: Description | null;
  duration: PositiveInt;
  price: Price | null;
};

export type ServiceDto = Spread<
  ServiceProps,
  {
    name: string;
    description: string | null;
    duration: number;
    price: number | null;
  }
>;

export class Service extends ValueObject<ServiceProps, ServiceDto> {
  private __class = this.constructor.name;

  get name(): Name {
    return this.props.name;
  }

  get description(): Description | null {
    return this.props.description;
  }

  get duration(): PositiveInt {
    return this.props.duration;
  }

  get price(): Price | null {
    return this.props.price;
  }

  private constructor(props: ServiceProps) {
    super(props);
  }

  public static create(props: ServiceProps): Service {
    return new Service(props);
  }

  public toDto(): ServiceDto {
    const { name, duration, price, ...rest } = this.props;

    return {
      ...rest,
      name: name.value,
      description: this.props.description ? this.props.description.value : null,
      duration: duration.value,
      price: price ? price.value : null,
    };
  }
}
