import { ValueObject } from '@shared/core/domain/ValueObject';
import { MaxDaysAheadErrors } from './MaxDaysAheadErrors';
import { PositiveInt } from '@shared/core/PositiveInt';
import { getLng, OptionalLng } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { BaseError } from '@shared/core/AppError';

interface MaxDaysAheadProps {
  value: number;
}

type MaxDaysAheadDto = number;

export class MaxDaysAhead extends ValueObject<MaxDaysAheadProps, MaxDaysAheadDto> {
  private __class = this.constructor.name;
  public static readonly MAX = 365;

  get value(): number {
    return this.props.value;
  }

  private constructor(props: MaxDaysAheadProps) {
    super(props);
  }

  public static create(args: { value: number } & OptionalLng): Result2<MaxDaysAhead> {
    const { value, lng } = args;
    let errors: BaseError[] = [];
    const positiveIntOrErrors = PositiveInt.create({ value, lng: getLng(lng) });
    if (positiveIntOrErrors.isFailure)
      errors = errors.concat(positiveIntOrErrors.errors!);

    if (value > MaxDaysAhead.MAX)
      errors = errors.concat([new MaxDaysAheadErrors.ExceedsMax(lng)]);

    if (errors.length) return Result2.fail(errors);

    const positiveInt = positiveIntOrErrors.value;

    return Result2.ok<MaxDaysAhead>(new MaxDaysAhead({ value: positiveInt.value }));
  }

  public toDto(): MaxDaysAheadDto {
    return this.value;
  }
}
