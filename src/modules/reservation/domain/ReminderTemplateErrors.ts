import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { getLng, PossibleLngs } from '@shared/utils/utils';
import { ReminderTemplate } from './ReminderTemplate';

export namespace ReminderTemplateErrors {
  export class TooLong extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({
        message: `${t.itShouldBe} ${t.most} ${ReminderTemplate.MAX_LENGTH} ${t.characters}`,
      });
    }
  }
}

const trans = {
  en: {
    itShouldBe: 'Template should be at least',
    most: 'most',
    characters: 'characters',
  },
  es: {
    itShouldBe: 'La plantilla debe tener',
    most: 'no más de',
    characters: 'caracteres',
  },
};

patch({ ReminderTemplateErrors });
