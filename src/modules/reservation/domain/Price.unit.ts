import { expect, test, describe } from 'vitest';
import { Price } from './Price';
import { UtilsClass } from '@shared/utils/UtilsClass';
import { expectErrorResultContaining } from '@shared/utils/test';

test(`Creates a new instance`, () => {
  const priceOrError = Price.create({ value: 0.005 });

  expect(priceOrError.isSuccess).toBe(true);
  expect(priceOrError.value).toBeInstanceOf(Price);
  expect(priceOrError.value.value).toBe(0.01);
});

describe(`fails`, () => {
  test(`with negative price`, () => {
    const priceOrError = Price.create({ value: -1 });

    expect(priceOrError.isFailure).toBe(true);
    expect(priceOrError.error).toMatchObject({
      message: expect.any(String),
      type: 'PriceErrors.CantBeNegative',
      status: 400,
    });
  });

  test(`when rounding errors`, () => {
    const result = Price.create({ value: UtilsClass.MAX_ROUNDING + 1 });

    expectErrorResultContaining({
      result,
      errorContaining: 'UtilsClassErrors.',
      code: 400,
    });
  });
});
