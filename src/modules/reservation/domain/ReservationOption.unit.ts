import { expect, test, it, describe } from 'vitest';
import { ReservationOption } from './ReservationOption';
import { Name } from './Name';
import { TimeReference } from './TimeReference';
import { uuidFormat } from '@shared/utils/utils';
import { createDescription, createService } from '../utils/test';
import { ExplanatoryNotes } from './ExplanatoryNotes';
import { createName } from '../utils/test';
import { Location } from './Location';
import { Identifier } from '../external';
import { Every } from './Every';
import {
  createLocation,
  createReservationOption,
  getCreateResOptReq,
} from '../utils/testExternal';
import { Description } from './Description';
import { orNull } from '@shared/utils/test';
import { sendAnalyticsDummy as sendAnalytics } from '@shared/utils/utils';

function getInputs() {
  const { service, serviceDto } = createService();
  const { location, locationDto } = createLocation();
  const dto = {
    ...getCreateResOptReq(),
    service: serviceDto,
    location: locationDto,
  };
  const args = {
    ...dto,
    name: Name.create({ value: dto.name }).value,
    service,
    location,
    timeReference: TimeReference.create({ ...dto.timeReference }).value,
    explanatoryNotes: ExplanatoryNotes.create(dto.explanatoryNotes),
    every: Every.create({ value: dto.every }).value,
  };
  return { dto, args };
}

it(`create & assemble back`, () => {
  const { dto, args } = getInputs();
  const created = ReservationOption.create(args);

  const defaultProps = {
    version: 1,
  };

  expect(created.toDto()).toMatchObject({
    ...dto,
    ...defaultProps,
    id: expect.stringMatching(uuidFormat),
  });

  const assembled = ReservationOption.assemble({
    ...dto,
    ...defaultProps,
    id: created.id.toString(),
  }, sendAnalytics);

  expect(assembled.equals(created)).toBe(true);
});

test(`enable & disable`, () => {
  const { args } = getInputs();
  const created = ReservationOption.create(args);
  created.disable();
  expect(created.toDto()).toMatchObject({
    active: false,
  });
  created.enable();
  expect(created.toDto()).toMatchObject({
    active: true,
  });
});

describe(`update`, () => {
  it(`increases version`, () => {
    const { args: createArgs } = getInputs();
    const resOpt = ReservationOption.create(createArgs);
    const id = resOpt.id.toString();
    const { dto: updateDto, args: updateArgs } = getInputs();
    resOpt.update(updateArgs);
    expect(resOpt.toDto()).toMatchObject({
      ...updateDto,
      version: 2,
      userId: createArgs.userId,
      id,
    });
  });

  it(`does not increase version when updating fields: name and active`, () => {
    const { args: createArgs } = getInputs();
    const resOpt = ReservationOption.create(createArgs);
    resOpt.update({
      ...createArgs,
      name: createName(),
      active: !resOpt.active,
    });
    expect(resOpt.toDto()).toMatchObject({
      version: 1,
    });
  });

  describe(`location`, () => {
    it(`increase version when changing showInfoWhenReserving`, () => {
      const { args: createArgs } = getInputs();
      const resOpt = ReservationOption.create(createArgs);
      resOpt.update({
        ...createArgs,
        location: Location.create({
          ...createArgs.location.toDto(),
          name: createArgs.location.name,
          description: createArgs.location.description,
          identifier: Identifier.create({ value: createArgs.location.identifier.value }, sendAnalytics)
            .value,
          showInfoWhenReserving: !createArgs.location.showInfoWhenReserving,
        }),
      });
      expect(resOpt.toDto()).toMatchObject({
        version: 2,
      });
    });

    it(`does not increase version when updating location.name & description but location.showInfoWhenReserving is false`, () => {
      const { args: createArgs } = getInputs();
      const resOpt = ReservationOption.create(createArgs);
      resOpt.update({ // Update ReservationOption to make sure showInfoWhenReserving is false
        ...createArgs,
        location: Location.create({
          ...createArgs.location.toDto(),
          name: Name.create({ value: createArgs.location.name.value }).value,
          description: createArgs.location.description === null? null : Description.create({ value: createArgs.location.description.value }).value,
          identifier: Identifier.create({ value: createArgs.location.identifier.value }, sendAnalytics)
            .value,
          showInfoWhenReserving: false,
        }),
      });
      const originalVersion = resOpt.version;

      resOpt.update({
        ...createArgs,
        location: Location.create({
          ...createArgs.location.toDto(),
          identifier: Identifier.create({ value: createArgs.location.identifier.value }, sendAnalytics).value,
          name: createName(),
          description: orNull(createDescription()),
          showInfoWhenReserving: false,
        }),
      });

      expect(resOpt.toDto()).toMatchObject({
        version: originalVersion,
      });
    });
  });
});

test(`setActive`, () => {
  const initialRO = createReservationOption().reservationOption;
  let value = false;
  initialRO.setActive(value);
  expect(initialRO.active).toBe(value);
  value = true;
  initialRO.setActive(value);
  expect(initialRO.active).toBe(value);
});
