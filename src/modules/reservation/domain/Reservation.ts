import { AggregateRoot } from '@shared/core/domain/AggregateRoot';
import { EntityID } from '@shared/core/domain/EntityID';
import { ReservationOptionDto } from './ReservationOption';
import { Dat } from '@shared/core/Dat';
import { logAndThrow, PossibleLngs, Spread } from '@shared/utils/utils';
import {
  CustomerUserDto,
  ManuallyCreatedCustomerDto,
} from '../external';
import { DomainEventBase } from '@shared/events/DomainEventBase';
import { ServiceDto } from './Service';
import { LocationDto } from './Location';
import { Result2 } from '@shared/core/Result2';
import { ReservationErrors } from './ReservationErrors';

export enum CancellationActor {
  CUSTOMER = 'CUSTOMER',
  BUSINESS = 'BUSINESS',
}

export enum CustomerType {
  REGISTERED = 'REGISTERED',
  MANUALLY_CREATED = 'MANUALLY_CREATED',
}

export enum ReminderStatus {
  NOT_SENT = 'NOT_SENT',
  SENT = 'SENT',
  CONFIRMED = 'CONFIRMED',
}

type ReservationInput = {
  userId: string; // linked to the BusinessUser entity in module user
  customerType: CustomerType;
  customerId: string; // id of a CustomerUser or ManuallyCreatedCustomer entity of module user
  // As the customer (REGISTERED or MANUALLY_CREATED) may change over time, we need to store a snapshot at the time of the reservation
  customerFirstName: CustomerUserDto['firstName'] | ManuallyCreatedCustomerDto['firstName'];
  customerLastName: CustomerUserDto['lastName'] | ManuallyCreatedCustomerDto['lastName'];
  customerPhone: CustomerUserDto['phone'] | null;  // el null se debe a que ManuallyCreatedCustomerDto no tiene;
  customerEmail: CustomerUserDto['email'] | null; // el null se debe a que ManuallyCreatedCustomerDto no tiene;
  start: Dat;
  eventId: string;
  roId: string;
  srvName: ServiceDto['name'];
  srvDescription: ServiceDto['description'];
  srvDuration: ServiceDto['duration'];
  srvPrice: ServiceDto['price'];
  locName: LocationDto['name'];
  locId: LocationDto['identifier'];
  roZone: ReservationOptionDto['timeReference']['zone'];
};

type ReservationProps = ReservationInput & {
  // Cancellation data. Cancellation is carried out by the business (CancelReservationByBusiness use case) or the customer (CancelReservation use case)
  cancelledBy: CancellationActor | null;
  cancelledDate: Dat | null;
  cancellationReason: string | null;  // Optional in both cases
  cancelReservationEventErrors: string[] | null;  // actually "[string, ...string[]] | null" would be more correct, but since this property is stored in the database, and I'm using ReservationRepoFake.ts which reads Reservations.json with cancelReservationEventErrors as "string[] | null", it would cause a TypeScript error.
  reminder: ReminderStatus;
};

export type ReservationDto = Spread<
  ReservationProps,
  {
    id: string;
    start: string;
    customerType: string;
    cancelledBy: string | null;
    cancelledDate: string | null;
    reminder: string;
  }
>;

export class Reservation extends AggregateRoot<ReservationProps, ReservationDto> {
  private __class = this.constructor.name;

  get userId(): string {
    return this.props.userId;
  }

  get start(): Dat {
    return this.props.start;
  }

  get eventId(): string {
    return this.props.eventId;
  }

  get roId(): string {
    return this.props.roId;
  }

  get roZone(): string {
    return this.props.roZone;
  }
  get srvName(): string {
    return this.props.srvName;
  }
  get srvDescription(): string | null {
    return this.props.srvDescription;
  }
  get srvDuration(): number {
    return this.props.srvDuration;
  }
  get srvPrice(): number {
    return this.props.srvDuration;
  }
  get locName(): string {
    return this.props.locName;
  }
  get locId(): string {
    return this.props.locId;
  }
  get customerType(): CustomerType {
    return this.props.customerType;
  }

  get customerId(): string {
    return this.props.customerId;
  }

  get customerFirstName(): ReservationInput['customerFirstName'] {
    return this.props.customerFirstName;
  }

  get customerLastName(): ReservationInput['customerLastName'] {
    return this.props.customerLastName;
  }

  get customerPhone(): ReservationInput['customerPhone'] {
    return this.props.customerPhone;
  }

  get customerEmail(): ReservationInput['customerEmail'] {
    return this.props.customerEmail;
  }

  get cancelledBy(): CancellationActor | null {
    return this.props.cancelledBy;
  }

  get cancellationReason(): string | null {
    return this.props.cancellationReason;
  }

  get cancelledDate(): Dat | null {
    return this.props.cancelledDate;
  }

  get cancelReservationEventErrors(): string[] | null {
    return this.props.cancelReservationEventErrors;
  }

  get reminder(): ReminderStatus {
    return this.props.reminder;
  }

  private constructor(props: ReservationProps, id?: EntityID) {
    super(props, id);
  }

  public static create(input: ReservationInput): Reservation {
    return new Reservation({
      ...input,
      cancelledBy: null,
      cancellationReason: null,
      cancelledDate: null,
      cancelReservationEventErrors: null,
      reminder: ReminderStatus.NOT_SENT,
    });
  }

  // Make addDomainEvent public to be used in the use case
  public addEvent(domainEvent: DomainEventBase): void {
    this.addDomainEvent(domainEvent);
  }

  public setReminder(reminder: ReminderStatus): void {
    this.props.reminder = reminder;
  }

  public static isReminderStatus = (status: string): false | ReminderStatus => {
    return Object.values(ReminderStatus).includes(status as ReminderStatus)?
      status as ReminderStatus
      : false;
  }

  public cancelByBusiness(args: {
    cancelledDate: Dat;
    reason: string | null;
    cancelReservationEventErrors: [string, ...string[]] | null;
    lng: PossibleLngs;
  }): Result2<void> {
    const { cancelledDate, reason, cancelReservationEventErrors, lng } = args;
    return this.cancel({
      actor: CancellationActor.BUSINESS,
      reason,
      cancelledDate,
      cancelReservationEventErrors,
      lng,
    });
  }

  public cancelByCustomer(args: {
    cancelledDate: Dat;
    reason: string | null;
    cancelReservationEventErrors: [string, ...string[]] | null;
    lng: PossibleLngs;
  }): Result2<void> {
    const { cancelledDate, reason, cancelReservationEventErrors, lng } = args;
    return this.cancel({
      actor: CancellationActor.CUSTOMER,
      reason,
      cancelledDate,
      cancelReservationEventErrors,
      lng,
    });
  }

  public isCancellable(lng: PossibleLngs): Result2<void> {
    const errors = [];
    if (this.props.cancelledBy)
      errors.push(new ReservationErrors.AlreadyCancelled(lng));
    if (this.props.start.t < Dat.create().value.t)
      errors.push(new ReservationErrors.ServiceStartTimeInPast(lng));

    return errors.length? Result2.fail(errors) : Result2.ok(undefined);
  }

  private cancel(args: {
    actor: CancellationActor;
    reason: string | null;
    cancelledDate: Dat;
    cancelReservationEventErrors: [string, ...string[]] | null;
    lng: PossibleLngs,
  }): Result2<void> {
    const { actor, reason, cancelReservationEventErrors, lng } = args;
    const isCancellableOrErrors = this.isCancellable(lng);
    if (isCancellableOrErrors.isFailure)
      return Result2.fail(isCancellableOrErrors.errors!);

    this.props.cancelledBy = actor;
    this.props.cancelledDate = Dat.create().value;
    this.props.cancellationReason = reason;
    this.props.cancelReservationEventErrors = cancelReservationEventErrors;

    return Result2.ok(undefined);

    /* Cuando CalendarApi.ts @ calendar devolvía alguno de los errores CalendarApiErrors.CalendarNotFound, CalendarApiErrors.CalendarEventNotFound o CalendarApiErrors.InvalidGrant, podía ser debido a un error del usuario y al principio pensé que sería bueno advertirlo, pero como en realidad es posible que no sea reparable por parte del usuario y podría agregar confusión al usuario, decidí no utilizar esa propiedad Reservation.cancelReservationEventErrorFixable.

    También agregaba complejidad a lo que tenía que retornar CancelReservationEvent @ calendar al ser llamado por CancelReservationByBusiness @ reservation, así como la necesidad de sincronizar el código de dichos controladores.*/

    /* if (cancelReservationEventError)
      this.props.cancelReservationEventErrorFixable =
        FixableCancelReservationEventErrors.includes(cancelReservationEventError);*/
  }

  public toDto(): ReservationDto {
    const { id, start, cancelledBy, cancelledDate, reminder } = this;

    return {
      ...this.props,
      id: id.toString(),
      start: start.s,
      cancelledBy: cancelledBy ? cancelledBy.toString() : null,
      cancelledDate: cancelledDate ? cancelledDate.s : null,
      reminder: reminder.toString(),
    };
  }

  public static assemble(dto: ReservationDto): Reservation {
    const {
      id,
      start,
      cancelledBy: _cancelledBy,
      customerType: _customerType,
      cancelledDate,
      reminder: _reminder,
    } = dto;

    let cancelledBy = null;
    if (_cancelledBy) {
      switch (_cancelledBy) {
        case 'CUSTOMER':
          cancelledBy = CancellationActor.CUSTOMER;
          break;
        case 'BUSINESS':
          cancelledBy = CancellationActor.BUSINESS;
          break;
        default:
          logAndThrow({
            msg: `Invalid cancelledBy: ${_cancelledBy}`,
            data: dto,
          });
      }
    }

    let customerType;
    switch (_customerType) {
      case 'REGISTERED':
        customerType = CustomerType.REGISTERED;
        break;
      case 'MANUALLY_CREATED':
        customerType = CustomerType.MANUALLY_CREATED;
        break;
      default:
        logAndThrow({
          msg: `Invalid customerType: ${_customerType}`,
          data: dto,
        });
    }

    let reminder;
    switch (_reminder) {
      case 'NOT_SENT':
        reminder = ReminderStatus.NOT_SENT;
        break;
      case 'SENT':
        reminder = ReminderStatus.SENT;
        break;
      case 'CONFIRMED':
        reminder = ReminderStatus.CONFIRMED;
        break;
      default:
        logAndThrow({
          msg: `Invalid reminder status: ${_reminder}`,
          data: dto,
        });
    }

    return new Reservation(
      {
        ...dto,
        start: Dat.create({ value: start }).value,
        cancelledBy,
        customerType,
        cancelledDate: cancelledDate ? Dat.create({ value: cancelledDate }).value : null,
        reminder,
      },
      new EntityID(id),
    );
  }
}
