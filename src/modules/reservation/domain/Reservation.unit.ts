import { describe, expect, test, it, vi } from 'vitest';
import {
  CancellationActor,
  CustomerType,
  Reservation,
  ReminderStatus,
} from './Reservation';
import { Dat } from '@shared/core/Dat';
import { uuidFormat } from '@shared/utils/utils';
import {
  createBusinessUser,
  createCustomerUser,
  createManuallyCreatedCustomer,
} from '../../user/utils/test';
import { ManuallyCreatedCustomerDto } from '../../user/domain/ManuallyCreatedCustomer';
import { CustomerUserDto } from '../../user/domain/CustomerUser';
import { Error1, expectErrorsResult, orNull, pickLng } from '@shared/utils/test';
import {
  createReservationOption,
} from '../utils/testExternal';
import { ReservationsInDb } from '@shared/infra/database/sequelize/migrations/Reservations.json';
import { ReservationRepo } from '../repos/ReservationRepo';
import { getFutureTime, getPastTime, getReservationByIndex } from '../utils/test';
import { Result2 } from '@shared/core/Result2';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const lng = pickLng();

function getCustomer(customerType: CustomerType) {
  let customer: CustomerUserDto | ManuallyCreatedCustomerDto;
  switch (customerType) {
    case 'REGISTERED':
      customer = createCustomerUser().dto;
      break;

    case 'MANUALLY_CREATED':
      customer = createManuallyCreatedCustomer().dto;
      break;

    default:
      throw Error(`Invalid customer type: ${customerType}`);
  }
  return { customer, customerType };
}

function getInputs(_customerType: CustomerType) {
  const { dto: reservationOption } = createReservationOption();

  const { customer, customerType } = getCustomer(_customerType);

  const { dto: business } = createBusinessUser();

  const {
    firstName: customerFirstName,
    lastName: customerLastName,
  } = customer;
  const input = {
    userId: business.id,
    start: getFutureTime(),
    eventId: chance.string(),
    roId: reservationOption.id,
    roZone: reservationOption.timeReference.zone,
    srvName: reservationOption.service.name,
    srvDescription: reservationOption.service.description,
    srvDuration: reservationOption.service.duration,
    srvPrice: reservationOption.service.price,
    locName: reservationOption.location.name,
    locId: reservationOption.location.identifier,
    customerId: customer.id,
    customerType,
    customerFirstName,
    customerLastName,
    // @ts-expect-error phone doesn't exist in ManuallyCreatedCustomerDto
    customerPhone: customer.phone? customer.phone : null,
    // @ts-expect-error email doesn't exist in ManuallyCreatedCustomerDto
    customerEmail: customer.email? customer.email : null,
    lng,
  };
  const args = {
    ...input,
    start: Dat.create({ value: input.start }).value,
  };
  return { input, args };
}

const defaultProps = {
  cancelledBy: null,
  cancellationReason: null,
  cancelledDate: null,
  cancelReservationEventErrors: null,
  reminder: ReminderStatus.NOT_SENT,
};

describe(`create & assemble with a`, () => {
  test.each([CustomerType.REGISTERED, CustomerType.MANUALLY_CREATED])(
    `%s customer`,
    (customerType: CustomerType) => {
      const { input, args } = getInputs(customerType);
      const created = Reservation.create(args);

      expect(created.toDto()).toMatchObject({
        ...input,
        ...defaultProps,
        id: expect.stringMatching(uuidFormat),
      });

      const assembled = Reservation.assemble({
        ...input,
        ...defaultProps,
        id: created.id.toString(),
      });

      expect(assembled.equals(created)).toBe(true);
    },
  );
});

const propsWithDefaults = (args: {
  cancelled: boolean;
  cancelledBy?: CancellationActor;
  cancelledError?: boolean;
}) => {
  const { cancelled, cancelledBy, cancelledError } = args;
  let r: {
    cancelledBy: string | null;
    cancellationReason: string | null;
    cancelledDate: string | null;
    cancelReservationEventErrors: string[] | null;
    reminder: string;
  } = {
    cancelledBy: null,
    cancellationReason: null,
    cancelledDate: null,
    cancelReservationEventErrors: null,
    reminder: ReminderStatus.NOT_SENT,
  };
  if (cancelled) {
    // Reservation was cancelled
    if (!cancelledBy)
      throw Error('cancelledBy is required when cancelled is true');
    r = {
      ...r,
      cancelledBy,
      cancellationReason: orNull(chance.string()),
      cancelledDate: Dat.create().value.s,
    };
    if (cancelledError) {
      // CancelReservationEvent gave an error
      const errorType = chance.string({
        length: 8,
        casing: 'lower',
        alpha: true,
      });
      const cancelReservationEventErrors = [errorType];
      r = {
        ...r,
        cancelReservationEventErrors,
      };
    }
  }
  return r;
};

describe(`assemble from db for`, () => {
  test.each([
    [
      CustomerType.REGISTERED,
      'customer, without cancellation data',
      false,
      undefined,
      undefined,
    ],
    [
      CustomerType.MANUALLY_CREATED,
      'customer, without cancellation data',
      false,
      undefined,
      undefined,
    ],

    [
      CustomerType.MANUALLY_CREATED,
      'customer, cancelled by business without error',
      true,
      CancellationActor.BUSINESS,
      false,
    ],
    [
      CustomerType.REGISTERED,
      'customer, cancelled by business without error ',
      true,
      CancellationActor.BUSINESS,
      false,
    ],
    [
      CustomerType.REGISTERED,
      'customer, cancelled by customer without error ',
      true,
      CancellationActor.CUSTOMER,
      false,
    ],

    [
      CustomerType.MANUALLY_CREATED,
      'customer, cancelled by business with error',
      true,
      CancellationActor.BUSINESS,
      true,
    ],
    [
      CustomerType.REGISTERED,
      'customer, cancelled by business with error ',
      true,
      CancellationActor.BUSINESS,
      true,
    ],
    [
      CustomerType.REGISTERED,
      'customer, cancelled by customer with error ',
      true,
      CancellationActor.CUSTOMER,
      true,
    ],
  ])(
    `%s %s`,
    (
      customerType: CustomerType,
      _title: string,
      cancelled: boolean,
      cancelledBy?: CancellationActor,
      cancelledError?: boolean,
    ) => {
      const { input } = getInputs(customerType);

      const inDB = {
        ...input,
        ...propsWithDefaults({ cancelled, cancelledBy, cancelledError }),
        id: chance.guid({ version: 4 }),
      };

      const assembled = Reservation.assemble(inDB);

      expect(assembled.toDto()).toMatchObject(inDB);
    },
  );
});

describe(`cancellation`, () => {

  const customerType = chance.pickone([
    CustomerType.REGISTERED,
    CustomerType.MANUALLY_CREATED,
  ]);

  test(`isCancellable returns success if it wasn't cancelled`, () => {
    const reservation = Reservation.create(getInputs(customerType).args);
    const result = reservation.isCancellable(lng);
    expect(result.isSuccess).toBe(true);
  });

  describe(`isCancellable errors if`, () => {
    it(`was already cancelled`, () => {
      const reservation = getReservationByIndex(3); // ReservationsInDb[3].cancelledBy = B
      const result = reservation.isCancellable(lng);
      expectErrorsResult({
        result,
        error: 'ReservationErrors.AlreadyCancelled',
        code: 409,
      });
    });

    test(`service start time is in the past`, () => {
      const reservation = Reservation.assemble(ReservationRepo.mapDb2Dto({
        ...ReservationsInDb[0], // ReservationsInDb[0].cancelledBy = null
        start: getPastTime(),
      }));
      const result = reservation.isCancellable(lng);
      expectErrorsResult({
        result,
        error: 'ReservationErrors.ServiceStartTimeInPast',
        code: 400,
      });
    });
  });

  test(`errors if isCancellable errors`, () => {
    const reservation = Reservation.create(getInputs(customerType).args);
    const method = chance.pickone(['cancelByBusiness', 'cancelByCustomer']);
    const error1 = new Error1()
    vi.spyOn(reservation, 'isCancellable').mockReturnValue(
      Result2.fail([error1])
    );

    const args = {
      cancelledDate: Dat.create().value,
      reason: orNull(chance.string()),
      cancelReservationEventErrors: null,
      lng,
    };
    const result = reservation[method as 'cancelByBusiness' | 'cancelByCustomer'](args);
    expectErrorsResult({
      result,
      error: error1.type,
      code: error1.status,
    });
  });
});

describe(`cancellation without errors when calling calendar module`, () => {
  test.each([
    [`cancelByBusiness`, CustomerType.REGISTERED, 'BUSINESS'],
    [`cancelByBusiness`, CustomerType.MANUALLY_CREATED, 'BUSINESS'],
    [`cancelByCustomer`, CustomerType.REGISTERED, 'CUSTOMER'],
  ])(
    `%s for a %s customer`,
    (method: string, customerType: CustomerType, cancelledBy: string) => {
      const reservation = Reservation.create(getInputs(customerType).args);

      const reason = orNull(chance.string());
      const cancelledDate = Dat.create().value;

      reservation[method as 'cancelByBusiness' | 'cancelByCustomer']({
        cancelledDate,
        reason,
        cancelReservationEventErrors: null,
        lng,
      });
      expect(reservation.toDto()).toMatchObject({
        cancelledBy,
        cancelledDate: cancelledDate.s,
        cancellationReason: reason,
        cancelReservationEventErrors: null,
      });
    },
  );
});

describe(`reminder property`, () => {
  test('is initialized with NOT_SENT when creating a new reservation', () => {
    const customerType = chance.pickone([
      CustomerType.REGISTERED,
      CustomerType.MANUALLY_CREATED,
    ]);
    const reservation = Reservation.create(getInputs(customerType).args);
    expect(reservation.reminder).toBe(ReminderStatus.NOT_SENT);
    const dto = reservation.toDto();
    expect(dto.reminder).toBe(ReminderStatus.NOT_SENT);
  });

  test.each([
    [ReminderStatus.NOT_SENT],
    [ReminderStatus.SENT],
    [ReminderStatus.CONFIRMED],
  ])('assemble correctly handles %s reminder status', (reminderStatus) => {
    const customerType = chance.pickone([
      CustomerType.REGISTERED,
      CustomerType.MANUALLY_CREATED,
    ]);
    const { input } = getInputs(customerType);

    const inDB = {
      ...input,
      ...defaultProps,
      reminder: reminderStatus,
      id: chance.guid({ version: 4 }),
    };

    const assembled = Reservation.assemble(inDB);
    expect(assembled.reminder).toBe(reminderStatus);
    expect(assembled.toDto().reminder).toBe(reminderStatus);
  });

  test('assemble throws an error for invalid reminder status', () => {
    const customerType = chance.pickone([
      CustomerType.REGISTERED,
      CustomerType.MANUALLY_CREATED,
    ]);
    const { input } = getInputs(customerType);

    const invalidReminderStatus = 'INVALID_STATUS';
    const inDB = {
      ...input,
      ...defaultProps,
      reminder: invalidReminderStatus,
      id: chance.guid({ version: 4 }),
    };

    expect(() => {
      Reservation.assemble(inDB);
    }).toThrow(`Invalid reminder status: ${invalidReminderStatus}`);
  });
});

describe(`cancellation with errors when calling calendar module`, () => {
  test.each([
    [
      `cancelByBusiness`,
      CustomerType.REGISTERED,
      'error CalendarApiErrors.CalendarNotFound',
      'BUSINESS',
      ['CalendarApiErrors.CalendarNotFound'] as [string, ...string[]],
    ],
    [
      `cancelByBusiness`,
      CustomerType.REGISTERED,
      'error CalendarApiErrors.CalendarEventNotFound',
      'BUSINESS',
      ['CalendarApiErrors.CalendarEventNotFound'] as [string, ...string[]],
    ],
    [
      `cancelByBusiness`,
      CustomerType.REGISTERED,
      'error CalendarApiErrors.InvalidGrant',
      'BUSINESS',
      ['CalendarApiErrors.InvalidGrant'] as [string, ...string[]],
    ],
    [
      `cancelByBusiness`,
      CustomerType.REGISTERED,
      'an error',
      'BUSINESS',
      ['Error1'] as [string, ...string[]],
    ],
    [
      `cancelByBusiness`,
      CustomerType.MANUALLY_CREATED,
      'error CalendarApiErrors.CalendarNotFound',
      'BUSINESS',
      ['CalendarApiErrors.CalendarNotFound'] as [string, ...string[]],
    ],
    [
      `cancelByBusiness`,
      CustomerType.MANUALLY_CREATED,
      'error CalendarApiErrors.CalendarEventNotFound',
      'BUSINESS',
      ['CalendarApiErrors.CalendarEventNotFound'] as [string, ...string[]],
    ],
    [
      `cancelByBusiness`,
      CustomerType.MANUALLY_CREATED,
      'error CalendarApiErrors.CalendarNotFound',
      'BUSINESS',
      ['CalendarApiErrors.InvalidGrant'] as [string, ...string[]],
    ],
    [
      `cancelByBusiness`,
      CustomerType.MANUALLY_CREATED,
      'an error',
      'BUSINESS',
      ['Error1'] as [string, ...string[]],
    ],
    [
      `cancelByCustomer`,
      CustomerType.REGISTERED,
      'error CalendarApiErrors.CalendarNotFound',
      'CUSTOMER',
      ['CalendarApiErrors.CalendarNotFound'] as [string, ...string[]],
    ],
    [
      `cancelByCustomer`,
      CustomerType.REGISTERED,
      'error CalendarApiErrors.CalendarEventNotFound',
      'CUSTOMER',
      ['CalendarApiErrors.CalendarEventNotFound'] as [string, ...string[]],
    ],
    [
      `cancelByCustomer`,
      CustomerType.REGISTERED,
      'error CalendarApiErrors.CalendarNotFound',
      'CUSTOMER',
      ['CalendarApiErrors.InvalidGrant'] as [string, ...string[]],
    ],
    [
      `cancelByCustomer`,
      CustomerType.REGISTERED,
      'an error',
      'CUSTOMER',
      ['Error1', 'Error2'] as [string, ...string[]],
    ],
  ])(
    `%s for a %s customer with %s`,
    (
      method: string,
      customerType: CustomerType,
      _title: string,
      cancelledBy: string,
      errors: [string, ...string[]],
    ) => {
      const reservation = Reservation.create(getInputs(customerType).args);

      const reason = orNull(chance.string());
      const cancelledDate = Dat.create().value;

      reservation[method as 'cancelByBusiness']({
        cancelledDate,
        reason,
        cancelReservationEventErrors: errors,
        lng,
      });

      expect(reservation.toDto()).toMatchObject({
        cancelledBy,
        cancelledDate: cancelledDate.s,
        cancellationReason: reason,
        cancelReservationEventErrors: errors,
      });
    },
  );
});

describe(`isReminderStatus`, () => {
  test.each([
    [ReminderStatus.NOT_SENT, ReminderStatus.NOT_SENT],
    [ReminderStatus.SENT, ReminderStatus.SENT],
    [ReminderStatus.CONFIRMED, ReminderStatus.CONFIRMED],
    ['INVALID_STATUS' as ReminderStatus, false as const],
  ])(
    `returns %s for %s`,
    (status: ReminderStatus, expected: ReminderStatus | false) => {
      expect(Reservation.isReminderStatus(status)).toBe(expected);
    },
  );
})
