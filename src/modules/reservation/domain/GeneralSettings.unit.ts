import { beforeEach, expect, test } from 'vitest';
import { GeneralSettings } from './GeneralSettings';
import { Slug } from '@shared/core/Slug';
import { createSlug, orNull, pickLng } from '@shared/utils/test';
import { createN0, createPositiveInt } from '@shared/infra/appsync/test';
import { createReservationLimits } from '../utils/test';
import { ReminderTemplate } from './ReminderTemplate';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const lng = pickLng();
let id: string;
beforeEach(() => {
  id = chance.guid();
});

test(`create, toDto and assemble back`, () => {
  const created = GeneralSettings.createDefault({ id, lng });
  expect(created).toBeInstanceOf(GeneralSettings);
  const dto = created.toDto();
  const assembled = GeneralSettings.assemble(dto);

  expect(created.equals(assembled)).toBe(true);
});
test(`create, update and assemble back`, () => {
  const created = GeneralSettings.createDefault({ id, lng });

  let slugStr: string | null = null;
  let slug: Slug | null = null;
  const { string: _string, slug: _slug } = createSlug();
  if (chance.bool()) {
    slugStr = _string;
    slug = _slug;
  }
  const update = {
    cancellationUpTo: createN0(),
    title: orNull(chance.sentence()),
    welcome: orNull(chance.sentence()),
    slug,
    phoneRequired: chance.bool(),
    maxSimultaneousReservations: createPositiveInt(),
    reservationLimits: createReservationLimits(),
    reminderTemplate: ReminderTemplate.create({ value: chance.sentence() }).value,
  };
  created.update(update);

  const updateDto = {
    cancellationUpTo: update.cancellationUpTo.value,
    title: update.title,
    welcome: update.welcome,
    slug: slugStr,
    phoneRequired: update.phoneRequired,
    maxSimultaneousReservations: update.maxSimultaneousReservations.value,
    reservationLimits: update.reservationLimits.toDto(),
    reminderTemplate: update.reminderTemplate.value,
  }

  const assembled = GeneralSettings.assemble({
    ...updateDto,
    id: created.id.toString(),
  });

  expect(created.equals(assembled)).toBe(true);
});
