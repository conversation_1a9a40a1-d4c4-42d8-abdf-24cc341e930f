import { expect, test } from 'vitest';
import { Name } from './Name';

test(`Creation`, () => {
  const result = Name.create({ value: ' test  - name ' });
  expect(result.isSuccess).toBe(true);
  const username = result.value;
  expect(username.value).toBe('test - name');
});

test(`Creation fails with a short name`, () => {
  const result = Name.create({ value: '' });

  expect(result).toMatchObject({
    isFailure: true,
    error: {
      type: 'NameErrors.TooShort',
      message: expect.any(String),
      status: 400,
    },
  });
});
