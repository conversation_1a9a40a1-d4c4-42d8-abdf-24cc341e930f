import { Name, NameInput, NameProps } from './Name';
import { OptionalLng } from '@shared/utils/utils';
import { Result } from '@shared/core/Result';

export class Description extends Name {
  protected __class = this.constructor.name;  // Without this, TypeScript will be ok assigning { description: Name.create(...).value }
  private constructor(props: NameProps) {
    super(props);
  }

  public static create(args: NameInput & OptionalLng): Result<Description> {
    const nameOrError = Name.create(args);
    if (nameOrError.isFailure) {
      return Result.fail(nameOrError.error!);
    }
    const name = nameOrError.value;
    return Result.ok(new Description({ value: name.value }));
  }
}
