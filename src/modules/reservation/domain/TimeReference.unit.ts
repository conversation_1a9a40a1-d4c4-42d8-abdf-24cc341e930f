import { expect, test, describe, it } from 'vitest';
import { TimeReference } from './TimeReference';
import {
  expectErrorsResult,
  expectMultipleErrorsResult,
} from '@shared/utils/test';
import { TimeReferenceErrors } from './TimeReferenceErrors';

// https://en.wikipedia.org/wiki/List_of_tz_database_time_zones

const valid = {
  hour: 0,
  minute: 30,
  zone: 'Pacific/Tongatapu', // +13
};

test(`creation`, () => {
  const result = TimeReference.create(valid);

  expect(result).toMatchObject({
    isSuccess: true,
    value: valid,
  });

  const created = result.value;
  const dto = created.toDto();

  expect(dto).toMatchObject(valid);
});

describe(`fails when`, () => {
  test.each([
    ['hour', -1, 'TimeReferenceErrors.HrOutsideBoundaries'],
    ['hour', 24, 'TimeReferenceErrors.HrOutsideBoundaries'],
    ['minute', -1, 'TimeReferenceErrors.MinOutsideBoundaries'],
    ['minute', 60, 'TimeReferenceErrors.MinOutsideBoundaries'],
  ])(
    `Time is outside boundaries: %s is %s`,
    (field: string, invalidTime: number, error: string) => {
      const result = TimeReference.create({
        ...valid,
        [field]: invalidTime,
      });

      expectErrorsResult({
        result,
        error,
        code: 400,
        field,
      });
    },
  );
  test.each([
    ['hour', 1.5, 'TimeReferenceErrors.HrShouldBeInteger'],
    ['minute', 2.5, 'TimeReferenceErrors.MinShouldBeInteger'],
  ])(`%s is fractional: %s`, (field: string, invalidTime: number, error: string) => {
    const result = TimeReference.create({
      ...valid,
      [field]: invalidTime,
    });

    expectErrorsResult({
      result,
      error,
      code: 400,
      field,
    });
  });

  test(`Time zone is invalid`, () => {
    const result = TimeReference.create({
      ...valid,
      zone: 'Europa/New_York',
    });

    expectErrorsResult({
      result,
      error: 'TimeReferenceErrors.InvalidTimeZone',
      code: 400,
      field: 'zone',
    });
  });

  it('has multiple errors', () => {
    const result = TimeReference.create({
      hour: -10.9,
      minute: 100.1,
      zone: 'invalid',
    });

    expectMultipleErrorsResult({
      result,
      errors: [
        new TimeReferenceErrors.HrShouldBeInteger().setField('hour'),
        new TimeReferenceErrors.HrOutsideBoundaries().setField('hour'),
        new TimeReferenceErrors.MinShouldBeInteger().setField('minute'),
        new TimeReferenceErrors.MinOutsideBoundaries().setField('minute'),
        new TimeReferenceErrors.InvalidTimeZone({ zone: 'invalid' }).setField('zone'),
      ],
    })
  });
});
