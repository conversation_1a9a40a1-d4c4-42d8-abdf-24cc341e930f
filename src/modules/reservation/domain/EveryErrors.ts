import { patch } from '@shared/core/utils';
import { BadRequest, BaseError } from '@shared/core/AppError';
import { Every } from './Every';
import { Status } from '@shared/core/Status';
import { getLng, OptionalLng, PossibleLngs } from '@shared/utils/utils';

// eslint-disable-next-line @typescript-eslint/no-namespace
export namespace EveryErrors {
  export class MinimumValueIs1 extends BadRequest {
    public constructor(args: {value: number } & OptionalLng) {
      const { value, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];
      super({
        message: `${t.minimumValueIs1}: ${value}`,
      });
    }
  }
  export class MaximumValueIsExceeded extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: `${t.maxIs} ${Every.MINUTES_OF_DAY} ${t.minutes}` });
    }
  }

  export class InvalidNumber extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: t.invalidNumber });
    }
  }

  type Data = { input: number; divisorDown: number; divisorUp: number };
  type Dto = {
    type: string;
    message: string;
    status: number;
    data: Data;
  };
  export class ShouldBeADivisorOfMinutesInDay extends BaseError {
    public readonly data: Data;
    public constructor(args: {
      input: number;
      divisorDown: number;
      divisorUp: number;
    } & OptionalLng) {
      const { input, divisorDown, divisorUp, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];
      super({
        message: `${t.shouldBeADivisorOfMinutesInDay} ${input} ${t.nearestAre} ${divisorDown} ${t.and} ${divisorUp}.`,
        status: Status.BAD_REQUEST,
      });
      this.data = { input, divisorDown, divisorUp };
    }

    public override toDto(): Dto {
      return {
        type: this.type,
        message: this.message,
        status: this.status,
        data: this.data,
      };
    }

    public static assemble(dto: Dto) {
      const { data } = dto;
      return new ShouldBeADivisorOfMinutesInDay(data);
    }
  }
  export class ShouldBeInteger extends BadRequest {
    public constructor(args: { value: number } & OptionalLng) {
      const { value: notAnInteger, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];
      super({
        message: `${t.shouldBeInteger}: ${notAnInteger}`,
      });
    }
  }
}

type Key = keyof typeof trans.en & keyof typeof trans.es;
const trans = {
  en: {
    minimumValueIs1: "Every can't be less than 1, but this was entered",
    maxIs: 'Maximum value is',
    minutes: 'minutes',
    shouldBeADivisorOfMinutesInDay: 'Every should be a divisor of the total minutes in a day (1440), but',
    nearestAre: 'was entered. The nearest values are',
    and: 'and',
    shouldBeInteger: 'Every should be an integer, but this was entered',
    invalidNumber: 'Invalid number',
  },
  es: {
    minimumValueIs1: 'No puede ser menor a 1, pero esto fue ingresado',
    maxIs: 'El máximo valor es',
    minutes: 'minutos',
    shouldBeADivisorOfMinutesInDay: 'Debe ser un divisor de los minutos totales en un día (1440), pero',
    nearestAre: 'fue ingresado. Los valores más cercanos son',
    and: 'y',
    shouldBeInteger: 'Debe ser un entero, pero esto fue ingresado',
    invalidNumber: 'Número inválido.',
  },
};

patch({ EveryErrors });
