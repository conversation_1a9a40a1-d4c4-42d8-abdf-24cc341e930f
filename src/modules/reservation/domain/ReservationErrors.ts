import { patch } from '@shared/core/utils';
import { BadRequest, BaseError } from '@shared/core/AppError';
import { getLng, PossibleLngs } from '@shared/utils/utils';
import { StatusError } from '@shared/core/Status';

export namespace ReservationErrors {
  export class AlreadyCancelled extends BaseError {
    public constructor(lng: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({
        message: t.alreadyCancelled,
        status: StatusError.CONFLICT,
      });
    }
  }
  export class ServiceStartTimeInPast extends BadRequest {
    public constructor(lng: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({ message: t.serviceStartTimeInPast });
    }
  }
}

const trans = {
  en: {
    alreadyCancelled: `Reservation already cancelled.`,
    serviceStartTimeInPast: `Service start time is in the past.`,
  },
  es: {
    alreadyCancelled: `Reserva ya cancelada.`,
    serviceStartTimeInPast: `La hora de inicio del servicio está en el pasado.`,
  },
};

patch({ ReservationErrors });
