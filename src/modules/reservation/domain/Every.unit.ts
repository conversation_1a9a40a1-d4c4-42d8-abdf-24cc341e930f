import { expect, test, it, describe } from 'vitest';
import { Every } from './Every';
import { getPossibleEvery } from '../utils/test';

test(`create`, () => {
  const value = getPossibleEvery();
  const result = Every.create({ value });

  expect(result).toMatchObject({
    isSuccess: true,
    value: {
      value,
    },
  });
});

describe(`creation fails if value`, () => {
  it(`is NaN`, () => {
    const result = Every.create({ value: NaN });

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        status: 400,
        message: expect.any(String),
        type: `EveryErrors.InvalidNumber`,
      }],
    });
  });

  it(`isn't an integer`, () => {
    const result = Every.create({ value: 1.5 });

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        status: 400,
        message: expect.any(String),
        type: `EveryErrors.ShouldBeInteger`,
      }],
    });
  });

  it(`is less than 1`, () => {
    const result = Every.create({ value: 0 });

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        status: 400,
        message: expect.any(String),
        type: `EveryErrors.MinimumValueIs1`,
      }],
    });
  });

  it(`is greater than 1440`, () => {
    const result = Every.create({ value: 1441 });

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        status: 400,
        message: expect.any(String),
        type: `EveryErrors.MaximumValueIsExceeded`,
      }],
    });
  });

  it(`isn't a divisor of minutes in a day`, () => {
    // 60m*24h = 1.440m
    const input = 7;
    const result = Every.create({ value: input });

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        status: 400,
        message: expect.any(String),
        type: `EveryErrors.ShouldBeADivisorOfMinutesInDay`,
        data: {
          input,
          divisorDown: 6,
          divisorUp: 8,
        },
      }],
    });
  });
});
