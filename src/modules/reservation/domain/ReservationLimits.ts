import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { ReservationLimitsErrors } from './ReservationLimitsErrors';
import { N0 } from '@shared/core/N0';
import { getLng, OptionalLng } from '@shared/utils/utils';
import { MaxDaysAhead } from './MaxDaysAhead';

interface ReservationLimitsProps {
  maxDaysAhead: MaxDaysAhead;
  minTimeBeforeService: N0; // in minutes
}

export type ReservationLimitsDto = {
  maxDaysAhead: number;
  minTimeBeforeService: number;
};

export class ReservationLimits extends ValueObject<
  ReservationLimitsProps,
  ReservationLimitsDto
> {
  private __class = this.constructor.name;
  private static readonly DEFAULT_MAX_DAYS_AHEAD = 30;
  private static readonly DEFAULT_MIN_TIME_BEFORE_SERVICE = 1440; // Default minimum advance notice required for service provision is 24 hours

  get maxDaysAhead(): MaxDaysAhead {
    return this.props.maxDaysAhead;
  }

  get minTimeBeforeService(): N0 {
    return this.props.minTimeBeforeService;
  }

  private constructor(props: ReservationLimitsProps) {
    super(props);
  }

  public static create(args: {
    maxDaysAhead: MaxDaysAhead;
    minTimeBeforeService: N0;
  } & OptionalLng): Result<ReservationLimits> {
    const { maxDaysAhead, minTimeBeforeService, lng: _lng } = args;
    const lng = getLng(_lng);
    const daysBeforeService = minTimeBeforeService.value / 60 / 24;
    if (daysBeforeService >= maxDaysAhead.value)
      return Result.fail(
        new ReservationLimitsErrors.MinTimeBeforeServiceExceedsMaxDaysAhead({
          maxDaysAhead: maxDaysAhead.value,
          lng,
        }),
      );

    return Result.ok<ReservationLimits>(new ReservationLimits(args));
  }

  public static createDefault(): ReservationLimits {
    return new ReservationLimits({
      maxDaysAhead: MaxDaysAhead.create({ value: ReservationLimits.DEFAULT_MAX_DAYS_AHEAD })
        .value,
      minTimeBeforeService: N0.create(
        { value: ReservationLimits.DEFAULT_MIN_TIME_BEFORE_SERVICE },
      ).value,
    });
  }

  public toDto(): ReservationLimitsDto {
    return {
      maxDaysAhead: this.maxDaysAhead.value,
      minTimeBeforeService: this.minTimeBeforeService.value,
    };
  }
}
