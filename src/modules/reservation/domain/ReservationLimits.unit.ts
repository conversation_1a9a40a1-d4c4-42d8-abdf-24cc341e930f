import { expect, test, it } from 'vitest';
import { ReservationLimits } from './ReservationLimits';
import { createN0 } from '@shared/infra/appsync/test';
import { createMaxDaysAhead } from '../utils/test';

const maxDaysAhead = createMaxDaysAhead(10);
const minTimeBeforeService = createN0(0);
const args = {
  maxDaysAhead,
  minTimeBeforeService,
};
const dto = {
  maxDaysAhead: maxDaysAhead.value,
  minTimeBeforeService: minTimeBeforeService.value,
};

test(`create`, () => {
  const created = ReservationLimits.create(args).value;
  expect(created.toDto()).toEqual(dto);
});
test(`create default`, () => {
  const created = ReservationLimits.createDefault();
  expect(created).toBeInstanceOf(ReservationLimits);
});
it(`fails if minTimeBeforeService exceeds maxDaysAhead`, () => {

  const maxDaysAheadInMinutes = maxDaysAhead.value * 24 * 60;
  const result = ReservationLimits.create({
    ...args,
    minTimeBeforeService: createN0(maxDaysAheadInMinutes),
  });
  expect(result).toMatchObject({
    isFailure: true,
    error: {
      type: 'ReservationLimitsErrors.MinTimeBeforeServiceExceedsMaxDaysAhead',
      message: expect.any(String),
      status: 400,
    },
  });
});
