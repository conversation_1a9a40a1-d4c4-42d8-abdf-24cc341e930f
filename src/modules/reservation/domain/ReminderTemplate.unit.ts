import { describe, expect, it, beforeEach } from 'vitest';
import { ReminderTemplate } from './ReminderTemplate';
import { expectErrorsResult, pickLng } from '@shared/utils/test';
import { vi } from 'vitest';

const replacements = {
  firstName: 'John',
  service: 'Haircut',
  timeZone: 'America/New_York',
  start: '2023-05-15T14:30:00Z',
  lng: 'en',
};

describe('ReminderTemplate', () => {
  it('should create a default reminder template containing all variables', () => {
    const lng = pickLng();
    const defaultTemplate = ReminderTemplate.createDefault(lng);
    expect(defaultTemplate).toBeInstanceOf(ReminderTemplate);
    expect(defaultTemplate.value).toContain('{firstName}');
    expect(defaultTemplate.value).toContain('{service}');
    expect(defaultTemplate.value).toContain('{day}');
    expect(defaultTemplate.value).toContain('{time}');
    expect(defaultTemplate.value).toContain(lng === 'es' ? 'Hola' : 'Hello');
  });

  it('should create and assemble back from dto', () => {
    const validTemplate = 'Hello {firstName}, your appointment for {service} is on {day} at {time}';
    const reminderTemplateOrError = ReminderTemplate.create({ value: validTemplate });

    expect(reminderTemplateOrError.isSuccess).toBe(true);
    const reminderTemplate = reminderTemplateOrError.value;
    expect(reminderTemplate.value).toBe(validTemplate);
    const assembled = ReminderTemplate.assemble(reminderTemplate.toDto());
    expect(reminderTemplate.equals(assembled)).toBe(true);
  });

  it(`should be '' if it only contains spaces`, () => {
    const onlySpaces = '  \n\n \n';
    const reminderTemplateOrError = ReminderTemplate.create({ value: onlySpaces });

    expect(reminderTemplateOrError.isSuccess).toBe(true);
    const reminderTemplate = reminderTemplateOrError.value;
    expect(reminderTemplate.value).toBe('');
  });

  it('should fail if template is too long', () => {
    // Create a string longer than MAX_LENGTH
    const tooLongTemplate = 'A'.repeat(ReminderTemplate.MAX_LENGTH + 1);
    const reminderTemplateOrError = ReminderTemplate.create({ value: tooLongTemplate });

    expectErrorsResult({
      result: reminderTemplateOrError,
      error: 'ReminderTemplateErrors.TooLong',
      code: 400,
    });
  });

  describe('replaceVariables', () => {
    // Mock the Intl.DateTimeFormat to ensure consistent test results
    const mockDateFormat = vi.fn();
    const mockTimeFormat = vi.fn();

    beforeEach(() => {
      // Reset mocks
      mockDateFormat.mockReset();
      mockTimeFormat.mockReset();

      // Set up mock return values
      mockDateFormat.mockReturnValue('Monday, May 15, 2023');
      mockTimeFormat.mockReturnValue('14:30');

      // Mock the Intl.DateTimeFormat constructor and format method
      const originalDateTimeFormat = global.Intl.DateTimeFormat;
      vi.spyOn(global.Intl, 'DateTimeFormat').mockImplementation((_locale, options) => {
        if (options?.hour !== undefined) {
          return { format: mockTimeFormat } as unknown as Intl.DateTimeFormat;
        } else {
          return { format: mockDateFormat } as unknown as Intl.DateTimeFormat;
        }
      });

      // Return cleanup function
      return () => {
        global.Intl.DateTimeFormat = originalDateTimeFormat;
      };
    });

    it('should replace all variables in the template', () => {
      const template = 'Hello {firstName}, your {service} appointment is on {day} at {time}';
      const reminderTemplate = ReminderTemplate.create({ value: template }).value;

      const result = reminderTemplate.replaceVariables(replacements);

      expect(result).toBe('Hello John, your Haircut appointment is on Monday, May 15, 2023 at 14:30');
    });

    it('should handle null firstName', () => {
      const template = 'Hello {firstName}, your appointment is scheduled';
      const reminderTemplate = ReminderTemplate.create({ value: template }).value;

      const result = reminderTemplate.replaceVariables({
        ...replacements,
        firstName: null,
      });

      expect(result).toBe('Hello , your appointment is scheduled');
    });

    it('should handle variables with whitespace in curly braces', () => {
      const template = 'Hello { firstName }, your { service } appointment is on { day } at { time }';
      const reminderTemplate = ReminderTemplate.create({ value: template }).value;

      const result = reminderTemplate.replaceVariables(replacements);

      expect(result).toBe('Hello John, your Haircut appointment is on Monday, May 15, 2023 at 14:30');
    });

    it('should leave unrecognized variables unchanged', () => {
      const template = 'Hello {firstName}, your {service} is at {location}';
      const reminderTemplate = ReminderTemplate.create({ value: template }).value;

      const result = reminderTemplate.replaceVariables(replacements);

      expect(result).toBe('Hello John, your Haircut is at {location}');
    });

    it('should format date and time according to locale and timezone', () => {
      const template = '{day} at {time}';
      const reminderTemplate = ReminderTemplate.create({ value: template }).value;

      reminderTemplate.replaceVariables({
        ...replacements,
        lng: 'fr-FR',
      });

      // Verify that Intl.DateTimeFormat was called with the correct parameters
      expect(global.Intl.DateTimeFormat).toHaveBeenCalledWith('fr-FR', {
        weekday: 'long',
        day: 'numeric',
        month: 'long',
        timeZone: 'America/New_York',
      });

      expect(global.Intl.DateTimeFormat).toHaveBeenCalledWith('fr-FR', {
        hour: 'numeric',
        minute: 'numeric',
        hourCycle: 'h23',
        timeZone: 'America/New_York',
      });
    });
  });
});
