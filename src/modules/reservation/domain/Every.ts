import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result2 } from '@shared/core/Result2';
import { EveryErrors } from './EveryErrors';
import { BaseError } from '@shared/core/AppError';
import { getLng, OptionalLng } from '@shared/utils/utils';

type EveryDto = number;

interface EveryProps {
  value: EveryDto;
}

// Integer number of minutes, divisor of minutes in a day
export class Every extends ValueObject<EveryProps, EveryDto> {
  private __class = this.constructor.name;
  public static MINUTES_OF_DAY = 60 * 24;
  get value(): number {
    return this.props.value;
  }

  private constructor(props: EveryProps) {
    super(props);
  }

  public toDto(): EveryDto {
    return this.value;
  }

  public static create(args: { value: number } & OptionalLng): Result2<Every> {
    const { value: every, lng: _lng } = args;
    const lng = getLng(_lng);
    if (isNaN(every)) return Result2.fail([new EveryErrors.InvalidNumber(lng)]);

    const errors: BaseError[] = [];
    if (every < 1) {
      errors.push(new EveryErrors.MinimumValueIs1({ value: every, lng }));
    }

    if (every > Every.MINUTES_OF_DAY) {
      errors.push(new EveryErrors.MaximumValueIsExceeded(lng));
    }
    const withinRage = !errors.length;

    if (!Number.isInteger(every)){
      errors.push(new EveryErrors.ShouldBeInteger({ value: every, lng }));
    }

    if (withinRage) {
      // Check day minutes are divisible by every
      const division = Every.MINUTES_OF_DAY / every;
      if (!Number.isInteger(division)) {
        const { divisorDown, divisorUp } = nearestDivisors(every);
        errors.push(
          new EveryErrors.ShouldBeADivisorOfMinutesInDay({
            input: every,
            divisorDown,
            divisorUp,
            lng,
          }),
        );
      }
    }

    if (errors.length) return Result2.fail(errors);

    // return Result.ok<Every>(new Every({ value: every }));
    return Result2.ok<Every>(new Every({ value: every }));
  }
}

function nearestDivisors(num: number): { divisorUp: number; divisorDown: number } {
  // No need to check that num is not NaN as it's already done in Every.create
  const target = 1440;
  let divisorDown = 0;
  let divisorUp;

  // Finding the nearest divisor less than or equal to the target
  for (let i = num; i > 0; i--) {
    if (target % i === 0) {
      divisorDown = i;
      break;
    }
  }

  // Finding the nearest divisor greater than or equal to the target
  for (let i = num; ; i++) {
    if (target % i === 0) {
      divisorUp = i;
      break;
    }
  }

  return { divisorDown, divisorUp };
}
