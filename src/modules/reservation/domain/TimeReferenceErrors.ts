import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { PossibleLngs, getLng, OptionalLng } from '@shared/utils/utils';

export namespace TimeReferenceErrors {
  export class HrOutsideBoundaries extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: t.hrOutsideBoundaries });
    }
  }
  export class MinOutsideBoundaries extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: t.minOutsideBoundaries });
    }
  }
  export class HrShouldBeInteger extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: t.hrShouldBeInteger });
    }
  }
  export class MinShouldBeInteger extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: t.minShouldBeInteger });
    }
  }
  export class InvalidTimeZone extends BadRequest {
    public constructor(args: { zone: string } & OptionalLng) {
      const { zone, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: `${t.invalidTimeZone}: ${zone}` });
    }
  }
}

type Key = keyof typeof trans.en & keyof typeof trans.es;
const trans = {
  en: {
    hrOutsideBoundaries: 'Hour outside boundaries, allowed range is 0-23',
    minOutsideBoundaries: 'Minutes outside boundaries, allowed range is 0-59',
    hrShouldBeInteger: "Hour should be integer",
    minShouldBeInteger: "Minutes should be integer",
    invalidTimeZone: 'Invalid time zone',
  },
  es: {
    hrOutsideBoundaries: 'Hora fuera de los límites, el rango permitido es 0-23',
    minOutsideBoundaries: 'Minutos fuera de los límites, el rango permitido es 0-59',
    hrShouldBeInteger: 'Hora debe ser un número entero',
    minShouldBeInteger: 'Minutos debe ser un número entero',
    invalidTimeZone: 'Zona horaria inválida',
  },
};

patch({ TimeReferenceErrors });
