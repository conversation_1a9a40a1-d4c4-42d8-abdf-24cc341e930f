import { ValueObject } from '@shared/core/domain/ValueObject';
import { TimeReferenceErrors } from './TimeReferenceErrors';
import { IANAZone } from 'luxon';
import { getLng, OptionalLng } from '@shared/utils/utils';
import { BaseError } from '@shared/core/AppError';
import { Result2 } from '@shared/core/Result2';

interface TimeReferenceProps {
  hour: number; // hour of the day, 0-23
  minute: number; // minute of the hour, 0-59
  zone: string;
  // Para adquirirla desde el browser o Node.js: Intl.DateTimeFormat().resolvedOptions().timeZone
  // Para adquirir las zonas soportadas: Intl.supportedValuesOf("timeZone")
  // https://nodatime.org/TimeZones
  // https://en.wikipedia.org/wiki/List_of_tz_database_time_zones
}

export type TimeReferenceDto = TimeReferenceProps;
export type TimeReferenceInput = TimeReferenceProps;

export class TimeReference extends ValueObject<
  TimeReferenceProps,
  TimeReferenceDto
> {
  private __class = this.constructor.name;
  get hour(): number {
    return this.props.hour;
  }
  get minute(): number {
    return this.props.minute;
  }
  get zone(): string {
    return this.props.zone;
  }

  private constructor(props: TimeReferenceProps) {
    super(props);
  }

  public toDto(): TimeReferenceDto {
    return this.props;
  }

  public static create(args: TimeReferenceInput & OptionalLng): Result2<TimeReference> {
    const { hour, minute, zone, lng } = args;

    let errors: BaseError[] = [];
    const checkedOrErrors = TimeReference.checkTime({ hour, minute, lng });
    if (checkedOrErrors.isFailure)
      errors = errors.concat(checkedOrErrors.errors!);

    if (!IANAZone.isValidZone(zone))
      errors = errors.concat(new TimeReferenceErrors.InvalidTimeZone({ zone, lng }).setField('zone'));

    if (errors.length)
      return Result2.fail(errors);

    return Result2.ok(new TimeReference({ hour, minute, zone }));
  }

  private static checkTime(args: { hour: number; minute: number } & OptionalLng): Result2<null> {
    const { hour, minute, lng: _lng } = args;
    const lng = getLng(_lng);
    const errors: BaseError[] = [];

    if (!Number.isInteger(hour))
      errors.push(new TimeReferenceErrors.HrShouldBeInteger(lng).setField('hour'));
    if (hour < 0 || hour > 23)
      errors.push(new TimeReferenceErrors.HrOutsideBoundaries(lng).setField('hour'));

    if (!Number.isInteger(minute))
      errors.push(new TimeReferenceErrors.MinShouldBeInteger(lng).setField('minute'));
    if (minute < 0 || minute > 59)
      errors.push(new TimeReferenceErrors.MinOutsideBoundaries(lng).setField('minute'));

    if (errors.length)
      return Result2.fail(errors);

    return Result2.ok(null);
  }
}
