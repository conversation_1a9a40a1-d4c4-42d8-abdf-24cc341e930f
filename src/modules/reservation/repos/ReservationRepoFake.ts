import {
  IReservationRepo,
} from './IReservationRepo';
import { Reservation } from '../domain/Reservation';
import { ReservationInDb, ReservationRepo, ReservationRepo as repo } from './ReservationRepo';
import { ReservationsInDb as json } from '@shared/infra/database/sequelize/migrations/Reservations.json';
import { nonEmpty } from '@shared/utils/utils';
import { getFutureTime } from '../utils/test';

export class ReservationRepoFake implements IReservationRepo {
  private readonly ReservationsInDb: ReservationInDb[];

  public constructor() {
    const copy = [...json]; // Load a brand-new copy of the json for every fake repo
    copy.map((r) => {
      r.start = getFutureTime();
    });
    this.ReservationsInDb = copy;
  }

  public async create(reservation: Reservation) {
    const now = new Date().toISOString();
    this.ReservationsInDb.push({
      ...reservation.toDto(),
      created_at: now,
      updated_at: now,
      deleted_at: null,
    });
  }

  public async list(userId: string): Promise<[Reservation, ...Reservation[]] | null> {
    const reservationsInDb = this.ReservationsInDb.filter(
      (res) => res.userId === userId && !res.deleted_at,
    );
    if (!reservationsInDb.length) return null;

    const dtos = reservationsInDb.map(repo.mapDb2Dto);
    return nonEmpty (dtos.map(Reservation.assemble));
  }

  public async get(args: {
    userId: string;
    id: string;
  }): Promise<Reservation | null> {
    const { userId, id } = args;
    const reservationInDb: ReservationInDb = this.ReservationsInDb.filter(
      (res) => res.id === id && res.userId === userId && !res.deleted_at,
    )[0];
    if (!reservationInDb) return null;

    const dto = repo.mapDb2Dto(reservationInDb);
    return Reservation.assemble(dto);
  }

  public async getByCustomer(args: {
    customerId: string;
    id: string;
  }): Promise<Reservation | null> {
    const { customerId, id } = args;
    const reservationInDb: ReservationInDb = this.ReservationsInDb.filter(
      (res) => res.id === id && res.customerId === customerId && !res.deleted_at,
    )[0];
    if (!reservationInDb) return null;

    const dto = repo.mapDb2Dto(reservationInDb);
    return Reservation.assemble(dto);
  }

  public async update(reservation: Reservation) {
    const dto = reservation.toDto();
    const toDb = repo.mapDto2Db(dto);
    const idx = this.ReservationsInDb.findIndex(
      (res) => res.id === reservation.id.toString() && !res.deleted_at,
    );
    const now = new Date().toJSON();
    this.ReservationsInDb[idx] = {
      ...this.ReservationsInDb[idx], // to keep same created_at
      ...toDb,
      updated_at: now,
    };
    return ReservationRepo.mapDb2Dto(this.ReservationsInDb[idx]);
  }

  public async pendingReservations(args: { userId: string, customerId: string }): Promise<number> {
    const { userId, customerId } = args;
    const found = this.ReservationsInDb.filter(
      (res) =>
        res.userId === userId &&
        res.customerId === customerId &&
        !res.deleted_at &&
        !res.cancelledBy &&
        new Date(res.start).getTime() > new Date().getTime(),
    );
    return found.length;
  }
}
