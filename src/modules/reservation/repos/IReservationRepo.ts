import { Reservation, ReservationDto } from '../domain/Reservation';

export declare interface IReservationRepo {
  create(reservation: Reservation): Promise<void>;
  update(reservation: Reservation): Promise<ReservationDto>;
  list(userId: string): Promise<[Reservation, ...Reservation[]] | null>;
  get(args: { userId: string; id: string }): Promise<Reservation | null>; // Cuando el business cancela la reserva primero debo obtenerla
  getByCustomer(args: {
    customerId: string;
    id: string;
  }): Promise<Reservation | null>; // Cuando el customer cancela la reserva primero debo obtenerla
  pendingReservations(args: { userId: string; customerId: string }): Promise<number>;
}
