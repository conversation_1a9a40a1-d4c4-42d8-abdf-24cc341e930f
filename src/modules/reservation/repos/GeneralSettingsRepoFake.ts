import { GeneralSettings } from '../domain/GeneralSettings';
import { IGeneralSettingsRepo } from './IGeneralSettingsRepo';
import { GeneralSettingsForReservationsInDb as json } from '@shared/infra/database/sequelize/migrations/GeneralSettingsForReservations.json';
import {
  GeneralSettingsInDb,
  GeneralSettingsRepo as repo,
} from './GeneralSettingsRepo';
import { Dat } from '@shared/core/Dat';
import { Slug } from '@shared/core/Slug';

export class GeneralSettingsRepoFake implements IGeneralSettingsRepo {
  private readonly GeneralSettingsInDb: GeneralSettingsInDb[];

  public constructor() {
    this.GeneralSettingsInDb = [...json];
  }
  public async create(gs: GeneralSettings) {
    const now = new Date().toISOString();
    this.GeneralSettingsInDb.push({
      ...repo.mapDto2Db(gs.toDto()),
      created_at: now,
      updated_at: now,
    });
  }

  public async update(gs: GeneralSettings) {
    const toDb = repo.mapDto2Db(gs.toDto());
    const idx = this.GeneralSettingsInDb.findIndex(
      (gs) => gs.id === gs.id.toString(),
    );
    const now = Dat.create().value.s;
    this.GeneralSettingsInDb[idx] = {
      ...this.GeneralSettingsInDb[idx], // to keep same created_at
      ...toDb,
      updated_at: now,
    };
  }

  public async get(id: string): Promise<GeneralSettings | null> {
    const gsInDb = this.GeneralSettingsInDb.filter((gs) => gs.id === id);

    return !gsInDb.length ? null : GeneralSettings.assemble(repo.mapDb2Dto(gsInDb[0]));
  }

  public async isSlugAvailable(args: {
    userIdAsking: string;
    slug: Slug;
  }): Promise<boolean> {
    const { userIdAsking, slug } = args;
    const found = this.GeneralSettingsInDb.filter(
      (gs) => gs.slug === slug.value && gs.id !== userIdAsking,
    );
    return !found.length;
  }
}
