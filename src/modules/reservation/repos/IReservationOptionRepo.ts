import {
  ReservationOption,
  ReservationOptionDto,
} from '../domain/ReservationOption';

export declare interface IReservationOptionRepo {
  create(reservationOption: ReservationOption): Promise<void>;
  list(
    userId: string,
  ): Promise<[ReservationOption, ...ReservationOption[]] | null>;
  get(args: { userId: string; id: string }): Promise<ReservationOption | null>;
  update(args: {
    reservationOption: ReservationOption;
    skip_updated_at?: true;
  }): Promise<ReservationOptionDto>;
  delete(reservationOption: ReservationOption): Promise<void>;
}
