import { GeneralSettings } from '../domain/GeneralSettings';
import { Slug } from '@shared/core/Slug';

export declare interface IGeneralSettingsRepo {
  create(generalSettings: GeneralSettings): Promise<void>;
  update(generalSettings: GeneralSettings): Promise<void>;
  get(id: string): Promise<GeneralSettings | null>;
  isSlugAvailable(args: { userIdAsking: string; slug: Slug }): Promise<boolean>;
}
