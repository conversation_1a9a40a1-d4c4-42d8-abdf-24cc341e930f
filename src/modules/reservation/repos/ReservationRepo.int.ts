import { expect, test } from 'vitest';
import { ReservationRepo } from './ReservationRepo';
import { Reservation } from '../domain/Reservation';
import { createCustomerUser, createReservation } from '../utils/testExternal';
import { pickLng } from '@shared/utils/test';
import { Dat } from '@shared/core/Dat';
import { rmReservations } from '../utils/test';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const userId = chance.guid();
const customerId = chance.guid();
const customerUser = createCustomerUser(customerId).user;
const repo = new ReservationRepo(models.Reservation);
const lng = pickLng();
const cancellationArgs = {
  cancelledDate: Dat.create().value,
  reason: chance.string(),
  cancelReservationEventErrors: null,
  lng,
}

test('pendingReservations', async () => {
  const all = [];

  const pendingReservations: Reservation[] = Array.from({ length: chance.integer({min: 0, max: 5}) }, () =>
    createReservation({ userId, customerUser }).reservation
  );

  const cancelledReservations: Reservation[] = [];
  for (let i = 0; i < chance.integer({ min: 0, max: 5 }); i++) {
    const reservation = createReservation({ userId, customerUser }).reservation;
    if (chance.bool())
      reservation.cancelByBusiness(cancellationArgs);
    else
      reservation.cancelByCustomer(cancellationArgs);
    cancelledReservations.push(reservation);
  }

  const passedReservations: Reservation[] = [];
  for (let i = 0; i < chance.integer({ min: 0, max: 5 }); i++) {
    const reservation = createReservation({ userId, customerUser }).reservation;
    passedReservations.push(Reservation.assemble({
      ...reservation.toDto(),
      start: Dat.create({ value: new Date().getTime() - chance.integer({ min: 60 * 1000, max: 365 * 24 * 60 * 60 * 1000 })}).value.s, // At least 1m ago
    }));
  }

  all.push(
    ...pendingReservations,
    ...cancelledReservations,
    ...passedReservations,
  );

  await Promise.all(all.map(row => repo.create(row)));

  expect(await repo.pendingReservations({ userId, customerId })).toBe(pendingReservations.length);

  await models.Reservation.destroy({
    where: {
      id: pendingReservations.map(r => r.id.toString()),
    },
  });
  expect(await repo.pendingReservations({ userId, customerId })).toBe(0);

  await rmReservations(all.map(r => r.id.toString()));

});