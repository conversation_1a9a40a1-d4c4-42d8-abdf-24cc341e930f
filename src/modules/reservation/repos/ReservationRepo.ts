import { Reservation, ReservationDto } from '../domain/Reservation';
import {
  IReservationRepo,
} from './IReservationRepo';
import { Repository } from '@shared/core/Repository';
import { Name } from '../domain/Name';
import { Timestamps } from '@shared/infra/database/sequelize/config/Timestamps';
import { logAndThrow, nonEmpty } from '@shared/utils/utils';
import { Op } from 'sequelize';

type ReservationToDb = ReservationDto;
export type ReservationInDb = ReservationToDb & Timestamps;

export class ReservationRepo
  extends Repository<Reservation>
  implements IReservationRepo
{
  private Reservation: any; // eslint-disable-line @typescript-eslint/no-explicit-any

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public constructor(ReservationModel: any) {
    super();
    if (ReservationModel.tableName !== 'reservations')
      throw Error(
        `Wrong model passed in: ${ReservationModel.tableName}, while correct is reservations`,
      );
    this.Reservation = ReservationModel;
  }

  public static mapDb2Dto = (db: ReservationInDb): ReservationDto => {
    const { cancelledBy, customerType, reminder, ...rest } = db;

    let _cancelledBy = null;
    if (cancelledBy) {
      switch (cancelledBy) {
        case 'C':
          _cancelledBy = 'CUSTOMER';
          break;
        case 'B':
          _cancelledBy = 'BUSINESS';
          break;
        default:
          logAndThrow({
            msg: `Invalid cancelledBy: ${cancelledBy}`,
            data: db,
          });
      }
    }

    let _customerType;
    switch (customerType) {
      case 'R':
        _customerType = 'REGISTERED';
        break;
      case 'M':
        _customerType = 'MANUALLY_CREATED';
        break;
      default:
        logAndThrow({
          msg: `Invalid customerType: ${customerType}`,
          data: db,
        });
    }

    // Validate reminder status
    if (reminder !== 'NOT_SENT' && reminder !== 'SENT' && reminder !== 'CONFIRMED') {
      logAndThrow({
        msg: `Invalid reminder status: ${reminder}`,
        data: db,
      });
    }

    return {
      ...rest,
      cancelledBy: _cancelledBy,
      customerType: _customerType,
      reminder,
    };
  };

  public static mapDto2Db = (dto: ReservationDto): ReservationToDb => {
    const {
      cancelledBy: _cancelledBy,
      customerType: _customerType,
      ...rest
    } = dto;

    let cancelledBy = null;
    if (_cancelledBy) {
      switch (_cancelledBy) {
        case 'CUSTOMER':
          cancelledBy = 'C';
          break;
        case 'BUSINESS':
          cancelledBy = 'B';
          break;
        default:
          logAndThrow({
            msg: `Invalid cancelledBy: ${_cancelledBy}`,
            data: dto,
          });
      }
    }

    let customerType;
    switch (_customerType) {
      case 'REGISTERED':
        customerType = 'R';
        break;
      case 'MANUALLY_CREATED':
        customerType = 'M';
        break;
      default:
        logAndThrow({
          msg: `Invalid customerType: ${_customerType}`,
          data: dto,
        });
    }

    return {
      ...rest,
      cancelledBy,
      customerType,
    };
  };

  public async list(userId: string): Promise<[Reservation, ...Reservation[]] | null> {
    const found: {
      dataValues: ReservationInDb;
    }[] = await this.Reservation.findAll({
      where: {
        userId,
      },
      transaction: this.transaction,
    });
    if (!found.length) return null;

    const resInDb = found.map((f) => f.dataValues);
    const dtos = resInDb.map(ReservationRepo.mapDb2Dto);
    return nonEmpty(dtos.map(Reservation.assemble));
  }

  public async exists(args: { userId: string; name: Name }): Promise<boolean> {
    const { userId, name } = args;

    const found = await this.Reservation.findOne({
      where: {
        userId,
        name: name.value,
      },
      transaction: this.transaction,
    });
    return !!found;
  }

  public async create(reservation: Reservation) {
    return this.Reservation.create(
      ReservationRepo.mapDto2Db(reservation.toDto()),
      {
        transaction: this.transaction,
      },
    );
  }

  public async get(args: {
    userId: string;
    id: string;
  }): Promise<Reservation | null> {
    const { userId, id } = args;
    const found = await this.Reservation.findOne({
      where: {
        userId,
        id,
      },
      transaction: this.transaction,
    });
    if (!found) return null;

    const dto = ReservationRepo.mapDb2Dto(found.dataValues);
    return Reservation.assemble(dto);
  }

  public async getByCustomer(args: {
    customerId: string;
    id: string;
  }): Promise<Reservation | null> {
    const { id, customerId } = args;
    const found = await this.Reservation.findOne({
      where: {
        id,
        customerId,
      },
      transaction: this.transaction,
    });
    if (!found) return null;

    const dto = ReservationRepo.mapDb2Dto(found.dataValues);
    return Reservation.assemble(dto);
  }

  public async update(reservation: Reservation) {
    const [ , [{ dataValues }]] = await this.Reservation.update(
      ReservationRepo.mapDto2Db(reservation.toDto()),
      {
        where: {
          id: reservation.id.toString(),
        },
        returning: true,  // Postgres feature https://sequelize.org/api/v6/class/src/model.js~model#static-method-update
        transaction: this.transaction,
      },
    );
    return ReservationRepo.mapDb2Dto(dataValues);
  }

  public async pendingReservations(args: { userId: string, customerId: string }): Promise<number> {
    const { userId, customerId } = args;
    const found: {
      dataValues: ReservationInDb;
    }[] = await this.Reservation.findAll({
      where: {
        userId,
        customerId,
        cancelledBy: null,
        start: {
          [Op.gt]: new Date().getTime(),
        },
      },
      transaction: this.transaction,
    });
    return found.length;
  }
}
