import {
  ReservationOption,
  ReservationOptionDto,
} from '../domain/ReservationOption';
import { IReservationOptionRepo } from './IReservationOptionRepo';
import { Repository } from '@shared/core/Repository';
import { Timestamps } from '@shared/infra/database/sequelize/config/Timestamps';
import { nonEmpty } from '@shared/utils/utils';
import { IContextProvider } from '@shared/context/IContextProvider';

// In the case of ReservationOption, the data persisted in the db doesn't match ReservationOptionDto because it relies on objects Service, Location, TimeReference and ExplanatoryNotes, so we need to introduce the new type ReservationOptionInDb
type ReservationOptionToDb = Omit<
  ReservationOptionDto,
  'service' | 'location' | 'timeReference' | 'explanatoryNotes'
> & {
  srvName: string;
  srvDescription: string | null;
  srvDuration: number;
  srvPrice: number | null;

  locName: string;
  locDescription: string | null;
  locIdentifier: string;
  locShowInfoWhenReserving: boolean;

  trHour: number;
  trMinute: number;
  trZone: string;

  noteWhile: string | null;
  noteAfter: string | null;
};

export type ReservationOptionInDb = ReservationOptionToDb & Timestamps;

export class ReservationOptionRepo
  extends Repository<ReservationOption>
  implements IReservationOptionRepo
{
  private ReservationOption: any; // eslint-disable-line @typescript-eslint/no-explicit-any
  private readonly sendAnalytics: IContextProvider['sendAnalytics'];

  public constructor(args: {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    ReservationOptionModel: any;
    sendAnalytics: IContextProvider['sendAnalytics'];
}) {
    const { ReservationOptionModel, sendAnalytics } = args;
    if (ReservationOptionModel.tableName !== 'reservation_options')
      throw Error(
        `Wrong model passed in: ${ReservationOptionModel.tableName}, while correct is reservation_options`,
      );
    super();
    this.ReservationOption = ReservationOptionModel;
    this.sendAnalytics = sendAnalytics;
  }

  public static mapDto2Db = (dto: ReservationOptionDto): ReservationOptionToDb => {
    const { service, location, timeReference, explanatoryNotes, ...rest } = dto;
    const {
      name: srvName,
      description: srvDescription,
      duration: srvDuration,
      price: srvPrice,
    } = service;
    const {
      name: locName,
      description: locDescription,
      identifier: locIdentifier,
      showInfoWhenReserving: locShowInfoWhenReserving,
    } = location;
    const { hour: trHour, minute: trMinute, zone: trZone } = timeReference;
    const { while: noteWhile, after: noteAfter } = explanatoryNotes;
    return {
      ...rest,
      srvName,
      srvDescription,
      srvDuration,
      srvPrice,
      locName,
      locDescription,
      locIdentifier,
      locShowInfoWhenReserving,
      trHour,
      trMinute,
      trZone,
      noteWhile,
      noteAfter,
    };
  };

  public static mapDb2Dto = (
    resOptInDb: ReservationOptionInDb,
  ): ReservationOptionDto => {
    const {
      srvName,
      srvDescription,
      srvDuration,
      srvPrice,
      locName,
      locDescription,
      locIdentifier,
      locShowInfoWhenReserving,
      trHour,
      trMinute,
      trZone,
      noteWhile,
      noteAfter,
      ...rest
    } = resOptInDb;
    return {
      ...rest,
      service: {
        name: srvName,
        description: srvDescription,
        duration: srvDuration,
        price: srvPrice,
      },
      location: {
        name: locName,
        description: locDescription,
        identifier: locIdentifier,
        showInfoWhenReserving: locShowInfoWhenReserving,
      },
      timeReference: {
        hour: trHour,
        minute: trMinute,
        zone: trZone,
      },
      explanatoryNotes: {
        while: noteWhile,
        after: noteAfter,
      },
    };
  };

  public async list(
    userId: string,
  ): Promise<[ReservationOption, ...ReservationOption[]] | null> {
    const found: {
      dataValues: ReservationOptionInDb;
    }[] = await this.ReservationOption.findAll({
      where: {
        userId,
      },
      transaction: this.transaction,
    });
    if (!found.length) return null;

    const resOptsInDb = found.map((f) => f.dataValues);

    const dtos = resOptsInDb.map(ReservationOptionRepo.mapDb2Dto);
    return nonEmpty(dtos.map(dto => ReservationOption.assemble(dto, this.sendAnalytics)));
  }

  public async create(reservationOption: ReservationOption) {
    const toDb = ReservationOptionRepo.mapDto2Db(reservationOption.toDto());
    return this.ReservationOption.create(toDb, {
      transaction: this.transaction,
    });
  }

  public async get(args: {
    userId: string;
    id: string;
  }): Promise<ReservationOption | null> {
    const { userId, id } = args;
    const found = await this.ReservationOption.findOne({
      where: {
        userId,
        id,
      },
      transaction: this.transaction,
    });
    if (!found) return null;

    const dto = ReservationOptionRepo.mapDb2Dto(found.dataValues);
    return ReservationOption.assemble(dto, this.sendAnalytics);
  }

  public async update(args: {
    reservationOption: ReservationOption,
    skip_updated_at?: true,
  }) {
    const { reservationOption, skip_updated_at } = args;
    const toDb = ReservationOptionRepo.mapDto2Db(reservationOption.toDto());
    const [ , [{ dataValues }]] = await this.ReservationOption.update(toDb, {
      where: {
        id: reservationOption.id.toString(),
      },
      returning: true,  // Postgres feature https://sequelize.org/api/v6/class/src/model.js~model#static-method-update
      transaction: this.transaction,
      ... (skip_updated_at ? { silent: true } : {}),
    });
    return ReservationOptionRepo.mapDb2Dto(dataValues);
  }

  public async delete(reservationOption: ReservationOption) {
    return this.ReservationOption.destroy({
      where: {
        id: reservationOption.id.toString(),
      },
      transaction: this.transaction,
    });
  }
}
