import { test, expect } from 'vitest';
import { GeneralSettingsRepo } from './GeneralSettingsRepo';
import { ReservationOptionRepo } from './ReservationOptionRepo';
import { ReservationRepo } from './ReservationRepo';
import { sendAnalyticsDummy as sendAnalytics } from '@shared/utils/utils';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize/models');

test(`GeneralSettingsRepo receives gs_for_reservations table`, () => {
  expect(() => new GeneralSettingsRepo(models.Calendar)).toThrow();
  expect(
    () => new GeneralSettingsRepo(models.GeneralSettingsForReservation),
  ).not.toThrow();
});

test(`ReservationOptionRepo receives reservation_options table`, () => {
  expect(() => new ReservationOptionRepo({
    ReservationOptionModel: models.Calendar,
    sendAnalytics,
  })).toThrow();
  expect(() => new ReservationOptionRepo({
    ReservationOptionModel: models.ReservationOption,
    sendAnalytics,
  })).not.toThrow();
});

test(`ReservationRepo receives reservations table`, () => {
  expect(() => new ReservationRepo(models.Calendar)).toThrow();
  expect(() => new ReservationRepo(models.Reservation)).not.toThrow();
});
