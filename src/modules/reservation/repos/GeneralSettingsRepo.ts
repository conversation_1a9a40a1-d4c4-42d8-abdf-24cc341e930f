import { GeneralSettings, GeneralSettingsDto } from '../domain/GeneralSettings';
import { IGeneralSettingsRepo } from './IGeneralSettingsRepo';
import { Timestamps } from '@shared/infra/database/sequelize/config/Timestamps';
import { Repository } from '@shared/core/Repository';
import { Slug } from '@shared/core/Slug';
import { Op } from 'sequelize';

type GeneralSettingsToDb = Omit<GeneralSettingsDto,
  'reservationLimits'
> & {
  resLimMaxDaysAhead: number;
  resLimMinTimeBeforeService: number;
};
export type GeneralSettingsInDb = GeneralSettingsToDb &
  Omit<Timestamps, 'deleted_at'>;

export class GeneralSettingsRepo
  extends Repository<GeneralSettings>
  implements IGeneralSettingsRepo
{
  private GeneralSettings: any; // eslint-disable-line @typescript-eslint/no-explicit-any

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public constructor(GeneralSettingsModel: any) {
    super();
    if (GeneralSettingsModel.tableName !== 'gs_for_reservations')
      throw Error(
        `Wrong model passed in: ${GeneralSettingsModel.tableName}, while correct is gs_for_reservations`,
      );
    this.GeneralSettings = GeneralSettingsModel;
  }

  public async create(gs: GeneralSettings) {
    const toDb = GeneralSettingsRepo.mapDto2Db(gs.toDto());
    await this.GeneralSettings.create(toDb, { transaction: this.transaction });
  }

  public async update(gs: GeneralSettings) {
    const toDb = GeneralSettingsRepo.mapDto2Db(gs.toDto());
    return this.GeneralSettings.update(toDb, {
      where: {
        id: gs.id.toString(),
      },
      transaction: this.transaction,
    });
  }

  public async get(id: string): Promise<GeneralSettings | null> {
    const found = await this.GeneralSettings.findOne({
      where: { id },
      transaction: this.transaction,
    });
    if (!found) return null;

    const dto = GeneralSettingsRepo.mapDb2Dto(found.dataValues);
    return GeneralSettings.assemble(dto);
  }

  public static mapDto2Db = (dto: GeneralSettingsDto): GeneralSettingsToDb => {
    const { reservationLimits, ...rest } = dto;
    return {
      ...rest,
      resLimMaxDaysAhead: reservationLimits.maxDaysAhead,
      resLimMinTimeBeforeService: reservationLimits.minTimeBeforeService,
    };
  };

  public static mapDb2Dto = (db: GeneralSettingsInDb): GeneralSettingsDto => {
    const { resLimMaxDaysAhead, resLimMinTimeBeforeService, ...rest } = db;
    return {
      ...rest,
      reservationLimits: {
        maxDaysAhead: resLimMaxDaysAhead,
        minTimeBeforeService: resLimMinTimeBeforeService,
      },
    };
  };

  public async isSlugAvailable(args: {
    userIdAsking: string;
    slug: Slug;
  }): Promise<boolean> {
    const { userIdAsking, slug } = args;
    const found = await this.GeneralSettings.findAll({
      where: {
        [Op.and]: [{ slug: slug.value }, { id: { [Op.ne]: userIdAsking } }],
      },
      transaction: this.transaction,
    });
    return !found.length;
  }
}
