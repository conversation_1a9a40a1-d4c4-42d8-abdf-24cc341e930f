import {
  ReservationOption,
  ReservationOptionDto,
} from '../domain/ReservationOption';
import { Dat } from '@shared/core/Dat';
import {
  ReservationOptionInDb,
  ReservationOptionRepo,
  ReservationOptionRepo as repo,
} from './ReservationOptionRepo';
import { IReservationOptionRepo } from './IReservationOptionRepo';
import { ReservationOptionsInDb as json } from '@shared/infra/database/sequelize/migrations/ReservationOptions.json';
import { nonEmpty } from '@shared/utils/utils';
import { sendAnalyticsDummy as sendAnalytics } from '@shared/utils/utils';

export class ReservationOptionRepoFake implements IReservationOptionRepo {
  private ReservationOptionsInDb: ReservationOptionInDb[];

  public constructor() {
    this.ReservationOptionsInDb = [...json];
  }

  public async create(reservationOption: ReservationOption) {
    const now = new Date().toISOString();
    this.ReservationOptionsInDb.push({
      ...repo.mapDto2Db(reservationOption.toDto()),
      created_at: now,
      updated_at: now,
      deleted_at: null,
    });
  }

  public async list(
    userId: string,
  ): Promise<[ReservationOption, ...ReservationOption[]] | null> {
    const resOptsInDb = this.ReservationOptionsInDb.filter(
      (restOpt) => restOpt.userId === userId && !restOpt.deleted_at,
    );
    if (!resOptsInDb.length) return null;

    const dtos = resOptsInDb.map(repo.mapDb2Dto);
    return nonEmpty(dtos.map(dto => ReservationOption.assemble(dto, sendAnalytics)));
  }

  public async get(args: {
    userId: string;
    id: string;
  }): Promise<ReservationOption | null> {
    const { userId, id } = args;

    const resOptInDb: ReservationOptionInDb = this.ReservationOptionsInDb.filter(
      (resOpt) => resOpt.id === id && resOpt.userId === userId,
    )[0];
    if (!resOptInDb) return null;

    const dto = repo.mapDb2Dto(resOptInDb);
    return ReservationOption.assemble(dto, sendAnalytics);
  }

  public async update(args: {
    reservationOption: ReservationOption,
    skip_updated_at?: true,
  }): Promise<ReservationOptionDto> {
    const { reservationOption, skip_updated_at } = args;
    const dto = reservationOption.toDto();
    const toDb = repo.mapDto2Db(dto);
    const idx = this.ReservationOptionsInDb.findIndex(
      (resOpt) => resOpt.id === dto.id && resOpt.userId === dto.userId,
    );
    const now = Dat.create().value.s;
    this.ReservationOptionsInDb[idx] = {
      ...this.ReservationOptionsInDb[idx], // to keep same created_at
      ...toDb,
      ...skip_updated_at? {} : { updated_at: now },
    };
    return ReservationOptionRepo.mapDb2Dto(this.ReservationOptionsInDb[idx]);
  }

  public async delete(reservationOption: ReservationOption): Promise<void> {
    const idx = this.ReservationOptionsInDb.findIndex(
      (resOpt) =>
        resOpt.id === reservationOption.id.toString() &&
        resOpt.userId === reservationOption.userId,
    );
    this.ReservationOptionsInDb.splice(idx, 1);
  }
}
