import { Calendar, CalendarType } from '../domain/Calendar';

export declare interface ICalendarRepo {
  create(calendar: Calendar): Promise<void>;
  get(id: string): Promise<Calendar | null>;
  delete(args: { userId: string; type: CalendarType }): Promise<number>;
  deleteById(args: {
    userId: string;
    type: CalendarType;
    calendarId: string;
  }): Promise<number>;
  getAvailability(userId: string): Promise<Calendar | null>;
  getReservations(userId: string): Promise<Calendar | null>;
  update(calendar: Calendar): Promise<void>;
}
