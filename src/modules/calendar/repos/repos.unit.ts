import { test, expect } from 'vitest';
import { CalendarRepo } from './CalendarRepo';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize/models');

test(`CalendarRepo receives calendars table`, () => {
  expect(() => new CalendarRepo(models.Reservation)).toThrow();
  expect(() => new CalendarRepo(models.Calendar)).not.toThrow();
});
