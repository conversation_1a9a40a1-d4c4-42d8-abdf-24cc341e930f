import { ICalendarRepo } from './ICalendarRepo';
import { Calendar, CalendarType } from '../domain/Calendar';
import { CalendarInDb, CalendarRepo } from './CalendarRepo';
import { CalendarsInDb as json } from '@shared/infra/database/sequelize/migrations/Calendars.json';
import { Dat } from '@shared/core/Dat';

export class CalendarRepoFake implements ICalendarRepo {
  private CalendarsInDb: CalendarInDb[];

  public constructor() {
    this.CalendarsInDb = [...json];
  }

  public async create(calendar: Calendar) {
    const now = new Date().toISOString();
    this.CalendarsInDb.push({
      ...CalendarRepo.mapDto2Db(calendar.toDto()),
      created_at: now,
      updated_at: now,
    });
  }

  public async get(id: string): Promise<Calendar | null> {
    const calendarInDb = this.CalendarsInDb.filter(
      (calendar) => calendar.id === id,
    )[0];
    if (!calendarInDb) return null;
    return Calendar.assemble({
      ...calendarInDb,
      type: CalendarRepo.getDomainType(calendarInDb.type),
    });
  }

  public async getAvailability(userId: string): Promise<Calendar | null> {
    return this.getCalendar({ userId, type: CalendarType.AVAILABILITY });
  }

  public async getReservations(userId: string): Promise<Calendar | null> {
    return this.getCalendar({ userId, type: CalendarType.RESERVATIONS });
  }

  private getCalendar(args: {
    userId: string;
    type: CalendarType;
  }): Calendar | null {
    const { userId, type } = args;
    const calendarInDb = this.CalendarsInDb.filter(
      (calendar) =>
        calendar.userId === userId &&
        calendar.type === CalendarRepo.getDbType(type),
    )[0];
    if (!calendarInDb) return null;
    return Calendar.assemble(CalendarRepo.mapDb2Dto(calendarInDb));
  }

  public async delete(args: {
    userId: string;
    type: CalendarType;
  }): Promise<number> {
    const { userId, type } = args;
    const initialLength = this.CalendarsInDb.length;
    this.CalendarsInDb = this.CalendarsInDb.filter(
      (calendar) =>
        !(
          calendar.userId === userId &&
          calendar.type === CalendarRepo.getDbType(type)
        ),
    );
    return initialLength - this.CalendarsInDb.length;
  }

  public async deleteById(args: {
    userId: string;
    type: CalendarType;
    calendarId: string;
  }) {
    const { userId, type, calendarId } = args;
    const initialLength = this.CalendarsInDb.length;
    this.CalendarsInDb = this.CalendarsInDb.filter(
      (calendar) =>
        !(
          calendar.userId === userId &&
          calendar.type === CalendarRepo.getDbType(type) &&
          calendar.id === calendarId
        ),
    );
    return initialLength - this.CalendarsInDb.length;
  }

  public async update(calendar: Calendar): Promise<void> {
    const dto = calendar.toDto();
    const toDb = CalendarRepo.mapDto2Db(dto);
    const idx = this.CalendarsInDb.findIndex(
      (c) => c.id === dto.id && c.userId === dto.userId,
    );
    const now = Dat.create().value.s;
    this.CalendarsInDb[idx] = {
      ...this.CalendarsInDb[idx], // to keep same created_at
      ...toDb,
      updated_at: now,
    };
  }
}
