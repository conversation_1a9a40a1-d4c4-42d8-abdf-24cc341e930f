import { Repository } from '@shared/core/Repository';
import { ICalendarRepo } from './ICalendarRepo';
import { Calendar, CalendarDto, CalendarType } from '../domain/Calendar';
import { Timestamps } from '@shared/infra/database/sequelize/config/Timestamps';

type CalendarToDb = CalendarDto;

export type CalendarInDb = CalendarToDb & Omit<Timestamps, 'deleted_at'>;

export class CalendarRepo extends Repository<Calendar> implements ICalendarRepo {
  private Calendar: any; // eslint-disable-line @typescript-eslint/no-explicit-any

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public constructor(CalendarModel: any) {
    super();
    if (CalendarModel.tableName !== 'calendars')
      throw Error(
        `Wrong model passed in: ${CalendarModel.tableName}, while correct is calendars`,
      );
    this.Calendar = CalendarModel;
  }

  public static mapDto2Db = (dto: CalendarDto): CalendarToDb => {
    const { type, ...rest } = dto;
    return { ...rest, type: CalendarRepo.getDbType(type) };
  };

  public static mapDb2Dto = (calendarInDb: CalendarInDb): CalendarDto => {
    const { type, ...rest } = calendarInDb;
    return { ...rest, type: CalendarRepo.getDomainType(type) };
  };

  public async create(calendar: Calendar) {
    const toDb = CalendarRepo.mapDto2Db(calendar.toDto());
    await this.Calendar.create(toDb, { transaction: this.transaction });
  }

  public async get(id: string): Promise<Calendar | null> {
    const found = await this.Calendar.findOne({
      where: { id },
      transaction: this.transaction,
    });
    if (!found) return null;
    return Calendar.assemble(CalendarRepo.mapDb2Dto(found.dataValues));
  }

  public async delete(args: {
    userId: string;
    type: CalendarType;
  }): Promise<number> {
    const { userId, type } = args;
    return this.Calendar.destroy({
      where: {
        userId,
        type: CalendarRepo.getDbType(type),
      },
      transaction: this.transaction,
    });
  }

  public async deleteById(args: {
    calendarId: string;
    userId: string;
    type: CalendarType;
  }) {
    const { userId, type, calendarId } = args;
    return this.Calendar.destroy({
      where: {
        userId,
        id: calendarId,
        type: CalendarRepo.getDbType(type),
      },
      transaction: this.transaction,
    });
  }

  public async getAvailability(userId: string): Promise<Calendar | null> {
    return this.getCalendar({ userId, type: CalendarType.AVAILABILITY });
  }

  public async getReservations(userId: string): Promise<Calendar | null> {
    return this.getCalendar({ userId, type: CalendarType.RESERVATIONS });
  }

  private async getCalendar(args: {
    userId: string;
    type: CalendarType;
  }): Promise<Calendar | null> {
    const { userId, type } = args;
    const found = await this.Calendar.findOne({
      where: {
        userId,
        type: CalendarRepo.getDbType(type),
      },
      transaction: this.transaction,
    });
    if (!found) return null;
    return Calendar.assemble(CalendarRepo.mapDb2Dto(found.dataValues));
  }

  public async update(calendar: Calendar) {
    const toDb = CalendarRepo.mapDto2Db(calendar.toDto());
    return this.Calendar.update(toDb, {
      where: {
        id: calendar.id.toString(),
      },
      transaction: this.transaction,
    });
  }

  public static getDomainType(repoType: string): CalendarType {
    let domainType;
    switch (repoType) {
      case 'A':
        domainType = CalendarType.AVAILABILITY;
        break;

      case 'R':
        domainType = CalendarType.RESERVATIONS;
        break;

      default:
        throw Error(`Invalid calendar type retrieved from repo ${repoType}`);
    }
    return domainType;
  }

  public static getDbType(domainType: CalendarType | string): string {
    let dbType;
    switch (domainType) {
      case CalendarType.AVAILABILITY:
        dbType = 'A';
        break;

      case CalendarType.RESERVATIONS:
        dbType = 'R';
        break;

      default:
        throw Error(`Invalid calendar type ${domainType}`);
    }
    return dbType;
  }
}
