// Este archivo no debe tener ningún código fake, ni test, ya que es lo que se deploya a producción. Usar utils/testExternal.ts para ese tipo de código importado desde otros módulos.
import { Every as _Every } from '../reservation/domain/Every';
import {
  TimeReference as _TimeReference,
  TimeReferenceDto as _TimeReferenceDto,
} from '../reservation/domain/TimeReference';
import { Name as _Name } from '../reservation/domain/Name';
import { BusinessUserCreatedEventDto as _BusinessUserCreatedEventDto } from '../user/domain/events/BusinessUserCreatedEvent';
import {
  ReservationLimits as _ReservationLimits,
  ReservationLimitsDto as _ReservationLimitsDto,
} from '../reservation/domain/ReservationLimits';
import {
  MaxDaysAhead as _MaxDaysAhead,
} from '../reservation/domain/MaxDaysAhead';

export const Every = _Every;
export type EveryT = _Every;
export const TimeReference = _TimeReference;
export type TimeReferenceT = _TimeReference;
export type TimeReferenceDto = _TimeReferenceDto;
export const Name = _Name;
export type BusinessUserCreatedEventDto = _BusinessUserCreatedEventDto;
export const ReservationLimits = _ReservationLimits;
export type ReservationLimitsT = _ReservationLimits;
export type ReservationLimitsDto = _ReservationLimitsDto;
export const MaxDaysAhead = _MaxDaysAhead;
