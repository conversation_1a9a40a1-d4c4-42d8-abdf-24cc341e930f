import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';

export namespace SpotsErrors {
  export class MinimumValueIs0 extends BadRequest {
    public constructor(spots: number) {
      super({ message: `Minimum value for spots is 0, but got ${spots}` });
    }
  }

  export class SpotsShouldBeInteger extends BadRequest {
    public constructor(spots: number) {
      super({ message: `Spots should be integer, but got ${spots}` });
    }
  }
}

patch({ SpotsErrors });
