import { expect, test, it, describe, vi } from 'vitest';
import { Every, TimeReference } from '../external';
import { TimeWithSpots } from './TimeWithSpots';
import { SlotWithSpots } from './SlotWithSpots';
import { createSlotWithSpots } from '../utils/test';
import { Slot } from './Slot';
import { Spots } from './Spots';
import { Dat } from '@shared/core/Dat';
import { PositiveInt } from '@shared/core/PositiveInt';
import { nonEmpty } from '@shared/utils/utils';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const sendAnalytics = vi.fn();

const maxTimes = PositiveInt.create({ value: 9999999 }).value;

describe(`getPossibleTimes`, () => {
  test(`using zone America/Cordoba (-3)`, async () => {
    const timeReference = TimeReference.create({
      hour: 8,
      minute: 30,
      zone: 'America/Cordoba',
    }).value;

    const slotsDto = [
      {
        slot: {
          start: '2023-12-31T23:31:00-03:00', // Permitidos 00:00 y 00:30
          end: '2024-01-01T01:15:00-03:00',
        },
        spots: 5,
      },
      {
        slot: {
          start: '2024-01-01T08:00:00-03:00', // Permitidos 08:00 y 8:30
          end: '2024-01-01T09:00:00-03:00',
        },
        spots: 1,
      },
      {
        slot: {
          start: '2024-01-01T10:01:00-03:00', // Permitidos 10:30 y 11:00
          end: '2024-01-01T11:59:00-03:00',
        },
        spots: 2,
      },
      {
        slot: {
          start: '2024-01-01T14:01:00-03:00', // Ninguno
          end: '2024-01-01T14:59:00-03:00',
        },
        spots: 2,
      },
    ];

    const slots = nonEmpty(slotsDto.map(createSlotWithSpots));
    const every = Every.create({ value: 30 }).value;

    const possibleTimes = await TimeWithSpots.getPossibleTimes({
      timeReference,
      chunks: slots,
      every,
      duration: PositiveInt.create({ value: 30 }).value,
      maxTimes,
    }, sendAnalytics);

    if (!possibleTimes) throw Error('Expected possibleTimes to be defined');
    const possibleTimesDto = possibleTimes.map((time) => time.toDto());

    expect(possibleTimes.length).toBe(6);
    expect(possibleTimesDto).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          time: '2024-01-01T03:00Z', // 2024-01-01T00:00:00-03:00
          spots: 5,
        }),
        expect.objectContaining({
          time: '2024-01-01T03:30Z', // 2024-01-01T00:30:00-03:00
          spots: 5,
        }),
        expect.objectContaining({
          time: '2024-01-01T11:00Z', // 2024-01-01T08:00:00-03:00
          spots: 1,
        }),
        expect.objectContaining({
          time: '2024-01-01T11:30Z', // 2024-01-01T08:30:00-03:00
          spots: 1,
        }),
        expect.objectContaining({
          time: '2024-01-01T13:30Z', // 2024-01-01T10:30:00-03:00
          spots: 2,
        }),
        expect.objectContaining({
          time: '2024-01-01T14:00Z', // 2024-01-01T11:00:00-03:00
          spots: 2,
        }),
      ]),
    );
  });

  it(`honors maxTime argument`, async () => {
    // region same preparation as first test
    const timeReference = TimeReference.create({
      hour: 8,
      minute: 30,
      zone: 'America/Cordoba',
    }).value;

    const slotsDto = [
      {
        slot: {
          start: '2023-12-31T23:31:00-03:00', // Permitidos 00:00 y 00:30
          end: '2024-01-01T01:15:00-03:00',
        },
        spots: 5,
      },
      {
        slot: {
          start: '2024-01-01T08:00:00-03:00', // Permitidos 08:00 y 8:30
          end: '2024-01-01T09:00:00-03:00',
        },
        spots: 1,
      },
      {
        slot: {
          start: '2024-01-01T10:01:00-03:00', // Permitidos 10:30 y 11:00
          end: '2024-01-01T11:59:00-03:00',
        },
        spots: 2,
      },
      {
        slot: {
          start: '2024-01-01T14:01:00-03:00', // Ninguno
          end: '2024-01-01T14:59:00-03:00',
        },
        spots: 2,
      },
    ];

    const slots = nonEmpty(slotsDto.map(createSlotWithSpots));
    const every = Every.create({ value: 30 }).value;
    // endregion

    const maxTimesValue = chance.integer({ min: 1, max: 6 });
    const possibleTimes = await TimeWithSpots.getPossibleTimes({
      timeReference,
      chunks: slots,
      every,
      duration: PositiveInt.create({ value: 30 }).value,
      maxTimes: PositiveInt.create({ value: maxTimesValue }).value,
    }, sendAnalytics);

    if (!possibleTimes) throw Error('Expected possibleTimes to be defined');
    const possibleTimesDto = possibleTimes.map((time) => time.toDto());

    expect(possibleTimes.length).toBe(maxTimesValue);
    expect(possibleTimesDto).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          time: '2024-01-01T03:00Z', // 2024-01-01T00:00:00-03:00
          spots: 5,
        }),
        expect.objectContaining({
          time: '2024-01-01T03:30Z', // 2024-01-01T00:30:00-03:00
          spots: 5,
        }),
        expect.objectContaining({
          time: '2024-01-01T11:00Z', // 2024-01-01T08:00:00-03:00
          spots: 1,
        }),
      ].slice(0, maxTimesValue)),
    );
  });

  test(`when the same time can be allocate different quantity of spots depending on the slot used to start the computation, leave the greater computation of spots`, async () => {
    const timeReference = TimeReference.create({
      hour: 0,
      minute: 0,
      zone: 'America/Cordoba',
    }).value;

    const slotsDto = [
      {
        slot: {
          start: '2024-01-13T00:00:00.000-03:00', // Permitidos 00:10 1 (offset -03:00)
          end: '2024-01-13T00:10:00.000-03:00',
        },
        spots: 1,
      },
      {
        slot: {
          start: '2024-01-13T00:10:00.000-03:00', // Permitidos 00:10 2 y 00:20 2 (offset -03:00)
          end: '2024-01-13T00:30:00.000-03:00',
        },
        spots: 2,
      },
    ];

    const slots = nonEmpty(slotsDto.map(createSlotWithSpots));
    const every = Every.create({ value: 10 }).value;

    const possibleTimes = await TimeWithSpots.getPossibleTimes({
      timeReference,
      chunks: slots,
      every,
      duration: PositiveInt.create({ value: 10 }).value,
      maxTimes,
    }, sendAnalytics);

    if (!possibleTimes) throw Error('Expected possibleTimes to be defined');
    const possibleTimesDto = possibleTimes.map((time) => time.toDto());

    expect(possibleTimes.length).toBe(3);
    expect(possibleTimesDto).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          time: '2024-01-13T03:00Z', // 2024-01-13T00:00:00.000-03:00
          spots: 1,
        }),
        expect.objectContaining({
          time: '2024-01-13T03:10Z', // 2024-01-13T00:10:00.000-03:00
          spots: 2,
        }),
        expect.objectContaining({
          time: '2024-01-13T03:20Z', // 2024-01-13T00:20:00.000-03:00
          spots: 2,
        }),
      ]),
    );
  });

  test(`using zone Pacific/Tongatapu (+13)`, async () => {
    const timeReference = TimeReference.create({
      hour: 8,
      minute: 30,
      zone: 'Pacific/Tongatapu', // +13
    }).value;

    const slotsDto = [
      {
        slot: {
          start: '2023-12-31T23:31:00+13:00', // Permitidos 00:00 y 00:30
          end: '2024-01-01T01:15:00+13:00',
        },
        spots: 5,
      },
      {
        slot: {
          start: '2024-01-01T08:00:00+13:00', // Permitidos 08:00 y 8:30
          end: '2024-01-01T09:00:00+13:00',
        },
        spots: 1,
      },
      {
        slot: {
          start: '2024-01-01T10:01:00+13:00', // Permitidos 10:30 y 11:00
          end: '2024-01-01T11:59:00+13:00',
        },
        spots: 2,
      },
      {
        slot: {
          start: '2024-01-01T14:01:00+13:00', // Ninguno
          end: '2024-01-01T14:59:00+13:00',
        },
        spots: 2,
      },
    ];

    const slots = nonEmpty(slotsDto.map(createSlotWithSpots));
    const every = Every.create({ value: 30 }).value;

    const possibleTimes = await TimeWithSpots.getPossibleTimes({
      timeReference,
      chunks: slots,
      every,
      duration: PositiveInt.create({ value: 30 }).value,
      maxTimes,
    }, sendAnalytics);

    if (!possibleTimes) throw Error('Expected possibleTimes to be defined');
    const possibleTimesDto = possibleTimes.map((time) => time.toDto());

    expect(possibleTimes.length).toBe(6);
    expect(possibleTimesDto).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          time: '2023-12-31T11:00Z', // 2024-01-01T00:00:00+13:00
          spots: 5,
        }),
        expect.objectContaining({
          time: '2023-12-31T11:30Z', // 2024-01-01T00:30:00+13:00
          spots: 5,
        }),
        expect.objectContaining({
          time: '2023-12-31T19:00Z', // 2024-01-01T08:00:00+13:00
          spots: 1,
        }),
        expect.objectContaining({
          time: '2023-12-31T19:30Z', // 2024-01-01T08:30:00+13:00
          spots: 1,
        }),
        expect.objectContaining({
          time: '2023-12-31T21:30Z', // 2024-01-01T10:30:00+13:00
          spots: 2,
        }),
        expect.objectContaining({
          time: '2023-12-31T22:00Z', // 2024-01-01T11:00:00+13:00
          spots: 2,
        }),
      ]),
    );
  });

  test(`mixing zones America/Cordoba (-3) & Pacific/Tongatapu (+13)`, async () => {
    const timeReference = TimeReference.create({
      hour: 0,
      minute: 30,
      zone: 'America/Cordoba',
    }).value;

    const slotsDto = [
      {
        slot: {
          start: '2023-12-31T23:31:00+13:00', // Permitidos 00:00 y 00:30
          end: '2024-01-01T01:15:00+13:00',
        },
        spots: 5,
      },
      {
        slot: {
          start: '2024-01-01T08:00:00+13:00', // Permitidos 08:00 y 8:30
          end: '2024-01-01T09:00:00+13:00',
        },
        spots: 1,
      },
      {
        slot: {
          start: '2024-01-01T10:01:00+13:00', // Permitidos 10:30 y 11:00
          end: '2024-01-01T11:59:00+13:00',
        },
        spots: 2,
      },
      {
        slot: {
          start: '2024-01-01T14:01:00+13:00', // Ninguno
          end: '2024-01-01T14:59:00+13:00',
        },
        spots: 2,
      },
    ];

    const slots = nonEmpty(slotsDto.map(createSlotWithSpots));
    const every = Every.create({ value: 30 }).value;

    const possibleTimes = await TimeWithSpots.getPossibleTimes({
      timeReference,
      chunks: slots,
      every,
      duration: PositiveInt.create({ value: 30 }).value,
      maxTimes,
    }, sendAnalytics);

    if (!possibleTimes) throw Error('Expected possibleTimes to be defined');
    const possibleTimesDto = possibleTimes.map((time) => time.toDto());

    expect(possibleTimes.length).toBe(6);
    expect(possibleTimesDto).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          time: '2023-12-31T11:00Z', // 2024-01-01T00:00:00+13:00
          spots: 5,
        }),
        expect.objectContaining({
          time: '2023-12-31T11:30Z', // 2024-01-01T00:30:00+13:00
          spots: 5,
        }),
        expect.objectContaining({
          time: '2023-12-31T19:00Z', // 2024-01-01T08:00:00+13:00
          spots: 1,
        }),
        expect.objectContaining({
          time: '2023-12-31T19:30Z', // 2024-01-01T08:30:00+13:00
          spots: 1,
        }),
        expect.objectContaining({
          time: '2023-12-31T21:30Z', // 2024-01-01T10:30:00+13:00
          spots: 2,
        }),
        expect.objectContaining({
          time: '2023-12-31T22:00Z', // 2024-01-01T11:00:00+13:00
          spots: 2,
        }),
      ]),
    );
  });

  it(`considers adjacent slots`, async () => {
    const timeReference = TimeReference.create({
      hour: 8,
      minute: 30,
      zone: 'America/Cordoba',
    }).value;

    const slotsDto = [
      {
        slot: {
          start: '2023-12-31T23:31:00-03:00', // Permitidos 00:00, 00:30 y 01:00 gracias al siguiente slot
          end: '2024-01-01T01:15:00-03:00',
        },
        spots: 5,
      },
      {
        slot: {
          start: '2024-01-01T01:15:00-03:00', // Permitidos 01:30
          end: '2024-01-01T02:00:00-03:00',
        },
        spots: 1,
      },
      {
        slot: {
          start: '2024-01-01T10:01:00-03:00', // Permitidos 10:30, 11:00 y 11:30 gracias al próximo slot
          end: '2024-01-01T11:59:00-03:00',
        },
        spots: 2,
      },
      {
        slot: {
          start: '2024-01-01T11:50:00-03:00', // Permitidos 12:00
          end: '2024-01-01T12:59:00-03:00',
        },
        spots: 2,
      },
      {
        slot: {
          start: '2024-01-01T14:01:00-03:00', // Ninguno
          end: '2024-01-01T14:59:00-03:00',
        },
        spots: 2,
      },
    ];

    const slots = nonEmpty(slotsDto.map(createSlotWithSpots));
    const every = Every.create({ value: 30 }).value;

    const possibleTimes = await TimeWithSpots.getPossibleTimes({
      timeReference,
      chunks: slots,
      every,
      duration: PositiveInt.create({ value: 30 }).value,
      maxTimes,
    }, sendAnalytics);

    if (!possibleTimes) throw Error('Expected possibleTimes to be defined');
    const possibleTimesDto = possibleTimes.map((time) => time.toDto());

    expect(possibleTimes.length).toBe(8);
    expect(possibleTimesDto).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          time: '2024-01-01T03:00Z', // 2024-01-01T00:00:00-03:00
          spots: 5,
        }),
        expect.objectContaining({
          time: '2024-01-01T03:30Z', // 2024-01-01T00:30:00-03:00
          spots: 5,
        }),
        expect.objectContaining({
          time: '2024-01-01T04:00Z', // 2024-01-01T01:00:00-03:00
          spots: 1,
        }),
        expect.objectContaining({
          time: '2024-01-01T04:30Z', // 2024-01-01T01:30:00-03:00
          spots: 1,
        }),
        expect.objectContaining({
          time: '2024-01-01T13:30Z', // 2024-01-01T10:30:00-03:00
          spots: 2,
        }),
        expect.objectContaining({
          time: '2024-01-01T14:00Z', // 2024-01-01T11:00:00-03:00
          spots: 2,
        }),
        expect.objectContaining({
          time: '2024-01-01T14:30Z', // 2024-01-01T11:30:00-03:00
          spots: 2,
        }),
        expect.objectContaining({
          time: '2024-01-01T15:00Z', // 2024-01-01T12:00:00-03:00
          spots: 2,
        }),
      ]),
    );
  });

  it(`doesn't find when time reference falls outside slot`, async () => {
    const timeReference = TimeReference.create({
      hour: 8,
      minute: 30,
      zone: 'America/Cordoba',
    }).value;

    const slotsDto = [
      {
        slot: {
          start: '2023-12-31T23:31:00-03:00', // Ninguno
          end: '2023-12-31T23:59:00-03:00',
        },
        spots: 5,
      },
      {
        slot: {
          start: '2024-01-01T08:01:00-03:00', // Ninguno
          end: '2024-01-01T08:29:00-03:00',
        },
        spots: 1,
      },
    ];

    const slots = nonEmpty(slotsDto.map(createSlotWithSpots));
    const every = Every.create({ value: 30 }).value;

    const possibleTimes = await TimeWithSpots.getPossibleTimes({
      timeReference,
      chunks: slots,
      every,
      duration: PositiveInt.create({ value: 30 }).value,
      maxTimes,
    }, sendAnalytics);

    expect(possibleTimes).toBe(null);
  });

  it(`doesn't find when slot isn't large enough`, async () => {
    const timeReference = TimeReference.create({
      hour: 8,
      minute: 30,
      zone: 'America/Cordoba',
    }).value;

    const slotsDto = [
      {
        slot: {
          start: '2023-12-31T23:30:00-03:00', // Ninguno
          end: '2023-12-31T23:59:00-03:00',
        },
        spots: 5,
      },
    ];

    const slots = nonEmpty(slotsDto.map(createSlotWithSpots));
    const every = Every.create({ value: 30 }).value;

    const possibleTimes = await TimeWithSpots.getPossibleTimes({
      timeReference,
      chunks: slots,
      every,
      duration: PositiveInt.create({ value: 30 }).value,
      maxTimes,
    }, sendAnalytics);

    expect(possibleTimes).toBe(null);
  });
});

describe(`getSpotsForReservation`, () => {
  test(`outputs the less spots from the chunks`, () => {
    const input = {
      start: '2023-12-31T23:31:00-03:00',
      end: '2024-01-01T12:00:00-03:00',
      chunks: [
        {
          slot: {
            start: '2023-12-31T23:31:00-03:00',
            end: '2024-01-01T01:15:00-03:00',
          },
          spots: 5,
        },
        {
          slot: {
            start: '2024-01-01T01:15:00-03:00',
            end: '2024-01-01T09:00:00-03:00',
          },
          spots: 2,
        },
        {
          slot: {
            start: '2024-01-01T09:00:00-03:00',
            end: '2024-01-01T12:00:00-03:00',
          },
          spots: 3,
        },
      ],
    };

    const spots = TimeWithSpots.getSpotsForReservation({
      slot: Slot.create({
        start: Dat.create({ value: input.start }).value,
        end: Dat.create({ value: input.end }).value,
      }).value,
      chunks: input.chunks.map((chunk) =>
        SlotWithSpots.create({
          slot: Slot.create({
            start: Dat.create({ value: chunk.slot.start }).value,
            end: Dat.create({ value: chunk.slot.end }).value,
          }).value,
          spots: Spots.create(chunk.spots, sendAnalytics).value,
        }),
      ),
    }, sendAnalytics);

    expect(spots?.value).toBe(2);
  });

  it(`outputs null for 0 spots`, () => {
    const input = {
      start: '2023-12-31T23:31:00-03:00',
      end: '2024-01-01T12:00:00-03:00',
      chunks: [
        {
          slot: {
            start: '2023-12-31T23:31:00-03:00',
            end: '2024-01-01T01:15:00-03:00',
          },
          spots: 5,
        },
        {
          slot: {
            start: '2024-01-01T01:15:00-03:00',
            end: '2024-01-01T09:00:00-03:00',
          },
          spots: 2,
        },
        {
          slot: {
            start: '2024-01-01T09:00:00-03:00',
            end: '2024-01-01T12:00:00-03:00',
          },
          spots: 0,
        },
      ],
    };

    const spots = TimeWithSpots.getSpotsForReservation({
      slot: Slot.create({
        start: Dat.create({ value: input.start }).value,
        end: Dat.create({ value: input.end }).value,
      }).value,
      chunks: input.chunks.map((chunk) =>
        SlotWithSpots.create({
          slot: Slot.create({
            start: Dat.create({ value: chunk.slot.start }).value,
            end: Dat.create({ value: chunk.slot.end }).value,
          }).value,
          spots: Spots.create(chunk.spots, sendAnalytics).value,
        }),
      ),
    }, sendAnalytics);

    expect(spots).toBe(null);
  });

  test(`if chunks don't completely cover the reservation, outputs null (case 1)`, () => {
    const input = {
      start: '2023-12-31T23:31:00-03:00',
      end: '2024-01-01T09:00:00-03:00',
      chunks: [
        {
          slot: {
            start: '2023-12-31T23:31:00-03:00',
            end: '2024-01-01T01:15:00-03:00',
          },
          spots: 5,
        },
        {
          slot: {
            start: '2024-01-01T01:16:00-03:00',
            end: '2024-01-01T09:00:00-03:00',
          },
          spots: 2,
        },
      ],
    };

    const spots = TimeWithSpots.getSpotsForReservation({
      slot: Slot.create({
        start: Dat.create({ value: input.start }).value,
        end: Dat.create({ value: input.end }).value,
      }).value,
      chunks: input.chunks.map((chunk) =>
        SlotWithSpots.create({
          slot: Slot.create({
            start: Dat.create({ value: chunk.slot.start }).value,
            end: Dat.create({ value: chunk.slot.end }).value,
          }).value,
          spots: Spots.create(chunk.spots, sendAnalytics).value,
        }),
      ),
    }, sendAnalytics);

    expect(spots).toBe(null);
  });

  test(`if chunks don't completely cover the reservation, outputs null (case 2)`, () => {
    const input = {
      start: '2023-12-31T23:30:00-03:00',
      end: '2024-01-01T08:00:00-03:00',
      chunks: [
        {
          slot: {
            start: '2023-12-31T23:31:00-03:00',
            end: '2024-01-01T01:15:00-03:00',
          },
          spots: 5,
        },
        {
          slot: {
            start: '2024-01-01T01:15:00-03:00',
            end: '2024-01-01T09:00:00-03:00',
          },
          spots: 2,
        },
      ],
    };

    const spots = TimeWithSpots.getSpotsForReservation({
      slot: Slot.create({
        start: Dat.create({ value: input.start }).value,
        end: Dat.create({ value: input.end }).value,
      }).value,
      chunks: input.chunks.map((chunk) =>
        SlotWithSpots.create({
          slot: Slot.create({
            start: Dat.create({ value: chunk.slot.start }).value,
            end: Dat.create({ value: chunk.slot.end }).value,
          }).value,
          spots: Spots.create(chunk.spots, sendAnalytics).value,
        }),
      ),
    }, sendAnalytics);

    expect(spots).toBe(null);
  });
});
