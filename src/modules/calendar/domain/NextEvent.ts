import { ValueObject } from '@shared/core/domain/ValueObject';
import { nonEmpty } from '@shared/utils/utils';
import { EventRetrieved, EventRetrievedDto } from './EventRetrieved';
import { Detected } from './Identifier';

type DetectedDto = {
  identifier: string;
  spots: number;
};

type NextEventProps = {
  event: EventRetrieved;
  detected: [Detected, ...Detected[]] | null;
};
export type NextEventDto = {
  event: EventRetrievedDto;
  // Lo correcto para detected sería:
  // detected: [DetectedDto, ...DetectedDto[]] | null;
  // ...pero lo pongo así por la misma razón de src/modules/calendar/useCases/shared.ts
  detected: DetectedDto[] | null;
};

export class NextEvent extends ValueObject<NextEventProps, NextEventDto> {
  private __class = this.constructor.name;

  get event(): EventRetrieved {
    return this.props.event;
  }

  get detected(): [Detected, ...Detected[]] | null {
    return this.props.detected;
  }

  private constructor(props: NextEventProps) {
    super(props);
  }

  public static create(props: NextEventProps) {
    return new NextEvent(props);
  }

  public toDto(): NextEventDto {
    let detected = null;
    if (this.props.detected)
      detected = nonEmpty(
        this.props.detected.map(({ identifier, spots }) => ({
          identifier: identifier.value,
          spots: spots.value,
        })),
      );

    return {
      event: this.props.event.toDto(),
      detected,
    };
  }
}
