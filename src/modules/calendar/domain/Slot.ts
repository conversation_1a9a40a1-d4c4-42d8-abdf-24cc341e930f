import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { SlotErrors } from './SlotErrors';
import { Dat } from '@shared/core/Dat';
import { logAndThrow } from '@shared/utils/utils';

export interface SlotProps {
  start: Dat;
  end: Dat;
}

export interface SlotDto {
  start: string;
  end: string;
}

export class Slot extends ValueObject<SlotProps, SlotDto> {
  private __class = this.constructor.name;
  get start(): Dat {
    return this.props.start;
  }

  get end(): Dat {
    return this.props.end;
  }

  private constructor(props: SlotProps) {
    super(props);
  }

  public static create(props: SlotProps): Result<Slot> {
    const { start, end } = props;
    start.stripSeconds();
    end.stripSeconds();

    if (start.t >= end.t)
      return Result.fail(new SlotErrors.EndMustBeAfterStart({ start, end }));

    return Result.ok<Slot>(new Slot({ start, end }));
  }

  public static consolidate(slots: Slot[]): Slot[] {
    if (slots.length === 0) return [];

    // Sort slots by start time
    slots.sort((a, b) => a.start.t - b.start.t);

    // Initialize variables to track the current range
    let currentStart = slots[0].start;
    let currentEnd = slots[0].end;

    const consolidated = [];
    // Iterate through the sorted time ranges
    for (let i = 1; i < slots.length; i++) {
      const { start: nextStart, end: nextEnd } = slots[i];

      // Check for an overlap with the current range
      if (nextStart.t <= currentEnd.t) {
        // Merge the overlapping ranges
        currentEnd = Dat.create({ value: Math.max(currentEnd.t, nextEnd.t) }).value;
      } else {
        const slotOrError = Slot.create({ start: currentStart, end: currentEnd });
        if (slotOrError.isFailure)
          logAndThrow({
            msg: `Error when consolidating slots (1)`,
            data: { currentStart, currentEnd },
          });

        // Add the consolidated range to the result array
        consolidated.push(slotOrError.value);

        // Update the current range to the non-overlapping range
        currentStart = nextStart;
        currentEnd = nextEnd;
      }
    }

    // Add the last consolidated range to the result array
    const slotOrError = Slot.create({ start: currentStart, end: currentEnd });
    if (slotOrError.isFailure)
      logAndThrow({
        msg: `Error when consolidating slots (2)`,
        data: { currentStart, currentEnd },
      });
    consolidated.push(slotOrError.value);

    return consolidated;
  }

  public static adjustAllowSlots(args: {
    allowSlots: Slot[];
    busySlots: Slot[];
  }): Slot[] {
    const { allowSlots, busySlots } = args;

    const adjustedAllowSlots: Slot[] = [];

    allowSlots.forEach((allowSlot) => {
      let currentSlot: Slot | null = allowSlot;

      busySlots.forEach((busySlot) => {
        const allowStart = currentSlot?.start.t || 0;
        const allowEnd = currentSlot?.end.t || 0;
        const busyStart = busySlot.start.t;
        const busyEnd = busySlot.end.t;

        if (allowStart < busyEnd && allowEnd > busyStart) {
          // Adjust allowSlot by removing the overlapping portion
          if (allowStart < busyStart) {
            const adjustedSlot = Slot.create({
              start: allowSlot.start,
              end: busySlot.start,
            });
            if (adjustedSlot.isSuccess) {
              adjustedAllowSlots.push(adjustedSlot.value);
            }
          }

          if (allowEnd > busyEnd) {
            currentSlot = Slot.create({
              start: busySlot.end,
              end: allowSlot.end,
            }).value;
          } else {
            currentSlot = null;
          }
        }
      });

      // If there's no overlap or the adjusted slot ends before the busy range, add it
      if (currentSlot) {
        adjustedAllowSlots.push(currentSlot);
      }
    });

    return adjustedAllowSlots;
  }

  public toDto(): SlotDto {
    return {
      start: this.start.s,
      end: this.end.s,
    };
  }

  public static assemble(dto: SlotDto): Slot {
    return Slot.create({
      start: Dat.create({ value: dto.start }).value,
      end: Dat.create({ value: dto.end }).value,
    }).value;
  }
}
