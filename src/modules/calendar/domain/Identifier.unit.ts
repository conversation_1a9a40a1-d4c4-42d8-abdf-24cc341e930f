import { expect, test, it, describe, vi } from 'vitest';
import { Identifier } from './Identifier';
import { Spots } from './Spots';

const sendAnalytics = vi.fn();

test(`creation`, () => {
  const identifier = 'location_1';

  const result = Identifier.create({ value: identifier }, sendAnalytics);

  expect(result).toMatchObject({
    isSuccess: true,
    value: {
      value: identifier,
      query: `#${identifier}`,
    },
  });
});

describe(`Creation fails if identifier`, () => {
  it(`has spaces`, () => {
    const identifier = 'loca tion';

    const result = Identifier.create({ value: identifier }, sendAnalytics);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        message: expect.any(String),
        type: 'IdentifierErrors.SpacesNotAllowed',
        status: 400,
      }],
    });
  });

  it(`has uppercase`, () => {
    const identifier = 'locAtion';

    const result = Identifier.create({ value: identifier }, sendAnalytics);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        message: expect.any(String),
        type: 'IdentifierErrors.UppercaseNotAllowed',
        status: 400,
      }],
    });
  });

  it.each(['i$r', 'i%r', 'i*r', 'i-r', 'i+r', 'i=r', 'i#r'])('has %s', (invalidChar: string) => {
    const identifier = 'location' + invalidChar;

    const result = Identifier.create({ value: identifier }, sendAnalytics);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        message: expect.any(String),
        type: 'IdentifierErrors.InvalidCharacters',
        status: 400,
      }],
    });
  });

  it(`first char isn't a letter or number`, () => {
    const identifier = '_location';

    const result = Identifier.create({ value: identifier }, sendAnalytics);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        message: expect.any(String),
        type: 'IdentifierErrors.FirstCharShouldBeLetterOrNumber',
        status: 400,
      }],
    });
  });

  it(`last char isn't a letter or number`, () => {
    const identifier = 'location_';

    const result = Identifier.create({ value: identifier }, sendAnalytics);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        message: expect.any(String),
        type: 'IdentifierErrors.LastCharShouldBeLetterOrNumber',
        status: 400,
      }],
    });
  });

  it(`is too long`, () => {
    const identifier =
      '1234567890' + '1234567890' + '1234567890' + '1234567890' + 'a';

    const result = Identifier.create({ value: identifier }, sendAnalytics);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        message: expect.any(String),
        type: 'IdentifierErrors.TooLong',
        status: 400,
      }],
    });
  });

  it(`is too short`, () => {
    const identifier = 'a';

    const result = Identifier.create({ value: identifier }, sendAnalytics);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        message: expect.any(String),
        type: 'IdentifierErrors.TooShort',
        status: 400,
      }],
    });
  });

  it(`should have at least one letter`, () => {
    const identifier = '12';

    const result = Identifier.create({ value: identifier }, sendAnalytics);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        message: expect.any(String),
        type: 'IdentifierErrors.ShouldHaveAtLeast1letter',
        status: 400,
      }],
    });
  });
});

describe(`increaseSpots`, () => {
  const identifier = Identifier.create({ value: 'loc1' }, sendAnalytics).value;
  test(`structure #identifier:n`, () => {
    const existingReservationDescription =
      'Reserva de spots en locación 1 #loc1:2.\n#loc1:2';

    const increasedSpots = identifier.increaseSpots(
      existingReservationDescription,
    );

    expect(increasedSpots).toBe(
      'Reserva de spots en locación 1 #loc1:3.\n#loc1:3',
    );
  });
  test(`structure #identifier`, () => {
    const existingReservationDescription =
      'Reserva de spots en locación 1 #loc1.\n#loc1';

    const increasedSpots = identifier.increaseSpots(
      existingReservationDescription,
    );

    expect(increasedSpots).toBe(
      'Reserva de spots en locación 1 #loc1:2.\n#loc1:2',
    );
  });
  test(`with both structures #identifier and #identifier:1`, () => {
    const existingReservationDescription =
      'Reserva de spots en locación 1 #loc1.\n#loc1:1';

    const increasedSpots = identifier.increaseSpots(
      existingReservationDescription,
    );

    expect(increasedSpots).toBe(
      'Reserva de spots en locación 1 #loc1:2.\n#loc1:2',
    );
  });
});

describe(`decreaseSpots`, () => {
  const identifier = Identifier.create({ value: 'loc1' }, sendAnalytics).value;
  test(`structure #identifier:n`, async () => {
    const existingReservationDescription =
      'Reserva de spots en locación 1 #loc1:2.\n#loc1:2';

    const { updatedDescriptionPlainText, decreasedSpots} = await identifier.decreaseSpots(
      existingReservationDescription,
    );

    expect(updatedDescriptionPlainText).toBe(
      'Reserva de spots en locación 1 #loc1:1.\n#loc1:1',
    );
    expect(decreasedSpots.toDto()).toBe(1);
  });
  test(`structure #identifier`, async () => {
    const existingReservationDescription =
      'Reserva de spots en locación 1 #loc1.\n#loc1';

    const {updatedDescriptionPlainText, decreasedSpots} = await identifier.decreaseSpots(
      existingReservationDescription,
    );

    expect(updatedDescriptionPlainText).toBe(
      'Reserva de spots en locación 1 #loc1:0.\n#loc1:0',
    );
    expect(decreasedSpots.toDto()).toBe(0);
  });
  test(`with both structures #identifier and #identifier:1`, async () => {
    const existingReservationDescription =
      'Reserva de spots en locación 1 #loc1.\n#loc1:1';

    const {updatedDescriptionPlainText, decreasedSpots} = await identifier.decreaseSpots(
      existingReservationDescription,
    );

    expect(updatedDescriptionPlainText).toBe(
      'Reserva de spots en locación 1 #loc1:0.\n#loc1:0',
    );
    expect(decreasedSpots.toDto()).toBe(0);
  });
  it(`doesn't go lower than 0`, async () => {
    const existingReservationDescription =
      'Reserva de spots en locación 1 #loc1:0.';

    const {updatedDescriptionPlainText, decreasedSpots} = await identifier.decreaseSpots(
      existingReservationDescription,
    );

    expect(updatedDescriptionPlainText).toBe('Reserva de spots en locación 1 #loc1:0.');
    expect(decreasedSpots.toDto()).toBe(0);
  });
});

describe(`parse`, () => {
  describe(`return spots for`, () => {
    test(`#<identifier>:<n>`, () => {
      const identifier = '1c';
      const eventDescriptionPlainText =
        'RegExr was { #1c:090. } by (gskinner).com. #cre_a0ted1:0';

      const spots = Identifier.parse({
        identifier: Identifier.create({ value: identifier }, sendAnalytics).value,
        eventDescriptionPlainText,
      }, sendAnalytics);

      expect(spots?.toDto()).toBe(90);
    });

    test(`#<identifier> defaulting spots to 1, if no valid #<identifier>:<n> is found`, () => {
      const identifier = '1c';
      const eventDescriptionPlainText = `RegExr was { #${identifier} :u90. } by (gskinner).com. #cre_a0ted1:0`;

      const spots = Identifier.parse({
        identifier: Identifier.create({ value: identifier }, sendAnalytics).value,
        eventDescriptionPlainText,
      }, sendAnalytics);

      expect(spots).toMatchObject({
        value: 1,
      });
    });

    test(`#<identifier>:0`, () => {
      const identifier = '1c';
      const eventDescriptionPlainText =
        'RegExr was { #1c:0. } by (gskinner).com. #cre_a0ted1:0';

      const spots = Identifier.parse({
        identifier: Identifier.create({ value: identifier }, sendAnalytics).value,
        eventDescriptionPlainText,
      }, sendAnalytics);

      expect(spots?.toDto()).toBe(0);
    });
  });

  describe(`returns null spots`, () => {
    test(`when #<identifier> isn't found`, () => {
      const identifier = '1c';
      const eventDescriptionPlainText = `RegExr was { #1x:090. } by (gskinner).com. #cre_a0ted1:0 #${identifier}a`;

      const spots = Identifier.parse({
        identifier: Identifier.create({ value: identifier }, sendAnalytics).value,
        eventDescriptionPlainText,
      }, sendAnalytics);

      expect(spots).toBe(null);
    });

    test(`for malformed #<identifier>:<number> pattern`, () => {
      const identifier = '1c';
      const eventDescriptionPlainText = `#${identifier}:2f`;

      const spots = Identifier.parse({
        identifier: Identifier.create({ value: identifier }, sendAnalytics).value,
        eventDescriptionPlainText,
      }, sendAnalytics);

      expect(spots).toBe(null);
    });
    test(`for #<identifier>:`, () => {
      const identifier = '1c';
      const eventDescriptionPlainText = `#${identifier}:`;

      const spots = Identifier.parse({
        identifier: Identifier.create({ value: identifier }, sendAnalytics).value,
        eventDescriptionPlainText,
      }, sendAnalytics);

      expect(spots).toBe(null);
    });
  }); //  End of "returns null spots"
});

describe(`detectIdentifiersWithSpots`, () => {
  describe(`return spots when`, () => {
    test(`detecting #<identifier>:<n> and #<identifier>`, () => {
      const identifierValue1 = '1c';
      const identifierValue2 = 'cre_a0ted1';
      const identifierValue3 = 'rerfe';
      const eventDescription = `RegExr was { #${identifierValue1}:090. } #${identifierValue2}:0 by (gskinner).com.  #${identifierValue3}`;

      const detected = Identifier.detectIdentifiersWithSpots(eventDescription, sendAnalytics);

      expect(detected?.length).toBe(3);
      expect(detected).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            identifier: Identifier.create({ value: identifierValue1 }, sendAnalytics).value,
            spots: Spots.create(90, sendAnalytics).value,
          }),
          expect.objectContaining({
            identifier: Identifier.create({ value: identifierValue2 }, sendAnalytics).value,
            spots: Spots.create(0, sendAnalytics).value,
          }),
          expect.objectContaining({
            identifier: Identifier.create({ value: identifierValue3 }, sendAnalytics).value,
            spots: Spots.create(1, sendAnalytics).value,
          }),
        ]),
      );
    });

    test(`detecting #<identifier>, defaults spots to 1 if no valid #<identifier>:<n> is found`, () => {
      const identifierValue = '1c';
      const eventDescription = 'RegExr was { #1c :u90. } by (gskinner).com.';

      const detected = Identifier.detectIdentifiersWithSpots(eventDescription, sendAnalytics);

      expect(detected?.length).toBe(1);
      expect(detected?.[0]).toMatchObject({
        identifier: Identifier.create({ value: identifierValue }, sendAnalytics).value,
        spots: Spots.create(1, sendAnalytics).value,
      });
    });

    test(`for #<identifier>:0`, () => {
      const identifierValue1 = '1c';
      const identifierValue2 = 'cre_a0ted1';
      const eventDescription = `RegExr was { #${identifierValue1}:0. } by (gskinner).com. #${identifierValue2}:0`;

      const detected = Identifier.detectIdentifiersWithSpots(eventDescription, sendAnalytics);

      expect(detected?.length).toBe(2);
      expect(detected).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            identifier: Identifier.create({ value: identifierValue1 }, sendAnalytics).value,
            spots: Spots.create(0, sendAnalytics).value,
          }),
          expect.objectContaining({
            identifier: Identifier.create({ value: identifierValue2 }, sendAnalytics).value,
            spots: Spots.create(0, sendAnalytics).value,
          }),
        ]),
      );
    });
  }); // End of "return spots when"

  describe(`returns null spots`, () => {
    test(`when there are potential matches but they're malformed identifiers`, () => {
      const eventDescription = `#A1 #c #12345678_1_2345678_2_2345678_3_2345678_41`;

      const detected = Identifier.detectIdentifiersWithSpots(eventDescription, sendAnalytics);

      expect(detected).toBe(null);
    });
  }); // End of "returns null spots"
}); // End of "detectIdentifiersWithSpots"
