import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { Dat } from '@shared/core/Dat';

export namespace SlotErrors {
  export class EndMustBeAfterStart extends BadRequest {
    public constructor(args: { start: Dat; end: Dat }) {
      const { start, end } = args;
      super({
        message: `Computed start time (${start.s}) must be before end time (${end.s}), but they aren't`,
      }); // Uso computed en lugar de entered porque si el start/end time está fuera de reservationLimits, los entered son sobreescritos.
    }
  }
}

patch({ SlotErrors });
