import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { SpotsErrors } from './SpotsErrors';
import { IContextProvider } from '@shared/context/IContextProvider';

export type SpotsDto = number;

type SpotsProps = {
  value: SpotsDto;
};

export class Spots extends ValueObject<SpotsProps, SpotsDto> {
  private __class = this.constructor.name;
  private readonly sendAnalytics: IContextProvider['sendAnalytics'];
  get value(): number {
    return this.props.value;
  }

  private constructor(props: SpotsProps, sendAnalytics: IContextProvider['sendAnalytics']) {
    super(props);
    this.sendAnalytics = sendAnalytics;
  }

  public toDto(): SpotsDto {
    return this.value;
  }

  public static create(spots: number, sendAnalytics: IContextProvider['sendAnalytics']): Result<Spots> {
    if (spots < 0) return Result.fail(new SpotsErrors.MinimumValueIs0(spots));

    if (!Number.isInteger(spots))
      return Result.fail(new SpotsErrors.SpotsShouldBeInteger(spots));

    return Result.ok<Spots>(new Spots({ value: spots }, sendAnalytics));
  }

  public increase(): Spots {
    return Spots.create(this.props.value + 1, this.sendAnalytics).value;
  }

  public async decrease(): Promise<Spots> {
    let newValue = this.props.value - 1;
    if (newValue < 0) {
      const msg = `Spots.decrease gave a negative value`;
      console.log(msg, this);
      await this.sendAnalytics({
        spots: this,
        msg,
      });
      newValue = 0;
    }
    return Spots.create(newValue, this.sendAnalytics).value;
  }
}
