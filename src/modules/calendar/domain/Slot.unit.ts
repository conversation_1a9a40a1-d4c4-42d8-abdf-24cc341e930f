import { expect, test, it, describe } from 'vitest';
import { Slot, SlotDto } from './Slot';
import { tomorrowPlus } from '../utils/utils';
import { DateTime } from 'luxon';
import { Dat } from '@shared/core/Dat';

test(`create & assemble back`, () => {
  const dto: SlotDto = {
    start: '2024-01-01T00:00Z',
    end: '2024-01-01T01:00Z',
  };

  const created = Slot.create({
    start: Dat.create({ value: dto.start }).value,
    end: Dat.create({ value: dto.end }).value,
  }).value;

  expect(created.toDto()).toMatchObject(dto);

  const assembled = Slot.assemble(dto);

  const equals = assembled.equals(created);

  expect(equals).toBe(true);
});

it(`strips out seconds and milliseconds`, () => {
  const dto: SlotDto = {
    start: '2024-01-01T00:00:59.999Z',
    end: '2024-01-01T01:00:59.999Z',
  };

  const created = Slot.create({
    start: Dat.create({ value: dto.start }).value,
    end: Dat.create({ value: dto.end }).value,
  }).value;

  expect(created.toDto()).toMatchObject({
    start: '2024-01-01T00:00Z',
    end: '2024-01-01T01:00Z',
  });
});

test(`Creation`, () => {
  const validData = {
    start: tomorrowPlus(),
    end: tomorrowPlus(30),
  };

  const result = Slot.create(validData);
  expect(result.isSuccess).toBe(true);
  const slot = result.value;
  expect(slot.start).toStrictEqual(validData.start);
  expect(slot.end).toStrictEqual(validData.end);
});

test(`Creation fails when end isn't after start`, () => {
  const same = Dat.create({ value: DateTime.now().plus({ days: 1 }) }).value;
  const invalidData = {
    start: same,
    end: same,
  };

  const result = Slot.create(invalidData);
  expect(result).toMatchObject({
    isFailure: true,
    error: {
      message: expect.any(String),
      type: 'SlotErrors.EndMustBeAfterStart',
      status: 400,
    },
  });
});

describe(`Consolidation`, () => {
  it(`returns [] for input []`, () => {
    const slots: Slot[] = [];
    const consolidated = Slot.consolidate(slots);
    expect(consolidated).toStrictEqual([]);
  });
  it(`returns same slots ordered for non-overlapping slots`, () => {
    const dateRanges = [
      {
        start: '2000-01-01T00:20Z',
        end: '2000-01-01T00:30Z',
      },
      {
        start: '2000-01-01T00:00Z',
        end: '2000-01-01T00:10Z',
      },
    ];
    const slots = dateRanges.map(
      (range) =>
        Slot.create({
          start: Dat.create({ value: range.start }).value,
          end: Dat.create({ value: range.end }).value,
        }).value,
    );

    const consolidated = Slot.consolidate(slots);

    const consolidatedRanges = consolidated.map((slot) => slot.toDto());
    expect(consolidatedRanges).toStrictEqual([dateRanges[1], dateRanges[0]]);
  });
  it(`returns one slot when all overlap`, () => {
    const dateRanges = [
      {
        start: '2000-01-01T00:00Z',
        end: '2000-01-01T00:10Z',
      },
      {
        start: '2000-01-01T00:10Z',
        end: '2000-01-01T00:20Z',
      },
      {
        start: '2000-01-01T00:15Z',
        end: '2000-01-01T00:30Z',
      },
    ];
    const slots = dateRanges.map(
      (range) =>
        Slot.create({
          start: Dat.create({ value: range.start }).value,
          end: Dat.create({ value: range.end }).value,
        }).value,
    );

    const consolidated = Slot.consolidate(slots);

    const consolidatedRanges = consolidated.map((slot) => slot.toDto());
    expect(consolidatedRanges).toStrictEqual([
      {
        start: dateRanges[0].start,
        end: dateRanges[2].end,
      },
    ]);
  });
  it(`consolidates into three slots for 5 unordered slots`, () => {
    const dateRanges = [
      {
        start: '2000-01-01T00:45Z', // overlap group
        end: '2000-01-01T00:50Z',
      },
      {
        start: '2000-01-01T00:15Z', // no overlap
        end: '2000-01-01T00:20Z',
      },
      {
        start: '2000-01-01T00:35Z', // overlap group
        end: '2000-01-01T00:45Z',
      },
      {
        start: '2000-01-01T00:30Z', // overlap group
        end: '2000-01-01T00:40Z',
      },
      {
        start: '2000-01-01T00:00Z', // no overlap
        end: '2000-01-01T00:10Z',
      },
    ];
    const slots = dateRanges.map(
      (range) =>
        Slot.create({
          start: Dat.create({ value: range.start }).value,
          end: Dat.create({ value: range.end }).value,
        }).value,
    );

    const consolidated = Slot.consolidate(slots);

    const consolidatedRanges = consolidated.map((slot) => slot.toDto());
    expect(consolidatedRanges).toStrictEqual([
      {
        start: dateRanges[4].start,
        end: dateRanges[4].end,
      },
      {
        start: dateRanges[1].start,
        end: dateRanges[1].end,
      },
      {
        start: dateRanges[3].start,
        end: dateRanges[0].end,
      },
    ]);
  });
});

describe(`adjustAllowSlots`, () => {
  it(`6 allow & 7 busy into 5 adjusted slots`, () => {
    const allowRanges = [
      {
        start: '2000-01-01T00:00Z', // ok (i: 0)
        end: '2000-01-01T00:05Z',
      },
      {
        start: '2000-01-01T00:15Z', // 20 to end (i: 1)
        end: '2000-01-01T00:25Z',
      },
      {
        start: '2000-01-01T00:30Z', // no (i: 2)
        end: '2000-01-01T00:35Z',
      },
      {
        start: '2000-01-01T00:40Z', // 40 to 42 (i: 3)
        end: '2000-01-01T00:45Z',
      },
      {
        start: '2000-01-01T00:50Z', // no (i: 4)
        end: '2000-01-01T00:51Z',
      },
      {
        start: '2000-01-02T00:00Z', // 02T00:00 to 02T00:05 & 02T00:08 to 03T00:10 (i: 5)
        end: '2000-01-03T00:10Z',
      },
    ];
    const busyRanges = [
      {
        start: '2000-01-01T00:05Z', // (i: 0)
        end: '2000-01-01T00:10Z',
      },
      {
        start: '2000-01-01T00:15Z', // (i: 1)
        end: '2000-01-01T00:20Z',
      },
      {
        start: '2000-01-01T00:29Z', // (i: 2)
        end: '2000-01-01T00:36Z',
      },
      {
        start: '2000-01-01T00:42Z', // (i: 3)
        end: '2000-01-01T00:45Z',
      },
      {
        start: '2000-01-01T00:50Z', // (i: 4)
        end: '2000-01-01T00:51Z',
      },
      {
        start: '2000-01-02T00:05Z', // (i: 5)
        end: '2000-01-02T00:08Z',
      },
      {
        start: '2000-01-04T00:00Z', // (i: 6)
        end: '2000-01-05T00:00Z',
      },
    ];
    const allowSlots = allowRanges.map(
      (range) =>
        Slot.create({
          start: Dat.create({ value: range.start }).value,
          end: Dat.create({ value: range.end }).value,
        }).value,
    );
    const busySlots = busyRanges.map(
      (range) =>
        Slot.create({
          start: Dat.create({ value: range.start }).value,
          end: Dat.create({ value: range.end }).value,
        }).value,
    );

    const adjusted = Slot.adjustAllowSlots({ allowSlots, busySlots });

    const adjustedRanges = adjusted.map((slot) => slot.toDto());
    expect(adjustedRanges.length).toBe(5);
    expect(adjustedRanges).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          start: allowRanges[0].start,
          end: allowRanges[0].end,
        }),
        expect.objectContaining({
          start: busyRanges[1].end,
          end: allowRanges[1].end,
        }),
        expect.objectContaining({
          start: allowRanges[3].start,
          end: busyRanges[3].start,
        }),
        expect.objectContaining({
          start: allowRanges[5].start,
          end: busyRanges[5].start,
        }),
        expect.objectContaining({
          start: busyRanges[5].end,
          end: allowRanges[5].end,
        }),
      ]),
    );
  });
  it(`returns [] if allowSlots is []`, () => {
    const busyRanges = [
      {
        start: '2000-01-01T00:05:00-03:00', // (i: 0)
        end: '2000-01-01T00:10:00-03:00',
      },
      {
        start: '2000-01-01T00:15:00-03:00', // (i: 1)
        end: '2000-01-01T00:20:00-03:00',
      },
      {
        start: '2000-01-01T00:29:00-03:00', // (i: 2)
        end: '2000-01-01T00:36:00-03:00',
      },
      {
        start: '2000-01-01T00:42:00-03:00', // (i: 3)
        end: '2000-01-01T00:45:00-03:00',
      },
      {
        start: '2000-01-01T00:50:00-03:00', // (i: 4)
        end: '2000-01-01T00:51:00-03:00',
      },
      {
        start: '2000-01-02T00:05:00-03:00', // (i: 5)
        end: '2000-01-02T00:08:00-03:00',
      },
      {
        start: '2000-01-04T00:00:00-03:00', // (i: 6)
        end: '2000-01-05T00:00:00-03:00',
      },
    ];
    const busySlots = busyRanges.map(
      (range) =>
        Slot.create({
          start: Dat.create({ value: range.start }).value,
          end: Dat.create({ value: range.end }).value,
        }).value,
    );

    const adjusted = Slot.adjustAllowSlots({ allowSlots: [], busySlots });

    expect(adjusted).toStrictEqual([]);
  });
  it(`returns allowSlots if busySlots is []`, () => {
    const allowRanges = [
      {
        start: '2000-01-01T00:05Z', // (i: 0)
        end: '2000-01-01T00:10Z',
      },
      {
        start: '2000-01-01T00:15Z', // (i: 1)
        end: '2000-01-01T00:20Z',
      },
      {
        start: '2000-01-01T00:29Z', // (i: 2)
        end: '2000-01-01T00:36Z',
      },
    ];
    const allowSlots = allowRanges.map(
      (range) =>
        Slot.create({
          start: Dat.create({ value: range.start }).value,
          end: Dat.create({ value: range.end }).value,
        }).value,
    );

    const adjusted = Slot.adjustAllowSlots({ allowSlots, busySlots: [] });

    const adjustedRanges = adjusted.map((slot) => slot.toDto());
    expect(adjustedRanges).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          start: allowRanges[0].start,
          end: allowRanges[0].end,
        }),
        expect.objectContaining({
          start: allowRanges[1].start,
          end: allowRanges[1].end,
        }),
        expect.objectContaining({
          start: allowRanges[2].start,
          end: allowRanges[2].end,
        }),
      ]),
    );
  });
});

test(`mapToDto`, () => {
  const start = '2000-01-01T00:05Z';
  const end = '2000-01-01T00:10Z';
  const slot = Slot.create({
    start: Dat.create({ value: start }).value,
    end: Dat.create({ value: end }).value,
  }).value;

  const dto = slot.toDto();

  expect(dto).toMatchObject({
    start,
    end,
  });
});
