import { ValueObject } from '@shared/core/domain/ValueObject';
import { Spots, SpotsDto } from './Spots';
import { Slot, SlotDto } from './Slot';
import { Dat } from '@shared/core/Dat';
import { nonEmpty } from '@shared/utils/utils';
import { IContextProvider } from '@shared/context/IContextProvider';

interface SlotWithSpotsProps {
  slot: Slot;
  spots: Spots;
}

export interface SlotWithSpotsDto {
  slot: SlotDto;
  spots: SpotsDto;
}

export class SlotWithSpots extends ValueObject<
  SlotWithSpotsProps,
  SlotWithSpotsDto
> {
  private __class = this.constructor.name;

  get slot(): Slot {
    return this.props.slot;
  }

  get spots(): Spots {
    return this.props.spots;
  }

  private constructor(props: SlotWithSpotsProps) {
    super(props);
  }

  public static create(props: SlotWithSpotsProps): SlotWithSpots {
    return new SlotWithSpots(props);
  }
  public toDto(): SlotWithSpotsDto {
    return {
      slot: this.slot.toDto(),
      spots: this.spots.toDto(),
    };
  }

  // Chunkify, sort and consolidate
  public static chunkify(args: {
    slots: [SlotWithSpots, ...SlotWithSpots[]];
    range: Slot;
  }, sendAnalytics: IContextProvider['sendAnalytics']): [SlotWithSpots] | null {
    const { slots, range } = args;
    // Merge and sort time points
    let timePoints: Date[] = slots.reduce((acc, slotWithSpots) => {
      acc.push(slotWithSpots.slot.start.j, slotWithSpots.slot.end.j);
      return acc;
    }, [] as Date[]);

    timePoints = Array.from(new Set(timePoints)).sort(
      (a, b) => a.getTime() - b.getTime(),
    );

    // Filter dates within the range
    timePoints = timePoints.filter((point) => {
      const pointTime = point.getTime();
      return pointTime >= range.start.t && pointTime <= range.end.t;
    });

    // Check and insert the start date if it's not in the filtered array
    if (timePoints.length === 0 || timePoints[0].getTime() > range.start.t) {
      timePoints.unshift(range.start.j);
    }

    // Check and append the end date if it's not in the filtered array
    if (timePoints[timePoints.length - 1].getTime() < range.end.t) {
      timePoints.push(range.end.j);
    }

    const chunks: SlotWithSpots[] = [];
    for (let i = 0; i < timePoints.length - 1; i++) {
      // Skip chunks with the same start and end time
      if (timePoints[i].getTime() === timePoints[i + 1].getTime()) continue;

      const chunk: Slot = Slot.create({
        start: Dat.create({ value: timePoints[i] }).value,
        end: Dat.create({ value: timePoints[i + 1] }).value,
      }).value;

      let spots = 0;

      // Calculate available and reserved spots for the chunk
      slots.forEach((slotWithSpots) => {
        if (
          slotWithSpots.slot.start.t <= chunk.start.t &&
          slotWithSpots.slot.end.t >= chunk.end.t
        ) {
          spots += slotWithSpots.spots.value;
        }
      });

      if (spots)
        chunks.push(
          SlotWithSpots.create({
            slot: chunk,
            spots: Spots.create(spots, sendAnalytics).value,
          }),
        );
    }
    if (!chunks.length) return null;

    // Consolidate adjacent slots with equal spots
    return SlotWithSpots.consolidateAdjacentSlots(nonEmpty(chunks));
  }

  // Take sorted chunks and get those in which there are spots available
  public static getAvailableChunks(args: {
    availability: [SlotWithSpots, ...SlotWithSpots[]] | null; // already sorted: this function is called by CalendarsSrv.getAvailableChunks in which previously is called AvailabilitySrv.getChunks that returns sorted slots
    reserved: [SlotWithSpots, ...SlotWithSpots[]] | null;     // idem
  }, sendAnalytics: IContextProvider['sendAnalytics']): [SlotWithSpots, ...SlotWithSpots[]] | null {
    const { availability, reserved } = args;
    if (!availability) return null;
    const merged = reserved ? [...availability, ...reserved] : availability;
    // Merge and sort time points
    let timePoints: Date[] = merged.reduce((acc, slotWithSpots) => {
      acc.push(slotWithSpots.slot.start.j, slotWithSpots.slot.end.j);
      return acc;
    }, [] as Date[]);

    timePoints = Array.from(new Set(timePoints)).sort(
      (a, b) => a.getTime() - b.getTime(),
    );

    const chunks: SlotWithSpots[] = [];
    for (let i = 0; i < timePoints.length - 1; i++) {
      // Skip chunks with the same start and end time
      if (timePoints[i].getTime() === timePoints[i + 1].getTime()) continue;

      const chunk: Slot = Slot.create({
        start: Dat.create({ value: timePoints[i] }).value,
        end: Dat.create({ value: timePoints[i + 1] }).value,
      }).value;

      let availableSpots = 0;
      let occupiedSpots = 0;

      // Calculate available and occupied spots for the chunk
      for (const availabilitySlot of availability) {
        if (availabilitySlot.slot.start.t >= chunk.end.t) break;
        if (
          availabilitySlot.slot.start.t <= chunk.start.t &&
          availabilitySlot.slot.end.t >= chunk.end.t
        ) {
          availableSpots += availabilitySlot.spots.value;
        }
      }

      if (reserved) {
        for (const reservedSlot of reserved) {
          if (reservedSlot.slot.start.t >= chunk.end.t) break;
          if (
            reservedSlot.slot.start.t <= chunk.start.t &&
            reservedSlot.slot.end.t >= chunk.end.t
          ) {
            occupiedSpots += reservedSlot.spots.value;
          }
        }
      }

      const netSpots = availableSpots - occupiedSpots;
      if (netSpots > 0) {
        chunks.push(
          SlotWithSpots.create({
            slot: chunk,
            spots: Spots.create(netSpots, sendAnalytics).value,
          }),
        );
      }
    }

    if (!chunks.length) return null;

    // Consolidate adjacent slots with equal spots
    return SlotWithSpots.consolidateAdjacentSlots(nonEmpty(chunks));
  }

  private static consolidateAdjacentSlots(
    slotsWithSpots: [SlotWithSpots, ...SlotWithSpots[]],
  ): [SlotWithSpots] {
    const consolidated: SlotWithSpots[] = [];

    for (let i = 0; i < slotsWithSpots.length; i++) {
      let current = slotsWithSpots[i];

      // Check if the current slot can be merged with the next one
      while (
        i < slotsWithSpots.length - 1 &&
        current.slot.end.t === slotsWithSpots[i + 1].slot.start.t &&
        current.spots.equals(slotsWithSpots[i + 1].spots)
      ) {
        current = SlotWithSpots.create({
          slot: Slot.create({
            start: current.slot.start,
            end: slotsWithSpots[i + 1].slot.end,
          }).value,
          spots: current.spots,
        });
        i++;
      }

      consolidated.push(current);
    }

    return consolidated as [SlotWithSpots];
  }

  public static toDto(slot: SlotWithSpots): SlotWithSpotsDto {
    return {
      slot: {
        start: slot.slot.start.s,
        end: slot.slot.end.s,
      },
      spots: slot.spots.value,
    };
  }

  public static assemble(dto: SlotWithSpotsDto, sendAnalytics: IContextProvider['sendAnalytics']): SlotWithSpots {
    const {
      slot: { start, end },
      spots,
    } = dto;
    return SlotWithSpots.create({
      slot: Slot.create({
        start: Dat.create({ value: start }).value,
        end: Dat.create({ value: end }).value,
      }).value,
      spots: Spots.create(spots, sendAnalytics).value,
    });
  }
}
