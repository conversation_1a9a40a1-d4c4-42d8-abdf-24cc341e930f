import { AggregateRoot } from '@shared/core/domain/AggregateRoot';
import { EntityID } from '@shared/core/domain/EntityID';
import { logAndThrow, Spread } from '@shared/utils/utils';

export enum CalendarType {
  AVAILABILITY = 'AVAILABILITY',
  RESERVATIONS = 'RESERVATIONS',
}

type CalendarProps = {
  userId: string; // linked to BusinessUser in module user
  type: CalendarType;
  name: string;
};

export type CalendarDto = Spread<
  CalendarProps,
  {
    id: string; // Google Calendar id
    type: string;
  }
>;

export class Calendar extends AggregateRoot<CalendarProps, CalendarDto> {
  private __class = this.constructor.name;

  get userId(): string {
    return this.props.userId;
  }

  get type(): CalendarType {
    return this.props.type;
  }

  get name(): string {
    return this.props.name;
  }

  private constructor(props: CalendarProps, id: EntityID) {
    super(props, id);
  }

  public static create(props: CalendarProps, id: string): Calendar {
    return new Calendar(props, new EntityID(id));
  }

  public toDto(): CalendarDto {
    return {
      ...this.props,
      id: this.id.toString(),
    };
  }

  public static assemble(dto: CalendarDto): Calendar {
    const { type: _type, ...rest } = dto;

    let type;
    switch (_type) {
      case CalendarType.AVAILABILITY:
        type = CalendarType.AVAILABILITY;
        break;
      case CalendarType.RESERVATIONS:
        type = CalendarType.RESERVATIONS;
        break;
      default:
        logAndThrow({
          msg: `Invalid calendar type ${_type}`,
          data: dto,
        });
    }

    return new Calendar(
      {
        ...rest,
        type,
      },
      new EntityID(dto.id),
    );
  }
}
