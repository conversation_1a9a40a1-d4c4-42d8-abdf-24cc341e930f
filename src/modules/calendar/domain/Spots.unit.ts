import { expect, test, describe, it, vi } from 'vitest';
import { Spots } from './Spots';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const sendAnalytics = vi.fn();

it.each([0, 1])(`works for %s spots`, async (value) => {
  const result = Spots.create(value, sendAnalytics);

  expect(result).toMatchObject({
    isSuccess: true,
    value: {
      value,
    },
  });
});

describe(`creation fails if`, () => {
  describe(`spots is`, () => {
    test(`less than 1`, () => {
      const result = Spots.create(-1, sendAnalytics);

      expect(result).toMatchObject({
        isFailure: true,
        error: {
          status: 400,
          message: expect.any(String),
          type: `SpotsErrors.MinimumValueIs0`,
        },
      });
    });

    test(`not an integer`, () => {
      const result = Spots.create(2.5, sendAnalytics);

      expect(result).toMatchObject({
        isFailure: true,
        error: {
          status: 400,
          message: expect.any(String),
          type: `SpotsErrors.SpotsShouldBeInteger`,
        },
      });
    });
  });
});

test(`increase`, () => {
  const increased = Spots.create(2, sendAnalytics).value.increase();

  expect(increased).toMatchObject({
    value: 3,
  });
});

describe(`decrease`, () => {
  it.each([0, 1])(`works for %s spots, giving 0 spots`, async (value) => {
    const result = await Spots.create(value, sendAnalytics).value.decrease();

    expect(result).toMatchObject({
      value: 0,
    });
  });

  it(`works for a random value`, async () => {
    const value = chance.integer({ min: 1, max: 99999999 });
    const result = await Spots.create(value, sendAnalytics).value.decrease();

    expect(result).toMatchObject({
      value: value - 1,
    });
  });
});
