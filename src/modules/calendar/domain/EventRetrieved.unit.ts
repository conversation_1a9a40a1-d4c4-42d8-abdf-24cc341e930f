import { expect, test, it, describe } from 'vitest';
import { EventRetrieved } from './EventRetrieved';
import { Slot } from './Slot';
import { ApiEventData } from '../services/CalendarApiFake';
import { Dat } from '@shared/core/Dat';

const input = {
  id: 'test id',
  unknown: 1,
  name: 'test name',
  description: 'test description',
  slot: {
    start: '2012-12-23T05:00Z',
    end: '2012-12-23T06:30Z',
  },
};
let created: EventRetrieved;
test(`create`, () => {
  created = EventRetrieved.create({
    ...input,
    slot: Slot.create({
      start: Dat.create({ value: input.slot.start }).value,
      end: Dat.create({ value: input.slot.end }).value,
    }).value,
  });

  expect(created.toDto()).toMatchObject(input);
});

test(`descriptionPlainText should preserve new lines and should not insert additional line breaks for long lines`, () => {
  const description = 'input.description Reserva para Romual<PERSON> tomada por el negocio a las 2025-02-11T12:27:21-03:00\n' +
  '#loc';
  created = EventRetrieved.create({
    ...input,
    description,
    slot: Slot.create({
      start: Dat.create({ value: input.slot.start }).value,
      end: Dat.create({ value: input.slot.end }).value,
    }).value,
  });

  const descriptionPlainText = created.toDto().descriptionPlainText;

  expect(descriptionPlainText).toEqual(description);
});

test(`updateDescription`, () => {
  const newDescription = 'new description';
  const updated = created.updateDescription(newDescription);

  expect(updated.toDto()).toMatchObject({
    ...input,
    description: newDescription,
  });
});

describe(`listToDomain`, () => {
  it(`converts from Cordoba time zone`, () => {
    const apiData = {
      timeZone: 'America/Argentina/Cordoba',
      items: [
        {
          id: 'item0',
          start: { date: '2000-01-01' },
          end: { date: '2000-01-02' },
        },
        {
          id: 'item1',
          start: { dateTime: '2000-01-01T00:00:00-00:00' },
          end: { dateTime: '2000-01-01T10:00:00-00:00' },
        },
      ],
    };

    const events = EventRetrieved.listToDomain(apiData);
    if (!events) throw Error('events is null');

    expect(events[0].slot.start.s).toBe('2000-01-01T03:00Z');
    expect(events[0].slot.end.s).toBe('2000-01-02T03:00Z');
    expect(events[1].slot.start.s).toBe('2000-01-01T00:00Z');
    expect(events[1].slot.end.s).toBe('2000-01-01T10:00Z');
  });

  it(`converts from UTC time zone`, () => {
    const apiData = {
      timeZone: 'Europe/London',
      items: [
        {
          id: 'item0',
          start: { date: '2000-01-01' },
          end: { date: '2000-01-02' },
        },
        {
          id: 'item1',
          start: { dateTime: '2000-01-01T00:00:00-00:00' },
          end: { dateTime: '2000-01-01T10:00:00-00:00' },
        },
      ],
    };

    const events = EventRetrieved.listToDomain(apiData);
    if (!events) throw Error('events is null');

    expect(events[0].slot.start.s).toBe('2000-01-01T00:00Z');
    expect(events[0].slot.end.s).toBe('2000-01-02T00:00Z');
    expect(events[1].slot.start.s).toBe('2000-01-01T00:00Z');
    expect(events[1].slot.end.s).toBe('2000-01-01T10:00Z');
  });

  it(`removes <span> tags from description`, () => {
    const apiData = {
      timeZone: 'Europe/London',
      items: [
        {
          id: 'item0',
          start: { date: '2000-01-01' },
          end: { date: '2000-01-02' },
          description: '#<span>loc</span>',
        },
      ],
    };

    const events = EventRetrieved.listToDomain(apiData);
    if (!events) throw Error('events is null');

    expect(events[0].description).toBe('#<span>loc</span>');
    expect(events[0].descriptionPlainText).toBe('#loc');
  });
});

describe(`fromApi`, () => {
  const apiEventData: ApiEventData = {
    summary: 'testSummary',
    description: 'testDescription',
    id: 'testId',
    start: {
      dateTime: '2023-12-18T14:00:00-03:00',
    },
    end: {
      dateTime: '2023-12-18T18:00:00-03:00',
    },
    status: 'confirmed',
  };

  it(`succeeds`, () => {
    const eventRetrieved = EventRetrieved.fromApi(apiEventData);

    expect(eventRetrieved.toDto()).toMatchObject({
      ...apiEventData,
      slot: {
        start: Dat.create({ value: apiEventData.start.dateTime }).value.s,
        end: Dat.create({ value: apiEventData.end.dateTime }).value.s,
      },
      name: apiEventData.summary,
    });
  });

  it(`fails for bad start-end values`, () => {
    expect(() =>
      EventRetrieved.fromApi({
        ...apiEventData,
        end: {
          dateTime: apiEventData.start.dateTime,
        },
      }),
    ).toThrowError();
  });
});
