import { ValueObject } from '@shared/core/domain/ValueObject';
import { BaseError } from '@shared/core/AppError';
import { IdentifierErrors } from './IdentifierErrors';
import { Spots } from './Spots';
import {
  getLng,
  logAndThrow,
  nonEmpty,
  OptionalLng,
} from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { IContextProvider } from '@shared/context/IContextProvider';

interface IdentifierProps {
  value: string; // loc1
  query: string; // #loc1
}

export type IdentifierDto = IdentifierProps;

export type Detected = { identifier: Identifier; spots: Spots };
export type DetectIdentifiersWithSpotsResult = [Detected, ...Detected[]] | null;

export class Identifier extends ValueObject<IdentifierProps, IdentifierDto> {
  private __class = this.constructor.name;
  private static compoundStructure = ':[0-9]+\\b'; // #<identifier>:<n>
  private static simpleStructure = '\\b(?!:)'; // #<identifier>
  private readonly sendAnalytics: IContextProvider['sendAnalytics'];

  get value(): string {
    return this.props.value;
  }

  get query(): string {
    return this.props.query;
  }

  private constructor(props: IdentifierProps, sendAnalytics: IContextProvider['sendAnalytics']) {
    super(props);
    this.sendAnalytics = sendAnalytics;
  }

  public static create(args: { value: string } & OptionalLng, sendAnalytics: IContextProvider['sendAnalytics']): Result2<Identifier> {
    const { value: identifier, lng: _lng } = args;
    const lng = getLng(_lng);
    const check = checkIdentifier({ value: identifier, lng });
    if (check.isFailure) return Result2.fail(check.errors!);

    return Result2.ok<Identifier>(
      new Identifier({
        value: identifier,
        query: `#${identifier}`,
      }, sendAnalytics),
    );
  }

  // In the availability calendar, <n> is the capacity of spots
  // In the reservation calendar, <n> is the number of spots taken
  // If no valid #<identifier>:<n> is found, it searches for just #<identifier> defaulting spots to 1
  public static parse(props: {
    identifier: Identifier;
    eventDescriptionPlainText: string;
  }, sendAnalytics: IContextProvider['sendAnalytics']): Spots | null {
    const { identifier, eventDescriptionPlainText } = props;

    let spots = null;
    const regex = new RegExp(`${identifier.query}${this.compoundStructure}`);
    const match = eventDescriptionPlainText.match(regex);
    label1: if (match) {
      const [, _spots] = match[0].split(':');
      const parsed = parseInt(_spots);
      if (isNaN(parsed)) break label1;

      const spotsOrError = Spots.create(parsed, sendAnalytics);
      if (spotsOrError.isFailure) break label1;
      else {
        spots = spotsOrError.value;
      }
    }

    // If we failed to found a valid #<identifier>:<n> structure search for #<identifier> and default it to #<identifier>:1
    if (spots === null) {
      const regex = new RegExp(`${identifier.query}${this.simpleStructure}`);
      const match = eventDescriptionPlainText.match(regex);
      if (match) {
        spots = Spots.create(1, sendAnalytics).value;
      }
    }

    return spots ? spots : null;
  }

  public static detectIdentifiersWithSpots(
    eventDescriptionPlainText: string,
    sendAnalytics: IContextProvider['sendAnalytics']
  ): DetectIdentifiersWithSpotsResult {
    const regex = /#[a-z0-9_]+\b/g; // First do a quick search for potential matches
    const potentialMatches = eventDescriptionPlainText.match(regex);
    if (!potentialMatches) return null;

    const properMatches: Detected[] = [];
    const properIdentifiers: Identifier[] = [];
    potentialMatches.map((potential) => {
      const identifierOrError = Identifier.create({ value: potential.substring(1) }, sendAnalytics);
      if (identifierOrError.isFailure) return;
      const identifier = identifierOrError.value;

      if (properIdentifiers.find((proper) => proper.equals(identifier))) return;
      properIdentifiers.push(identifier);

      const spots = Identifier.parse({ identifier, eventDescriptionPlainText }, sendAnalytics);
      if (spots)
        properMatches.push({
          identifier,
          spots,
        });
    });
    return properMatches.length ? nonEmpty(properMatches) : null;
  }

  public increaseSpots(existingReservationDescriptionPlainText: string): string {
    const spots = Identifier.parse({
      identifier: this,
      eventDescriptionPlainText: existingReservationDescriptionPlainText,
    }, this.sendAnalytics);
    if (!spots)
      logAndThrow({
        // Esta situación no se debería dar ya que increaseSpot se llama desde CreateReservationEvent.ts, después de calendarsSrv.getSpotsForReservation() que devolvió Spots.
        msg: `ERROR: Couldn't determine the already reserved spots when updating event`,
        data: {
          identifier: this,
          existingReservationDescription: existingReservationDescriptionPlainText,
        },
      });
    const reservedSpots = spots.increase();

    let regex = new RegExp(`${this.query}${Identifier.compoundStructure}`, 'g');
    const increased = `${this.query}:${reservedSpots.value}`;
    const updatedDescriptionPlainText = existingReservationDescriptionPlainText.replaceAll(
      regex,
      increased,
    );
    regex = new RegExp(`${this.query}${Identifier.simpleStructure}`, 'g');
    return updatedDescriptionPlainText.replaceAll(regex, increased);
  }

  public async decreaseSpots(existingReservationDescriptionPlainText: string): Promise<{
    updatedDescriptionPlainText: string,
    decreasedSpots: Spots,
  }> {
    const spots = Identifier.parse({
      identifier: this,
      eventDescriptionPlainText: existingReservationDescriptionPlainText,
    }, this.sendAnalytics);
    if (!spots)
      logAndThrow({
        // Esta situación no se debería dar ya que increaseSpot se llama desde CancelReservationEvent.ts después de reservationSrv.getEvent() > Identifier.parse({ identifier, eventDescriptionPlainText: event.descriptionPlainText }) devolvió Spots.
        msg: `ERROR: Couldn't determine the already reserved spots when updating event`,
        data: {
          identifier: this,
          existingReservationDescription: existingReservationDescriptionPlainText,
        },
      });
    const decreasedSpots = await spots.decrease();

    let regex = new RegExp(`${this.query}${Identifier.compoundStructure}`, 'g');
    const decreasedId = `${this.query}:${decreasedSpots.value}`;
    const updatedDescriptionPlainText = existingReservationDescriptionPlainText.replaceAll(
      regex,
      decreasedId,
    );
    regex = new RegExp(`${this.query}${Identifier.simpleStructure}`, 'g');
    return {
      updatedDescriptionPlainText: updatedDescriptionPlainText.replaceAll(regex, decreasedId),
      decreasedSpots,
    };
  }

  public toDto(): IdentifierDto {
    return {
      value: this.props.value,
      query: this.props.query,
    };
  }
}

export function checkIdentifier(args: { value: string } & OptionalLng): Result2<string> {
  // All these checks are related to how the search of events in Google Calendar works using parameter q from method googleapis.calendar.events.list
  // https://github.com/googleapis/google-api-nodejs-client/blob/904e5f00d7fba3955bcba22f5b218980647e2211/src/apis/calendar/v3.ts#L4524
  // Notes about the search with q:
  // It's case-insensitive: searching for #Hi will find #hi too.
  // Some characters and the place where they were located caused some trouble. For example:
  // #x:x_x is found
  // #_x is not found
  // #x:-x_--x-x is not found
  // #he::l__lo is found
  // #he:_l_lo is not found
  // That's why I limited the possible characters to only letters, numbers and "_". Also, checking their locations.

  const { value: identifierInput, lng } = args;

  const maxLength = 40;
  const minLength = 2;

  const trimmed = identifierInput.trim();
  const errors: BaseError[] = [];

  const space = trimmed.match(/ /); // don't allow spaces. It's handled by this special error as printing spaces as the invalid characters isn't clear
  if (space) errors.push(new IdentifierErrors.SpacesNotAllowed(lng));

  const upperCases = trimmed.match(/[A-Z]/g);
  if (upperCases) errors.push(new IdentifierErrors.UppercaseNotAllowed({ invalidChars: upperCases, lng }));

  const invalidChars = trimmed.match(/[^a-zA-Z0-9_ ]/g); // invalid characters, excluding spaces and uppercases, as they are handled by SpacesNotAllowed and UppercaseNotAllowed errors
  if (invalidChars) errors.push(new IdentifierErrors.InvalidCharacters({ invalidChars, lng }));

  const atLeast1letter = trimmed.match(/[a-z]+/);
  if (!atLeast1letter) errors.push(new IdentifierErrors.ShouldHaveAtLeast1letter(lng));

  const initialCharInvalid = trimmed.match(/^[^a-z0-9]/);
  if (initialCharInvalid) errors.push(
      new IdentifierErrors.FirstCharShouldBeLetterOrNumber({ invalidChars: initialCharInvalid, lng }),
    );

  const lastCharInvalid = trimmed.match(/[^a-z0-9]$/);
  if (lastCharInvalid)
    errors.push(
      new IdentifierErrors.LastCharShouldBeLetterOrNumber({ invalidChars: lastCharInvalid, lng }),
    );

  if (trimmed.length < minLength) errors.push(new IdentifierErrors.TooShort({ minLength, lng }));
  if (trimmed.length > maxLength) errors.push(new IdentifierErrors.TooLong({ maxLength, lng }));

  if (errors.length > 0) return Result2.fail(errors);

  return Result2.ok(trimmed);
}
