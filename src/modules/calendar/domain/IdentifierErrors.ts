import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { getLng, OptionalLng, PossibleLngs } from '@shared/utils/utils';

export namespace IdentifierErrors {
  export class SpacesNotAllowed extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: t.spacesNotAllowed });
    }
  }

  export class UppercaseNotAllowed extends BadRequest {
    public constructor(args: { invalidChars: string[] } & OptionalLng) {
      const { invalidChars, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];
      super({
        message: `${t.uppercaseNotAllowed}: ${invalidChars.join()}`,
      });
    }
  }

  export class InvalidCharacters extends BadRequest {
    public constructor(args: { invalidChars: string[] } & OptionalLng) {
      const { invalidChars: _invalidChars, lng } = args;
      const unique = Array.from(new Set(_invalidChars));
      const t: Record<Key, string> = trans[getLng(lng)];
      super({
        message: `${t.invalidCharacters}: ${unique.join()}`,
      });
    }
  }

  export class FirstCharShouldBeLetterOrNumber extends BadRequest {
    public constructor(args: { invalidChars: string[] } & OptionalLng) {
      const { invalidChars, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];
      super({
        message: `${t.firstCharShouldBeLetterOrNumber}: ${invalidChars.join()}`,
      });
    }
  }

  export class LastCharShouldBeLetterOrNumber extends BadRequest {
    public constructor(args: { invalidChars: string[] } & OptionalLng) {
      const { invalidChars, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];
      super({
        message: `${t.lastCharShouldBeLetterOrNumber}: ${invalidChars.join()}`,
      });
    }
  }

  export class TooLong extends BadRequest {
    public constructor(args: { maxLength: number } & OptionalLng) {
      const { maxLength, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];
      super({
        message: `${t.atMost} ${maxLength} ${t.long}.`,
      });
    }
  }

  export class TooShort extends BadRequest {
    public constructor(args: { minLength: number } & OptionalLng) {
      const { minLength, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];
      super({
        message: `${t.atLeast} ${minLength} ${t.long}`,
      });
    }
  }

  export class ShouldHaveAtLeast1letter extends BadRequest {
    public constructor(lng?: PossibleLngs) {
      const t: Record<Key, string> = trans[getLng(lng)];
      super({ message: t.shouldHaveAtLeast1letter });
    }
  }
}

type Key = keyof typeof trans.en & keyof typeof trans.es;
const trans = {
  en: {
    spacesNotAllowed: "Identifier can't have spaces.",
    uppercaseNotAllowed: "Identifier can't have uppercase letters, please don't use",
    invalidCharacters: "Identifier can't have these characters",
    firstCharShouldBeLetterOrNumber: 'The first character of identifier should be a lowercase letter or number, not',
    lastCharShouldBeLetterOrNumber: 'The last character of identifier should be a lowercase letter or number, not',
    atMost: 'Identifier should be at most',
    long: 'characters long',
    atLeast: 'Identifier should be at least',
    shouldHaveAtLeast1letter: 'Identifier should have at least one letter.',
  },
  es: {
    spacesNotAllowed: 'El identificador no puede tener espacios.',
    uppercaseNotAllowed: 'El identificador no puede tener letras mayúsculas, por favor no uses',
    invalidCharacters: 'El identificador no puede tener estos caracteres',
    firstCharShouldBeLetterOrNumber: 'El primer carácter del identificador debe ser una letra minúscula o número, no',
    lastCharShouldBeLetterOrNumber: 'El último carácter del identificador debe ser una letra minúscula o número, no',
    atMost: 'El identificador debe tener a lo sumo',
    long: 'caracteres',
    atLeast: 'El identificador debe tener al menos',
    shouldHaveAtLeast1letter: 'El identificador debe tener al menos una letra.',
  },
};

patch({ IdentifierErrors });
