import { ValueObject } from '@shared/core/domain/ValueObject';
import { Slot } from './Slot';
import { AllDayLong, TimeRange } from '../services/CalendarApiFake';
import { DateTime } from 'luxon';
import { Dat } from '@shared/core/Dat';
import { calendar_v3 } from '@googleapis/calendar';
import { logAndThrow, nonEmpty, Spread } from '@shared/utils/utils';
import { convert } from 'html-to-text';
import Schema$Events = calendar_v3.Schema$Events;
import Schema$EventDateTime = calendar_v3.Schema$EventDateTime;

type EventRetrievedInput = {
  // Event though undefined name and description is possible in Google events, so I should type:
  // name?: string | null;
  // description?: string | null;
  // But EventRetrievedDto is sent to frontend and used by it (exported in @s4nt14go/book-backend). That means going through graphql and I don't want fields with undefined for it (fields should be string | number | boolean | null), then I use this type that plays better for the fullstack project:
  name: string | null;
  description: string | null;
  // ...verifiqué que usar estos tipos no produce nada raro. La prueba que hice fue crear un evento sin título (name para EventRetrievedDto, summary para la api de google calendar), ni description. Eso genera un evento sin los campos summary ni description. Hice un update con la api poniendo null en summary y description y no altera el evento, no agrega esos campos.
  slot: Slot;
  id: string;
};

type EventRetrievedProps = EventRetrievedInput & {
  descriptionPlainText: string | null;
};

export type EventRetrievedDto = Spread<
  EventRetrievedProps,
  {
    slot: {
      start: string;
      end: string;
    };
  }
>;

export class EventRetrieved extends ValueObject<
  EventRetrievedProps,
  EventRetrievedDto
> {
  private __class = this.constructor.name;

  get name(): string | null {
    return this.props.name;
  }

  get description(): string | null {
    return this.props.description;
  }

  get descriptionPlainText(): string | null {
    return this.props.descriptionPlainText;
  }

  get slot(): Slot {
    return this.props.slot;
  }

  get id(): string {
    return this.props.id;
  }

  private constructor(props: EventRetrievedProps) {
    super(props);
  }

  public static create(input: EventRetrievedInput) {
    let descriptionPlainText = null;
    if (input.description !== null) {
      descriptionPlainText = convert(input.description, {
        wordwrap: false,
        preserveNewlines: true,
      });
    }
    return new EventRetrieved({
      ...input,
      descriptionPlainText,
    });
  }

  // When updating the event description, the HTML format in EventRetrieved.description is lost, we write just plain text
  public updateDescription(newDescriptionPlainText: string) {
    return new EventRetrieved({
      ...this.props,
      description: newDescriptionPlainText,
      descriptionPlainText: newDescriptionPlainText,
    });
  }

  public toDto(): EventRetrievedDto {
    return {
      ...this.props,
      slot: this.props.slot.toDto(),
    };
  }

  // Convert list of events returned by api.listEvents to EventRetrieved[]
  public static listToDomain(
    apiData: Schema$Events,
  ): [EventRetrieved, ...EventRetrieved[]] | null {
    const { timeZone, items } = apiData;
    if (!items?.length) return null;
    if (!timeZone)
      logAndThrow({
        msg: `Api list events doesn't have timeZone`,
        data: apiData,
      });

    const events = [];
    for (const item of items) {
      const { summary, description, start: apiStart, end: apiEnd, id } = item;

      if (!apiStart || !apiEnd || !id)
        logAndThrow({
          msg: `Api event doesn't have start, end or id`,
          data: item,
        });
      const start = formatFromApi({ apiData: apiStart, timeZone });
      const end = formatFromApi({ apiData: apiEnd, timeZone });
      const slotOrError = Slot.create({
        start: Dat.create({ value: start }).value,
        end: Dat.create({ value: end }).value,
      });

      if (slotOrError.isFailure)
        logAndThrow({
          msg: `Slot couldn't be created from api event times`,
          data: item,
        });
      const slot = slotOrError.value;

      events.push(
        EventRetrieved.create({
          ...item, // I add any properties the event may have so when a new spot is reserved in an existing reservation I keep whatever data the customer may have added.
          name: summary ?? null,
          description: description ?? null,
          slot,
          id,
        }),
      );
    }

    return nonEmpty(events);
  }

  // Convert event returned by api.insertEvent and updateEvent to EventRetrieved
  public static fromApi(apiEvent: calendar_v3.Schema$Event): EventRetrieved {
    if (!apiEvent.start?.dateTime || !apiEvent.end?.dateTime)
      logAndThrow({
        msg: `Event start or end is undefined`,
        data: apiEvent,
      });
    if (!apiEvent.id)
      logAndThrow({
        msg: `Event id is undefined`,
        data: apiEvent,
      });

    const {
      summary,
      description,
      start: { dateTime: start },
      end: { dateTime: end },
      id,
    } = apiEvent;

    const slotOrError = Slot.create({
      start: Dat.create({ value: start }).value,
      end: Dat.create({ value: end }).value,
    });

    if (slotOrError.isFailure)
      logAndThrow({
        msg: `Slot couldn't be created from api event times`,
        data: apiEvent,
      });
    const slot = slotOrError.value;

    return EventRetrieved.create({
      ...apiEvent, // I add any properties the event may have so when a new spot is reserved in an existing reservation I keep whatever data the customer may have added.
      name: summary ?? null,
      description: description ?? null,
      slot,
      id,
    });
  }
}

export function formatFromApi(args: {
  apiData: Schema$EventDateTime;
  timeZone: string;
}): Date {
  const { apiData, timeZone } = args;
  let date;
  if (isAllDayLong(apiData)) {
    date = DateTime.fromFormat(apiData.date, 'yyyy-MM-dd', {
      zone: timeZone,
    }).toJSDate();
  } else if (isTimeRange(apiData)) {
    date = new Date(apiData.dateTime);
  } else
    logAndThrow({
      msg: `ERROR: Api event doesn't conform AllDayLong nor TimeRange`,
      data: apiData,
    });
  return date;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isAllDayLong(test: any): test is AllDayLong {
  return !!test.date;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isTimeRange(test: any): test is TimeRange {
  return !!test.dateTime;
}
