import { expect, test } from 'vitest';
import { Slot } from './Slot';
import { Event } from './Event';
import { Dat } from '@shared/core/Dat';

test(`create`, () => {
  const input = {
    name: 'test name',
    description: 'test description',
    slot: {
      start: '2012-12-24T08:30Z',
      end: '2012-12-25T08:30Z',
    },
  };

  const created = Event.create({
    ...input,
    slot: Slot.create({
      start: Dat.create({ value: input.slot.start }).value,
      end: Dat.create({ value: input.slot.end }).value,
    }).value,
  });

  expect(created.toDto()).toMatchObject(input);
});
