import { expect, test } from 'vitest';
import { Calendar, CalendarType } from './Calendar';

test(`create & assemble back`, () => {
  const dto = {
    id: 'testCalendarId',
    userId: 'testUserId',
    type: CalendarType.RESERVATIONS,
    name: 'test name',
  };

  const { id, ...rest } = dto;
  const created = Calendar.create(rest, id);

  expect(created.toDto()).toMatchObject(dto);

  const assembled = Calendar.assemble(dto);

  expect(assembled.equals(created)).toBe(true);
});
