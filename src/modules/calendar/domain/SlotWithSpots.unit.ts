import { expect, test, it, describe, vi } from 'vitest';
import { Spots } from './Spots';
import { SlotWithSpots, SlotWithSpotsDto } from './SlotWithSpots';
import { Slot } from './Slot';
import { createSlotWithSpots } from '../utils/test';
import { Dat } from '@shared/core/Dat';
import { nonEmpty } from '@shared/utils/utils';

const sendAnalytics = vi.fn();

test(`create & assemble back`, () => {
  const dto: SlotWithSpotsDto = {
    slot: {
      start: '2024-01-01T00:00Z',
      end: '2024-01-01T01:00Z',
    },
    spots: 3,
  };

  const args = {
    slot: Slot.create({
      start: Dat.create({ value: dto.slot.start }).value,
      end: Dat.create({ value: dto.slot.end }).value,
    }).value,
    spots: Spots.create(dto.spots, sendAnalytics).value,
  };
  const created = SlotWithSpots.create(args);

  expect(SlotWithSpots.toDto(created)).toMatchObject(dto);

  const assembled = SlotWithSpots.assemble(dto, sendAnalytics);

  const equals = assembled.equals(created);

  expect(equals).toBe(true);
});

describe(`getAvailableChunks`, () => {
  const availabilityDto = [
    {
      slot: {
        start: '2024-01-01T00:00Z',
        end: '2024-01-01T01:00Z',
      },
      spots: 5,
    },
    {
      slot: {
        start: '2024-01-01T03:00Z',
        end: '2024-01-01T05:00Z',
      },
      spots: 1,
    },
    {
      slot: {
        start: '2024-01-01T07:00Z',
        end: '2024-01-01T09:00Z',
      },
      spots: 2,
    },
    {
      slot: {
        start: '2024-01-01T11:00Z',
        end: '2024-01-01T13:00Z',
      },
      spots: 4,
    },
    {
      slot: {
        start: '2024-01-01T15:00Z',
        end: '2024-01-01T16:00Z',
      },
      spots: 4,
    },
  ];

  const reservedDto = [
    {
      slot: {
        start: '2024-01-01T00:30Z',
        end: '2024-01-01T01:00Z',
      },
      spots: 3,
    },
    {
      slot: {
        start: '2024-01-01T03:00Z',
        end: '2024-01-01T04:00Z',
      },
      spots: 1,
    },
    {
      slot: {
        start: '2024-01-01T08:00Z',
        end: '2024-01-01T09:00Z',
      },
      spots: 1,
    },
    {
      slot: {
        start: '2024-01-01T11:00Z',
        end: '2024-01-01T12:00Z',
      },
      spots: 2,
    },
  ];

  test(`common scenario`, () => {
    const availability = nonEmpty(availabilityDto.map(createSlotWithSpots));
    const reserved = nonEmpty(reservedDto.map(createSlotWithSpots));

    const available = SlotWithSpots.getAvailableChunks({ availability, reserved }, sendAnalytics);
    if (!available) throw Error('Expected available to be defined');
    const availableDto = available.map((avail) => avail.toDto());

    expect(availableDto.length).toBe(8);
    expect(availableDto).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          slot: {
            start: '2024-01-01T00:00Z',
            end: '2024-01-01T00:30Z',
          },
          spots: 5,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T00:30Z',
            end: '2024-01-01T01:00Z',
          },
          spots: 2,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T04:00Z',
            end: '2024-01-01T05:00Z',
          },
          spots: 1,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T07:00Z',
            end: '2024-01-01T08:00Z',
          },
          spots: 2,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T08:00Z',
            end: '2024-01-01T09:00Z',
          },
          spots: 1,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T11:00Z',
            end: '2024-01-01T12:00Z',
          },
          spots: 2,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T12:00Z',
            end: '2024-01-01T13:00Z',
          },
          spots: 4,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T15:00Z',
            end: '2024-01-01T16:00Z',
          },
          spots: 4,
        }),
      ]),
    );
  });

  test(`overlapping availability is computed`, () => {
    const availabilityDto = [
      {
        slot: {
          start: '2024-01-01T00:00Z',
          end: '2024-01-01T01:00Z',
        },
        spots: 5,
      },
      {
        slot: {
          start: '2024-01-01T00:30Z',
          end: '2024-01-01T02:00Z',
        },
        spots: 3,
      },
    ];

    const reservedDto = [
      {
        slot: {
          start: '2024-01-01T00:30Z',
          end: '2024-01-01T01:30Z',
        },
        spots: 2,
      },
    ];

    const availability = nonEmpty(availabilityDto.map(createSlotWithSpots));
    const reserved = nonEmpty(reservedDto.map(createSlotWithSpots));

    const available = SlotWithSpots.getAvailableChunks({ availability, reserved }, sendAnalytics);
    if (!available) throw Error('Expected available to be defined');
    const availableDto = available.map((avail) => avail.toDto());

    expect(availableDto.length).toBe(4);
    expect(availableDto).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          slot: {
            start: '2024-01-01T00:00Z',
            end: '2024-01-01T00:30Z',
          },
          spots: 5,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T00:30Z',
            end: '2024-01-01T01:00Z',
          },
          spots: 6,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T01:00Z',
            end: '2024-01-01T01:30Z',
          },
          spots: 1,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T01:30Z',
            end: '2024-01-01T02:00Z',
          },
          spots: 3,
        }),
      ]),
    );
  });

  test(`overlapping reservations are computed`, () => {
    const availabilityDto = [
      {
        slot: {
          start: '2024-01-01T00:00Z',
          end: '2024-01-01T01:00Z',
        },
        spots: 3,
      },
      {
        slot: {
          start: '2024-01-01T01:00Z',
          end: '2024-01-01T02:00Z',
        },
        spots: 6,
      },
    ];

    const reservedDto = [
      {
        slot: {
          start: '2024-01-01T00:30Z',
          end: '2024-01-01T01:30Z',
        },
        spots: 1,
      },
      {
        slot: {
          start: '2024-01-01T01:00Z',
          end: '2024-01-01T02:00Z',
        },
        spots: 4,
      },
    ];

    const availability = nonEmpty(availabilityDto.map(createSlotWithSpots));
    const reserved = nonEmpty(reservedDto.map(createSlotWithSpots));

    const available = SlotWithSpots.getAvailableChunks({ availability, reserved }, sendAnalytics);
    if (!available) throw Error('Expected available to be defined');
    const availableDto = available.map((avail) => avail.toDto());

    expect(availableDto.length).toBe(4);
    expect(availableDto).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          slot: {
            start: '2024-01-01T00:00Z',
            end: '2024-01-01T00:30Z',
          },
          spots: 3,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T00:30Z',
            end: '2024-01-01T01:00Z',
          },
          spots: 2,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T01:00Z',
            end: '2024-01-01T01:30Z',
          },
          spots: 1,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T01:30Z',
            end: '2024-01-01T02:00Z',
          },
          spots: 2,
        }),
      ]),
    );
  });

  it(`filters out when availability is less than 1`, () => {
    const availabilityDto = [
      {
        slot: {
          start: '2024-01-01T00:00Z',
          end: '2024-01-01T01:00Z',
        },
        spots: 3,
      },
      {
        slot: {
          start: '2024-01-01T01:00Z',
          end: '2024-01-01T02:00Z',
        },
        spots: 6,
      },
    ];

    const reservedDto = [
      {
        slot: {
          start: '2024-01-01T00:30Z',
          end: '2024-01-01T01:00Z',
        },
        spots: 3,
      },
      {
        slot: {
          start: '2024-01-01T01:00Z',
          end: '2024-01-01T02:00Z',
        },
        spots: 8,
      },
    ];

    const availability = nonEmpty(availabilityDto.map(createSlotWithSpots));
    const reserved = nonEmpty(reservedDto.map(createSlotWithSpots));

    const available = SlotWithSpots.getAvailableChunks({ availability, reserved }, sendAnalytics);
    if (!available) throw Error('Expected available to be defined');
    const availableDto = available.map((avail) => avail.toDto());

    expect(availableDto.length).toBe(1);
    expect(availableDto).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          slot: {
            start: '2024-01-01T00:00Z',
            end: '2024-01-01T00:30Z',
          },
          spots: 3,
        }),
      ]),
    );
  });

  it(`consolidate adjacent chunks when they have the same spots`, () => {
    const availabilityDto = [
      {
        slot: {
          start: '2024-01-01T00:00Z',
          end: '2024-01-01T01:00Z',
        },
        spots: 3,
      },
      {
        slot: {
          start: '2024-01-01T01:00Z',
          end: '2024-01-01T02:00Z',
        },
        spots: 6,
      },
    ];

    const reservedDto = [
      {
        slot: {
          start: '2024-01-01T00:00Z',
          end: '2024-01-01T01:00Z',
        },
        spots: 1,
      },
      {
        slot: {
          start: '2024-01-01T01:00Z',
          end: '2024-01-01T02:00Z',
        },
        spots: 4,
      },
    ];

    const availability = nonEmpty(availabilityDto.map(createSlotWithSpots));
    const reserved = nonEmpty(reservedDto.map(createSlotWithSpots));

    const available = SlotWithSpots.getAvailableChunks({ availability, reserved }, sendAnalytics);
    if (!available) throw Error('Expected available to be defined');
    const availableDto = available.map((avail) => avail.toDto());

    expect(availableDto.length).toBe(1);
    expect(availableDto[0]).toEqual(
      expect.objectContaining({
        slot: {
          start: '2024-01-01T00:00Z',
          end: '2024-01-01T02:00Z',
        },
        spots: 2,
      }),
    );
  });

  it(`returns null when there aren't chunks available`, () => {
    const common = nonEmpty(availabilityDto.map(createSlotWithSpots));
    const available = SlotWithSpots.getAvailableChunks({
      availability: common,
      reserved: common,
    }, sendAnalytics);

    expect(available).toBe(null);
  });

  it(`returns null when null availability is passed in`, () => {
    const available = SlotWithSpots.getAvailableChunks({
      availability: null,
      reserved: nonEmpty(reservedDto.map(createSlotWithSpots)),
    }, sendAnalytics);

    expect(available).toBe(null);
  });
});

describe(`chunkify`, () => {
  const slotsDto = [
    {
      slot: {
        start: '2024-01-01T00:00Z',
        end: '2024-01-01T01:00Z',
      },
      spots: 5,
    },
    {
      slot: {
        start: '2024-01-01T00:00Z',
        end: '2024-01-01T02:00Z',
      },
      spots: 2,
    },
    {
      slot: {
        start: '2024-01-01T03:00Z',
        end: '2024-01-01T04:00Z',
      },
      spots: 1,
    },
    {
      slot: {
        start: '2024-01-01T04:00Z',
        end: '2024-01-01T05:00Z',
      },
      spots: 4,
    },
    {
      slot: {
        start: '2024-01-01T05:00Z',
        end: '2024-01-01T07:00Z',
      },
      spots: 4,
    },
    {
      slot: {
        start: '2024-01-01T06:00Z',
        end: '2024-01-01T07:00Z',
      },
      spots: 1,
    },
  ];

  it(`succeeds`, () => {
    const range = Slot.create({
      start: Dat.create({ value: slotsDto[0].slot.start }).value,
      end: Dat.create({ value: slotsDto[5].slot.end }).value,
    }).value;

    const slots = nonEmpty(slotsDto.map(createSlotWithSpots));

    const chunks = SlotWithSpots.chunkify({ slots, range }, sendAnalytics);
    if (!chunks) throw Error('Expected chunks to be defined');
    const chunksDto = chunks.map((avail) => avail.toDto());

    expect(chunksDto.length).toBe(5);
    expect(chunksDto).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          slot: {
            start: '2024-01-01T00:00Z',
            end: '2024-01-01T01:00Z',
          },
          spots: 7,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T01:00Z',
            end: '2024-01-01T02:00Z',
          },
          spots: 2,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T03:00Z',
            end: '2024-01-01T04:00Z',
          },
          spots: 1,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T04:00Z',
            end: '2024-01-01T06:00Z',
          },
          spots: 4,
        }),
        expect.objectContaining({
          slot: {
            start: '2024-01-01T06:00Z',
            end: '2024-01-01T07:00Z',
          },
          spots: 5,
        }),
      ]),
    );
  });

  it(`honors range`, () => {
    const range = Slot.create({
      start: Dat.create({ value: '2024-01-01T00:00Z' }).value,
      end: Dat.create({ value: '2024-01-01T01:30Z' }).value,
    }).value;

    const slots = nonEmpty(slotsDto.map(createSlotWithSpots));

    const chunks = SlotWithSpots.chunkify({ slots, range }, sendAnalytics);
    if (!chunks) throw Error('Expected chunks to be defined');
    const chunksDto = chunks.map((avail) => avail.toDto());

    expect(chunksDto.length).toBe(2);
    expect(chunksDto).toEqual([
      {
        slot: {
          start: '2024-01-01T00:00Z',
          end: '2024-01-01T01:00Z',
        },
        spots: 7,
      },
      {
        slot: {
          start: '2024-01-01T01:00Z',
          end: '2024-01-01T01:30Z',
        },
        spots: 2,
      },
    ]);
  });
});
