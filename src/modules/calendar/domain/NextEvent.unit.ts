import { test, expect } from 'vitest';
import { NextEvent } from './NextEvent';
import {
  createEventRetrieved,
  createIdentifier,
  createSpots,
} from '../utils/test';
import { nonEmpty } from '@shared/utils/utils';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

function createDetected() {
  if (chance.bool()) return null;
  return nonEmpty(
    new Array(chance.integer({ min: 1, max: 5 })).fill(null).map(() => ({
      identifier: createIdentifier(),
      spots: createSpots(),
    })),
  );
}

test(`create & toDto`, () => {
  const event = createEventRetrieved();
  const detected = createDetected();
  const created = NextEvent.create({ event, detected });
  const dto = created.toDto();
  expect(dto.event).toStrictEqual(event.toDto());
  if (detected === null) {
    expect(dto.detected).toBe(null);
  } else {
    expect(dto.detected!.length).toBe(detected.length);
    for (const event of detected) {
      expect(dto.detected).toEqual(
        expect.arrayContaining([
          {
            identifier: event.identifier.value,
            spots: event.spots.value,
          },
        ]),
      );
    }
  }
});
