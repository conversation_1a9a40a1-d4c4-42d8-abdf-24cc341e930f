import { ValueObject } from '@shared/core/domain/ValueObject';
import { Spots } from './Spots';
import { Slot } from './Slot';
import { SlotWithSpots } from './SlotWithSpots';
import { EveryT, TimeReferenceT } from '../external';
import { Dat } from '@shared/core/Dat';
import { PositiveInt } from '@shared/core/PositiveInt';
import { nonEmpty } from '@shared/utils/utils';
import { IContextProvider } from '@shared/context/IContextProvider';

export interface GetPossibleTimesArgs {
  timeReference: TimeReferenceT; // this comes from the reservation option, it's a time in which reservations are allowed. With this reference and the "every" value, we can determine what are the available times for reservation.
  every: EveryT;
  duration: PositiveInt;
  chunks: [SlotWithSpots, ...SlotWithSpots[]];
  maxTimes: PositiveInt;
}

export interface GetSpotsForReservationArgs {
  slot: Slot;
  chunks: SlotWithSpots[];
}

interface TimeWithSpotsProps {
  time: Date;
  spots: Spots;
}

export interface TimeWithSpotsDto {
  time: string;
  spots: number;
}

type AccommodationResult =
  | {
  canAccommodate: true;
  minSpots: Spots;
}
  | {
  canAccommodate: false;
};

type IsWithinResult =
  | {
  isWithin: true;
  firstTime: Date;
}
  | {
  isWithin: false;
};

export class TimeWithSpots extends ValueObject<
  TimeWithSpotsProps,
  TimeWithSpotsDto
> {
  private __class = this.constructor.name;

  get time(): Date {
    return this.props.time;
  }

  get spots(): Spots {
    return this.props.spots;
  }

  private constructor(props: TimeWithSpotsProps) {
    super(props);
  }

  /**
   * Returns the times with the available spots
   * @param {TimeReference} args.timeReference Time reference to establish the starting point for calculating possible reservation times, which can be both before and after that reference
   * @param {Every} args.every Frequency to allow reservations
   * @param {PositiveInt} args.duration Service duration
   * @param {[SlotWithSpots]} args.chunks The available chunks
   * @param {[PositiveInt]} args.maxTimes The maximum number of times to return
   * @returns {[TimeWithSpots]} Times with available spots
   */
  public static async getPossibleTimes(
    args: GetPossibleTimesArgs,
    sendAnalytics: IContextProvider['sendAnalytics'],
  ): Promise<[TimeWithSpots, ...TimeWithSpots[]] | null> {
    // Es llamado por CalendarsSrv.getAvailableTimes con el resultado de CalendarsSrv.getAvailableChunks > AvailabilitySrv.getChunks en el argumento chunks, que es un array de SlotWithSpots no vacío y spots distintos de 0
    const {
      timeReference: _timeReference,
      chunks,
      every: _every,
      duration: _duration,
      maxTimes,
    } = args;

    const every = _every.value;
    const duration = _duration.value;

    // Combined tracking for possible times and max spots
    const timesSeen: Record<string, TimeWithSpots> = {};

    // Function to add minutes to a Date object
    const addMinutes = (date: Date, minutes: number): Date => {
      return new Date(date.getTime() + minutes * 60000);
    };

    // Recursive function to check if the time can be accommodated in the current or adjacent slots
    const canAccommodateService = (
      currentTime: Date,
      chunkIndex: number,
    ): AccommodationResult => {
      const serviceEndTime = addMinutes(currentTime, duration);
      const currentSlot = chunks[chunkIndex];

      if (serviceEndTime.getTime() <= currentSlot.slot.end.t) {
        return {canAccommodate: true, minSpots: currentSlot.spots};
      }

      // Check if the service can be accommodated in the next adjacent slots
      if (
        chunkIndex < chunks.length - 1 &&
        currentSlot.slot.end.t >= chunks[chunkIndex + 1].slot.start.t
      ) {
        const nextResult = canAccommodateService(currentTime, chunkIndex + 1);
        return nextResult.canAccommodate
          ? {
            canAccommodate: nextResult.canAccommodate,
            minSpots: Spots.create(
              Math.min(currentSlot.spots.value, nextResult.minSpots.value),
              sendAnalytics
            ).value,
          }
          : {
            canAccommodate: nextResult.canAccommodate,
          };
      }

      return {canAccommodate: false};
    };

    // Process each slot
    label1: for (let index = 0; index < chunks.length; index++) {
      const slotWithSpots = chunks[index];

      const isThereTimeResult = TimeWithSpots.isThereTimeWithinSlot({
        timeRef: _timeReference,
        slot: slotWithSpots.slot,
        every: _every,
      });
      if (!isThereTimeResult.isWithin) continue;

      const timeReference = isThereTimeResult.firstTime;

      // Start from the reference time and go forwards until the slot end
      let currentTime = new Date(timeReference);
      // eslint-disable-next-line no-constant-condition
      while (true) {
        const accommodation = canAccommodateService(currentTime, index);
        if (!accommodation.canAccommodate) break;

        const timeKey = currentTime.toJSON();

        // Update timesSeen with max spots for this time
        if (!timesSeen[timeKey] || accommodation.minSpots.value > timesSeen[timeKey].spots.value) {
          timesSeen[timeKey] = new TimeWithSpots({
            time: new Date(currentTime),
            spots: accommodation.minSpots,
          });
        }

        currentTime = addMinutes(currentTime, every);

        // Early return logic
        const elapsed = new Date().getTime() - Number(process.env.startingTime);
        const limit = 5;
        if (elapsed > limit * 1000) {
          const msg = `Elapsed time exceeded ${limit}s, early break at ${elapsed}ms index ${index}, ${chunks.length - 1 - index}/${chunks.length} chunks left unprocessed.`;
          await sendAnalytics({
            msg,
          });
          console.log(msg);
          console.log(`Last chunk processed`, chunks[index].toDto());
          break label1;
        }
      }
    }

    // Return null if no times found
    if (!Object.keys(timesSeen).length) return null;

    const array = Object.values(timesSeen).sort((a: TimeWithSpots, b: TimeWithSpots) => a.time.getTime() - b.time.getTime()).slice(0, maxTimes.value);

    return nonEmpty(array);
  }

  /**
   * Returns the available time if exists
   * @param {Slot} args.slot Start and end of reservation
   * @param {SlotWithSpots[]} args.chunks The available chunks sorted
   * @returns {Spots | null} Spots available for that reservation or null
   */
  public static getSpotsForReservation(
    args: GetSpotsForReservationArgs,
    sendAnalytics: IContextProvider['sendAnalytics']
  ): Spots | null {
    const {slot, chunks} = args;

    if (!chunks.length) return null;

    // Recursive function to check if the time can be accommodated in the current or adjacent slots
    const canAccommodateService = (args: {
      start: Date;
      chunkIndex: number;
    }): AccommodationResult => {
      const {start, chunkIndex} = args;
      const currentSlot = chunks[chunkIndex];

      if (slot.end.t <= currentSlot.slot.end.t) {
        return {canAccommodate: true, minSpots: currentSlot.spots};
      }

      // Check if the service can be accommodated in the next adjacent slots
      if (
        chunkIndex < chunks.length - 1 &&
        currentSlot.slot.end.t >= chunks[chunkIndex + 1].slot.start.t
      ) {
        const nextResult = canAccommodateService({
          start: start,
          chunkIndex: chunkIndex + 1,
        });
        return nextResult.canAccommodate
          ? {
            canAccommodate: nextResult.canAccommodate,
            minSpots: Spots.create(
              Math.min(currentSlot.spots.value, nextResult.minSpots.value),
              sendAnalytics
            ).value,
          }
          : {
            canAccommodate: nextResult.canAccommodate,
          };
      }

      return {canAccommodate: false};
    };

    let spots = null;

    chunks.forEach((chunk, chunkIndex) => {
      if (slot.start.t < chunk.slot.start.t || slot.start.t > chunk.slot.end.t)
        return;
      const accommodation = canAccommodateService({
        start: slot.start.j,
        chunkIndex,
      });
      if (accommodation.canAccommodate) spots = accommodation.minSpots;
    });

    if (!spots || !(spots as Spots).value) return null;

    return spots;
  }

  private static isThereTimeWithinSlot(arg: {
    timeRef: TimeReferenceT;
    slot: Slot;
    every: EveryT;
  }): IsWithinResult {
    const {timeRef, slot, every} = arg;

    // Convert Slot start and end to Luxon DateTime for comparison
    const checkFrom = slot.start.l.setZone(timeRef.zone).startOf('day');
    const checkTo = slot.end.l.setZone(timeRef.zone).endOf('day');

    // Check if there is a time that falls within the slot range
    for (
      let time = checkFrom;
      time <= checkTo;
      time = time.plus({minutes: every.value})
    ) {
      const timeUnix = time.toUnixInteger();

      // Check if the time is within the slot for that day
      if (
        timeUnix >= slot.start.l.toUnixInteger() &&
        timeUnix <= slot.end.l.toUnixInteger()
      ) {
        return {
          isWithin: true,
          firstTime: time.toJSDate(),
        };
      }
    }

    return {
      isWithin: false,
    };
  }

  public toDto(): TimeWithSpotsDto {
    const {time, spots} = this;
    return {
      time: Dat.create({value: time}).value.s,
      spots: spots.value,
    };
  }
}
