import { ValueObject } from '@shared/core/domain/ValueObject';
import { Slot } from './Slot';

interface EventProps {
  name: string;
  description: string;
  slot: Slot;
}

export type EventDto = {
  name: string;
  description: string;
  slot: {
    start: string;
    end: string;
  };
};

// Simpler than EventRetrieved, used by CalendarApi.insertEvent, with just what makes sense writing to the calendar
// Event.description is always plain text (unlike EventRetrieved.description, which may contain HTML).
export class Event extends ValueObject<EventProps, EventDto> {
  private __class = this.constructor.name;
  get name(): string {
    return this.props.name;
  }

  get description(): string {
    return this.props.description;
  }

  get slot(): Slot {
    return this.props.slot;
  }

  private constructor(props: EventProps) {
    super(props);
  }

  public static create(props: EventProps) {
    return new Event(props);
  }

  public toDto(): EventDto {
    return {
      ...this.props,
      slot: this.slot.toDto(),
    };
  }
}
