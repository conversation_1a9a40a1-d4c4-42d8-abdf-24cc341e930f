import { it, expect } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { createCalendar } from '../../utils/test';
import { dateFormat, expectErrorAppSync, pickLng } from '@shared/utils/test';
import { CalendarRepo } from '../../repos/CalendarRepo';
import { Request } from './DeleteCalendarDTOs';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const repo = new CalendarRepo(models.Calendar);
const appsync = new AppSyncClient();
const query = gql`
  mutation ($userId: ID!, $calendarId: ID!, $type: String!, $lng: String!) {
    deleteCalendar(
      userId: $userId
      type: $type
      calendarId: $calendarId
      lng: $lng
    ) {
      result
      time
    }
  }
`;

it(`returns 1 when deletes a calendar`, async () => {
  // First create a calendar
  const calendar = createCalendar();
  await repo.create(calendar);

  const received = await appsync.send<Request>({
    query,
    variables: {
      calendarId: calendar.id.toString(),
      userId: calendar.userId,
      type: calendar.type,
      lng: pickLng(),
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log('DeleteCalendar.e2e.ts 1 json:', json);
  const response = json.data.deleteCalendar;
  if (!response) console.log('DeleteCalendar.e2e.ts 2 json:', json);
  expect(response).toEqual({
    result: 1,
    time: expect.stringMatching(dateFormat),
  });

  expect(await repo.get(calendar.id.toString())).toBeNull();
});

it(`returns 0 when doesn't delete any calendar`, async () => {
  // First create a calendar
  const calendar = createCalendar();
  await repo.create(calendar);

  const received = await appsync.send<Request>({
    query,
    variables: {
      calendarId: chance.guid(),
      userId: calendar.userId,
      type: calendar.type,
      lng: pickLng(),
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log('DeleteCalendar.e2e.ts 1 json:', json);
  const response = json.data.deleteCalendar;
  if (!response) console.log('DeleteCalendar.e2e.ts 2 json:', json);
  expect(response).toEqual({
    result: 0,
    time: expect.stringMatching(dateFormat),
  });
});

it(`fails when calendar type is invalid`, async () => {
  const calendar = createCalendar();
  const received = await appsync.send<Request>({
    query,
    variables: {
      calendarId: calendar.id.toString(),
      userId: calendar.userId,
      type: 'INVALID',
      lng: pickLng(),
    },
  });

  expect(received.status).toBe(200);
  expectErrorAppSync({
    response: await received.json(),
    query: 'deleteCalendar',
    error: 'DeleteCalendarErrors.InvalidType',
    status: 400,
  });
});
