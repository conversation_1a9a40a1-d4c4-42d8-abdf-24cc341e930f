import { patch } from '@shared/core/utils';
import { BaseError } from '@shared/core/AppError';
import { Status } from '@shared/core/Status';
import { getLng, OptionalLng } from '@shared/utils/utils';

export namespace DeleteCalendarErrors {
  export class InvalidType extends BaseError {
    public constructor(args: { type: string } & OptionalLng) {
      const { type, lng } = args;
      const t: Record<Key, string> = trans[getLng(lng)];

      super({
        message: `${t.invalid} "${type}" ${t.type}`,
        status: Status.BAD_REQUEST,
      });
    }
  }
}

type Key = keyof typeof trans.en & keyof typeof trans.es;
const trans = {
  en: {
    invalid: 'Invalid',
    type: 'calendar type',
  },
  es: {
    invalid: 'Tipo de calendario',
    type: 'inválido',
  },
};

patch({ DeleteCalendarErrors });
