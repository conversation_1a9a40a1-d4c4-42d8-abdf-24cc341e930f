import { Request, Response } from './DeleteCalendarDTOs';
import { formatErrors } from '@shared/core/AppError';
import { Status } from '@shared/core/Status';
import { ControllerResult } from '@shared/core/ControllerResult';
import { ICalendarRepo } from '../../repos/ICalendarRepo';
import { DeleteCalendarErrors } from './DeleteCalendarErrors';
import { AppSyncController } from '@shared/infra/Controllers';
import { CalendarType } from '../../domain/Calendar';
import { getLng } from '@shared/utils/utils';
import { IContextProvider } from '@shared/context/IContextProvider';

export class DeleteCalendar extends AppSyncController<Request, Response> {
  private readonly repo: ICalendarRepo;

  public constructor(args: {
    repo: ICalendarRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { userId, calendarId, type: _type, lng: _lng } = dto;
    const lng = getLng(_lng);

    if (!(_type in CalendarType))
      return formatErrors([
        new DeleteCalendarErrors.InvalidType({ type: _type, lng }),
      ]);
    const deletedCount = await this.repo.deleteById({
      userId,
      calendarId,
      type: CalendarType[_type as CalendarType],
    });

    return {
      status: Status.OK,
      result: deletedCount,
    };
  }
}
