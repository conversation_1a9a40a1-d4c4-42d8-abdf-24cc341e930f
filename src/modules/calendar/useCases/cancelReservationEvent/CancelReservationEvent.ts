import { Request, Response } from './CancelReservationEventDTOs';
import { ICalendarRepo } from '../../repos/ICalendarRepo';
import { BaseError } from '@shared/core/AppError';
import { CancelReservationEventErrors } from './CancelReservationEventErrors';
import { Identifier } from '../../domain/Identifier';
import { IReservationSrv } from '../../services/ICalendarsSrv';
import { FunctionController2 } from '@shared/infra/Controllers';
import { Dat } from '@shared/core/Dat';
import { getLng } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { IContextProvider } from '@shared/context/IContextProvider';

export class CancelReservationEvent extends FunctionController2<Request, Response> {
  private readonly calendarRepo: ICalendarRepo;
  private readonly reservationSrv: IReservationSrv;
  private readonly sendAnalytics: IContextProvider['sendAnalytics'];

  public constructor(args: {
    calendarRepo: ICalendarRepo;
    reservationSrv: IReservationSrv;
    sendAnalytics: IContextProvider['sendAnalytics'];
  }) {
    super();
    const { calendarRepo, reservationSrv, sendAnalytics } = args;
    this.calendarRepo = calendarRepo;
    this.reservationSrv = reservationSrv;
    this.sendAnalytics = sendAnalytics;
  }

  public async executeImpl(dto: Request) {
    const {
      userId,
      eventId,
      identifier: _identifier,
      start: _start,
      end: _end,
      addToEventDescription,
      lng: _lng,
    } = dto;
    const lng = getLng(_lng);

    let errors: BaseError[] = [];

    const startOrError = Dat.create({ value: _start, lng });
    if (startOrError.isFailure)
      errors.push(startOrError.error!.setField('start'));

    const endOrError = Dat.create({ value: _end, lng });
    if (endOrError.isFailure)
      errors.push(endOrError.error!.setField('end'));

    const identifierOrErrors = Identifier.create({ value: _identifier, lng }, this.sendAnalytics);
    if (identifierOrErrors.isFailure)
      errors = errors.concat(identifierOrErrors.errors!.map(e => e.setField('identifier')));

    if (errors.length > 0) {
      const msg = `Errors when validating input`;
      console.log(msg, errors);
      await this.sendAnalytics({
        msg,
        errors,
      });
      return Result2.fail(errors);
    }
    const start = startOrError.value;
    const end = endOrError.value;
    const identifier = identifierOrErrors.value;

    const reservationCalendar = await this.calendarRepo.getReservations(userId);
    if (!reservationCalendar)
      return Result2.fail([
        new CancelReservationEventErrors.ReservationCalendarNotFoundForUserId({
          userId,
          lng,
        }).setField('userId'),
      ]);

    this.reservationSrv.setCalendarId(reservationCalendar.id.toString());

    const eventForReservationOrError = await this.reservationSrv.getEvent({
      eventId,
      identifier,
      start,
      end,
      lng,
    });
    if (eventForReservationOrError.isFailure) {
      console.log('Error when getting event', eventForReservationOrError.error);
      return Result2.fail([
        new CancelReservationEventErrors.WhenGettingEvent(lng),
      ]);
    }
    const eventForReservation = eventForReservationOrError.value;

    const { updatedDescriptionPlainText, decreasedSpots } = await identifier.decreaseSpots(
      eventForReservation.descriptionPlainText!,
    );

    const descriptionPlainTextUpdated = `${addToEventDescription}\n${updatedDescriptionPlainText}`;
    const updatedOrError = await this.reservationSrv.updateEvent({
      event: eventForReservation.updateDescription(descriptionPlainTextUpdated),
      lng,
    });
    if (updatedOrError.isFailure) {
      const msg = 'Error when trying to update calendar event',
            e = updatedOrError.error;
      console.log(msg, { e, descriptionPlainTextUpdated });
      await this.sendAnalytics({
        msg,
        e,
        descriptionPlainTextUpdated,
      });
      return Result2.fail([
        new CancelReservationEventErrors.WhenTryingToUpdateEvent(lng),
      ]);
    }

    if (decreasedSpots.value === 0) {
      const deletedOrError = await this.reservationSrv.deleteEvent({
        id: eventId,
        lng,
      });
      if (deletedOrError.isFailure) {
        console.log('Error when trying to delete calendar event', { e: deletedOrError.error, eventId });
        return Result2.fail([
          new CancelReservationEventErrors.WhenTryingToDeleteEvent(lng),
        ]);
      }
    }

    return Result2.ok(undefined);
  }
}
