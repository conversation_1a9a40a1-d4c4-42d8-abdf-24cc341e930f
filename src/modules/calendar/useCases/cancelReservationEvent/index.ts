import { CancelReservationEvent } from './CancelReservationEvent';
import { CalendarRepo } from '../../repos/CalendarRepo';
import { CalendarApi } from '../../services/CalendarApi';
import { ReservationSrv } from '../../services/ReservationSrv';
import { calendar } from '@googleapis/calendar';
import { IContextProvider } from '@shared/context/IContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('./../../../../shared/infra/database/sequelize/models'); // If I use @shared src/modules/reservation/useCases/cancelReservationByBusiness/CancelReservationByBusiness.unit.ts fails

export const createHandler = (sendAnalytics: IContextProvider['sendAnalytics']) => {
  const calendarRepo = new CalendarRepo(models.Calendar);
  const reservationSrv = new ReservationSrv({
    api: new CalendarApi({
      google: calendar({ version: 'v3' }),
      sendAnalytics,
    }),
    sendAnalytics,
  });
  const controller = new CancelReservationEvent({
    calendarRepo,
    reservationSrv,
    sendAnalytics,
  });

  return controller.executeImpl.bind(controller);
};
