import { patch } from '@shared/core/utils';
import { BaseError } from '@shared/core/AppError';
import { Status } from '@shared/core/Status';
import { getLng, OptionalLng, PossibleLngs } from '@shared/utils/utils';

export namespace CancelReservationEventErrors {
  export class ReservationCalendarNotFoundForUserId extends BaseError {
    public constructor(args: { userId: string } & OptionalLng) {
      const { userId, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.reservationCalendarNotFound(userId),
        status: Status.NOT_FOUND,
      });
    }
  }
  export class WhenGettingEvent extends BaseError {
    public constructor(lng: string) {
      const t = trans[getLng(lng)];
      super({
        message: t.whenGettingEvent,
        status: Status.NOT_FOUND,
      });
    }
  }
  export class WhenTryingToDeleteEvent extends BaseError {
    public constructor(lng: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({
        message: t.whenTryingToDeleteEvent,
        status: Status.BAD_GATEWAY,
      });
    }
  }
  export class WhenTryingToUpdateEvent extends BaseError {
    public constructor(lng: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({
        message: t.whenTryingToUpdateEvent,
        status: Status.BAD_GATEWAY,
      });
    }
  }
}

const trans = {
  en: {
    reservationCalendarNotFound(userId: string) {
      return `Reservation calendar for user id ${userId} not found`;
    },
    whenGettingEvent: `Error when trying to get calendar event.`,
    whenTryingToDeleteEvent: `Error when trying to delete the event in the reservation calendar.`,
    whenTryingToUpdateEvent: `Error when trying to update the event in the reservation calendar.`,
  },
  es: {
    reservationCalendarNotFound(userId: string) {
      return `Calendario de reservas para el id de usuario ${userId} no encontrado`;
    },
    whenGettingEvent: `Error al intentar obtener el evento del calendario.`,
    whenTryingToDeleteEvent: `Error al intentar borrar el evento en el calendario de reservas.`,
    whenTryingToUpdateEvent: `Error al intentar actualizar el evento en el calendario de reservas.`,
  },
};

patch({ CancelReservationEventErrors });
