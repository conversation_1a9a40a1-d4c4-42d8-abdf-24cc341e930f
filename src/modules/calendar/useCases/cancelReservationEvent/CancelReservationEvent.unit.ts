import { it, expect, test, describe, vi } from 'vitest';
process.env.calendar_clientKey = 'dummy';
process.env.calendar_clientEmail = 'dummy';
import { CancelReservationEvent } from './CancelReservationEvent';
import { CalendarRepoFake } from '../../repos/CalendarRepoFake';
import { ReservationSrv } from '../../services/ReservationSrv';
import { ApiResults, CalendarApiFake } from '../../services/CalendarApiFake';
import {
  BusinessUsersInDb,
  ReservationOptionsInDb,
} from '../../utils/testExternal';
import {
  nonExistingId,
  Error2,
  pickLng,
} from '@shared/utils/test';
import { Result } from '@shared/core/Result';
import { EventRetrieved } from '../../domain/EventRetrieved';
import { tomorrowPlus } from '../../utils/utils';
import { Slot } from '../../domain/Slot';

const createHandler = () => {
  const calendarRepo = new CalendarRepoFake();
  const sendAnalytics = vi.fn();
  const api = new CalendarApiFake();
  const reservationSrv = new ReservationSrv({
    api,
    sendAnalytics,
  });
  const controller = new CancelReservationEvent({
    calendarRepo,
    reservationSrv,
    sendAnalytics,
  });

  return {
    executeImpl: controller.executeImpl.bind(controller),
    api,
    reservationSrv,
  };
};

const input = {
  userId: BusinessUsersInDb[0].id,
  eventId: 'testEventId',
  identifier: ReservationOptionsInDb[0].locIdentifier,
  start: ApiResults.insertSuccess.data.start.dateTime,
  end: ApiResults.insertSuccess.data.end.dateTime,
  addToEventDescription: 'test cancellation',
  lng: pickLng(),
};
const eventRetrieved = {
  name: 'test name',
  id: 'testId',
  slot: Slot.create({
    start: tomorrowPlus(),
    end: tomorrowPlus(30),
  }).value,
};

describe('success', () => {
  it('should cancel, update to 0 reserved spots and delete event if there was only one spot reserved', async () => {
    const {
      executeImpl,
      api,
      reservationSrv,
    } = createHandler();
    api.setApiResult(ApiResults.deleteSuccess);
    const mockGetEvents = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.ok(EventRetrieved.create({
          ...eventRetrieved,
          description: `#${input.identifier}`,  // #loc1 means only one reservation for loc1, it should delete the event
        })));
      });
    vi.spyOn(reservationSrv, 'getEvent').mockImplementation(mockGetEvents);
    const spyOnDeleteEvent = vi.spyOn(reservationSrv, 'deleteEvent');
    const spyOnUpdateEvent = vi.spyOn(reservationSrv, 'updateEvent');

    const result = await executeImpl(input);
    expect(result.isSuccess).toBe(true);
    expect(spyOnUpdateEvent).toHaveBeenCalledWith({
      event: expect.objectContaining({
        props: {
          ...eventRetrieved,
          description: `${input.addToEventDescription}\n#${input.identifier}:0`,
          descriptionPlainText: `${input.addToEventDescription}\n#${input.identifier}:0`,
        },
      }),
      lng: input.lng,
    });
    expect(spyOnDeleteEvent).toHaveBeenCalledWith({
      id: input.eventId,
      lng: input.lng,
    });
  });

  it('should cancel and update event decreasing spots by 1 if there were more than one spot reserved', async () => {
    const {
      reservationSrv,
      executeImpl,
    } = createHandler();
    const mockGetEvents = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.ok(EventRetrieved.create({
          ...eventRetrieved,
          description: `#${input.identifier}:2`,  // #loc1:2 means there were 2 reservations for loc1, it should update the event
        })));
      });
    vi.spyOn(reservationSrv, 'getEvent').mockImplementation(mockGetEvents);
    const spyOnDeleteEvent = vi.spyOn(reservationSrv, 'deleteEvent');
    const spyOnUpdateEvent = vi.spyOn(reservationSrv, 'updateEvent');

    const result = await executeImpl(input);
    expect(result.isSuccess).toBe(true);
    expect(spyOnUpdateEvent).toHaveBeenCalledWith({
      event: expect.objectContaining({
        props: {
          ...eventRetrieved,
          description: `${input.addToEventDescription}\n#${input.identifier}:1`,
          descriptionPlainText: `${input.addToEventDescription}\n#${input.identifier}:1`,
        },
      }),
      lng: input.lng,
    });
    expect(spyOnDeleteEvent).not.toHaveBeenCalled();
  });
});

describe('failure when', () => {
  test('there are validation errors', async () => {
    const {
      executeImpl,
    } = createHandler();
    const result = await executeImpl({
      ...input,
      start: 'not a valid date',
      end: 'not a valid date',
      identifier: '-$a',
    });
    const { isFailure, errors } = result;
    expect(isFailure).toBe(true);
    expect(errors!.length).toBe(4);
    expect(errors!).toEqual(expect.arrayContaining([
      expect.objectContaining({  field: 'start', type: expect.stringContaining('DatErrors.') }),
      expect.objectContaining({  field: 'end', type: expect.stringContaining('DatErrors.') }),
      expect.objectContaining({  field: 'identifier', type: expect.stringContaining('IdentifierErrors.FirstCharShouldBeLetterOrNumber') }),
      expect.objectContaining({  field: 'identifier', type: expect.stringContaining('IdentifierErrors.InvalidCharacters') }),
    ]));
  });

  test(`reservation calendar isn't found`, async () => {
    const {
      executeImpl,
    } = createHandler();
    const result = await executeImpl({
      ...input,
      userId: nonExistingId,
    });
    expect(result).toMatchObject({
      isFailure: true,
      errors: [
        expect.objectContaining({
          message: expect.any(String),
          type: 'CancelReservationEventErrors.ReservationCalendarNotFoundForUserId',
          field: 'userId',
          status: 404,
        }),
      ],
    })
  });

  test(`reservationSrv.getEvent errors`, async () => {
    const {
      reservationSrv,
      executeImpl,
    } = createHandler();
    const mockGetEvents = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.fail(new Error2()));
      });
    vi.spyOn(reservationSrv, 'getEvent').mockImplementation(mockGetEvents);
    
    const result = await executeImpl(input);
    expect(result).toMatchObject({
      isFailure: true,
      errors: [
        expect.objectContaining({
          message: expect.any(String),
          type: 'CancelReservationEventErrors.WhenGettingEvent',
          status: 404,
        }),
      ],
    });
  });
  test(`reservationSrv.deleteEvent errors`, async () => {
    const {
      reservationSrv,
      executeImpl,
    } = createHandler();
    const mockGetEvents = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.ok(EventRetrieved.create({
          ...eventRetrieved,
          description: `#${input.identifier}`,  // #loc1 means only one reservation for loc1, it should delete the event
        })));
      });
    vi.spyOn(reservationSrv, 'getEvent').mockImplementation(mockGetEvents);
    const mockDeleteEvent = () =>
      new Promise<Result<void>>((resolve) => {
        resolve(Result.fail(new Error2()));
      });
    vi.spyOn(reservationSrv, 'deleteEvent').mockImplementation(mockDeleteEvent);

    const result = await executeImpl(input);
    expect(result).toMatchObject({
      isFailure: true,
      errors: [
        expect.objectContaining({
          message: expect.any(String),
          type: 'CancelReservationEventErrors.WhenTryingToDeleteEvent',
          status: 502,
        }),
      ],
    });
  });
  test(`reservationSrv.updateEvent errors`, async () => {
    const {
      reservationSrv,
      executeImpl,
    } = createHandler();
    const mockGetEvents = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.ok(EventRetrieved.create({
          ...eventRetrieved,
          description: `#${input.identifier}:2`,  // #loc1:2 means there were 2 reservations for loc1, it should update the event
        })));
      });
    vi.spyOn(reservationSrv, 'getEvent').mockImplementation(mockGetEvents);
    const mockUpdateEvent = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.fail(new Error2()));
      });
    vi.spyOn(reservationSrv, 'updateEvent').mockImplementation(mockUpdateEvent);

    const result = await executeImpl(input);
    expect(result).toMatchObject({
      isFailure: true,
      errors: [
        expect.objectContaining({
          message: expect.any(String),
          type: 'CancelReservationEventErrors.WhenTryingToUpdateEvent',
          status: 502,
        }),
      ],
    });
  });
});
