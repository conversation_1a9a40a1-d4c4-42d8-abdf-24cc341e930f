import { CreateReservationEvent } from './CreateReservationEvent';
import { CalendarsSrv } from '../../services/CalendarsSrv';
import { CalendarRepo } from '../../repos/CalendarRepo';
import { CalendarApi } from '../../services/CalendarApi';
import { ReservationSrv } from '../../services/ReservationSrv';
import { AvailabilitySrv } from '../../services/AvailabilitySrv';
import { calendar } from '@googleapis/calendar';
import { IContextProvider } from '@shared/context/IContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('./../../../../shared/infra/database/sequelize/models');  // Si uso @shared falla CreateReservationByBusiness.unit.ts

export const createHandler = (sendAnalytics: IContextProvider['sendAnalytics']) => {
  const calendarRepo = new CalendarRepo(models.Calendar);
  const reservationSrv = new ReservationSrv({
    api: new CalendarApi({
      google: calendar({ version: 'v3' }),
      sendAnalytics,
    }),
    sendAnalytics,
  });
  const availabilitySrv = new AvailabilitySrv({
    api: new CalendarApi({
      google: calendar({ version: 'v3' }),
      sendAnalytics,
    }),
    sendAnalytics,
  });
  const calendarsSrv = new CalendarsSrv({ reservationSrv, availabilitySrv, sendAnalytics });
  const controller = new CreateReservationEvent({
    calendarRepo,
    calendarsSrv,
    reservationSrv,
    sendAnalytics,
  });

  return controller.executeImpl.bind(controller);
};