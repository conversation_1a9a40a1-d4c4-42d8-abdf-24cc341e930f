import { expect, test, vi, it, describe, beforeEach } from 'vitest';
vi.stubEnv('calendar_clientEmail', 'dummy');
vi.stubEnv('calendar_clientKey', 'dummy');
import { Calendar } from '../../domain/Calendar';
import { CalendarsInDb } from '@shared/infra/database/sequelize/migrations/Calendars.json';
import { CreateReservationEvent } from './CreateReservationEvent';
import { CalendarRepoFake } from '../../repos/CalendarRepoFake';
import { CalendarsSrv } from '../../services/CalendarsSrv';
import { CalendarApiFake } from '../../services/CalendarApiFake';
import { Result } from '@shared/core/Result';
import { EventRetrieved } from '../../domain/EventRetrieved';
import { Spots } from '../../domain/Spots';
import { Slot } from '../../domain/Slot';
import { Event } from '../../domain/Event';
import {
  Error2,
  nonExistingId,
} from '@shared/utils/test';
import { ReservationOptionsInDb } from '../../utils/testExternal';
import { tomorrowPlus } from '../../utils/utils';
import { createEventRetrieved } from '../../utils/test';
import { ReservationSrv } from '../../services/ReservationSrv';
import { AvailabilitySrv } from '../../services/AvailabilitySrv';
import { getLng, PossibleLngs } from '@shared/utils/utils';
import { CreateReservationEventErrors } from './CreateReservationEventErrors';
import { Identifier } from '../../domain/Identifier';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const sendAnalytics = vi.fn();

const identifier = 'loc1';
const inputSlot = Slot.create({
  start: tomorrowPlus(),
  end: tomorrowPlus(30),
}).value;
const name = ReservationOptionsInDb[0].name;

const input = {
  userId: CalendarsInDb[0].userId,
  identifier,
  start: inputSlot.start.s,
  end: inputSlot.end.s,
  name,
  addToEventDescription: 'Add to event description',
  lng: getLng(),
  maxDaysAhead: 10,
  minTimeBeforeService: 0,
};

const reservationSrv = new ReservationSrv({
  api: new CalendarApiFake(),
  sendAnalytics,
});
const availabilitySrv = new AvailabilitySrv({
  api: new CalendarApiFake(),
  sendAnalytics,
});
const calendarsSrv = new CalendarsSrv({ availabilitySrv, reservationSrv, sendAnalytics });
const calendarRepo = new CalendarRepoFake();

let createReservationEvent: CreateReservationEvent;

beforeEach(() => {
  vi.clearAllMocks();
  createReservationEvent = new CreateReservationEvent({
    calendarRepo,
    calendarsSrv,
    reservationSrv,
    sendAnalytics,
  });
});

describe(`succeeds`, () => {
  it(`creates an event`, async () => {
    const mockGetSpotsForReservation = () =>
      new Promise<
        Result<{
          spots: Spots | null;
          existingReservation: EventRetrieved | null;
        }>
      >((resolve) => {
        resolve(
          Result.ok({
            spots: Spots.create(1, sendAnalytics).value,
            existingReservation: null,
          }),
        );
      });
    vi.spyOn(calendarsSrv, 'getSpotsForReservation').mockImplementation(
      mockGetSpotsForReservation,
    );

    const inserted = createEventRetrieved();
    const mockInsertEvent = (args: { event: Event, lng: PossibleLngs }) =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        const { event } = args;
        const eventDto = event.toDto();
        const descriptionData = [input.addToEventDescription, `#${identifier}`];
        descriptionData.forEach((data) => {
          expect(eventDto.description).toContain(data);
        });
        expect(eventDto).toMatchObject({
          name,
          slot: {
            start: inputSlot.start.s,
            end: inputSlot.end.s,
          },
        });
        resolve(Result.ok(inserted));
      });
    vi.spyOn(reservationSrv, 'insertEvent').mockImplementation(mockInsertEvent);

    const result = await createReservationEvent.executeImpl(input);

    expect(result).toMatchObject({
      isSuccess: true,
      value: {
        action: 'CREATED',
        event: inserted.toDto(),
      },
    });
  });

  it(`updates an event`, async () => {
    const existingComment = 'Existing comment';
    const existingReservation = EventRetrieved.create({
      name: 'Existing reservation',
      description: `${existingComment}\n#${identifier}:2`,
      slot: inputSlot,
      id: chance.string(),
    });

    const mockGetSpotsForReservation = () =>
      new Promise<
        Result<{
          spots: Spots | null;
          existingReservation: EventRetrieved | null;
        }>
      >((resolve) => {
        resolve(
          Result.ok({
            spots: Spots.create(3, sendAnalytics).value,
            existingReservation,
          }),
        );
      });
    vi.spyOn(calendarsSrv, 'getSpotsForReservation').mockImplementation(
      mockGetSpotsForReservation,
    );

    const updated = createEventRetrieved();
    const mockUpdateEvent = (args: { event: EventRetrieved; lng: PossibleLngs }) =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        const { event } = args;
        const eventDto = event.toDto();
        const descriptionData = [
          existingComment,
          input.addToEventDescription,
          `#${identifier}:3`,
        ];
        descriptionData.forEach((data) => {
          expect(eventDto.description).toContain(data);
        });
        expect(eventDto).toMatchObject({
          name: existingReservation.name,
          slot: {
            start: inputSlot.start.s,
            end: inputSlot.end.s,
          },
          id: existingReservation.id,
        });
        resolve(Result.ok(updated));
      });
    vi.spyOn(reservationSrv, 'updateEvent').mockImplementation(mockUpdateEvent);

    const result = await createReservationEvent.executeImpl(input);

    expect(result).toMatchObject({
      isSuccess: true,
      value: {
        action: 'UPDATED',
        event: updated.toDto(),
      },
    });
  });
});

describe(`fails`, () => {
  test.each([
    [
      'start',
      'invalid date-time string',
      'CreateReservationEventErrors.StartIsInvalid',
    ],
    [
      'end',
      'invalid date-time string',
      'CreateReservationEventErrors.EndIsInvalid',
    ],
  ])(`field %s is an %s`, async (field, invalidValue, error) => {
    const result = await createReservationEvent.executeImpl({
      ...input,
      [field]: invalidValue,
    });

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        status: 400,
        message: expect.any(String),
        type: error,
        field,
      }],
    })
  });

  test(`with an invalid identifier`, async () => {
    const result = await createReservationEvent.executeImpl({
      ...input,
      identifier: 'invalid id',
    });

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        status: 400,
        message: expect.any(String),
        type: expect.stringContaining('IdentifierErrors.'),
        field: 'identifier',
      }],
    })
  });

  test(`with invalid start-end values`, async () => {
    const result = await createReservationEvent.executeImpl({
      ...input,
      end: inputSlot.start.s,
    });

    expect(result).toMatchObject({
      isFailure: true,
      errors: [{
        status: 400,
        message: expect.any(String),
        type: expect.stringContaining('SlotErrors.'),
        field: 'start',
      },{
        status: 400,
        message: expect.any(String),
        type: expect.stringContaining('SlotErrors.'),
        field: 'end',
      }],
    })
  });

  test(`for a user without calendars`, async () => {
    const result = await createReservationEvent.executeImpl({
      ...input,
      userId: nonExistingId,
    });

    expect(result).toMatchObject({
      isFailure: true,
      errors: [
        new CreateReservationEventErrors.AvailabilityCalendarNotFoundForUserId(nonExistingId).setField('userId'),
        new CreateReservationEventErrors.ReservationCalendarNotFoundForUserId(nonExistingId).setField('userId'),
      ],
    });
  });

  test(`when user isn't found in reservations repo`, async () => {
    const calendarRepo = new CalendarRepoFake();

    const mockGetReservations = (): Promise<Calendar | null> => {
      return new Promise((resolve) => {
        resolve(null);
      });
    };
    vi.spyOn(calendarRepo, 'getReservations').mockImplementation(
      mockGetReservations,
    );

    const createReservationEvent = new CreateReservationEvent({
      calendarRepo,
      calendarsSrv,
      reservationSrv,
      sendAnalytics,
    });

    const result = await createReservationEvent.executeImpl(input);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [
        new CreateReservationEventErrors.ReservationCalendarNotFoundForUserId(input.userId).setField('userId'),
      ],
    });
  });

  test(`when no spots are found`, async () => {
    const calendarsSrv = new CalendarsSrv({ availabilitySrv, reservationSrv, sendAnalytics });
    const mockGetSpotsForReservation = () =>
      new Promise<
        Result<{
          spots: Spots | null;
          existingReservation: EventRetrieved | null;
        }>
      >((resolve) => {
        resolve(
          Result.ok({
            spots: null,
            existingReservation: null,
          }),
        );
      });
    vi.spyOn(calendarsSrv, 'getSpotsForReservation').mockImplementation(
      mockGetSpotsForReservation,
    );

    const createReservationEvent = new CreateReservationEvent({
      calendarRepo,
      calendarsSrv,
      reservationSrv,
      sendAnalytics,
    });

    const result = await createReservationEvent.executeImpl(input);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [
        new CreateReservationEventErrors.NoSpotsFound({
          identifier: Identifier.create({ value: identifier}, sendAnalytics).value,
          start: inputSlot.start.s,
          end: inputSlot.end.s,
        }),
      ],
    });
    /*expectControllerError<Response>({
      code: 404,
      error: 'CreateReservationEventErrors.NoSpotsFound',
      response,
    });*/
  });

  const error2 = new Error2();

  test(`when calendarsSrv.updateReservation fails `, async () => {
    const calendarsSrv = new CalendarsSrv({ availabilitySrv, reservationSrv, sendAnalytics });

    const mockGetSpotsForReservation = () =>
      new Promise<
        Result<{
          spots: Spots | null;
          existingReservation: EventRetrieved | null;
        }>
      >((resolve) => {
        const existingReservation = EventRetrieved.create({
          name: 'Fully booked reservation',
          description: `#${identifier}:2`,
          slot: inputSlot,
          id: chance.string(),
        });
        resolve(
          Result.ok({
            spots: Spots.create(2, sendAnalytics).value,
            existingReservation: existingReservation,
          }),
        );
      });
    vi.spyOn(calendarsSrv, 'getSpotsForReservation').mockImplementation(
      mockGetSpotsForReservation,
    );
    const mockUpdateEvent = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.fail(error2));
      });
    vi.spyOn(reservationSrv, 'updateEvent').mockImplementation(mockUpdateEvent);

    const createReservationEvent = new CreateReservationEvent({
      calendarRepo,
      calendarsSrv,
      reservationSrv,
      sendAnalytics,
    });

    const result = await createReservationEvent.executeImpl(input);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [error2],
    });
  });

  test(`when calendarsSrv.insertEvent fails`, async () => {
    const mockGetSpotsForReservation = () =>
      new Promise<
        Result<{
          spots: Spots | null;
          existingReservation: EventRetrieved | null;
        }>
      >((resolve) => {
        resolve(
          Result.ok({
            spots: Spots.create(1, sendAnalytics).value,
            existingReservation: null,
          }),
        );
      });
    vi.spyOn(calendarsSrv, 'getSpotsForReservation').mockImplementation(
      mockGetSpotsForReservation,
    );

    const mockInsertEvent = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.fail(error2));
      });
    vi.spyOn(reservationSrv, 'insertEvent').mockImplementation(mockInsertEvent);

    const createReservationEvent = new CreateReservationEvent({
      calendarRepo,
      calendarsSrv,
      reservationSrv,
      sendAnalytics,
    });

    const result = await createReservationEvent.executeImpl(input);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [error2],
    })
  });
});
