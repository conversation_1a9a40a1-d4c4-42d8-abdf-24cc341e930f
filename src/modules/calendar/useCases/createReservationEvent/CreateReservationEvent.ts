import { Request, Response } from './CreateReservationEventDTOs';
import { ICalendarRepo } from '../../repos/ICalendarRepo';
import { BaseError } from '@shared/core/AppError';
import { CreateReservationEventErrors } from './CreateReservationEventErrors';
import { Identifier } from '../../domain/Identifier';
import { ICalendarsSrv, IReservationSrv } from '../../services/ICalendarsSrv';
import { Slot } from '../../domain/Slot';
import { Event } from '../../domain/Event';
import { EventRetrieved } from '../../domain/EventRetrieved';
import { FunctionController2 } from '@shared/infra/Controllers';
import { Dat } from '@shared/core/Dat';
import { getLng, PossibleLngs } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { Result } from '@shared/core/Result';
import { MaxDaysAhead, ReservationLimits } from '../../external';
import { N0 } from '@shared/core/N0';
import { IContextProvider } from '@shared/context/IContextProvider';

export enum Action {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
}

export class CreateReservationEvent extends FunctionController2<Request, Response> {
  private readonly calendarRepo: ICalendarRepo;
  private readonly calendarsSrv: ICalendarsSrv;
  private readonly reservationSrv: IReservationSrv;
  private readonly sendAnalytics: IContextProvider['sendAnalytics'];

  public constructor(args: {
    calendarRepo: ICalendarRepo;
    calendarsSrv: ICalendarsSrv;
    reservationSrv: IReservationSrv;
    sendAnalytics: IContextProvider['sendAnalytics'];
  }) {
    super();
    const { calendarRepo, calendarsSrv, reservationSrv, sendAnalytics } = args;
    this.calendarRepo = calendarRepo;
    this.calendarsSrv = calendarsSrv;
    this.reservationSrv = reservationSrv;
    this.sendAnalytics = sendAnalytics;
  }

  public async executeImpl(dto: Request) {
    const {
      userId,
      identifier: _identifier,
      name,
      start: _start,
      end: _end,
      addToEventDescription,
      lng: _lng,
      maxDaysAhead,
      minTimeBeforeService,
    } = dto;
    const lng = getLng(_lng);

    const errors: BaseError[] = [];

    const identifierOrErrors = Identifier.create({ value: _identifier, lng: getLng() }, this.sendAnalytics);
    if (identifierOrErrors.isFailure)
      for (const error of identifierOrErrors.errors!)
        errors.push(error.setField('identifier'));

    let start: Dat;
    const startOrError = Dat.create({ value: _start, lng });
    if (startOrError.isFailure)
      errors.push(
        new CreateReservationEventErrors.StartIsInvalid(_start).setField('start'),
      );
    else
      start = startOrError.value;

    let end: Dat;
    const endOrError = Dat.create({ value: _end, lng });
    if (endOrError.isFailure)
      errors.push(new CreateReservationEventErrors.EndIsInvalid(_end).setField('end'));
    else
      end = endOrError.value;

    let slotOrError: Result<Slot>;
    // @ts-expect-error start and end may be undefined
    if (start && end) {
      slotOrError = Slot.create({ start, end });
      if (slotOrError.isFailure) {
        const error = slotOrError.error!.setField('start');
        errors.push(error, error.clone('end'));
      }
    }

    if (errors.length)
      return Result2.fail(errors);

    const identifier = identifierOrErrors.value;
    const slot = slotOrError!.value;

    const [availabilityCalendar, reservationCalendar] = await Promise.all([
      this.calendarRepo.getAvailability(userId),
      this.calendarRepo.getReservations(userId),
    ]);

    if (!availabilityCalendar)
      errors.push(
        new CreateReservationEventErrors.AvailabilityCalendarNotFoundForUserId(
          userId,
        ).setField('userId')
      );
    if (!reservationCalendar)
      errors.push(
        new CreateReservationEventErrors.ReservationCalendarNotFoundForUserId(
          userId,
        ).setField('userId')
      );

    if (errors.length)
      return Result2.fail(errors);

    this.calendarsSrv.setCalendarIds({
      availabilityCalendarId: availabilityCalendar!.id.toString(),
      reservationCalendarId: reservationCalendar!.id.toString(),
    });

    // This should never fail since it comes from CreateReservation@reservation and CreateReservationByBusiness@reservation, where it is read a proper GeneralSettings.reservationLimits
    const reservationLimits = ReservationLimits.create({
      maxDaysAhead: MaxDaysAhead.create({ value: maxDaysAhead }).value,
      minTimeBeforeService: N0.create({ value: minTimeBeforeService }).value,
    }).value;

    const spotsOrError = await this.calendarsSrv.getSpotsForReservation({
      identifier,
      slot,
      reservationLimits,
      lng,
    });
    if (spotsOrError.isFailure)
      return Result2.fail([spotsOrError.error!]);

    const { spots, existingReservation } = spotsOrError.value;
    if (!spots)
      return Result2.fail([
        new CreateReservationEventErrors.NoSpotsFound({
          identifier,
          start: start!.s,
          end: end!.s,
        }),
      ]);

    this.reservationSrv.setCalendarId(reservationCalendar!.id.toString());
    return existingReservation
      ? this.handleExistingReservation({
          existingReservation,
          addToEventDescription,
          identifier,
          lng,
        })
      : this.handleNewReservation({
          name,
          addToEventDescription,
          slot,
          identifier,
          lng,
        });
  }

  private async handleExistingReservation(args: {
    existingReservation: EventRetrieved;
    addToEventDescription: string;
    identifier: Identifier;
    lng: PossibleLngs;
  }): Promise<Result2<Response>> {
    const { existingReservation, addToEventDescription, identifier, lng } = args;

    const descriptionPlainTextUpdated = `${addToEventDescription}\n${identifier.increaseSpots(
      existingReservation.descriptionPlainText!,
    )}`;
    const updatedOrError = await this.reservationSrv.updateEvent({
      event: existingReservation.updateDescription(descriptionPlainTextUpdated),
      lng,
    });

    if (updatedOrError.isFailure) {
      return Result2.fail([updatedOrError.error!]);
    }

    return Result2.ok({
      action: Action.UPDATED,
      event: updatedOrError.value.toDto(),
    });
  }

  private async handleNewReservation(args: {
    name: string;
    addToEventDescription: string;
    slot: Slot;
    identifier: Identifier;
    lng: PossibleLngs;
  }): Promise<Result2<Response>> {
    const { name, addToEventDescription, slot, identifier, lng } = args;
    const createdOrError = await this.reservationSrv.insertEvent({
      event: Event.create({
        name,
        description: `${addToEventDescription}\n${identifier.query}`,
        slot,
      }),
      lng,
    });

    if (createdOrError.isFailure)
      return Result2.fail([createdOrError.error!]);

    return Result2.ok({
      action: Action.CREATED,
      event: createdOrError.value.toDto(),
    });
  }
}
