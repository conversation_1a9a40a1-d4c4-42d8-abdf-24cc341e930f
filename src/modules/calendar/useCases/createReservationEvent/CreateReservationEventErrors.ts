import { patch } from '@shared/core/utils';
import { BadRequest, BaseError } from '@shared/core/AppError';
import { Status } from '@shared/core/Status';
import { Identifier } from '../../domain/Identifier';

export namespace CreateReservationEventErrors {
  export class StartIsInvalid extends BadRequest {
    public constructor(start: string) {
      super({ message: `Start field is invalid: ${start}` });
    }
  }
  export class EndIsInvalid extends BadRequest {
    public constructor(end: string) {
      super({ message: `End field is invalid: ${end}` });
    }
  }
  export class AvailabilityCalendarNotFoundForUserId extends BaseError {
    public constructor(userId: string) {
      super({
        message: `Availability calendar for user id ${userId} not found`,
        status: Status.NOT_FOUND,
      });
    }
  }
  export class ReservationCalendarNotFoundForUserId extends BaseError {
    public constructor(userId: string) {
      super({
        message: `Reservation calendar for user id ${userId} not found`,
        status: Status.NOT_FOUND,
      });
    }
  }
  export class NoSpotsFound extends BaseError {
    public constructor(args: {
      identifier: Identifier;
      start: string;
      end: string;
    }) {
      const { identifier, start, end } = args;
      super({
        message: `No spots found for a reservation with a start at ${start} and end at ${end}, in location ${identifier.query}`,
        status: Status.NOT_FOUND,
      });
    }
  }
}

patch({ CreateReservationEventErrors });
