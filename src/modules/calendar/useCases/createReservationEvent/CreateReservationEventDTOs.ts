import { Action } from './CreateReservationEvent';
import { EventRetrievedDto } from '../../domain/EventRetrieved';
import { ReservationLimitsDto } from '../../external';

export type Request = {
  userId: string;
  identifier: string;
  start: string;
  end: string;
  name: string;
  addToEventDescription: string;
  lng: string;
  maxDaysAhead: ReservationLimitsDto['maxDaysAhead'];
  minTimeBeforeService: ReservationLimitsDto['minTimeBeforeService'];
};

export type Response = {
  action: Action;
  event: EventRetrievedDto;
};
