import { CheckMakeChangesAndSave } from './CheckMakeChangesAndSave';
import { ReturnUnexpectedError } from '@shared/decorators/ReturnUnexpectedError';
import { CalendarRepo } from '../../repos/CalendarRepo';
import { ReservationSrv } from '../../services/ReservationSrv';
import { AvailabilitySrv } from '../../services/AvailabilitySrv';
import { CalendarApi } from '../../services/CalendarApi';
import { calendar } from '@googleapis/calendar';
import { GuardUuid } from '@shared/decorators/GuardUuid';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });
const sendAnalytics = contextProvider.sendAnalytics.bind(contextProvider);

const calendarRepo = new CalendarRepo(models.Calendar);
const reservationSrv = new ReservationSrv({
  api: new CalendarApi({
    google: calendar({ version: 'v3' }),
    sendAnalytics,
  }),
  sendAnalytics,
});
const availabilitySrv = new AvailabilitySrv({
  api: new CalendarApi({
    google: calendar({ version: 'v3' }),
    sendAnalytics,
  }),
  sendAnalytics,
});
const controller = new CheckMakeChangesAndSave({
  calendarRepo,
  reservationSrv,
  availabilitySrv,
  contextProvider,
});

const decorated1 = new GuardUuid({ controller, uuids: ['userId'] });
const decorated2 = new ReturnUnexpectedError({
  wrapee: decorated1,
  contextProvider: new ContextProvider({ invoker }),
});
export const handler = decorated2.execute.bind(decorated2);
