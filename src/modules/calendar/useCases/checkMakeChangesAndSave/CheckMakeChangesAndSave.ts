// This file is almost identical to CheckGetChangesAndSave.ts
import { CurrentCalendar, Request, Response } from './CheckMakeChangesAndSaveDTOs';
import { Status } from '@shared/core/Status';
import { ControllerResult } from '@shared/core/ControllerResult';
import { ICalendarRepo } from '../../repos/ICalendarRepo';
import { Calendar, CalendarType } from '../../domain/Calendar';
import { getLng, OptionalLng } from '@shared/utils/utils';
import { AppSyncController } from '@shared/infra/Controllers';
import { IReservationSrv, IAvailabilitySrv } from '../../services/ICalendarsSrv';
import { IContextProvider } from '@shared/context/IContextProvider';
import { CheckMakeChangesAndSaveErrors } from './CheckMakeChangesAndSaveErrors';
import { formatErrors } from '@shared/core/AppError';

type CheckMakeChangesAndSaveProps = {
  calendarRepo: ICalendarRepo;
  reservationSrv: IReservationSrv;
  availabilitySrv: IAvailabilitySrv;
  contextProvider: IContextProvider;
};

export class CheckMakeChangesAndSave extends AppSyncController<Request, Response> {
  private readonly calendarRepo: ICalendarRepo;
  private readonly reservationSrv: IReservationSrv;
  private readonly availabilitySrv: IAvailabilitySrv;

  public constructor({
    calendarRepo,
    reservationSrv,
    availabilitySrv,
    contextProvider,
  }: CheckMakeChangesAndSaveProps) {
    super({ contextProvider });
    this.calendarRepo = calendarRepo;
    this.reservationSrv = reservationSrv;
    this.availabilitySrv = availabilitySrv;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { userId, calendarId, lng: _lng, type: _type } = dto;
    const lng = getLng(_lng);
    const t = trans[lng];

    // Validate calendar type
    if (!Object.values(CalendarType).includes(_type as CalendarType)) {
      return formatErrors([new CheckMakeChangesAndSaveErrors.InvalidType({ type: _type, lng })]);
    }
    const type = _type as CalendarType;

    let action = Action.NO_CHANGES;

    let currentCalendar: Response['currentCalendar'] = null;
    const got = type === CalendarType.RESERVATIONS
      ? await this.calendarRepo.getReservations(userId)
      : await this.calendarRepo.getAvailability(userId);
    if (got) {
      const { id, name } = got.toDto();
      currentCalendar = {
        id,
        name,
        nextEventsSearched: false,
        nextEvents: null,
      };
    }

    const taken = await this.calendarRepo.get(calendarId);
    const noChangesMsg = getNoChangesMsg({ currentCalendar, lng });
    let reCheckingRegisteredCalendar = false;
    if (taken) {
      if (taken.userId !== userId) {
        return {
          status: Status.OK,
          result: {
            action,
            currentCalendar,
            messageError: `${t.calendarId} "${calendarId}" ${t.registeredByAnother}. ${noChangesMsg}`,
            checkError: null,
          },
        };
      } else if (taken.type !== type) {
        // Calendar is already registered with a different type
        const conflictMessage = type === CalendarType.RESERVATIONS
          ? `${t.calendarId} ${calendarId} ${t.alreadyAsYourAvailability}. ${noChangesMsg}`
          : `${t.calendarId} ${calendarId} ${t.alreadyAsYourReservation}. ${noChangesMsg}`;
        return {
          status: Status.OK,
          result: {
            action,
            currentCalendar,
            messageError: conflictMessage,
            checkError: null,
          },
        };
      }
      // Calendar taken, the current user is the owner, the type in the request is the same type as the one already in repo
      else reCheckingRegisteredCalendar = true;
    }

    // Use the appropriate service based on calendar type
    const calendarSrv = type === CalendarType.RESERVATIONS ? this.reservationSrv : this.availabilitySrv;
    calendarSrv.setCalendarId(calendarId);

    const checkOrError = await calendarSrv.checkMakeChanges(lng);
    if (checkOrError.isFailure) {
      if (reCheckingRegisteredCalendar) {
        await this.calendarRepo.delete({
          userId,
          type,
        });
        return {
          status: Status.OK,
          result: {
            action: Action.DISCONNECTED,
            currentCalendar: null,
            messageError: `${t.errorForAlreadyRegistered} "${calendarId}", ${t.removed}.`,
            checkError: checkOrError.error!.toDto(),
          },
        };
      } else
        return {
          status: Status.OK,
          result: {
            action: Action.NO_CHANGES,
            currentCalendar,
            messageError: `${t.errorCheckingCalendar} "${calendarId}". ${noChangesMsg}`,
            checkError: checkOrError.error!.toDto(),
          },
        };
    }
    const check = checkOrError.value;

    if (!reCheckingRegisteredCalendar) {
      const deletedCount = await this.calendarRepo.delete({
        userId,
        type,
      });
      action = deletedCount ? Action.UPDATED : Action.CREATED;

      const newCalendar = Calendar.create(
        {
          userId,
          name: check.name,
          type,
        },
        calendarId,
      );
      await this.calendarRepo.create(newCalendar);
    } else {
      if (check.name !== currentCalendar!.name) {
        const updatedCalendar = Calendar.create(
          {
            userId,
            name: check.name,
            type,
          },
          calendarId,
        );
        await this.calendarRepo.update(updatedCalendar);
        action = Action.UPDATED;
      }
    }

    return {
      status: action === Action.CREATED ? Status.CREATED : Status.OK,
      result: {
        action,
        currentCalendar: {
          id: calendarId,
          name: check.name,
          nextEventsSearched: true,
          nextEvents: check.nextEvents
            ? check.nextEvents.map((ne) => ne.toDto())
            : null,
        },
        messageError: null,
        checkError: null,
      },
    };
  }
}

const trans = {
  en: {
    alreadyAsYourAvailability:
      'already registered as your availability calendar, disconnect it first and try again',
    alreadyAsYourReservation:
      'already registered as your reservation calendar, disconnect it first and try again',
    calendarId: 'Calendar id',
    registeredByAnother: 'already registered by another user',
    errorForAlreadyRegistered: 'Error checking registered calendar',
    removed:
      'this has been disconnected/removed from your settings. Please, configure a calendar properly and try again',
    errorCheckingCalendar: 'Error checking calendar id',
  },
  es: {
    alreadyAsYourAvailability:
      'ya registrado como tu calendario de disponibilidad, desconéctalo primero e intenta nuevamente',
    alreadyAsYourReservation:
      'ya registrado como tu calendario de reservas, desconéctalo primero e intenta nuevamente',
    calendarId: 'Calendario con id',
    registeredByAnother: 'ya registrado por otro usuario',
    errorForAlreadyRegistered: 'Error al verificar calendario registrado',
    removed:
      'este ha sido desconectado/eliminado de tus configuraciones. Por favor, configura un calendario adecuadamente e intenta nuevamente',
    errorCheckingCalendar: 'Error al verificar calendario con id',
  },
};

export enum Action {
  NO_CHANGES = 'NO_CHANGES',
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  DISCONNECTED = 'DISCONNECTED',
}

const getNoChangesMsg = (args: { currentCalendar: CurrentCalendar | null } & OptionalLng) => {
  const { currentCalendar, lng } = args;

  const trans = {
    en: {
      noChanges: 'No changes were done',
      keepsBeing: 'your current calendar keeps being ',
      stillNoCalendar: 'you still have no calendar configured',
    },
    es: {
      noChanges: 'No se hicieron cambios',
      keepsBeing: 'tu calendario actual sigue siendo ',
      stillNoCalendar: 'aún no tienes un calendario configurado',
    },
  };

  const t = trans[getLng(lng)];

  return `${t.noChanges}, ${
    currentCalendar
      ? t.keepsBeing + currentCalendar.id + ' (' + currentCalendar.name + ')'
      : t.stillNoCalendar
  }.`;
};
// endregion

