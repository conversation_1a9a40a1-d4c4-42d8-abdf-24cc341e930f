// This file is almost identical to CheckGetChangesAndSave.unit.ts
import { expect, test, vi, it, describe, beforeEach, MockInstance } from 'vitest';

process.env.calendar_clientEmail = 'dummy';
process.env.calendar_clientKey = 'dummy';
import { CheckMakeChangesAndSave } from './CheckMakeChangesAndSave';
import { CalendarRepoFake } from '../../repos/CalendarRepoFake';
import { CalendarApiFake } from '../../services/CalendarApiFake';
import { ReservationSrv } from '../../services/ReservationSrv';
import { AvailabilitySrv } from '../../services/AvailabilitySrv';
import { Result } from '@shared/core/Result';
import {
  Error1,
  nonExistingId,
  pickLng,
  invokerDummy as invoker,
} from '@shared/utils/test';
import {
  CalendarsInDb,
} from '@shared/infra/database/sequelize/migrations/Calendars.json';
import { createNextEvents } from '../../utils/test';
import { NextEvent } from '../../domain/NextEvent';
import { Calendar, CalendarType } from '../../domain/Calendar';
import { ContextProvider } from '@shared/context/ContextProvider';
import { IContextProvider } from '@shared/context/IContextProvider';

// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const lng = pickLng();

const name = CalendarsInDb[1].name;
let nextEvents: [NextEvent, ...NextEvent[]] | null;

let calendarRepo: CalendarRepoFake,
  checkMakeChangesAndSave: CheckMakeChangesAndSave,
  contextProvider: ContextProvider,
  sendAnalytics: IContextProvider['sendAnalytics'],
  reservationSrv: ReservationSrv,
  availabilitySrv: AvailabilitySrv,
  spyOnGetReservations: MockInstance<(userId: string) => Promise<Calendar | null>>,
  spyOnGetAvailability: MockInstance<(userId: string) => Promise<Calendar | null>>;
beforeEach(() => {
  nextEvents = createNextEvents();
  calendarRepo = new CalendarRepoFake();
  spyOnGetReservations = vi.spyOn(calendarRepo, 'getReservations');
  spyOnGetAvailability = vi.spyOn(calendarRepo, 'getAvailability');

  contextProvider = new ContextProvider({ invoker });
  sendAnalytics = contextProvider.sendAnalytics.bind(contextProvider);

  reservationSrv = new ReservationSrv({
    api:new CalendarApiFake(),
    sendAnalytics,
  });
  availabilitySrv = new AvailabilitySrv({
    api:new CalendarApiFake(),
    sendAnalytics,
  });
  const mockCheckMakeChanges = () =>
    new Promise<
      Result<{ name: string; nextEvents: [NextEvent, ...NextEvent[]] | null }> // Taken from CheckResult @ ICalendarsSrv.ts
    >((resolve) => {
      resolve(Result.ok({ name, nextEvents }));
    });
  vi.spyOn(reservationSrv, 'checkMakeChanges').mockImplementation(
    mockCheckMakeChanges,
  );
  vi.spyOn(availabilitySrv, 'checkMakeChanges').mockImplementation(
    mockCheckMakeChanges,
  );

  checkMakeChangesAndSave = new CheckMakeChangesAndSave({
    calendarRepo,
    reservationSrv,
    availabilitySrv,
    contextProvider,
  });
});

describe(`Verify calendar id already registered by another user & the user`, () => {
  test(`has a calendar configured`, async () => {
    const response = await checkMakeChangesAndSave.executeImpl({
      userId: CalendarsInDb[1].userId,
      calendarId: CalendarsInDb[3].id,
      lng,
      type: CalendarType.RESERVATIONS,
    });

    expect(response).toMatchObject({
      status: 200,
      result: {
        action: 'NO_CHANGES',
        currentCalendar: {
          id: CalendarsInDb[1].id,
          name: CalendarsInDb[1].name,
          nextEventsSearched: false,
          nextEvents: null,
        },
        messageError: expect.any(String),
        checkError: null,
      },
    });
  });
  test(`doesn't have a calendar configured yet`, async () => {
    const response = await checkMakeChangesAndSave.executeImpl({
      userId: nonExistingId,
      calendarId: CalendarsInDb[3].id,
      lng,
      type: CalendarType.RESERVATIONS,
    });

    expect(response).toMatchObject({
      status: 200,
      result: {
        action: 'NO_CHANGES',
        currentCalendar: null,
        messageError: expect.any(String),
        checkError: null,
      },
    });
  });
});

test(`Verify calendar id already registered as availability calendar`, async () => {
  const response = await checkMakeChangesAndSave.executeImpl({
    // CalendarsInDb[0].type = 'A'
    userId: CalendarsInDb[1].userId,
    calendarId: CalendarsInDb[0].id,
    lng,
    type: CalendarType.RESERVATIONS,
  });

  expect(response).toMatchObject({
    status: 200,
    result: {
      action: 'NO_CHANGES',
      currentCalendar: {
        id: CalendarsInDb[1].id,
        name: CalendarsInDb[1].name,
        nextEventsSearched: false,
        nextEvents: null,
      },
      messageError: expect.any(String),
      checkError: null,
    },
  });
});

describe(`Relays error if reservationSrv.checkMakeChanges() errors &`, () => {
  const error1 = new Error1();
  it(`disconnects/removes calendar if it was a recheck`, async () => {
    const reservationSrv = new ReservationSrv({
      api:new CalendarApiFake(),
      sendAnalytics,
    });
    const mockCheckMakeChanges = () =>
      new Promise<
        Result<{ name: string; nextEvents: [NextEvent, ...NextEvent[]] | null }>
      >((resolve) => {
        resolve(Result.fail(error1));
      });
    vi.spyOn(reservationSrv, 'checkMakeChanges').mockImplementation(
      mockCheckMakeChanges,
    );
    const checkMakeChangesAndSave = new CheckMakeChangesAndSave({
      calendarRepo,
      reservationSrv,
      availabilitySrv,
      contextProvider,
    });

    const response = await checkMakeChangesAndSave.executeImpl({
      userId: CalendarsInDb[1].userId,
      calendarId: CalendarsInDb[1].id,
      lng: 'en',
      type: CalendarType.RESERVATIONS,
    });

    expect(response).toMatchObject({
      status: 200,
      result: {
        action: 'DISCONNECTED',
        currentCalendar: null,
        messageError: expect.stringMatching(
          new RegExp(`${CalendarsInDb[1].id}[\\S\\s]*disconnected`, 'i'),
        ),
        checkError: {
          message: error1.message,
          type: error1.type,
          status: error1.status,
        },
      },
    });
    const got = await calendarRepo.getReservations(CalendarsInDb[1].userId);
    expect(got).toBe(null);
  });

  it(`doesn't make changes if it wasn't a recheck`, async () => {
    const reservationSrv = new ReservationSrv({
      api:new CalendarApiFake(),
      sendAnalytics,
    });
    const mockCheckMakeChanges = () =>
      new Promise<
        Result<{ name: string; nextEvents: [NextEvent, ...NextEvent[]] | null }>
      >((resolve) => {
        resolve(Result.fail(error1));
      });
    vi.spyOn(reservationSrv, 'checkMakeChanges').mockImplementation(
      mockCheckMakeChanges,
    );
    const checkMakeChangesAndSave = new CheckMakeChangesAndSave({
      calendarRepo,
      reservationSrv,
      availabilitySrv,
      contextProvider,
    });

    const response = await checkMakeChangesAndSave.executeImpl({
      userId: CalendarsInDb[1].userId,
      calendarId: nonExistingId,
      lng: 'en',
      type: CalendarType.RESERVATIONS,
    });

    expect(response).toMatchObject({
      status: 200,
      result: {
        action: 'NO_CHANGES',
        currentCalendar: {
          id: CalendarsInDb[1].id,
          name: CalendarsInDb[1].name,
          nextEventsSearched: false,
          nextEvents: null,
        },
        messageError: expect.stringMatching(
          new RegExp(
            `${nonExistingId}[\\S\\s]*no changes[\\S\\s]*${CalendarsInDb[1].id}[\\S\\s]*${CalendarsInDb[1].name}`,
            'i',
          ),
        ),
        checkError: {
          message: error1.message,
          type: error1.type,
          status: error1.status,
        },
      },
    });
    const got = await calendarRepo.getReservations(CalendarsInDb[1].userId);
    expect(got).toBeDefined();
  });
});

describe(`Success`, () => {
  it('creates a new calendar for non-existent user and reservationCalendarId', async () => {
    calendarRepo = new CalendarRepoFake();
    const spyCreate = vi.spyOn(calendarRepo, 'create');
    checkMakeChangesAndSave = new CheckMakeChangesAndSave({
      calendarRepo,
      reservationSrv,
      availabilitySrv,
      contextProvider,
    });

    const spyOnGetReservations = vi.spyOn(calendarRepo, 'getReservations');
    const spyOnGetAvailability = vi.spyOn(calendarRepo, 'getAvailability');

    const userId = chance.guid();
    const calendarId = chance.guid();
    const result = await checkMakeChangesAndSave.executeImpl({
      userId,
      calendarId,
      lng,
      type: CalendarType.RESERVATIONS,
    });

    expect(result).toMatchObject({
      status: 201,
      result: {
        action: 'CREATED',
        currentCalendar: {
          id: calendarId,
          name,
          nextEventsSearched: true,
          nextEvents: nextEvents?.map((ne) => ne.toDto()),
        },
        messageError: null,
        checkError: null,
      },
    });
    expect(spyCreate).toHaveBeenCalledOnce();
    expect(spyOnGetReservations).toHaveBeenCalledOnce();
    expect(spyOnGetAvailability).not.toHaveBeenCalled();
  });

  it('updates the calendar for an existent user', async () => {
    calendarRepo = new CalendarRepoFake();
    const spyCreate = vi.spyOn(calendarRepo, 'create');
    checkMakeChangesAndSave = new CheckMakeChangesAndSave({
      calendarRepo,
      reservationSrv,
      availabilitySrv,
      contextProvider,
    });

    const calendarId = chance.guid();
    const result = await checkMakeChangesAndSave.executeImpl({
      userId: CalendarsInDb[1].userId,
      calendarId,
      lng,
      type: CalendarType.RESERVATIONS,
    });

    expect(result).toMatchObject({
      status: 200,
      result: {
        action: 'UPDATED',
        currentCalendar: {
          id: calendarId,
          name,
          nextEventsSearched: true,
          nextEvents: nextEvents?.map((ne) => ne.toDto()),
        },
        messageError: null,
        checkError: null,
      },
    });
    expect(spyCreate).toHaveBeenCalledOnce();
  });

  it(`doesn't make changes when its' a recheck`, async () => {
    calendarRepo = new CalendarRepoFake();
    const spyCreate = vi.spyOn(calendarRepo, 'create');
    checkMakeChangesAndSave = new CheckMakeChangesAndSave({
      calendarRepo,
      reservationSrv,
      availabilitySrv,
      contextProvider,
    });

    const result = await checkMakeChangesAndSave.executeImpl({
      userId: CalendarsInDb[1].userId,
      calendarId: CalendarsInDb[1].id,
      lng,
      type: CalendarType.RESERVATIONS,
    });

    expect(result).toMatchObject({
      status: 200,
      result: {
        action: 'NO_CHANGES',
        currentCalendar: {
          id: CalendarsInDb[1].id,
          name,
          nextEventsSearched: true,
          nextEvents: nextEvents?.map((ne) => ne.toDto()),
        },
        messageError: null,
        checkError: null,
      },
    });
    expect(spyCreate).not.toHaveBeenCalled();
  });

  it(`updates the calendar name when it's different from DB when doing a recheck`, async () => {
    const reservationSrv = new ReservationSrv({
      api:new CalendarApiFake(),
      sendAnalytics,
    });
    const mockCheckMakeChanges = () =>
      new Promise<
        Result<{ name: string; nextEvents: [NextEvent, ...NextEvent[]] | null }> // Taken from CheckResult @ ICalendarsSrv.ts
      >((resolve) => {
        resolve(Result.ok({ name: 'different from DB', nextEvents }));
      });
    vi.spyOn(reservationSrv, 'checkMakeChanges').mockImplementation(
      mockCheckMakeChanges,
    );

    calendarRepo = new CalendarRepoFake();
    const spyUpdate = vi.spyOn(calendarRepo, 'update');
    const checkMakeChangesAndSave = new CheckMakeChangesAndSave({
      calendarRepo,
      reservationSrv,
      availabilitySrv,
      contextProvider,
    });

    const result = await checkMakeChangesAndSave.executeImpl({
      userId: CalendarsInDb[1].userId,
      calendarId: CalendarsInDb[1].id,
      lng,
      type: CalendarType.RESERVATIONS,
    });

    expect(result).toMatchObject({
      status: 200,
      result: {
        action: 'UPDATED',
        currentCalendar: {
          id: CalendarsInDb[1].id,
          name: 'different from DB',
          nextEventsSearched: true,
          nextEvents: nextEvents?.map((ne) => ne.toDto()),
        },
        messageError: null,
        checkError: null,
      },
    });
    expect(spyUpdate).toHaveBeenCalledWith(
      Calendar.create(
        {
          userId: CalendarsInDb[1].userId,
          name: 'different from DB',
          type: CalendarType.RESERVATIONS,
        },
        CalendarsInDb[1].id,
      ),
    );
  });
});

describe('Type validation', () => {
  it('should return error for invalid calendar type', async () => {
    const result = await checkMakeChangesAndSave.executeImpl({
      userId: CalendarsInDb[1].userId,
      calendarId: CalendarsInDb[1].id,
      lng: 'en',
      type: 'INVALID_TYPE',
    });

    expect(result).toMatchObject({
      status: 400,
      result: [
        {
          type: 'CheckMakeChangesAndSaveErrors.InvalidType',
          message: expect.stringContaining('Invalid'),
          status: 400,
        },
      ],
    });
  });

  it('should work with AVAILABILITY type', async () => {
    const result = await checkMakeChangesAndSave.executeImpl({
      userId: chance.guid(),
      calendarId: chance.guid(),
      lng,
      type: CalendarType.AVAILABILITY,
    });

    expect(result.status).toBe(201);
    expect(result.result).toMatchObject({
      action: 'CREATED',
      messageError: null,
      checkError: null,
    });
    expect(spyOnGetAvailability).toHaveBeenCalledOnce();
    expect(spyOnGetReservations).not.toHaveBeenCalled();
  });
});
