// This file is almost identical to CheckGetChangesAndSave.e2e.ts
import { beforeAll, vi, test, expect } from 'vitest';
import { getCalendarCreds } from '@shared/infra/iac/helpers';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { createSlot, rmCalendar } from '../../utils/test';
import { CalendarApi } from '../../services/CalendarApi';
import { Event } from '../../domain/Event';
import { tomorrowPlus } from '../../utils/utils';
import {
  dateFormat,
  nonExistingId,
  pickLng,
} from '@shared/utils/test';
import { calendar } from '@googleapis/calendar';
import { CalendarsInDb } from '@shared/infra/database/sequelize/migrations/Calendars.json';
import { Request } from './CheckMakeChangesAndSaveDTOs';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

// Add all process.env used:
const { PROJECT, STAGE } = process.env;
if (!PROJECT || !STAGE) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}

const sendAnalytics = vi.fn();

beforeAll(async () => {
  const { calendar_clientEmail, calendar_clientKey } = await getCalendarCreds({
    name: PROJECT,
    stage: STAGE,
  });
  vi.stubEnv('calendar_clientEmail', calendar_clientEmail);
  vi.stubEnv('calendar_clientKey', calendar_clientKey);
});

const lng = pickLng();

const appsync = new AppSyncClient();
const query = gql`
  mutation ($userId: ID!, $calendarId: ID!, $lng: String!, $type: String!) {
    checkMakeChangesAndSave(
      userId: $userId
      calendarId: $calendarId
      lng: $lng
      type: $type
    ) {
      result {
        action
        currentCalendar {
          name
          id
          nextEventsSearched
          nextEvents {
            event {
              id
              name
              description
              slot {
                start
                end
              }
            }
            detected {
              identifier
              spots
            }
          }
        }
        messageError
        checkError {
          type
          message
          status
        }
      }
      time
    }
  }
`;

test(`cases CREATED, NO_CHANGES and UPDATED`, async () => {
  const userId = chance.guid({ version: 4 });
  // 'reservations 2'
  let calendarId =
    '<EMAIL>';
  const input = {
    userId,
    calendarId,
    lng,
    type: 'RESERVATIONS',
  };

  let received = await appsync.send<Request>({ query, variables: input });

  expect(received.status).toBe(200);
  let json = await received.json();
  let response = json.data.checkMakeChangesAndSave;
  if (!response) console.log('CheckMakeChangesAndSave.e2e 1', json);
  expect(response).toMatchObject({
    result: {
      action: 'CREATED',
      currentCalendar: {
        name: 'reservations 2',
        id: calendarId,
        nextEventsSearched: true,
        nextEvents: null,
      },
    },
    time: expect.stringMatching(dateFormat),
  });

  // Now create some nextEvents
  const calendarApi = new CalendarApi({
    google: calendar({ version: 'v3' }),
    sendAnalytics,
  });
  calendarApi.setCalendarId(input.calendarId);
  const event1 = {
    name: 'next event test 1',
    description: 'next event test 1 description #loc1',
    slot: {
      start: tomorrowPlus().s,
      end: tomorrowPlus(30).s,
    },
  };
  const event2 = {
    name: 'next event test 2',
    description: 'next event test 2 description #loc2:2',
    slot: {
      start: tomorrowPlus(60).s,
      end: tomorrowPlus(90).s,
    },
  };
  const inserted1 = (
    await calendarApi.insertEvent({
      event: Event.create({
        ...event1,
        slot: createSlot(event1.slot),
      }),
      lng,
    })
  ).value.id;
  const inserted2 = (
    await calendarApi.insertEvent({
      event: Event.create({
        ...event2,
        slot: createSlot(event2.slot),
      }),
      lng,
    })
  ).value.id;

  received = await appsync.send<Request>({ query, variables: input });

  expect(received.status).toBe(200);
  json = await received.json();
  if (!json.data) console.log('CheckMakeChangesAndSave.e2e 2', json)
  response = json.data.checkMakeChangesAndSave;
  expect(response).toMatchObject({
    result: {
      action: 'NO_CHANGES', // is a recheck
      currentCalendar: expect.objectContaining({
        name: 'reservations 2',
        id: calendarId,
        nextEventsSearched: true,
      }),
    },
    time: expect.stringMatching(dateFormat),
  });
  const nextEvents = response.result.currentCalendar.nextEvents;
  expect(nextEvents).toHaveLength(2);
  expect(nextEvents).toEqual(
    expect.arrayContaining([
      expect.objectContaining({
        event: expect.objectContaining({
          ...event1,
          id: inserted1,
        }),
        detected: expect.arrayContaining([
          expect.objectContaining({
            identifier: 'loc1',
            spots: 1,
          }),
        ]),
      }),
      expect.objectContaining({
        event: expect.objectContaining({
          ...event2,
          id: inserted2,
        }),
        detected: expect.arrayContaining([
          expect.objectContaining({
            identifier: 'loc2',
            spots: 2,
          }),
        ]),
      }),
    ]),
  );

  await calendarApi.deleteEvent({ id: inserted1, lng });
  await calendarApi.deleteEvent({ id: inserted2, lng });

  // 'reservations 3'
  calendarId =
    '<EMAIL>';
  received = await appsync.send<Request>({
    query,
    variables: {
      ...input,
      calendarId,
    },
  });
  response = (await received.json()).data.checkMakeChangesAndSave;
  expect(response).toMatchObject({
    result: {
      action: 'UPDATED',
      currentCalendar: {
        name: 'reservations 3',
        id: calendarId,
        nextEventsSearched: true,
        nextEvents: null,
      },
    },
    time: expect.stringMatching(dateFormat),
  });

  await rmCalendar(calendarId);
});

test(`Non-existent calendar id gives NO_CHANGES and a checkError`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      userId: CalendarsInDb[1].userId,
      calendarId: nonExistingId,
      lng: 'en',
      type: 'RESERVATIONS',
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log('CheckMakeChangesAndSave.e2e 3:', json);
  const response = json.data.checkMakeChangesAndSave;
  if (!response) console.log('CheckMakeChangesAndSave.e2e 4:', json);
  expect(response).toMatchObject({
    result: {
      action: 'NO_CHANGES',
      currentCalendar: {
        id: CalendarsInDb[1].id,
        name: CalendarsInDb[1].name,
        nextEventsSearched: false,
        nextEvents: null,
      },
      messageError: expect.stringMatching(
        new RegExp(
          `${nonExistingId}[\\S\\s]*no changes[\\S\\s]*${CalendarsInDb[1].id}[\\S\\s]*${CalendarsInDb[1].name}`,
          'i',
        ),
      ),
      checkError: {
        type: expect.stringMatching(`Errors.`),
        message: expect.stringMatching(`${nonExistingId}`),
        status: 404,
      },
    },
    time: expect.stringMatching(dateFormat),
  });
});
