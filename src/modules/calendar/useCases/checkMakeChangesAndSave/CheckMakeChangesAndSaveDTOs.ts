import { BaseErrorDto } from '@shared/core/AppError';
import { NextEventDto } from '../../domain/NextEvent';

export type Request = {
  userId: string;
  calendarId: string;
  lng: string;
  type: string;
};

export type Response = {
  action: string | null;
  currentCalendar: CurrentCalendar | null;
  messageError: string | null;
  checkError: BaseErrorDto | null;
};

export type CurrentCalendar = {
  id: string;
  name: string;
  nextEventsSearched: boolean;
  // Lo correcto para nextEvents sería:
  // nextEvents: [NextEventDto, ...NextEventDto[]] | null;
  // ...sin embargo como NextEventDto es enviado al front pasando por graphql, y teniendo en cuenta que:
  // * graphql tiene la limitación que no se puede expresar un array no empty
  // * quiero utilizar el type NextEventDto en el frontend (exportado mediante front.ts)
  // ...adopto este type que funciona mejor con el proyecto fullstack:
  nextEvents: NextEventDto[] | null;
};