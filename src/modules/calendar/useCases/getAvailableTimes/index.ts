import { GetAvailableTimes } from './GetAvailableTimes';
import { CalendarRepo } from '../../repos/CalendarRepo';
import { CalendarsSrv } from '../../services/CalendarsSrv';
import { AvailabilitySrv } from '../../services/AvailabilitySrv';
import { CalendarApi } from '../../services/CalendarApi';
import { ReservationSrv } from '../../services/ReservationSrv';
import { calendar } from '@googleapis/calendar';
import { IContextProvider } from '@shared/context/IContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models'); // If I use @shared, unit tests give error


export const createHandler = (sendAnalytics: IContextProvider['sendAnalytics']) => {
  const calendarRepo = new CalendarRepo(models.Calendar);
  const availabilitySrv = new AvailabilitySrv({
    api: new CalendarApi({
      google: calendar({ version: 'v3' }),
      sendAnalytics,
    }),
    sendAnalytics,
  });
  const reservationSrv = new ReservationSrv({
    api: new CalendarApi({
      google: calendar({ version: 'v3' }),
      sendAnalytics,
    }),
    sendAnalytics,
  });
  const calendarsSrv = new CalendarsSrv({ availabilitySrv, reservationSrv, sendAnalytics });
  const controller = new GetAvailableTimes({
    calendarRepo,
    calendarsSrv,
    sendAnalytics,
  });

  return controller.executeImpl.bind(controller);
};