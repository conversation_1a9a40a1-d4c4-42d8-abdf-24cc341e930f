import { Request, Response } from './GetAvailableTimesDTOs';
import { ICalendarRepo } from '../../repos/ICalendarRepo';
import { BaseError } from '@shared/core/AppError';
import { GetAvailableTimesErrors } from './GetAvailableTimesErrors';
import { Identifier } from '../../domain/Identifier';
import {
  Every,
  MaxDaysAhead,
  ReservationLimits,
  TimeReference,
} from '../../external';
import { ICalendarsSrv } from '../../services/ICalendarsSrv';
import { Slot } from '../../domain/Slot';
import { Dat } from '@shared/core/Dat';
import { FunctionController2 } from '@shared/infra/Controllers';
import { PositiveInt } from '@shared/core/PositiveInt';
import { getLng } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { N0 } from '@shared/core/N0';
import { IContextProvider } from '@shared/context/IContextProvider';

type GetAvailableTimesProps = {
  calendarRepo: ICalendarRepo;
  calendarsSrv: ICalendarsSrv;
  sendAnalytics: IContextProvider['sendAnalytics'];
};

export class GetAvailableTimes extends FunctionController2<Request, Response> {
  private readonly calendarRepo: ICalendarRepo;
  private readonly calendarsSrv: ICalendarsSrv;
  private readonly sendAnalytics: IContextProvider['sendAnalytics'];

  public constructor({
    calendarRepo,
    calendarsSrv,
    sendAnalytics,
  }: GetAvailableTimesProps) {
    super();
    this.calendarRepo = calendarRepo;
    this.calendarsSrv = calendarsSrv;
    this.sendAnalytics = sendAnalytics;
  }

  public async executeImpl(dto: Request) {
    // await new Promise(resolve => setTimeout(resolve, 9999999)); // force timeout error for testing purposes
    process.env.startingTime = new Date().getTime().toString(); // Used by early return logic to limit reservations being asked
    const {
      userId,
      identifier: _identifier,
      every: _every,
      from: _from,
      to: _to,
      timeReference: _timeReference,
      duration: _duration,
      lng: _lng,
      maxTimes: _maxTimes,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      roId, // Included to track in case of error. See GetAvailableTimesDTOs.ts
      maxDaysAhead,
      minTimeBeforeService,
    } = dto;
    const lng = getLng(_lng);

    let errors: BaseError[] = [];

    const fromOrError = Dat.create({ value: _from, lng });
    if (fromOrError.isFailure)
      errors.push(fromOrError.error!.setField('from'));

    const toOrError = Dat.create({ value: _to, lng });
    if (toOrError.isFailure)
      errors.push(toOrError.error!.setField('to'));

    let from: Dat, to: Dat, slot: Slot;
    if (!errors.length) {
      from = fromOrError.value;
      to = toOrError.value;
      const slotOrError = Slot.create({ start: from, end: to });
      if (slotOrError.isFailure) {
        const error = new GetAvailableTimesErrors.InvalidSlot({ from: _from, to: _to, lng }).setField('from');
        errors.push(
          error,
          error.clone('to'),
        );
      } else
        slot = slotOrError.value;
    }

    const maxTimesOrErrors = PositiveInt.create({ value: _maxTimes, lng });
    if (maxTimesOrErrors.isFailure) {
      for (const e of maxTimesOrErrors.errors!)
        e.setField('maxTimes');
      errors = errors.concat(maxTimesOrErrors.errors!);
    }

    const identifierOrErrors = Identifier.create({ value: _identifier, lng }, this.sendAnalytics);
    if (identifierOrErrors.isFailure) {
      for (const e of identifierOrErrors.errors!)
        e.setField('identifier');
      errors = errors.concat(identifierOrErrors.errors!);
    }

    const everyOrErrors = Every.create({ value: _every, lng });
    if (everyOrErrors.isFailure) {
      for (const e of everyOrErrors.errors!)
        e.setField('every');
      errors = errors.concat(everyOrErrors.errors!);
    }

    const timeReferenceOrErrors = TimeReference.create({ ..._timeReference, lng });
    if (timeReferenceOrErrors.isFailure) {
      for (const e of timeReferenceOrErrors.errors!)
        e.prefixField('timeReference');
      errors = errors.concat(timeReferenceOrErrors.errors!);
    }

    const durationOrErrors = PositiveInt.create({ value: _duration, lng });
    if (durationOrErrors.isFailure) {
      for (const e of durationOrErrors.errors!)
        e.setField('duration');
      errors = errors.concat(durationOrErrors.errors!);
    }

    if (errors.length)
      return Result2.fail(errors);

    const maxTimes = maxTimesOrErrors.value;
    const identifier = identifierOrErrors.value;
    const every = everyOrErrors.value;
    const timeReference = timeReferenceOrErrors.value;
    const duration = durationOrErrors.value;

    // This should never fail since it comes from GetAvailabilityOfReservationOptions@reservation, where it is read a proper GeneralSettings.reservationLimits
    const reservationLimits = ReservationLimits.create({
      maxDaysAhead: MaxDaysAhead.create({ value: maxDaysAhead }).value,
      minTimeBeforeService: N0.create({ value: minTimeBeforeService }).value,
    }).value;

    const [availabilityCalendar, reservationCalendar] = await Promise.all([
      this.calendarRepo.getAvailability(userId),
      this.calendarRepo.getReservations(userId),
    ]);

    if (!availabilityCalendar)
      errors.push(
        new GetAvailableTimesErrors.AvailabilityCalendarNotFoundForUserId(
          { userId, lng }
        )
      );
    if (!reservationCalendar)
      errors.push(
        new GetAvailableTimesErrors.ReservationCalendarNotFoundForUserId(
          { userId, lng }
        )
      );

    if (errors.length)
      return Result2.fail(errors);

    this.calendarsSrv.setCalendarIds({
      availabilityCalendarId: availabilityCalendar!.id.toString(),
      reservationCalendarId: reservationCalendar!.id.toString(),
    });

    const availableTimesOrError = await this.calendarsSrv.getAvailableTimes({
      identifier,
      slot: slot!,
      timeReference,
      every,
      duration,
      reservationLimits,
      lng,
      maxTimes,
    });
    if (availableTimesOrError.isFailure)
      return Result2.fail([availableTimesOrError.error!]);
    const availableTimes = availableTimesOrError.value;

    /*// Add error for testing
    if (roId === '03a6f6a4-f5a8-4266-a4ab-8b0b8915f347') {  // Res Opt 4 de userId 00ad8920-c36d-11ee-8adf-3f66c88fcbdd
      return formatErrors([new GetAvailableTimesErrors.ReservationCalendarNotFoundForUserId({ userId, lng })]);
    }*/

    return Result2.ok(
      availableTimes
        ? availableTimes.map((time) => time.toDto())
        : null
    )
  }
}
