import { expect, test, vi, it, describe } from 'vitest';
vi.stubEnv('calendar_clientEmail', 'dummy');
vi.stubEnv('calendar_clientKey', 'dummy');
import { Every, TimeReference } from '../../external';
import { CalendarRepoFake } from '../../repos/CalendarRepoFake';
import { CalendarsSrv } from '../../services/CalendarsSrv';
import { GetAvailableTimes } from './GetAvailableTimes';
import { CalendarApiFake } from '../../services/CalendarApiFake';
import { Result } from '@shared/core/Result';
import { SlotWithSpots } from '../../domain/SlotWithSpots';
import { Slot } from '../../domain/Slot';
import { Spots } from '../../domain/Spots';
import { DateTime } from 'luxon';
import {
  GetPossibleTimesArgs,
  TimeWithSpots,
} from '../../domain/TimeWithSpots';
import { Calendar } from '../../domain/Calendar';
import { Dat } from '@shared/core/Dat';
import { Error1 } from '@shared/utils/test';
import { PositiveInt } from '@shared/core/PositiveInt';
import { CalendarsInDb } from '@shared/infra/database/sequelize/migrations/Calendars.json';
import { getLng, nonEmpty } from '@shared/utils/utils';
import { AvailabilitySrv } from '../../services/AvailabilitySrv';
import { ReservationSrv } from '../../services/ReservationSrv';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const sendAnalytics = vi.fn();

const nextDay = DateTime.now().plus({ day: 1 }).startOf('day');

const every = 10;
const spots = 1;
const duration = 30;
const timeReference = {
  hour: 0,
  minute: 0,
  zone: 'America/Cordoba',
};
const from = Dat.create({ value: nextDay }).value.s;
const to = Dat.create({ value: nextDay.plus({ minutes: duration }) }).value.s;
const maxTimes = 99999;

const request = {
  to,
  from,
  identifier: 'test_identifier',
  every,
  duration: every,
  userId: CalendarsInDb[0].userId,
  timeReference,
  lng: getLng(),
  maxTimes,
  roId: chance.guid(),
  maxDaysAhead: 10,
  minTimeBeforeService: 0,
};

const availableSlots = nonEmpty([
  SlotWithSpots.create({
    slot: Slot.create({
      start: Dat.create({ value: from }).value,
      end: Dat.create({ value: to }).value,
    }).value,
    spots: Spots.create(spots, sendAnalytics).value,
  }),
]);

const getPossibleTimesArgs: GetPossibleTimesArgs = {
  every: Every.create({ value: every }).value,
  duration: PositiveInt.create({ value: duration }).value,
  chunks: availableSlots,
  timeReference: TimeReference.create(timeReference).value,
  maxTimes: PositiveInt.create({ value: maxTimes }).value,
};

const availabilitySrv = new AvailabilitySrv({
  api: new CalendarApiFake(),
  sendAnalytics,
});
const reservationSrv = new ReservationSrv({
  api: new CalendarApiFake(),
  sendAnalytics,
});
const calendarsSrv = new CalendarsSrv({ availabilitySrv, reservationSrv, sendAnalytics });
const mockGetAvailableTimes = async () =>
  new Promise<Result<[TimeWithSpots, ...TimeWithSpots[]] | null>>((resolve) => {
    // Si bien podría hacer público el constructor new TimeWithSpots() y usarlo directamente, para no tener que usar TimeWithSpots.getPossibleTimes que es más complicado, como por diseño new TimeWithSpots() por ahora no necesito que sea público es que uso TimeWithSpots.getPossibleTimes, ya que no me convence hacer público new TimeWithSpots() sólo por los tests
    TimeWithSpots.getPossibleTimes(getPossibleTimesArgs, sendAnalytics).then(possible => {
      resolve(Result.ok(possible));
    });
  });
vi.spyOn(calendarsSrv, 'getAvailableTimes').mockImplementation(
  mockGetAvailableTimes,
);
const calendarRepo = new CalendarRepoFake();
const getAvailableTimes = new GetAvailableTimes({
  calendarRepo,
  calendarsSrv,
  sendAnalytics,
});

describe(`succeeds`, () => {
  test(`finding available times`, async () => {
    const result = await getAvailableTimes.executeImpl(request);

    expect(result.isSuccess).toBe(true)
    expect(result.value).toEqual([{
      time: from,
      spots,
    }])
  });

  test(`but doesn't find any available times`, async () => {
    const calendarsSrv = new CalendarsSrv({ availabilitySrv, reservationSrv, sendAnalytics });
    const mockGetAvailableTimes = async () =>
      new Promise<Result<[TimeWithSpots] | null>>((resolve) => {
        resolve(Result.ok(null));
      });
    vi.spyOn(calendarsSrv, 'getAvailableTimes').mockImplementation(
      mockGetAvailableTimes,
    );

    const getAvailableTimes = new GetAvailableTimes({
      calendarRepo,
      calendarsSrv,
      sendAnalytics,
    });

    const result = await getAvailableTimes.executeImpl(request);

    expect(result).toMatchObject({
      isSuccess: true,
      value: null,
    })
  });
});

describe(`fails when`, () => {
  it(`there are invalid inputs`, async () => {
    const result = await getAvailableTimes.executeImpl({
      ...request,
      from: 'invalid date-time string',
      to: 'invalid date-time string',
      maxTimes: -1.5,
      identifier: 'invalid identifier',
      every: 11,
      timeReference: {
        hour: 0,
        minute: 0.5,
        zone: 'America/Cordoba',
      },
      duration: 0,
    });

    expect(result).toMatchObject({
      isFailure: true,
      errors: expect.arrayContaining([
        expect.objectContaining({ field: 'from' }),
        expect.objectContaining({ field: 'to' }),
        expect.objectContaining({ field: 'maxTimes' }),
        expect.objectContaining({ field: 'identifier' }),
        expect.objectContaining({ field: 'every' }),
        expect.objectContaining({ field: expect.stringContaining('timeReference.') }),
        expect.objectContaining({ field: 'duration' }),
      ]),
    });
  });

  it(`from-to range is invalid`, async () => {
    const result = await getAvailableTimes.executeImpl({
      ...request,
      to: from,
    });

    expect(result).toMatchObject({
      isFailure: true,
      errors: expect.arrayContaining([
        expect.objectContaining({
          field: 'from',
          type: 'GetAvailableTimesErrors.InvalidSlot',
          status: 400,
          message: expect.any(String),
        }),
        expect.objectContaining({
          field: 'to',
          type: 'GetAvailableTimesErrors.InvalidSlot',
          status: 400,
          message: expect.any(String),
        }),
      ]),
    });
    expect(result.errors).toHaveLength(2);
  });

  it(`doesn't find either the availability or the reservation calendar.`, async () => {
    const calendarRepo = new CalendarRepoFake();
    const mockGetAvailability = () =>
      new Promise<Calendar | null>((resolve) => {
        resolve(null);
      });
    vi.spyOn(calendarRepo, 'getAvailability').mockImplementation(
      mockGetAvailability,
    );
    const mockGetReservations = () =>
      new Promise<Calendar | null>((resolve) => {
        resolve(null);
      });
    vi.spyOn(calendarRepo, 'getReservations').mockImplementation(
      mockGetReservations,
    );

    const getAvailableTimes = new GetAvailableTimes({
      calendarRepo: calendarRepo,
      calendarsSrv,
      sendAnalytics,
    });

    const result = await getAvailableTimes.executeImpl(request);

    expect(result).toMatchObject({
      isFailure: true,
      errors: expect.arrayContaining([
        expect.objectContaining({
          type: 'GetAvailableTimesErrors.AvailabilityCalendarNotFoundForUserId',
          status: 404,
          message: expect.any(String),
        }),
        expect.objectContaining({
          type: 'GetAvailableTimesErrors.ReservationCalendarNotFoundForUserId',
          status: 404,
          message: expect.any(String),
        }),
      ]),
    });
    expect(result.errors).toHaveLength(2);
  });

  it(`should relay the error from calendarsSrv.getAvailableTimes`, async () => {
    const calendarsSrv = new CalendarsSrv({ availabilitySrv, reservationSrv, sendAnalytics });
    const error1 = new Error1();
    const mockGetAvailableTimes = async () =>
      new Promise<Result<[TimeWithSpots] | null>>((resolve) => {
        resolve(Result.fail(error1));
      });
    vi.spyOn(calendarsSrv, 'getAvailableTimes').mockImplementation(
      mockGetAvailableTimes,
    );

    const getAvailableTimes = new GetAvailableTimes({
      calendarRepo,
      calendarsSrv,
      sendAnalytics,
    });

    const result = await getAvailableTimes.executeImpl(request);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [error1],
    })
  });
});
