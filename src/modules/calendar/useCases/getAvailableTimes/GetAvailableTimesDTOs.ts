import { ReservationLimitsDto, TimeReferenceDto } from '../../external';
import { TimeWithSpotsDto } from '../../domain/TimeWithSpots';

export type Request = {
  userId: string;
  identifier: string;
  every: number;
  duration: number;
  timeReference: TimeReferenceDto;
  from: string;
  to: string;
  lng: string;
  maxTimes: number;
  roId: string; // Included to track the reservation option in case of error. GetAvailabilityOfReservationOptions @ reservation uses LambdaInvoker to call GetAvailableTimes, and LambdaInvoker logs payload.
  maxDaysAhead: ReservationLimitsDto['maxDaysAhead'];
  minTimeBeforeService: ReservationLimitsDto['minTimeBeforeService'];
};

export type Response = TimeWithSpotsDto[] | null;
