import { patch } from '@shared/core/utils';
import { BadRequest, BaseError } from '@shared/core/AppError';
import { Status } from '@shared/core/Status';
import { getLng, OptionalLng } from '@shared/utils/utils';

export namespace GetAvailableTimesErrors {
  export class InvalidSlot extends BadRequest {
    public constructor(args: { from: string, to: string } & OptionalLng) {
      const { from, to, lng } = args;
      const t = trans[getLng(lng)];
      super({ message: t.invalidSlot({ from, to }) });
    }
  }
  export class AvailabilityCalendarNotFoundForUserId extends BaseError {
    public constructor(args: { userId: string } & OptionalLng) {
      const { userId, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.availabilityCalendarNotFound(userId),
        status: Status.NOT_FOUND,
      });
    }
  }
  export class ReservationCalendarNotFoundForUserId extends BaseError {
    public constructor(args: { userId: string } & OptionalLng) {
      const { userId, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.reservationCalendarNotFoundForUserId(userId),
        status: Status.NOT_FOUND,
      });
    }
  }
}

const trans = {
  en: {
    invalidSlot(args: { from: string, to: string }) {
      const { from, to } = args;
      return `Couldn't build a valid interval with values from "${from}" and to "${to}".`;
    },
    availabilityCalendarNotFound(v: string) {
      return `Availability calendar for user ${v} not found`;
    },
    reservationCalendarNotFoundForUserId(v: string) {
      return `Reservation calendar for user ${v} not found`;
    },
  },
  es: {
    invalidSlot(args: { from: string, to: string }) {
      const { from, to } = args;
      return `No pudo ser construído un intervalo válido con los valores desde "${from}" y hasta "${to}".`;
    },
    availabilityCalendarNotFound(v: string) {
      return `Calendario de disponibilidad para el usuario ${v} no encontrado`;
    },
    reservationCalendarNotFoundForUserId(v: string) {
      return `Calendario de reservas para el usuario ${v} no encontrado`;
    },
  },
};

patch({ GetAvailableTimesErrors });
