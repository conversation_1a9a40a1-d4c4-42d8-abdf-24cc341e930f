import { it, expect } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { dateFormat, nonExistingId } from '@shared/utils/test';
import { CalendarsInDb } from '@shared/infra/database/sequelize/migrations/Calendars.json';
import { Request } from './GetCalendarsDTOs';
import { CalendarFragment } from '../../utils/fragments';

const appsync = new AppSyncClient();
const query = gql`
  query ($userId: ID!) {
    getCalendars(userId: $userId) {
      time
      result {
        availability {
          ...CalendarFragment
        }
        reservations {
          ...CalendarFragment
        }
      }
    }
  }
  ${CalendarFragment}
`;

it(`gets calendars`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      userId: CalendarsInDb[0].userId,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log('GetCalendars.e2e.ts 1 json:', json);
  const response = json.data.getCalendars;
  if (!response) console.log('GetCalendars.e2e.ts 2 json:', json);
  expect(response).toEqual({
    time: expect.stringMatching(dateFormat),
    result: {
      availability: {
        name: CalendarsInDb[0].name,
        id: CalendarsInDb[0].id,
      },
      reservations: {
        name: CalendarsInDb[1].name,
        id: CalendarsInDb[1].id,
      },
    },
  });
});

it(`returns null when userId isn't found`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      userId: nonExistingId,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log('GetCalendars.e2e.ts 3 json:', json);
  const response = json.data.getCalendars;
  if (!response) console.log('GetCalendars.e2e.ts 4 json:', json);
  expect(response).toEqual({
    time: expect.stringMatching(dateFormat),
    result: {
      availability: null,
      reservations: null,
    },
  });
});
