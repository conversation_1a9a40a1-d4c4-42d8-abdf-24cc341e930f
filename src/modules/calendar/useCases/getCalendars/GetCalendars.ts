import { Request, Response } from './GetCalendarsDTOs';
import { ICalendarRepo } from '../../repos/ICalendarRepo';
import { Status } from '@shared/core/Status';
import { ControllerResult } from '@shared/core/ControllerResult';
import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';

type GetCalendarsProps = {
  calendarRepo: ICalendarRepo;
  contextProvider: IContextProvider;
};

export class GetCalendars extends AppSyncController<Request, Response> {
  private readonly calendarRepo: ICalendarRepo;

  public constructor({ calendarRepo, contextProvider }: GetCalendarsProps) {
    super({ contextProvider });
    this.calendarRepo = calendarRepo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { userId } = dto;

    let availability: Response['availability'] = null, reservations: Response['reservations'] = null;
    const [availabilityCalendar, reservationCalendar] = await Promise.all([
      this.calendarRepo.getAvailability(userId),
      this.calendarRepo.getReservations(userId),
    ]);

    if (availabilityCalendar)
      availability = {
        id: availabilityCalendar.id.toString(),
        name: availabilityCalendar.name,
      };
    if (reservationCalendar)
      reservations = {
        id: reservationCalendar.id.toString(),
        name: reservationCalendar.name,
      };

    return {
      status: Status.OK,
      result: {
        availability,
        reservations,
      },
    };
  }
}
