// Funciones usadas en los tests que no dependen de otros módulos externos
import { vi } from 'vitest';
import { Event } from '../domain/Event';
import { Slot } from '../domain/Slot';
import { DateTime } from 'luxon';
import { SlotWithSpots, SlotWithSpotsDto } from '../domain/SlotWithSpots';
import { Spots } from '../domain/Spots';
import { EventRetrieved } from '../domain/EventRetrieved';
import { Dat } from '@shared/core/Dat';
import { tomorrowPlus } from './utils';
import {
  DetectIdentifiersWithSpotsResult,
  Identifier,
} from '../domain/Identifier';
import { nonEmpty } from '@shared/utils/utils';
import { NextEvent } from '../domain/NextEvent';
import { Calendar, CalendarType } from '../domain/Calendar';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const sendAnalytics = vi.fn();
const CalendarModel = models.Calendar;

export function createEvent({
  name = 'test event name',
  description = 'test event description',
  start = tomorrowPlus(),
  end = tomorrowPlus(30),
}): Event {
  const slot = Slot.create({ start, end }).value;
  return Event.create({
    name,
    description,
    slot,
  });
}

export function createSlotWithSpots(dto: SlotWithSpotsDto): SlotWithSpots {
  return SlotWithSpots.create({
    slot: Slot.create({
      start: Dat.create({ value: dto.slot.start }).value,
      end: Dat.create({ value: dto.slot.end }).value,
    }).value,
    spots: Spots.create(dto.spots, sendAnalytics).value,
  });
}

export function createSlot(args?: { start: string; end: string }): Slot {
  let start = tomorrowPlus();
  let end = tomorrowPlus(30);
  if (args) {
    start = Dat.create({ value: args.start }).value;
    end = Dat.create({ value: args.end }).value;
  }
  return Slot.create({
    start,
    end,
  }).value;
}

export function createIdentifier(): Identifier {
  return Identifier.create(
    { value: chance.string({ length: 8, casing: 'lower', alpha: true, numeric: true }) },
    sendAnalytics
  ).value;
}

export function createEventRetrieved(): EventRetrieved {
  const now = DateTime.now();
  return EventRetrieved.create({
    name: 'testName',
    slot: Slot.create({
      start: Dat.create({ value: now }).value,
      end: Dat.create({ value: now.plus({ minutes: 10 }) }).value,
    }).value,
    id: 'testId',
    description: 'testDescription',
  });
}

export function createSpots(): Spots {
  return Spots.create(chance.integer({ min: 1, max: 10 }), sendAnalytics).value;
}

export function createDetectedIdentifiersWithSpots(): DetectIdentifiersWithSpotsResult {
  return nonEmpty(
    new Array(chance.integer({ min: 1, max: 5 })).fill(null).map(() => ({
      identifier: createIdentifier(),
      spots: createSpots(),
    })),
  );
}

export function createNextEvents(): [NextEvent, ...NextEvent[]] | null {
  return nonEmpty(
    new Array(
      chance.integer({
        min: 1,
        max: 5,
      }),
    )
      .fill(null)
      .map(() =>
        NextEvent.create({
          event: createEventRetrieved(),
          detected: chance.bool() ? createDetectedIdentifiersWithSpots() : null,
        }),
      ),
  );
}

export async function rmCalendar(id: string) {
  return CalendarModel.destroy({
    where: { id },
  });
}

export function getEvent(offset: number, description?: string): Event {
  const start = Dat.create({ value: tomorrowPlus(offset).l.startOf('hour') }).value;
  const end = Dat.create({
    value:
      start.l.plus({
        minutes: chance.integer({
          min: 20,
          max: 60,
        }),
      }),
  }).value;
  return Event.create({
    name: chance.string({
      length: 8,
      casing: 'lower',
      alpha: true,
      numeric: true,
    }),
    description: description !== undefined ? description : chance.sentence(),
    slot: Slot.create({
      start,
      end,
    }).value,
  });
}

export function createCalendar(): Calendar {
  return Calendar.create(
    {
      userId: chance.guid({ version: 4 }),
      type: chance.pickone([CalendarType.AVAILABILITY, CalendarType.RESERVATIONS]),
      name: chance.word(),
    },
    chance.guid({ version: 4 }),
  );
}