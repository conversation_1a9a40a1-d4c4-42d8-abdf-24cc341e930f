import { DateTime } from 'luxon';
import { Dat } from '@shared/core/Dat';
import { CalendarApi } from '../services/CalendarApi';

/**
 * Returns the time in 24 hrs plus x minutes
 * @param {number} [minutes]
 * @returns {Date}
 */
export function tomorrowPlus(minutes?: number): Dat {
  if (!minutes) minutes = 0;
  return Dat.create({ value: DateTime.now().plus({ days: 1, minutes }) }).value;
}

type InsertedEvent = { value: { id: string } };
export async function rmEvents(args: {
  inserted: InsertedEvent[];
  calendarApi: CalendarApi;
}) {
  const { inserted, calendarApi } = args;
  await Promise.all(
    inserted.map(async (event: InsertedEvent) => {
      await calendarApi.deleteEvent({id: event.value.id, lng: 'es'});
    }),
  );
}
