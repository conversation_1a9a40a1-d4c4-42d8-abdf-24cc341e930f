// source ../../../../setAwsProfile.sh
// PROJECT=turnero STAGE=dev AWS_PROFILE=dev AWS_REGION=us-east-1 ts-node seedEvents.ts
import * as dotenv from 'dotenv';
dotenv.config();
import { getCalendarCreds } from '@shared/infra/iac/helpers';
import { CalendarApi } from '../services/CalendarApi';
import { calendar } from '@googleapis/calendar';
import { CalendarsInDb } from '@shared/infra/database/sequelize/migrations/Calendars.json';
import { Slot } from '../domain/Slot';
import { Dat } from '@shared/core/Dat';
import { Identifier } from '../domain/Identifier';
import { rmEvents, tomorrowPlus } from './utils';
import { Event } from '../domain/Event';
import { sendAnalyticsDummy as sendAnalytics } from '@shared/utils/utils';

async function initialization() {
  // Add all process.env used:
  const { PROJECT, STAGE } = process.env;
  if (!PROJECT || !STAGE) {
    console.log('process.env', process.env);
    throw new Error(`Undefined env var!`);
  }


  const { calendar_clientEmail, calendar_clientKey } = await getCalendarCreds({
    name: PROJECT,
    stage: STAGE,
  });
  process.env.calendar_clientEmail = calendar_clientEmail;
  process.env.calendar_clientKey = calendar_clientKey;
}

const initial = Dat.create({ value: tomorrowPlus().l.startOf('hour') }).value;
const identifier = Identifier.create({ value: 'loc1'}, sendAnalytics).value;
const lng = 'es';

async function insertAvailabilityEvents(availabilityApi: CalendarApi) {

  // First create 2 availability events with 2 and 3 spots
  const availabilityEvent1 = Event.create({
    name: 'Availability 1',
    description: `${identifier.query}:2`,
    slot: Slot.create({
      start: initial,
      end: Dat.create({ value: initial.l.plus({ minutes: 60 }) }).value,
    }).value,
  });
  const availabilityEvent2 = Event.create({
    name: 'Availability 2',
    description: `${identifier.query}:3`,
    slot: Slot.create({
      start: Dat.create({ value: initial.l.plus({ minutes: 30 }) }).value,
      end: Dat.create({ value: initial.l.plus({ minutes: 90 }) }).value,
    }).value,
  });

  const availInserted1 = await availabilityApi.insertEvent({ event: availabilityEvent1, lng }); // Como preparación de este test necesito tener acceso a escritura del availability calendar, pero en la app de producción sólo necesito acceso de lectura
  if (!availInserted1.isSuccess) throw Error('availInserted1 failed');
  const availInserted2 = await availabilityApi.insertEvent({ event: availabilityEvent2, lng });
  if (!availInserted2.isSuccess) throw Error('availInserted2 failed');

  return [availInserted1, availInserted2];

}
async function listAvailabilityEvents(slot: Slot): Promise<void> {

  const availabilityApi = new CalendarApi({
    google: calendar({ version: 'v3' }),
    sendAnalytics: sendAnalytics,
  });
  availabilityApi.setCalendarId(CalendarsInDb[0].id);

  const availabilityEventsOrError = await availabilityApi.listEvents({
    slot,
    identifier,
    lng,
  });
  if (!availabilityEventsOrError.isSuccess) throw Error('availabilityApi.listEvents failed');

  const availabilityEvents = availabilityEventsOrError.value
  if (!availabilityEvents) throw Error('No availabilityEvents');
  const availabilityEventsDtos = availabilityEvents.map((e) => e.toDto());
  console.log('availabilityEventsDtos', JSON.stringify(availabilityEventsDtos, null, 2));
}

async function insertReservationEvents(reservationApi: CalendarApi) {

  // Create 2 reservations events with 1 and 3 spots taken
  const reservation1 = Event.create({
    name: 'Reservation 1',
    description: `${identifier.query}:1`,
    slot: Slot.create({
      start: Dat.create({ value: initial.l.plus({ minutes: 21 }) }).value,
      end: Dat.create({ value: initial.l.plus({ minutes: 45 }) }).value,
    }).value,
  });
  const reservation2 = Event.create({
    name: 'Reservation 2',
    description: `${identifier.query}:3`,
    slot: Slot.create({
      start: Dat.create({ value: initial.l.plus({ minutes: 40 }) }).value,
      end: Dat.create({ value: initial.l.plus({ minutes: 65 }) }).value,
    }).value,
  });

  const resInserted1 = await reservationApi.insertEvent({ event: reservation1, lng });
  if (!resInserted1.isSuccess) throw Error('resInserted1 failed');
  const resInserted2 = await reservationApi.insertEvent({ event: reservation2, lng });
  if (!resInserted2.isSuccess) throw Error('resInserted2 failed');

  return [resInserted1, resInserted2];
}

async function listReservationEvents(slot: Slot) {

  const reservationApi = new CalendarApi({
    google: calendar({ version: 'v3' }),
    sendAnalytics: sendAnalytics,
  });
  reservationApi.setCalendarId(CalendarsInDb[1].id);

  const reservationEventsOrError = await reservationApi.listEvents({
    slot,
    identifier,
    lng,
  });
  if (!reservationEventsOrError.isSuccess) throw Error('reservationApi.listEvents failed');

  const reservationEvents = reservationEventsOrError.value
  if (!reservationEvents) throw Error('No reservationEvents');
  const reservationEventsDtos = reservationEvents.map((e) => e.toDto());
  console.log('reservationEventsDtos', JSON.stringify(reservationEventsDtos, null, 2));

}
async function main(): Promise<void> {
  await initialization();

  const availabilityApi = new CalendarApi({
    google: calendar({ version: 'v3' }),
    sendAnalytics: sendAnalytics,
  });
  availabilityApi.setCalendarId(CalendarsInDb[0].id); // calendario "availability" de userId 00ad8920-c36d-11ee-8adf-3f66c88fcbdd
  const availInserted = await insertAvailabilityEvents(availabilityApi);

  const slot = Slot.create({
    start: Dat.create().value,
    end: Dat.create({ value: initial.l.plus({ days: 2 }) }).value,
  }).value;
  await listAvailabilityEvents(slot);

  const reservationApi = new CalendarApi({
    google: calendar({ version: 'v3' }),
    sendAnalytics: sendAnalytics,
  });
  reservationApi.setCalendarId(CalendarsInDb[1].id);  // calendario "reservations" de userId 00ad8920-c36d-11ee-8adf-3f66c88fcbdd
  const resInserted = await insertReservationEvents(reservationApi);
  await listReservationEvents(slot);

  await rmEvents({
    inserted: availInserted,
    calendarApi: availabilityApi,
  });
  await rmEvents({
    inserted: resInserted,
    calendarApi: reservationApi,
  });
}

void main();