import { expect, it, describe, vi, test, beforeEach } from 'vitest';
process.env.calendar_clientKey = 'dummy';
process.env.calendar_clientEmail = 'dummy';
import { Dat } from '@shared/core/Dat';
import { EventRetrieved } from '../domain/EventRetrieved';
import { Slot } from '../domain/Slot';
import { ApiResults } from './CalendarApiFake';
import { Event } from '../domain/Event';
import {
  createEventRetrieved,
  createIdentifier,
  createSlot,
  getEvent,
} from '../utils/test';
import { calendar, calendar_v3 } from '@googleapis/calendar';
import { CalendarApi } from './CalendarApi';
import { PossibleLngs } from '@shared/utils/utils';
import { Error2, pickLng } from '@shared/utils/test';
import { CalendarApiErrors } from './CalendarApiErrors';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const sendAnalytics = vi.fn();

const getApi = () => {
  const google = calendar({ version: 'v3' });
  const api = new CalendarApi({
    google,
    sendAnalytics,
  });
  api.setCalendarId('dummy');
  return { api, google };
};

let api: CalendarApi, google: calendar_v3.Calendar, lng: PossibleLngs;

beforeEach(() => {
  const result = getApi();
  api = result.api;
  google = result.google;
  lng = pickLng();
});

describe(`getCalendarName`, () => {
  it(`succeeds`, async () => {
    vi.spyOn(google.calendars, 'get').mockImplementation(
      () => ApiResults.getCalendarSuccess,
    );

    const result = await api.getCalendarName(lng);

    expect(result.isSuccess).toBe(true);
    expect(result.value).toBe(ApiResults.getCalendarSuccess.data!.summary);
  });
  describe(`fails when`, () => {
    it(`google doesn't return data`, async () => {
      vi.spyOn(google.calendars, 'get').mockImplementation(() => ({
        status: 200,
      }));
      const result = await api.getCalendarName(lng);
      expect(result).toMatchObject({
        isFailure: true,
        error: {
          message: expect.any(String),
          type: 'CalendarApiErrors.NoName',
          status: 404,
        },
      });
    });
    test(`calendar doesn't have summary property`, async () => {
      vi.spyOn(google.calendars, 'get').mockImplementation(() => ({
        status: 200,
        data: {},
      }));
      const result = await api.getCalendarName(lng);
      expect(result).toMatchObject({
        isFailure: true,
        error: {
          message: expect.any(String),
          type: 'CalendarApiErrors.NoName',
          status: 404,
        },
      });
    });
  });
});

describe(`success`, () => {
  it(`insertEvent`, async () => {
    const start = new Date('2000-01-01T00:10:00-03:00');
    const end = new Date('2000-01-01T00:20:00-03:00');
    const event = Event.create({
      name: 'test name',
      description: 'test description',
      slot: Slot.create({
        start: Dat.create({ value: start }).value,
        end: Dat.create({ value: end }).value,
      }).value,
    });

    vi.spyOn(google.events, 'insert').mockImplementation(
      () => ApiResults.insertSuccess,
    );

    const result = await api.insertEvent({ event, lng });

    expect(result.isSuccess).toBe(true);
    expect(
      result.value?.equals(EventRetrieved.fromApi(ApiResults.insertSuccess.data)),
    ).toBe(true);
  });

  describe.each(['listEvents', 'nextEvents'])(`%s`, (method) => {
    function callApiAccordingTestCase(method: string) {
      switch (method) {
        case 'listEvents':
          return api.listEvents({
            identifier: createIdentifier(),
            slot: createSlot(),
            lng,
          });
        case 'nextEvents':
          return api.nextEvents(lng);
        default:
          throw Error(`Unexpected method ${method}`);
      }
    }

    it(`returns the upcoming events`, async () => {
      const { listSuccess1 } = ApiResults;
      const { items } = listSuccess1.data;
      vi.spyOn(google.events, 'list').mockImplementation(() => listSuccess1);

      const result = await callApiAccordingTestCase(method);

      expect(result.isSuccess).toBe(true);
      const resultEvents = result.value;
      if (!resultEvents) throw Error('Expected events');
      expect(resultEvents.length).toBe(items.length);

      const inputEvents = EventRetrieved.listToDomain(listSuccess1.data);
      if (!inputEvents) throw Error('Expected events');
      for (let i = 0; i < resultEvents.length; i++) {
        expect(resultEvents[i].name).toBe(items[i].summary);
        expect(resultEvents[i].description).toBe(items[i].description ?? null);

        expect(resultEvents[i].slot.toDto()).toMatchObject({
          ...inputEvents[i].slot.toDto(),
        });
      }
      expect(resultEvents.length).toBe(inputEvents.length);
    });
    it(`returns null when there are no upcoming events`, async () => {
      vi.spyOn(google.events, 'list').mockImplementation(
        () => ApiResults.listSuccess3noEvents,
      );

      const result = await callApiAccordingTestCase(method);

      expect(result.isSuccess).toBe(true);
      expect(result.value).toBe(null);
    });
  });

  it(`getEvent finds event`, async () => {
    vi.spyOn(google.events, 'get').mockImplementation(
      () => ApiResults.insertSuccess,
    );

    const result = await api.getEvent({ id: 'dummy', lng });
    expect(result.isSuccess).toBe(true);
    expect(
      result.value?.equals(EventRetrieved.fromApi(ApiResults.insertSuccess.data)),
    ).toBe(true);
  });

  it(`updateEvent`, async () => {
    vi.spyOn(google.events, 'update').mockImplementation(
      () => ApiResults.insertSuccess,
    );
    const result = await api.updateEvent({ event: createEventRetrieved(), lng });

    expect(result.isSuccess).toBe(true);
    expect(
      result.value?.equals(EventRetrieved.fromApi(ApiResults.insertSuccess.data)),
    ).toBe(true);
  });

  test(`deleteEvent`, async () => {
    vi.spyOn(google.events, 'delete').mockImplementation(
      () => ({ status: 204 }),
    );
    const result = await api.deleteEvent({ id: 'dummy', lng });

    expect(result.isSuccess).toBe(true);
  });
});

test('getEvent returns error EventDeleted for deleted event', async () => {
  vi.spyOn(google.events, 'get').mockImplementation(
    () => ApiResults.deletedEvent,
  );

  const result = await api.getEvent({ id: 'dummy', lng });

  expect(result).toMatchObject({
    isFailure: true,
    error: {
      message: expect.any(String),
      type: 'CalendarApiErrors.EventDeleted',
      status: 410,
    },
  });
});

describe(`common error handling`, () => {
  it.each([
    ['getCalendarName', 'CalendarApiErrors.NotOk', 'calendars', 'get', undefined],
    ['insertEvent', 'CalendarApiErrors.NotOk', 'events', 'insert', { event: getEvent(6), lng }],
    [
      'listEvents',
      'CalendarApiErrors.NotOk',
      'events',
      'list',
      { identifier: createIdentifier(), slot: createSlot() },
    ],
    [
      'getEvent',
      'CalendarApiErrors.NotOkWhenGettingEvent',
      'events',
      'get',
      'dummy',
    ],
    ['nextEvents', 'CalendarApiErrors.NotOk', 'events', 'list', undefined],
    [
      'updateEvent',
      'CalendarApiErrors.NotOk',
      'events',
      'update',
      { event: createEventRetrieved(), lng },
    ],
    [
      'deleteEvent',
      'CalendarApiErrors.NotOk',
      'events',
      'delete',
      { id: 'dummy', lng },
    ],
  ])(
    `%s fails with %s when google errors`,
    async (method, error, gResource, gMethod, args) => {
      // @ts-expect-error
      vi.spyOn(google[gResource], gMethod).mockImplementation(() => ({
        status: 502,
      }));

      // @ts-expect-error
      const result = await api[method](args);

      expect(result).toMatchObject({
        isFailure: true,
        error: {
          message: expect.any(String),
          type: error,
          status: 502,
        },
      });
    },
  );

  it(`fails if calendarId is not set`, async () => {
    const api = new CalendarApi({
      google,
      sendAnalytics,
    });
    await expect(api.getCalendarName(lng)).rejects.toThrowError('calendarId not set');
    const event1 = Event.create({
      name: 'test name',
      description: 'test description',
      slot: Slot.create({
        start: Dat.create({ value: new Date('2000-01-01T00:10:00-03:00') }).value,
        end: Dat.create({ value: new Date('2000-01-01T00:20:00-03:00') }).value,
      }).value,
    });
    await expect(api.insertEvent({ event: event1, lng })).rejects.toThrowError(
      'calendarId not set',
    );
    await expect(
      api.listEvents({ identifier: createIdentifier(), slot: createSlot(), lng }),
    ).rejects.toThrowError('calendarId not set');
    await expect(api.nextEvents(lng)).rejects.toThrowError('calendarId not set');
    const event2 = EventRetrieved.create({
      id: 'test id',
      name: 'test name',
      description: 'test description',
      slot: Slot.create({
        start: Dat.create({ value: '2000-01-01T00:10:00-03:00' }).value,
        end: Dat.create({ value: '2000-01-01T00:20:00-03:00' }).value,
      }).value,
    });
    await expect(api.updateEvent({ event: event2, lng })).rejects.toThrowError(
      'calendarId not set',
    );
    await expect(api.getEvent({ id: 'dummy', lng })).rejects.toThrowError('calendarId not set');
    await expect(api.deleteEvent({ id: 'dummy', lng })).rejects.toThrowError('calendarId not set');
  });

});

describe(`deleteEvent fails if`, () => {
  it(`google throws`, async () => {
    vi.spyOn(google.events, 'delete').mockImplementation(() => {
      throw Error('faked error');
    });
    const result = await api.deleteEvent({id: 'dummy', lng });
    expect(result).toMatchObject({
      isFailure: true,
      error: new CalendarApiErrors.UnexpectedThrow({ calendarId: 'dummy', lng, method: 'deleteEvent' }),
    });
  });
});

describe(`backOff`, () => {
  it.each([
    ['getCalendarName', 'calendars', 'get', undefined, ApiResults.getCalendarSuccess],
    ['insertEvent', 'events', 'insert', { event: getEvent(6), lng }, ApiResults.insertSuccess],
    [
      'listEvents',
      'events',
      'list',
      { identifier: createIdentifier(), slot: createSlot() },
      ApiResults.listSuccess1,
    ],
    [
      'getEvent',
      'events',
      'get',
      'dummy',
      ApiResults.insertSuccess,
    ],
    ['nextEvents', 'events', 'list', undefined, ApiResults.listSuccess1],
    [
      'updateEvent',
      'events',
      'update',
      { event: createEventRetrieved(), lng },
      ApiResults.insertSuccess,
    ],
    [
      'deleteEvent',
      'events',
      'delete',
      { id: 'dummy', lng },
      { status: 204 },
    ],
  ])(
    `%s makes 8 retries when google gives a rate limit exceeded errors`,
    async (method, gResource, gMethod, args, apiResponse) => {
      let attempts = 0;
      // @ts-expect-error opt out ts
      const spy = vi.spyOn(google[gResource], gMethod).mockImplementation(() => {
        attempts++;
        if (attempts < 8)
          throw Error('Rate');
        else
          return apiResponse;
      });

      // @ts-expect-error opt out ts
      const result = await api[method](args);

      expect(spy).toHaveBeenCalledTimes(8);

      expect(result.isSuccess).toBe(true);
    },
  );

  it.each([
    ['getCalendarName', 'calendars', 'get', { lng }],
    ['insertEvent', 'events', 'insert', { event: getEvent(6), lng }],
    [
      'listEvents',
      'events',
      'list',
      { identifier: createIdentifier(), slot: createSlot(), lng },
    ],
    [
      'getEvent',
      'events',
      'get',
      { lng },
    ],
    ['nextEvents', 'events', 'list', { lng }],
    [
      'updateEvent',
      'events',
      'update',
      { event: createEventRetrieved(), lng },
    ],
    [
      'deleteEvent',
      'events',
      'delete',
      { id: 'dummy', lng },
    ],
  ])(
    `%s returns CalendarApiErrors.RateLimitExceeded after the 8th attempt, if google gives all the time rate limit exceeded errors`,
    async (method, gResource, gMethod, args) => {
      // @ts-expect-error opt out ts
      const spy = vi.spyOn(google[gResource], gMethod).mockImplementation(() => {
        throw Error('Rate');
      });

      // @ts-expect-error opt out ts
      const result = await api[method](args);

      expect(spy).toHaveBeenCalledTimes(8);

      expect(result).toMatchObject({
        isFailure: true,
        error: new CalendarApiErrors.RateLimitExceeded({ lng: args.lng, method }),
      });
    },
  );

  it.each([
    ['getCalendarName', 'calendars', 'get', { lng }],
    ['insertEvent', 'events', 'insert', { event: getEvent(6), lng }],
    [
      'listEvents',
      'events',
      'list',
      { identifier: createIdentifier(), slot: createSlot(), lng },
    ],
    [
      'getEvent',
      'events',
      'get',
      { lng },
    ],
    ['nextEvents', 'events', 'list', { lng }],
    [
      'updateEvent',
      'events',
      'update',
      { event: createEventRetrieved(), lng },
    ],
    [
      'deleteEvent',
      'events',
      'delete',
      { id: 'dummy', lng },
    ],
  ])(
    `%s returns whatever error receives after receiving a non rate limit exceeded error`,
    async (method, gResource, gMethod, args) => {
      const maxAttempts = chance.integer({ min: 1, max: 8 });
      let attempts = 0;

      const picked = chance.pickone([
        {
          exception: Error('invalid_grant'),
          error: expect.objectContaining({
            message: expect.any(String),
            type: 'CalendarApiErrors.InvalidGrant',
            status: 401,
          }),
        },
        {
          exception: new Error404(),
          error: expect.objectContaining({
            message: expect.any(String),
            status: 404,
            type: expect.stringMatching(/CalendarApiErrors\.Calendar.*NotFound/),
          }),
        },
        {
          exception: new Error2(),
          error: expect.objectContaining({
            message: expect.any(String),
            type: 'CalendarApiErrors.UnexpectedThrow',
            status: 502,
          }),
        },
      ]);
      // @ts-expect-error opt out ts
      const spy = vi.spyOn(google[gResource], gMethod).mockImplementation(() => {
        attempts++;
        if (attempts < maxAttempts)
          throw Error('Rate');
        else
          throw picked.exception;
      });

      // @ts-expect-error opt out ts
      const result = await api[method](args);

      expect(spy).toHaveBeenCalledTimes(maxAttempts);

      expect(result).toMatchObject({
        isFailure: true,
        error: picked.error,
      });
    },
  );
});

class Error404 extends Error {
  public status = 404;
  public constructor() {
    super('Custom 404 error')
  }
}