import { patch } from '@shared/core/utils';
import { BaseError } from '@shared/core/AppError';
import { Status } from '@shared/core/Status';

export namespace ReservationSrvErrors {
  export class StartOrEndModified extends BaseError {
    public constructor() {
      super({
        message: `Start or end time were modified`,
        status: Status.CONFLICT,
      });
    }
  }
  export class LocationNotFound extends BaseError {
    public constructor() {
      super({
        message: `Location identifier not found in description`,
        status: Status.NOT_FOUND,
      });
    }
  }
}

patch({ ReservationSrvErrors });
