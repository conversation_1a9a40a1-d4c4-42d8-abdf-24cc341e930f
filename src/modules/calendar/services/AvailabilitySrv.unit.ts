import { expect, vi, it, describe, beforeEach, MockInstance, test } from 'vitest';
vi.stubEnv('calendar_clientEmail', 'dummy');
vi.stubEnv('calendar_clientKey', 'dummy');
import {
  CalendarApiFake,
  TimeRange,
  AllDayLong,
  ApiListData,
  ApiResults,
} from './CalendarApiFake';
import { EventRetrieved, formatFromApi } from '../domain/EventRetrieved';
import { Result } from '@shared/core/Result';
import { DateTime } from 'luxon';
import { Identifier } from '../domain/Identifier';
import { Slot } from '../domain/Slot';
import { Dat } from '@shared/core/Dat';
import { N0 } from '@shared/core/N0';
import { AvailabilitySrv } from './AvailabilitySrv';
import {
  Error1,
  Error2,
  expectError2,
  pickLng,
} from '@shared/utils/test';
import { PossibleLngs } from '@shared/utils/utils';
import { Spots } from '../domain/Spots';
import { MaxDaysAhead, ReservationLimits, ReservationLimitsT } from '../external';
import { createEvent } from '../utils/test';

const sendAnalytics = vi.fn();
const lng = pickLng();

const api = new CalendarApiFake();
api.setCalendarId('dummy');

const calendarName = 'calendar name';
const mockGetCalendarName = () =>
  new Promise<Result<string>>((resolve) => {
    resolve(Result.ok(calendarName));
  });

const maxDaysAhead = MaxDaysAhead.MAX;
const minTimeBeforeService = 0;
const availabilitySrv = new AvailabilitySrv({
  api,
  sendAnalytics,
});

let spyOnListEvents: MockInstance<
  (_args: {
    identifier: Identifier;
    slot: Slot;
  }) => Promise<Result<[EventRetrieved, ...EventRetrieved[]] | null>>
>;

const error2 = new Error2();

beforeEach(async () => {
  // reset the number of calls on update made by previous tests
  if (spyOnListEvents) spyOnListEvents.mockClear();
});

describe('getChunks', function () {
  let now: DateTime,
    slot: Slot,
    timeZone: string,
    identifier: Identifier,
    apiData: ApiListData,
    reservationLimits: ReservationLimitsT,
    args: {
      identifier: Identifier;
      slot: Slot;
      reservationLimits: ReservationLimitsT;
      lng: PossibleLngs;
    };
  beforeEach(() => {
    now = DateTime.now();

    slot = Slot.create({
      start: Dat.create({ value: now }).value,
      end: Dat.create({ value: now.plus({ days: 9 }) }).value,
    }).value;

    timeZone = 'America/Argentina/Cordoba';
    identifier = Identifier.create({ value: 'test_identifier' }, sendAnalytics).value;

    reservationLimits = ReservationLimits.create({
      maxDaysAhead: MaxDaysAhead.create({ value: maxDaysAhead }).value,
      minTimeBeforeService: N0.create({ value: minTimeBeforeService }).value,
    }).value;

    args = {
      identifier,
      slot,
      reservationLimits,
      lng,
    };

    apiData = {
      timeZone,
      items: [
        {
          id: 'item1',
          summary: 'title1',
          description: `${identifier.query}:2`,
          start: { dateTime: now.toISO() } as TimeRange, // 0
          end: { dateTime: now.plus({ minutes: 20 }).toISO() } as TimeRange, // toISO: '2023-12-16T14:00:00-03:00'
        },
        {
          id: 'item2',
          summary: 'title2',
          description: `${identifier.query}:2`,
          start: { dateTime: now.plus({ minutes: 20 }).toISO() } as TimeRange, // 1
          end: { dateTime: now.plus({ minutes: 40 }).toISO() } as TimeRange,
        },
        {
          id: 'item3',
          summary: 'title3',
          description: `${identifier.query}:4`,
          start: { dateTime: now.plus({ minutes: 30 }).toISO() } as TimeRange, // 2
          end: { dateTime: now.plus({ minutes: 50 }).toISO() } as TimeRange,
        },
        {
          id: 'item4',
          summary: 'title4',
          description: `${identifier.query}`,
          start: { date: now.plus({ days: 3 }).toISODate() } as AllDayLong, // 3
          end: { date: now.plus({ days: 5 }).toISODate() } as AllDayLong, // toISODate: '2000-01-02'
        },
        {
          id: 'item5',
          summary: 'title5',
          description: `${identifier.query}:5`,
          start: { date: now.plus({ days: 4 }).toISODate() } as AllDayLong, // 4
          end: { date: now.plus({ days: 7 }).toISODate() } as AllDayLong,
        },
        {
          id: 'item6',
          summary: 'title6',
          description: `${identifier.query}:0`,
          start: { date: now.plus({ days: 8 }).toISODate() } as AllDayLong, // 5
          end: { date: now.plus({ days: 9 }).toISODate() } as AllDayLong,
        },
        {
          id: 'item7',
          summary: 'title7',
          description: `${identifier.query}`,
          start: { date: now.plus({ days: 10 }).toISODate() } as AllDayLong, // 6
          end: { date: now.plus({ days: 11 }).toISODate() } as AllDayLong,
        },
      ],
    };

    const mockListEvents = () =>
      new Promise<Result<[EventRetrieved, ...EventRetrieved[]] | null>>(
        (resolve) => {
          resolve(Result.ok(EventRetrieved.listToDomain(apiData)));
        },
      );

    spyOnListEvents = vi
      .spyOn(api, 'listEvents')
      .mockImplementation(mockListEvents);
  });

  describe(`success`, () => {
    it(`succeeds`, async () => {
      const result = await availabilitySrv.getChunks(args);

      const { items: apiItems } = apiData;
      expect(result.isSuccess).toBe(true);
      if (!result.value.chunks) throw Error('chunks is null');
      expect(result.value.chunks.length).toBe(6);
      const resultChunksDto = result.value.chunks.map((c) => c.toDto());
      expect(resultChunksDto).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            slot: {
              start: Dat.create({
                value:
                  formatFromApi({apiData: apiItems[0].start, timeZone}),
              }).value.s,
              end: Dat.create({
                value:
                  formatFromApi({apiData: apiItems[2].start, timeZone}),
              }).value.s,
            },
            spots: 2,
          }),
          expect.objectContaining({
            slot: {
              start: Dat.create({
                value:
                  formatFromApi({apiData: apiItems[2].start, timeZone}),
              }).value.s,
              end: Dat.create({
                value:
                  formatFromApi({apiData: apiItems[1].end, timeZone}),
              }).value.s,
            },
            spots: 6,
          }),
          expect.objectContaining({
            slot: {
              start: Dat.create({
                value:
                  formatFromApi({apiData: apiItems[1].end, timeZone}),
              }).value.s,
              end: Dat.create({
                value:
                  formatFromApi({apiData: apiItems[2].end, timeZone}),
              }).value.s,
            },
            spots: 4,
          }),
          expect.objectContaining({
            slot: {
              start: Dat.create({
                value:
                  formatFromApi({apiData: apiItems[3].start, timeZone}),
              }).value.s,
              end: Dat.create({
                value:
                  formatFromApi({apiData: apiItems[4].start, timeZone}),
              }).value.s,
            },
            spots: 1,
          }),
          expect.objectContaining({
            slot: {
              start: Dat.create({
                value:
                  formatFromApi({apiData: apiItems[4].start, timeZone}),
              }).value.s,
              end: Dat.create({
                value:
                  formatFromApi({apiData: apiItems[3].end, timeZone}),
              }).value.s,
            },
            spots: 6,
          }),
          expect.objectContaining({
            slot: {
              start: Dat.create({
                value:
                  formatFromApi({apiData: apiItems[3].end, timeZone}),
              }).value.s,
              end: Dat.create({
                value:
                  formatFromApi({apiData: apiItems[4].end, timeZone}),
              }).value.s,
            },
            spots: 5,
          }),
        ]),
      );
      expect(resultChunksDto.length).toBe(6);
      if (!result.value.existingEvents) throw Error('existingEvents is null');
      for (let i = 0; i < result.value.existingEvents.length; i++) {
        const existingEvent = result.value.existingEvents[i];
        const apiItem = apiItems[i];
        expect(existingEvent.id).toEqual(apiItem.id);
        expect(existingEvent.name).toEqual(apiItem.summary);
        expect(existingEvent.description).toEqual(apiItem.description);
        const apiStart = formatFromApi({ apiData: apiItem.start, timeZone });
        expect(existingEvent.slot.start.s).toEqual(Dat.create({ value: apiStart }).value.s);
        const apiEnd = formatFromApi({ apiData: apiItem.end, timeZone });
        expect(existingEvent.slot.end.s).toEqual(Dat.create({ value: apiEnd }).value.s);
      }
    });

    it(`corrects start and end times when they're out of reservationLimits`, async () => {
      const api = new CalendarApiFake();
      api.setCalendarId('dummy');

      const outOfBoundariesSlot = Slot.create({
        start: Dat.create({ value: now.minus({ minutes: minTimeBeforeService + 1 }) }).value,
        end: Dat.create({ value: now.plus({ days: maxDaysAhead + 1 }) }).value,
      }).value;

      const mockListEvents = (args: { identifier: Identifier; slot: Slot }) =>
        new Promise<Result<[EventRetrieved, ...EventRetrieved[]] | null>>(
          (resolve) => {
            const { slot } = args;
            const now = DateTime.now();
            expect(slot.start.t).toBeGreaterThanOrEqual(
              outOfBoundariesSlot.start.t,
            );
            expect(slot.start.t).toBeLessThanOrEqual(Dat.create({ value: now }).value.t);
            expect(slot.end.t).toBeLessThanOrEqual(outOfBoundariesSlot.end.t);
            expect(slot.end.t).toBeGreaterThanOrEqual(
              Dat.create({ value: now.plus({ days: maxDaysAhead - 1 }) }).value.t,
            );
            resolve(Result.ok(EventRetrieved.listToDomain(apiData)));
          },
        );

      vi.spyOn(api, 'listEvents').mockImplementation(mockListEvents);

      const calendarSrv = new AvailabilitySrv({
        api,
        sendAnalytics,
      });

      const result = await calendarSrv.getChunks({
        ...args,
        slot: outOfBoundariesSlot,
      });

      expect(result.isSuccess).toBe(true);
      if (!result.value.chunks) throw Error('chunks is null');
      expect(result.value.chunks.length).toBe(7);
    });

    it(`returns null for chunks and existingEvents when api.listEvents gives events without description`, async () => {
      const api = new CalendarApiFake();
      api.setCalendarId('dummy');
      const mockListEvents = () =>
        new Promise<Result<[EventRetrieved, ...EventRetrieved[]] | null>>(
          (resolve) => {
            resolve(
              Result.ok(
                EventRetrieved.listToDomain({
                  timeZone,
                  items: [
                    {
                      id: 'item',
                      start: { dateTime: now.toISO() } as TimeRange,
                      end: {
                        dateTime: now.plus({ minutes: 20 }).toISO(),
                      } as TimeRange,
                    },
                  ],
                }),
              ),
            );
          },
        );

      spyOnListEvents = vi
        .spyOn(api, 'listEvents')
        .mockImplementation(mockListEvents);

      const availabilitySrv = new AvailabilitySrv({
        api,
        sendAnalytics,
      });
      const result = await availabilitySrv.getChunks(args);
      expect(result.isSuccess).toBe(true);
      expect(result.value).toEqual({
        chunks: null,
        existingEvents: null,
      });
    });

    it(`returns null for chunks and existingEvents when api.listEvents gives events without valid spots in description`, async () => {
      const api = new CalendarApiFake();
      api.setCalendarId('dummy');
      const mockListEvents = () =>
        new Promise<Result<[EventRetrieved, ...EventRetrieved[]] | null>>(
          (resolve) => {
            resolve(
              Result.ok(
                EventRetrieved.listToDomain({
                  timeZone,
                  items: [
                    {
                      id: 'item',
                      description: `There are no spots for identifier # ${identifier.value} #location`,
                      start: { dateTime: now.toISO() } as TimeRange,
                      end: {
                        dateTime: now.plus({ minutes: 20 }).toISO(),
                      } as TimeRange,
                    },
                  ],
                }),
              ),
            );
          },
        );

      spyOnListEvents = vi
        .spyOn(api, 'listEvents')
        .mockImplementation(mockListEvents);

      const availabilitySrv = new AvailabilitySrv({
        api,
        sendAnalytics,
      });
      const result = await availabilitySrv.getChunks(args);
      expect(result.isSuccess).toBe(true);
      expect(result.value).toEqual({
        chunks: null,
        existingEvents: null,
      });
    });

    it(`returns null for chunks but returns existingEvents when api.listEvents gives events with 0 spots`, async () => {
      const api = new CalendarApiFake();
      api.setCalendarId('dummy');
      const apiData = {
        timeZone,
        items: [
          {
            id: 'item',
            summary: 'title',
            description: `There are no spots for identifier #${identifier.value}:0`,
            start: { dateTime: now.toISO() } as TimeRange,
            end: { dateTime: now.plus({ minutes: 20 }).toISO() } as TimeRange,
          },
        ],
      };
      const mockListEvents = () =>
        new Promise<Result<[EventRetrieved, ...EventRetrieved[]] | null>>(
          (resolve) => {
            resolve(Result.ok(EventRetrieved.listToDomain(apiData)));
          },
        );

      spyOnListEvents = vi
        .spyOn(api, 'listEvents')
        .mockImplementation(mockListEvents);

      const availabilitySrv = new AvailabilitySrv({
        api,
        sendAnalytics,
      });
      const result = await availabilitySrv.getChunks(args);
      expect(result.isSuccess).toBe(true);
      expect(result.value.chunks).toBe(null);
      if (!result.value.existingEvents) throw Error('existingEvents is null');

      const existingEvent = result.value.existingEvents[0];
      const apiItem = apiData.items[0];
      expect(existingEvent.id).toEqual(apiItem.id);
      expect(existingEvent.name).toEqual(apiItem.summary);
      expect(existingEvent.description).toEqual(apiItem.description);
      expect(existingEvent.slot.start.s).toEqual(
        Dat.create({ value: apiItem.start.dateTime }).value.s,
      );
      expect(existingEvent.slot.end.s).toEqual(
        Dat.create({ value: apiItem.end.dateTime }).value.s,
      );
    });
  });

  describe(`fails if`, () => {
    test(`api.listEvents errors out`, async () => {
      const error1 = new Error1();
      const mockListEvents = () =>
        new Promise<Result<[EventRetrieved]>>((resolve) => {
          resolve(Result.fail(error1));
        });

      spyOnListEvents = vi
        .spyOn(api, 'listEvents')
        .mockImplementation(mockListEvents);

      const result = await availabilitySrv.getChunks(args);

      expect(result).toMatchObject({
        isFailure: true,
        error: {
          message: error1.message,
          type: error1.type,
          status: error1.status,
        },
      });
    });
  });
});

describe('checkGetEvents', function () {
  
  it(`succeeds`, async () => {
    const api = new CalendarApiFake();
    api.setCalendarId('dummy');
    vi.spyOn(api, 'getCalendarName').mockImplementation(mockGetCalendarName);

    const availabilitySrv = new AvailabilitySrv({
      api,
      sendAnalytics,
    });

    const result = await availabilitySrv.checkGetEvents(lng);

    expect(result.isSuccess).toBe(true);
    expect(result.value.name).toBe(calendarName);
    const nextEvents = result.value.nextEvents;
    expect(nextEvents?.length).toBe(ApiResults.listSuccess1.data.items.length);
    const apiData = EventRetrieved.listToDomain(ApiResults.listSuccess1.data)!;
    expect(nextEvents).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          event: apiData[0],
          detected: null,
        }),
        expect.objectContaining({
          event: apiData[1],
          detected: null,
        }),
        expect.objectContaining({
          event: apiData[2],
          detected: [
            {
              identifier: Identifier.create({ value: 'loc1' }, sendAnalytics).value,
              spots: Spots.create(1, sendAnalytics).value,
            },
          ],
        }),
        expect.objectContaining({
          event: apiData[3],
          detected: [
            {
              identifier: Identifier.create({ value: 'loc1' }, sendAnalytics).value,
              spots: Spots.create(4, sendAnalytics).value,
            },
          ],
        }),
        expect.objectContaining({
          event: apiData[4],
          detected: [
            {
              identifier: Identifier.create({ value: 'loc1' }, sendAnalytics).value,
              spots: Spots.create(5, sendAnalytics).value,
            },
          ],
        }),
      ]),
    );
  });

  it(`returns null when there are no next events`, async () => {
    const api = new CalendarApiFake();
    api.setCalendarId('dummy');
    vi.spyOn(api, 'getCalendarName').mockImplementation(mockGetCalendarName);

    const mockNextEvents = () =>
      new Promise<Result<[EventRetrieved, ...EventRetrieved[]] | null>>(
        (resolve) => {
          resolve(Result.ok(null));
        },
      );
    vi.spyOn(api, 'nextEvents').mockImplementation(mockNextEvents);

    const availabilitySrv = new AvailabilitySrv({
      api,
      sendAnalytics,
    });

    const result = await availabilitySrv.checkGetEvents(lng);

    expect(result.isSuccess).toBe(true);
    expect(result.value.name).toBe(calendarName);
    expect(result.value.nextEvents).toBe(null);
  });

  it(`relays getCalendarName error from api`, async () => {
    const api = new CalendarApiFake();
    api.setCalendarId('dummy');
    const mockGetCalendarName = () =>
      new Promise<Result<string>>((resolve) => {
        resolve(Result.fail(new Error2()));
      });

    vi.spyOn(api, 'getCalendarName').mockImplementation(mockGetCalendarName);
    const availabilitySrv = new AvailabilitySrv({
      api,
      sendAnalytics,
    });

    const response = await availabilitySrv.checkGetEvents(lng);

    expectError2(response);
  });
});

describe('checkMakeChanges', () => {
  const mockInsertEvent = () =>
    new Promise<Result<EventRetrieved>>((resolve) => {
      resolve(Result.ok(EventRetrieved.fromApi(ApiResults.insertSuccess.data)));
    });

  it(`succeeds`, async () => {
    const api = new CalendarApiFake();
    api.setCalendarId('dummy');
    api.setApiResult(ApiResults.listSuccess1);
    
    vi.spyOn(api, 'getCalendarName').mockImplementation(mockGetCalendarName);
    vi.spyOn(api, 'insertEvent').mockImplementation(mockInsertEvent);

    const availabilitySrv = new AvailabilitySrv({
      api,
      sendAnalytics,
    });

    const result = await availabilitySrv.checkMakeChanges(lng);

    expect(result.isSuccess).toBe(true);
    expect(result.value.name).toBe(calendarName);
    const nextEvents = result.value.nextEvents;
    expect(nextEvents?.length).toBe(ApiResults.listSuccess1.data.items.length);
    const apiData = EventRetrieved.listToDomain(ApiResults.listSuccess1.data)!;
    expect(nextEvents).toEqual(
      expect.arrayContaining([
        expect.objectContaining({
          event: apiData[0],
          detected: null,
        }),
        expect.objectContaining({
          event: apiData[1],
          detected: null,
        }),
        expect.objectContaining({
          event: apiData[2],
          detected: [
            {
              identifier: Identifier.create({ value: 'loc1' }, sendAnalytics).value,
              spots: Spots.create(1, sendAnalytics).value,
            },
          ],
        }),
        expect.objectContaining({
          event: apiData[3],
          detected: [
            {
              identifier: Identifier.create({ value: 'loc1' }, sendAnalytics).value,
              spots: Spots.create(4, sendAnalytics).value,
            },
          ],
        }),
        expect.objectContaining({
          event: apiData[4],
          detected: [
            {
              identifier: Identifier.create({ value: 'loc1' }, sendAnalytics).value,
              spots: Spots.create(5, sendAnalytics).value,
            },
          ],
        }),
      ]),
    );
  });

  it(`returns null for next events when there aren't`, async () => {
    const api = new CalendarApiFake();
    api.setCalendarId('dummy');
    api.setApiResult(ApiResults.deleteSuccess);
    vi.spyOn(api, 'getCalendarName').mockImplementation(mockGetCalendarName);
    vi.spyOn(api, 'insertEvent').mockImplementation(mockInsertEvent);

    const mockNextEvents = () =>
      new Promise<Result<[EventRetrieved, ...EventRetrieved[]] | null>>(
        (resolve) => {
          resolve(Result.ok(null));
        },
      );
    vi.spyOn(api, 'nextEvents').mockImplementation(mockNextEvents);

    const availabilitySrv = new AvailabilitySrv({
      api,
      sendAnalytics,
    });

    const result = await availabilitySrv.checkMakeChanges(lng);

    expect(result.isSuccess).toBe(true);
    expect(result.value.name).toBe(calendarName);
    expect(result.value.nextEvents).toBe(null);
  });

  it(`relays getCalendarName error from api`, async () => {
    const api = new CalendarApiFake();
    api.setCalendarId('dummy');
    const mockGetCalendarName = () =>
      new Promise<Result<string>>((resolve) => {
        resolve(Result.fail(error2));
      });

    vi.spyOn(api, 'getCalendarName').mockImplementation(mockGetCalendarName);
    const availabilitySrv = new AvailabilitySrv({
      api,
      sendAnalytics,
    });

    const response = await availabilitySrv.checkMakeChanges(lng);

    expectError2(response);
  });

  it(`relays insertEvent error from api`, async () => {
    const api = new CalendarApiFake();
    api.setCalendarId('dummy');
    vi.spyOn(api, 'getCalendarName').mockImplementation(mockGetCalendarName);

    const mockInsertEvent = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.fail(error2));
      });
    vi.spyOn(api, 'insertEvent').mockImplementation(mockInsertEvent);

    const availabilitySrv = new AvailabilitySrv({
      api,
      sendAnalytics,
    });

    const response = await availabilitySrv.checkMakeChanges(lng);

    expectError2(response);
  });
});

describe('insertEvent', () => {
  it(`succeeds`, async () => {
    const api = new CalendarApiFake();
    api.setCalendarId('dummy');

    const mockInsertEvent = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.ok(EventRetrieved.fromApi(ApiResults.insertSuccess.data)));
      });
    vi.spyOn(api, 'insertEvent').mockImplementation(mockInsertEvent);

    const availabilitySrv = new AvailabilitySrv({
      api,
      sendAnalytics,
    });

    const result = await availabilitySrv.insertEvent({
      event: createEvent({}),
      lng,
    });

    expect(result.isSuccess).toBe(true);
    expect(result.value.id).toBe(ApiResults.insertSuccess.data.id);
  });

  it(`relays insert error from api`, async () => {
    const api = new CalendarApiFake();
    api.setCalendarId('dummy');

    const mockInsertEvent = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.fail(error2));
      });
    vi.spyOn(api, 'insertEvent').mockImplementation(mockInsertEvent);

    const availabilitySrv = new AvailabilitySrv({
      api,
      sendAnalytics,
    });

    const response = await availabilitySrv.insertEvent({
      event: createEvent({}),
      lng,
    });

    expectError2(response);
  });
});
