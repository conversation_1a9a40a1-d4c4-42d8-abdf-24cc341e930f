import { expect, it, describe, beforeEach, test, vi } from 'vitest';
import { Dat } from '@shared/core/Dat';
import { EventRetrieved } from '../domain/EventRetrieved';
import { Slot } from '../domain/Slot';
import { calendar } from '@googleapis/calendar';
import { CalendarApi } from './CalendarApi';
import { CalendarsInDb } from '@shared/infra/database/sequelize/migrations/Calendars.json';
import credentials from '../../../../winged-precept-448216-r7-31510f88d470.json';
import { expectErrorResult, pickLng } from '@shared/utils/test';
import {
  createEventRetrieved,
  createIdentifier,
  createSlot,
  getEvent,
} from '../utils/test';
import { PossibleLngs } from '@shared/utils/utils';

const sendAnalytics = vi.fn();

const calendarId = CalendarsInDb[1].id;
let reservationApi: CalendarApi, api: CalendarApi, lng: PossibleLngs;
beforeEach(() => {
  process.env.calendar_clientEmail = credentials.client_email;
  process.env.calendar_clientKey = credentials.private_key;
  api = new CalendarApi({
    google,
    sendAnalytics,
  });
  api.setCalendarId(calendarId);
  reservationApi = new CalendarApi({
    google: calendar({ version: 'v3' }),
    sendAnalytics,
  });
  reservationApi.setCalendarId(CalendarsInDb[1].id);
  lng = pickLng();
});
const google = calendar({ version: 'v3' });

describe(`success`, () => {
  test(`getCalendarName`, async () => {
    const result = await api.getCalendarName(lng);

    expect(result).toMatchObject({
      isSuccess: true,
      _value: 'reservations',
    });
  });
  test(`insertEvent`, async () => {
    const reservationEvent = getEvent(0);
    const result = await api.insertEvent({ event: reservationEvent, lng });

    expect(result.isSuccess).toBe(true);
    const got = await api.getEvent({ id: result.value.id, lng });
    expect(got.value).toMatchObject(
      EventRetrieved.create({
        name: reservationEvent.name,
        description: reservationEvent.description,
        slot: reservationEvent.slot,
        id: expect.any(String),
      }),
    );

    // Clean up
    await api.deleteEvent({ id: result.value.id, lng });
  });
  test(`listEvents & nextEvents`, async () => {
    const identifier = createIdentifier();
    const event1 = getEvent(12, identifier.query);
    const inserted1 = await api.insertEvent({ event: event1, lng });
    expect(inserted1.isSuccess).toBe(true);
    const event2 = getEvent(12, identifier.query);
    const inserted2 = await api.insertEvent({ event: event2, lng });
    expect(inserted2.isSuccess).toBe(true);

    let result = await api.listEvents({
      identifier,
      slot: Slot.create({
        start: event1.slot.start,
        end: Dat.create({ value: event1.slot.start.l.plus({ minute: 65 }) }).value, // event from getEvent has a maximum duration of 60m
      }).value,
      lng,
    });

    expect(result.isSuccess).toBe(true);
    expect(result.value?.length).toEqual(2);
    const expectedEvents = expect.arrayContaining([
      expect.objectContaining({
        name: event1.name,
        description: event1.description,
        slot: event1.slot,
        id: expect.any(String),
      }),
      expect.objectContaining({
        name: event2.name,
        description: event2.description,
        slot: event2.slot,
        id: expect.any(String),
      }),
    ]);
    expect(result.value).toEqual(expectedEvents);

    result = await api.nextEvents(lng);
    expect(result.isSuccess).toBe(true);
    expect(result.value).toEqual(expectedEvents);

    // Clean up
    await api.deleteEvent({ id: inserted1.value.id, lng });
    await api.deleteEvent({ id: inserted2.value.id, lng });
  });
  test(`getEvent`, async () => {
    const event = getEvent(14);
    const inserted = await api.insertEvent({ event, lng });
    expect(inserted.isSuccess).toBe(true);

    const result = await api.getEvent({ id: inserted.value.id, lng });

    expect(result.isSuccess).toBe(true);
    expect(result.value).toMatchObject({
      name: event.name,
      description: event.description,
      slot: event.slot,
      id: expect.any(String),
    });

    // Clean up
    await api.deleteEvent({ id: inserted.value.id, lng });
  });
  test(`updateEvent`, async () => {
    const event = getEvent(14);
    const inserted = await api.insertEvent({ event, lng });
    expect(inserted.isSuccess).toBe(true);
    const insertedId = inserted.value.id;

    let got = await api.getEvent({ id: insertedId, lng });
    expect(got.isSuccess).toBe(true);
    if (!got.value) throw Error('Inserted event not found');

    const update = got.value.updateDescription('updated description');
    const result = await api.updateEvent({ event: update, lng });

    if (!result.isSuccess) console.log('CalendarApi.e2e.ts result', result);
    expect(result.isSuccess).toBe(true);
    got = await api.getEvent({ id: insertedId, lng });
    expect(got.isSuccess).toBe(true);
    expect(got.value).toMatchObject({
      name: update.name,
      description: update.description,
      slot: update.slot,
      id: insertedId,
    });

    // Clean up
    await api.deleteEvent({ id: insertedId, lng });
  });
});

describe(`failure`, () => {
  describe(`user's fault`, () => {
    it.each([
      ['getCalendarName', undefined, 'CalendarApiErrors.CalendarNotFound'],
      ['insertEvent', { event: getEvent(2), lng }, 'CalendarApiErrors.CalendarNotFound'],
      [
        'listEvents',
        { identifier: createIdentifier(), slot: createSlot() },
        'CalendarApiErrors.CalendarNotFound',
      ],
      ['nextEvents', undefined, 'CalendarApiErrors.CalendarNotFound'],
      ['getEvent', { id: 'dummy', lng }, 'CalendarApiErrors.CalendarEventNotFound'],
      [
        'updateEvent',
        { event: createEventRetrieved(), lng },
        'CalendarApiErrors.CalendarEventNotFound',
      ],
    ])(
      `calendar not found for %s`,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      async (method: any, args: unknown, error: string) => {
        const api = new CalendarApi({
          google,
          sendAnalytics,
        });
        api.setCalendarId('incorrectCalendarId');

        // @ts-expect-error
        const result = await api[method](args);

        expect(result.isSuccess).toBe(false);
        expectErrorResult({
          result,
          error,
          code: 404,
        });
      },
    );
    it.each([
      ['getCalendarName', undefined],
      ['insertEvent', { event: getEvent(4), lng }],
      ['listEvents', { identifier: createIdentifier(), slot: createSlot() }],
      ['nextEvents', undefined],
      ['getEvent', { id: 'dummy', lng }],
      ['updateEvent', { event: createEventRetrieved(), lng }],
    ])(`invalid grant for %s`, async (method: string, args: unknown) => {
      process.env.calendar_clientEmail = '<EMAIL>';
      const api = new CalendarApi({
        google,
        sendAnalytics,
      });
      api.setCalendarId(calendarId);

      // @ts-expect-error
      const result = await api[method](args);

      expect(result.isSuccess).toBe(false);
      expectErrorResult({
        result,
        error: 'CalendarApiErrors.InvalidGrant',
        code: 401,
      });
    });
  });
  describe(`server error`, () => {
    it.each([
      ['getCalendarName', undefined],
      ['insertEvent', getEvent(8)],
      ['listEvents', { identifier: createIdentifier(), slot: createSlot() }],
      ['nextEvents', undefined],
      ['getEvent', 'dummy'],
      ['updateEvent', createEventRetrieved()],
    ])(`unexpected error thrown for %s`, async () => {
      process.env.calendar_clientKey = 'invalidKey';
      const api = new CalendarApi({
        google,
        sendAnalytics,
      });
      api.setCalendarId(calendarId);

      const reservationEvent = getEvent(6);
      const result = await api.insertEvent({ event: reservationEvent, lng });

      expect(result.isSuccess).toBe(false);
      expectErrorResult({
        result,
        error: 'CalendarApiErrors.UnexpectedThrow',
        code: 502,
      });
    });
  });
});
