import { Result } from '@shared/core/Result';
import {
  GetSpotsForReservationArgs,
  GetAvailableTimesArgs,
  ICalendarsSrv,
  IAvailabilitySrv,
  IReservationSrv,
} from './ICalendarsSrv';
import { SlotWithSpots } from '../domain/SlotWithSpots';
import { TimeWithSpots } from '../domain/TimeWithSpots';
import { Identifier } from '../domain/Identifier';
import { Spots } from '../domain/Spots';
import { EventRetrieved } from '../domain/EventRetrieved';
import { Slot } from '../domain/Slot';
import { nonEmpty, PossibleLngs } from '@shared/utils/utils';
import { ReservationLimitsT } from '../external';
import { IContextProvider } from '@shared/context/IContextProvider';

// Uses both services: AvailabilitySrv and ReservationSrv
export class CalendarsSrv implements ICalendarsSrv {
  private availabilitySrv: IAvailabilitySrv;
  private reservationSrv: IReservationSrv;
  private sendAnalytics: IContextProvider['sendAnalytics'];

  public constructor(args: {
    availabilitySrv: IAvailabilitySrv;
    reservationSrv: IReservationSrv;
    sendAnalytics: IContextProvider['sendAnalytics'];
  }) {
    const { availabilitySrv, reservationSrv, sendAnalytics } = args;
    this.availabilitySrv = availabilitySrv;
    this.reservationSrv = reservationSrv;
    this.sendAnalytics = sendAnalytics;
  }

  public setCalendarIds(args: {
    availabilityCalendarId: string;
    reservationCalendarId: string;
  }) {
    const { availabilityCalendarId, reservationCalendarId } = args;
    this.availabilitySrv.setCalendarId(availabilityCalendarId);
    this.reservationSrv.setCalendarId(reservationCalendarId);
  }

  public async getAvailableTimes(
    args: GetAvailableTimesArgs,
  ): Promise<Result<[TimeWithSpots, ...TimeWithSpots[]] | null>> {
    const { identifier, slot, timeReference, every, reservationLimits, duration, lng, maxTimes } =
      args;

    const availableChunksOrError = await this.getAvailableChunks({
      identifier,
      slot,
      reservationLimits,
      lng,
    });
    if (availableChunksOrError.isFailure)
      return Result.fail(availableChunksOrError.error!);
    const availableChunks = availableChunksOrError.value.chunks;

    if (!availableChunks) return Result.ok(null);

    return Result.ok(
      await TimeWithSpots.getPossibleTimes({
        timeReference,
        every,
        duration,
        chunks: nonEmpty(availableChunks),
        maxTimes,
      }, this.sendAnalytics),
    );
  }

  public async getSpotsForReservation(args: GetSpotsForReservationArgs): Promise<
    Result<{
      spots: Spots | null;
      existingReservation: EventRetrieved | null;
    }>
  > {
    const { identifier, slot, reservationLimits, lng } = args;

    const availableChunksOrError = await this.getAvailableChunks({
      identifier,
      slot,
      reservationLimits,
      lng,
    });
    if (availableChunksOrError.isFailure)
      return Result.fail(availableChunksOrError.error!);
    const { chunks, existingEvents } = availableChunksOrError.value;

    let existingReservation = null;
    if (existingEvents) {
      const sameTime = existingEvents.filter((event) => {
        return (
          slot.start.t === event.slot.start.t && slot.end.t === event.slot.end.t
        );
      });

      existingReservation = sameTime.filter((event) => {
        const { descriptionPlainText } = event;
        if (descriptionPlainText === null) return false;
        return Identifier.parse({
          eventDescriptionPlainText: descriptionPlainText,
          identifier,
        }, this.sendAnalytics);
      })[0]; // If the event has the same start and end time, and contains the event identifier, it's assumed that the first event that matches these conditions is the same reservation we are booking for, so we have to update that event instead of creating a new event.
    }

    if (!chunks) return Result.ok({ spots: null, existingReservation });

    return Result.ok({
      spots: TimeWithSpots.getSpotsForReservation({ slot, chunks }, this.sendAnalytics),
      existingReservation,
    });
  }

  // Get the chunks in which there are spots available. The resulting chunks are sorted.
  private async getAvailableChunks(args: {
    identifier: Identifier;
    slot: Slot;
    reservationLimits: ReservationLimitsT;
    lng: PossibleLngs;
  }): Promise<
    Result<{
      chunks: [SlotWithSpots, ...SlotWithSpots[]] | null;
      existingEvents: [EventRetrieved, ...EventRetrieved[]] | null;
    }>
  > {
    const { identifier, slot, reservationLimits, lng } = args;

    // Chunkify, sort and consolidate by calling availabilitySrv.getChunks > SlotWithSpots.chunkify
    const availabilityChunksOrError = await this.availabilitySrv.getChunks({
      identifier,
      slot,
      reservationLimits,
      lng,
    });
    if (availabilityChunksOrError.isFailure)
      return Result.fail(availabilityChunksOrError.error!);
    const availabilityChunks = availabilityChunksOrError.value.chunks;

    // Chunkify, sort and consolidate by calling reservationSrv.getChunks > SlotWithSpots.chunkify
    const reservationChunksOrError = await this.reservationSrv.getChunks({
      identifier,
      slot,
      reservationLimits,
      lng,
    });
    if (reservationChunksOrError.isFailure)
      return Result.fail(reservationChunksOrError.error!);
    const reservationChunks = reservationChunksOrError.value.chunks;

    const chunks = SlotWithSpots.getAvailableChunks({
      availability: availabilityChunks,
      reserved: reservationChunks,
    }, this.sendAnalytics);
    return Result.ok({
      chunks,
      existingEvents: reservationChunksOrError.value.existingEvents,
    });
  }
}
