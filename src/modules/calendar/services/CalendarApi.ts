import { Event } from '../domain/Event';
import { Result } from '@shared/core/Result';
import { ICalendarApi } from './ICalendarApi';
import { CalendarApiErrors } from './CalendarApiErrors';
import { Identifier } from '../domain/Identifier';
import { EventRetrieved } from '../domain/EventRetrieved';
import { Slot } from '../domain/Slot';
import { calendar_v3 } from '@googleapis/calendar';
// https://googleapis.dev/nodejs/googleapis/latest/calendar/classes/Calendar.html
// https://googleapis.dev/nodejs/googleapis/latest/calendar/classes/Resource$Events.html
import { JWT } from 'google-auth-library';
import {
  commonBackOffOptions,
  isRateLimitExceeded,
  PossibleLngs,
} from '@shared/utils/utils';
import { backOff } from 'exponential-backoff';
import { BaseError } from '@shared/core/AppError';
import { IContextProvider } from '@shared/context/IContextProvider';

/*
Throws de métodos de Google calendar_v3.Calendar:
.calendars.get({
  // Si la combinación de calendarId y eventId no existe, throwea
  calendarId,
  eventId: 'nonExistent',
  // Si no tiene autorización para el calendar, throwea
  auth,
})
.events.insert({
  // Sin calendarId, throwea
  calendarId,
  // Si no tiene autorización para el calendar, throwea
  auth,
  // Sin requestBody.start & end, throwea
  requestBody: {
    start: {
      dateTime: event.slot.start.j.toISOString(),
    },
    end: {
      dateTime: event.slot.end.j.toISOString(),
    },
  },
});
.events.list({
  // Sin calendarId, throwea
  calendarId,
  // Si no tiene autorización, throwea
  auth,
})
.events.get({
  // Sin calendarId, throwea
  calendarId,
  // Si no tiene autorización para el calendar, throwea
  auth,
  // Si no existe el eventId, throwea
  eventId,
});
.events.update({
  // Sin calendarId, throwea (ver error 1). Con el calendarId incorrecto, throwea (ver error 1.1)
  calendarId,
  // Si no auth, throwea (ver error 2). Caso bad auth.key ver error 3. Caso auth.email inexistente o sin permiso ver error 4.
  auth,
  // Si no existe el eventId, throwea. Ver error 5
  eventId,
  // Sin requestBody.start & end, throwea
  requestBody: {
    start: {
      dateTime: event.slot.start.j.toISOString(),
    },
    end: {
      dateTime: event.slot.end.j.toISOString(),
    },
  },
})
Determinar cuando se trata de los errores 1.1, 4 y 5 ya que puede ser una mal configuración por parte del usuario que debe ser advertida para que la corrija. Para los otros errores, como el usuario no los puede arreglar, no hace falta.
Error 1:
Error: Missing required parameters: calendarId
Error 1.1:
GaxiosError: Not Found
{
  status: 404,
  code: 404,
  errors: [ { domain: 'global', reason: 'notFound', message: 'Not Found' } ]
}

Error 2:
GaxiosError: Login Required
{
  status: 401,
  code: 401,
  errors: [
    {
      message: 'Login Required.',
      domain: 'global',
      reason: 'required',
      location: 'Authorization',
      locationType: 'header'
    }
  ]
}
Error 3:
Error: error:1E08010C:DECODER routines::unsupported
    at Sign.sign (node:internal/crypto/sig:128:29)
    at Object.sign (/Users/<USER>/desarrollo/turnero/02calendarApi/node_modules/jwa/index.js:152:45)
    at Object.jwsSign [as sign] (/Users/<USER>/desarrollo/turnero/02calendarApi/node_modules/jws/lib/sign-stream.js:32:24)
    at GoogleToken.requestToken (/Users/<USER>/desarrollo/turnero/02calendarApi/node_modules/gtoken/build/src/index.js:224:31)
    at GoogleToken.getTokenAsyncInner (/Users/<USER>/desarrollo/turnero/02calendarApi/node_modules/gtoken/build/src/index.js:158:21)
    at GoogleToken.getTokenAsync (/Users/<USER>/desarrollo/turnero/02calendarApi/node_modules/gtoken/build/src/index.js:137:55)
    at GoogleToken.getToken (/Users/<USER>/desarrollo/turnero/02calendarApi/node_modules/gtoken/build/src/index.js:96:21)
    at JWT.refreshTokenNoCache (/Users/<USER>/desarrollo/turnero/02calendarApi/node_modules/google-auth-library/build/src/auth/jwtclient.js:165:36)
    at JWT.refreshToken (/Users/<USER>/desarrollo/turnero/02calendarApi/node_modules/google-auth-library/build/src/auth/oauth2client.js:150:24)
    at JWT.getRequestMetadataAsync (/Users/<USER>/desarrollo/turnero/02calendarApi/node_modules/google-auth-library/build/src/auth/oauth2client.js:295:28)
{
  library: 'DECODER routines',
  reason: 'unsupported',
  code: 'ERR_OSSL_UNSUPPORTED'
}
Error 4:
GaxiosError: invalid_grant: Invalid grant: account not found
Error 5:
Not Found
{
  status: 404,
  code: 404,
  errors: [ { domain: 'global', reason: 'notFound', message: 'Not Found' } ]
}
*/

/* Decidí no indicar que el error es potencialmente fixable, ver nota en Reservation.cancel() @ reservation
export function isFixableError(errorType: string) {
  const fixableErrors = [
    'CalendarApiErrors.CalendarNotFound',
    'CalendarApiErrors.CalendarEventNotFound',
    'CalendarApiErrors.InvalidGrant',
  ];
  return fixableErrors.includes(errorType);
} */

export class CalendarApi implements ICalendarApi {
  private readonly common: {
    auth: JWT;
    calendarId?: string;
  };

  private api: calendar_v3.Calendar;
  private readonly sendAnalytics: IContextProvider['sendAnalytics'];

  public constructor(args: {
    google: calendar_v3.Calendar,
    sendAnalytics: IContextProvider['sendAnalytics'];
  }) {
    const { google, sendAnalytics } = args;
    // Add all process.env used:
    const {calendar_clientEmail, calendar_clientKey} = process.env;
    if (!calendar_clientEmail || !calendar_clientKey) {
      console.log('process.env', process.env);
      throw new Error(`Undefined env var!`);
    }
    this.common = {
      auth: new JWT({
        email: calendar_clientEmail,
        key: calendar_clientKey,
        scopes: [
          // set the right scope
          'https://www.googleapis.com/auth/calendar',
          'https://www.googleapis.com/auth/calendar.events',
        ],
      }),
    };
    this.api = google;
    this.sendAnalytics = sendAnalytics;
  }

  public setCalendarId(calendarId: string) {
    this.common.calendarId = calendarId;
  }

  public async getCalendarName(lng: PossibleLngs): Promise<Result<string>> {
    if (!this.common.calendarId) throw Error(`calendarId not set`);

    const method = 'getCalendarName';
    let got;
    try {
      got = await backOff(() =>
          this.api.calendars.get(this.common),
        this.backOffOptions(method));
    } catch (e) {
      await this.sendAnalytics({
        e,
        method,
      });

      const notFound = this.isExceptionForCalendarNotFound({e, lng});
      if (notFound) return Result.fail(notFound);

      return this.processException({e, lng, method});
    }
    if (!isSuccess(got.status)) {  // returns 200
      console.log(`ERROR when getting calendar info`, got);
      return this.notOk(lng);
    }
    if (!got.data || got.data.summary === null || got.data.summary === undefined) {
      console.log(
        `ERROR when getting calendar info, there is no data.summary`,
        got.data,
      );
      await this.sendAnalytics({
        method,
        got,
      });
      return Result.fail(new CalendarApiErrors.NoName({
        calendarId: this.common.calendarId,
        lng,
      }));
    }
    return Result.ok(got.data.summary);
  }

  public async insertEvent(args: {
    event: Event;
    lng: PossibleLngs
  }): Promise<Result<EventRetrieved>> {
    const {event, lng} = args;
    if (!this.common.calendarId) throw Error(`calendarId not set`);

    let inserted;
    const method = 'insertEvent';
    try {
      inserted = await backOff(() =>
          this.api.events.insert({
            requestBody: {
              summary: event.name,
              description: event.description,
              start: {
                dateTime: event.slot.start.j.toISOString(),
              },
              end: {
                dateTime: event.slot.end.j.toISOString(),
              },
            },
            ...this.common,
          }),
        this.backOffOptions(method));
    } catch (e) {
      console.log(`Error when inserting event`, e);
      await this.sendAnalytics({
        e,
        method,
      });

      const notFound = this.isExceptionForCalendarNotFound({e, lng});
      if (notFound) return Result.fail(notFound);

      return this.processException({e, lng, method});
    }

    if (!isSuccess(inserted.status)) {  // returns 200
      console.log(`ERROR when inserting event`, inserted);
      await this.sendAnalytics({
        method,
        inserted,
      });
      return this.notOk(lng);
    }
    
    return Result.ok(EventRetrieved.fromApi(inserted.data));
  }

  // Retrieves the Google calendar events that contain Identifier.query
  public async listEvents(args: {
    identifier: Identifier;
    slot: Slot;
    lng: PossibleLngs;
  }): Promise<Result<[EventRetrieved, ...EventRetrieved[]] | null>> {
    if (!this.common.calendarId) throw Error(`calendarId not set`);
    const {identifier, slot, lng} = args;

    let listed;
    const method = 'listEvents';
    try {
      listed = await backOff(() =>
          this.api.events.list({
            timeMin: slot.start.j.toISOString(), // Includes the event if timeMin: equals event start or falls inside the event. Doesn't include the event if timeMin equals event end.
            timeMax: slot.end.j.toISOString(), // Doesn't include the event if timeMax equals event start. Includes the event if timeMax: falls inside the event or after the event end.
            showDeleted: false,
            singleEvents: true,
            orderBy: 'startTime',
            q: identifier.query, // Finds the events if #<identifier> is in any place, for example: summary, location, description. It has some quirks, check domain/Identifier.ts:checkIdentifier
            ...this.common,
          }),
        this.backOffOptions(method));
    } catch (e) {
      console.log(`Error when listing events`, e);
      await this.sendAnalytics({
        e,
        method,
      });

      const notFound = this.isExceptionForCalendarNotFound({e, lng});
      if (notFound) return Result.fail(notFound);

      return this.processException({e, lng, method});
    }

    // console.log(`Returned from api list`, listed);
    if (!isSuccess(listed.status)) {  // returns 200
      console.log(`ERROR when listing events`);
      await this.sendAnalytics({
        method,
        listed,
      });
      return this.notOk(lng);
    }
    // console.log(`items`, listed.data.items);
    if (!listed.data.items || !listed.data.items.length) return Result.ok(null);

    return Result.ok(EventRetrieved.listToDomain(listed.data));
  }

  public async nextEvents(lng: PossibleLngs): Promise<
    Result<[EventRetrieved, ...EventRetrieved[]] | null>
  > {
    if (!this.common.calendarId) throw Error(`calendarId not set`);

    let listed;
    const method = 'nextEvents';
    try {
      listed = await backOff(() =>
          this.api.events.list({
            timeMin: new Date().toISOString(), // Includes the event if timeMin: equals event start or falls inside the event. Doesn't include the event if timeMin equals event end.
            showDeleted: false,
            singleEvents: true,
            orderBy: 'startTime',
            ...this.common,
          }),
        this.backOffOptions(method));
    } catch (e) {
      console.log(`Error when listing next events`, e);
      await this.sendAnalytics({
        e,
        method,
      });

      const notFound = this.isExceptionForCalendarNotFound({e, lng});
      if (notFound) return Result.fail(notFound);

      return this.processException({e, lng, method});
    }

    console.log(`Returned from api list`, listed);
    if (!isSuccess(listed.status)) {  // returns 200
      console.log(`ERROR when listing events`);
      await this.sendAnalytics({
        method,
        listed,
      });
      return this.notOk(lng);
    }
    console.log(`items`, listed.data.items);
    if (!listed.data.items || !listed.data.items.length) return Result.ok(null);

    return Result.ok(EventRetrieved.listToDomain(listed.data));
  }

  public async getEvent(args: {
    id: string;
    lng: PossibleLngs
  }): Promise<Result<EventRetrieved>> {
    const {id, lng} = args;
    if (!this.common.calendarId) throw Error(`calendarId not set`);

    let got;
    const method = 'getEvent';
    try {
      got = await backOff(() =>
          this.api.events.get({
            eventId: id,
            ...this.common,
          }),
        this.backOffOptions(method));
    } catch (e) {
      console.log(`Error when getting event`, e);
      await this.sendAnalytics({
        e,
        method,
      });

      const notFound = this.isExceptionForEventNotFound({e, eventId: id, lng});
      if (notFound) return Result.fail(notFound);

      return this.processException({e, lng, method});
    }

    console.log(`Returned from api get`, got);
    if (!isSuccess(got.status)) { // returns 200
      console.log(
        `ERROR when getting event ${id} from calendar ${this.common.calendarId}`,
      );
      await this.sendAnalytics({
        method,
        got,
      });
      return Result.fail(
        new CalendarApiErrors.NotOkWhenGettingEvent({
          calendarId: this.common.calendarId,
          id,
          lng,
        }),
      );
    }

    if (got.data.status === 'cancelled') {
      console.log(`Event ${id} is cancelled`);
      return Result.fail(new CalendarApiErrors.EventDeleted(lng));
    }
    if (got.data.status !== 'confirmed') {
      console.log(`Event status is neither confirmed nor cancelled?`, got);
      await this.sendAnalytics({
        method,
        got,
      });
      
    }

    console.log(`got`, got.data);
    return Result.ok(EventRetrieved.fromApi(got.data));
  }

  public async updateEvent(args: {
    event: EventRetrieved;
    lng: PossibleLngs;
  }): Promise<Result<EventRetrieved>> {
    const {event, lng} = args;
    if (!this.common.calendarId) throw Error(`calendarId not set`);

    let updated;
    const method = 'updateEvent';
    try {
      updated = await backOff(() =>
          this.api.events.update({
            ...this.common,
            eventId: event.id,
            requestBody: {
              ...event.props,
              summary: event.name,
              description: event.description,
              start: {
                dateTime: event.slot.start.j.toISOString(),
              },
              end: {
                dateTime: event.slot.end.j.toISOString(),
              },
            },
          }),
        this.backOffOptions(method));
    } catch (e) {
      console.log(`Error when updating event`, e);
      await this.sendAnalytics({
        method,
        e,
      });

      const notFound = this.isExceptionForEventNotFound({e, eventId: event.id, lng});
      if (notFound) return Result.fail(notFound);

      return this.processException({e, lng, method});
    }

    if (!isSuccess(updated.status)) { // returns 200
      console.log(`ERROR when updating event`, updated);
      await this.sendAnalytics({
        method,
        updated,
      });
      return this.notOk(lng);
    }
    return Result.ok(EventRetrieved.fromApi(updated.data));
  }

  public async deleteEvent(args: {
    id: string,
    lng: PossibleLngs,
  }): Promise<Result<void>> {
    const {id, lng} = args;
    if (!this.common.calendarId) throw Error(`calendarId not set`);
    let deleted;
    const method = 'deleteEvent';
    try {
      deleted = await backOff(() =>
          this.api.events.delete({
            eventId: id,
            ...this.common,
          }),
        this.backOffOptions(method));
    } catch (e) {
      console.log(`Error when deleting event ${id}`, e);
      await this.sendAnalytics({
        method,
        e,
      });

      const notFound = this.isExceptionForEventNotFound({e, eventId: id, lng});
      if (notFound) return Result.fail(notFound);

      return this.processException({e, lng, method});
    }

    if (!isSuccess(deleted.status)) { // returns 204
      console.log(`ERROR status when deleting event ${id}`, deleted);
      await this.sendAnalytics({
        method,
        deleted,
      });
      return this.notOk(lng);
    }
    return Result.ok(undefined);
  }

  private notOk(lng: PossibleLngs) {
    return Result.fail(new CalendarApiErrors.NotOk({
      calendarId: this.common.calendarId!,
      lng,
    }));
  }

  private async processException(args: { e: unknown; lng: PossibleLngs, method: string }) {
    const {e, lng, method} = args;
    // @ts-expect-error opt out ts
    if (e && e.message) console.log({message: e.message});

    // @ts-expect-error opt out ts
    if (e && e.message && typeof e.message === 'string' && e.message.includes('invalid_grant')) {
      return Result.fail(
        new CalendarApiErrors.InvalidGrant({
          email: this.common.auth.email!,
          calendarId: this.common.calendarId!,
          lng,
          method,
        }),
      );
    }

    if (isRateLimitExceeded(e)) { // "Rate Limit Exceeded" https://developers.google.com/calendar/api/guides/errors?hl=es-419#403_rate_limit_exceeded
      return Result.fail(
        new CalendarApiErrors.RateLimitExceeded({lng, method}),
      );
    }

    const calendarId = this.common.calendarId!;
    await this.sendAnalytics({
      method,
      e: 'CalendarApiErrors.UnexpectedThrow',
      calendarId,
    });
    return Result.fail(
      new CalendarApiErrors.UnexpectedThrow({
        calendarId,
        lng,
        method,
      }),
    );
  }

  private isExceptionForCalendarNotFound(args: { e: unknown; lng: PossibleLngs }): BaseError | null {
    const {e, lng} = args;
    // @ts-expect-error opt out ts
    if (e && e.status === 404)
        return new CalendarApiErrors.CalendarNotFound({
          calendarId: this.common.calendarId!,
          email: this.common.auth.email!,
          lng,
        });
    else
      return null;
  }

  private isExceptionForEventNotFound(args: {
    e: unknown;
    eventId: string;
    lng: PossibleLngs
  }): BaseError | null {
    const {e, eventId, lng} = args;
    // @ts-expect-error opt out ts
    if (e && e.status === 404)
      return new CalendarApiErrors.CalendarEventNotFound({
          calendarId: this.common.calendarId!,
          email: this.common.auth.email!,
          eventId,
          lng,
        });
    else
      return null;
  }
  
  private backOffOptions = (method: string) => ({
    ...commonBackOffOptions,
    retry: async (e: unknown, attempt: number) => {
      const rateExceeded = isRateLimitExceeded(e);
      await this.sendAnalytics({
        method,
        e,
        attempt,
        rateExceededInCalendar: rateExceeded,
      });
      if (rateExceeded) {
        console.log(`Rate limit exceeded error persists in CalendarApi.${method}, backOff attempt number ${attempt}`, e);
        return true;
      } else {
        console.log(`A different error from rate limit exceeded in CalendarApi.${method}, backOff stops at attempt number ${attempt}`, e);
        return false;
      }
    },
  });
}

function isSuccess(status: number) {
  return [200, 201, 204].includes(status);
}