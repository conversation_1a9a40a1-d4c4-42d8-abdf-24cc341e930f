import { Result } from '@shared/core/Result';
import { Identifier } from '../domain/Identifier';
import { EveryT, ReservationLimitsT, TimeReferenceT } from '../external';
import { TimeWithSpots } from '../domain/TimeWithSpots';
import { Spots } from '../domain/Spots';
import { EventRetrieved } from '../domain/EventRetrieved';
import { Event } from '../domain/Event';
import { Slot } from '../domain/Slot';
import { PositiveInt } from '@shared/core/PositiveInt';
import { NextEvent } from '../domain/NextEvent';
import { SlotWithSpots } from '../domain/SlotWithSpots';
import { Dat } from '@shared/core/Dat';
import { PossibleLngs } from '@shared/utils/utils';

export type GetAvailableTimesArgs = {
  identifier: Identifier;
  slot: Slot;
  timeReference: TimeReferenceT;
  every: EveryT;
  reservationLimits: ReservationLimitsT;
  duration: PositiveInt;
  lng: PossibleLngs;
  maxTimes: PositiveInt,
};
export type GetSpotsForReservationArgs = {
  identifier: Identifier;
  slot: Slot;
  reservationLimits: ReservationLimitsT;
  lng: PossibleLngs;
};

export type ICalendarsSrv = {
  getAvailableTimes(
    args: GetAvailableTimesArgs,
  ): Promise<Result<[TimeWithSpots, ...TimeWithSpots[]] | null>>;
  getSpotsForReservation(args: GetSpotsForReservationArgs): Promise<
    Result<{
      spots: Spots | null;
      existingReservation: EventRetrieved | null;
    }>
  >;
  setCalendarIds(args: {
    availabilityCalendarId: string;
    reservationCalendarId: string;
  }): void;
};

type CheckResult = Result<{
  name: string;
  nextEvents: [NextEvent, ...NextEvent[]] | null;
}>;
export type IAvailabilitySrv = {
  setCalendarId(calendarId: string): void;
  getChunks(args: {
    identifier: Identifier;
    slot: Slot;
    reservationLimits: ReservationLimitsT;
    lng: PossibleLngs;
  }): Promise<
    Result<{
      chunks: [SlotWithSpots, ...SlotWithSpots[]] | null;
      existingEvents: [EventRetrieved, ...EventRetrieved[]] | null;
    }>
  >;
  checkGetEvents(lng: PossibleLngs): Promise<CheckResult>;
  checkMakeChanges(lng: PossibleLngs): Promise<CheckResult>;
  insertEvent(args: { event: Event; lng: PossibleLngs}): Promise<Result<EventRetrieved>>;
  deleteEvent(args: { id: string, lng: PossibleLngs }): Promise<Result<void>>;
};
export type IReservationSrv = IAvailabilitySrv & {
  updateEvent(args: { event: EventRetrieved; lng: PossibleLngs}): Promise<Result<EventRetrieved>>;
  getEvent(args: {
    eventId: string;
    identifier: Identifier;
    start: Dat;
    end: Dat;
    lng: PossibleLngs;
  }): Promise<Result<EventRetrieved>>;
};
