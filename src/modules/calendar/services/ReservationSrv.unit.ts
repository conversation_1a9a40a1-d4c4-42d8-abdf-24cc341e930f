import { expect, vi, it, describe, beforeEach } from 'vitest';
vi.stubEnv('calendar_clientEmail', 'dummy');
vi.stubEnv('calendar_clientKey', 'dummy');
import { CalendarApiFake } from './CalendarApiFake';
import { EventRetrieved } from '../domain/EventRetrieved';
import { Result } from '@shared/core/Result';
import { Identifier } from '../domain/Identifier';
import { Slot } from '../domain/Slot';
import {
  expectErrorResult,
  expectError2,
  pickLng,
  Error2,
  Error1,
} from '@shared/utils/test';
import { ReservationSrv } from './ReservationSrv';
import { Dat } from '@shared/core/Dat';
import { DateTime } from 'luxon';
import { PossibleLngs } from '@shared/utils/utils';

const sendAnalytics = vi.fn();

const api = new CalendarApiFake();
api.setCalendarId('dummy');

let lng: PossibleLngs;
beforeEach(() => {
  lng = pickLng();
});

describe(`getEvent`, () => {
  const now = DateTime.now();
  const start = Dat.create({ value: now }).value;
  const end = Dat.create({ value: now.plus({ minutes: 10 }) }).value;
  const identifierValue = 'test';
  const eventArgs = {
    name: 'testName',
    slot: Slot.create({ start, end }).value,
    id: 'testId',
    description: `#${identifierValue}`,
  };
  const event = EventRetrieved.create(eventArgs);

  const getEventArgs = {
    eventId: 'dummy',
    identifier: Identifier.create({ value: identifierValue }, sendAnalytics).value,
    start,
    end,
    lng,
  };

  it(`finds an event`, async () => {
    const mockGetEvent = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.ok(event));
      });
    vi.spyOn(api, 'getEvent').mockImplementation(mockGetEvent);

    const reservationSrv = new ReservationSrv({
      api,
      sendAnalytics,
    });

    const result = await reservationSrv.getEvent(getEventArgs);

    expect(result.isSuccess).toBe(true);
    expect(result.value?.equals(event)).toBe(true);
  });

  it(
    `relays the error received from api.getEvent`,
    async () => {
      const error1 = new Error1();
      const mockGetEvent = () =>
        new Promise<Result<EventRetrieved>>((resolve) => {
          resolve(Result.fail(error1));
        });
      vi.spyOn(api, 'getEvent').mockImplementation(mockGetEvent);

      const reservationSrv = new ReservationSrv({
        api,
        sendAnalytics,
      });

      const result = await reservationSrv.getEvent(getEventArgs);

      expectErrorResult({
        result,
        code: error1.status,
        error: error1.type,
      });
    },
  );

  it(`api.getEvent fails`, async () => {
    const error2 = new Error2();
    const mockGetEvent = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.fail(error2));
      });
    vi.spyOn(api, 'getEvent').mockImplementation(mockGetEvent);

    const reservationSrv = new ReservationSrv({
      api,
      sendAnalytics,
    });

    const result = await reservationSrv.getEvent(getEventArgs);

    expectError2(result);
  });
});
