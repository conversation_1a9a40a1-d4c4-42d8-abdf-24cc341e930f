import { Result } from '@shared/core/Result';
import { ICalendarApi } from './ICalendarApi';
import { Identifier } from '../domain/Identifier';
import { EventRetrieved } from '../domain/EventRetrieved';
import { PossibleLngs } from '@shared/utils/utils';
import { IReservationSrv } from './ICalendarsSrv';
import { AvailabilitySrv } from './AvailabilitySrv';
import { Dat } from '@shared/core/Dat';
import { ReservationSrvErrors } from './ReservationSrvErrors';
import { IContextProvider } from '@shared/context/IContextProvider';

export class ReservationSrv extends AvailabilitySrv implements IReservationSrv {
  public constructor(args: {
    api: ICalendarApi,
    sendAnalytics: IContextProvider['sendAnalytics'],
  }) {
    const { api, sendAnalytics } = args;
    super({
      api,
      sendAnalytics,
    });
  }

  public async updateEvent(args: {
     event: EventRetrieved;
     lng: PossibleLngs;
   }): Promise<Result<EventRetrieved>> {
    return await this.api.updateEvent(args);
  }

  // Returns the reservation event if eventId is found and identifier, start and end match
  // This method is called only by CancelReservationEvent use case, when api.getEvent doesn't find the event, it returns CalendarApiErrors.CalendarEventNotFound
  public async getEvent(args: {
    eventId: string;
    identifier: Identifier;
    start: Dat;
    end: Dat;
    lng: PossibleLngs;
  }): Promise<Result<EventRetrieved>> {
    const { identifier, start, end, eventId, lng } = args;
    const eventOrError = await this.api.getEvent({ id: eventId, lng });
    if (eventOrError.isFailure)
      return Result.fail(eventOrError.error!);
    const event = eventOrError.value;

    if (!event.slot.start.equals(start) || !event.slot.end.equals(end))
      return Result.fail(new ReservationSrvErrors.StartOrEndModified());

    if (
      event.descriptionPlainText === null ||
      !Identifier.parse({ identifier, eventDescriptionPlainText: event.descriptionPlainText }, this.sendAnalytics)
    )
      return Result.fail(new ReservationSrvErrors.LocationNotFound());

    return Result.ok(event);
  }
}