import { expect, test, it, describe, vi } from 'vitest';
vi.stubEnv('calendar_clientEmail', 'dummy');
vi.stubEnv('calendar_clientKey', 'dummy');
import { Identifier } from '../domain/Identifier';
import { CalendarApiFake } from './CalendarApiFake';
import { DateTime } from 'luxon';
import { Result } from '@shared/core/Result';
import { Slot } from '../domain/Slot';
import { SlotWithSpots } from '../domain/SlotWithSpots';
import { Spots } from '../domain/Spots';
import {
  TimeReference,
  Every,
  ReservationLimits,
  MaxDaysAhead,
} from '../external';
import { CalendarsSrv } from './CalendarsSrv';
import { EventRetrieved } from '../domain/EventRetrieved';
import { Dat } from '@shared/core/Dat';
import { N0 } from '@shared/core/N0';
import { PositiveInt } from '@shared/core/PositiveInt';
import { nonEmpty } from '@shared/utils/utils';
import { AvailabilitySrv } from './AvailabilitySrv';
import { ReservationSrv } from './ReservationSrv';
import {
  Error1,
  pickLng,
} from '@shared/utils/test';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const sendAnalytics = vi.fn()

const nextDay = DateTime.now().plus({ day: 1 }).startOf('day');
const identifier = Identifier.create({ value: 'test_identifier' }, sendAnalytics).value;

type Dto = {
  slot: {
    start: Dat;
    end: Dat;
  };
  spots: number;
};

function convertToSlotWithSpots(dto: Dto) {
  return SlotWithSpots.create({
    slot: Slot.create({ start: dto.slot.start, end: dto.slot.end }).value,
    spots: Spots.create(dto.spots, sendAnalytics).value,
  });
}

function getExistingEvents(dtos: Dto[]) {
  const items = dtos.map((e) => {
    return {
      summary: chance.word(),
      start: { dateTime: e.slot.start.j.toString() },
      end: { dateTime: e.slot.end.j.toString() },
      id: chance.string(),
      randomProperty: chance.string(),
      description: `${identifier.query}:${e.spots}`,
    };
  });
  const apiData = {
    timeZone: 'America/Argentina/Cordoba',
    items,
  };
  return EventRetrieved.listToDomain(apiData);
}

function getCalendarsSrv(args: {
  availabilityDto: Dto[];
  reservedDto: Dto[];
  existingEvents?: boolean;
  /*availabilityExistingEvents?: [EventRetrieved, ...EventRetrieved[]] | null;
  reservedExistingEvents?: [EventRetrieved, ...EventRetrieved[]] | null;*/
}) {
  // const { availabilityDto, reservedDto, availabilityExistingEvents, reservedExistingEvents } = args;
  const { availabilityDto, reservedDto, existingEvents } = args;
  const availabilitySrv = new AvailabilitySrv({
    api: new CalendarApiFake(),
    sendAnalytics: sendAnalytics,
  });
  const reservationSrv = new ReservationSrv({
    api: new CalendarApiFake(),
    sendAnalytics: sendAnalytics,
  });

  vi.spyOn(availabilitySrv, 'getChunks').mockImplementation(
    () =>
      new Promise<
        Result<{
          chunks: [SlotWithSpots, ...SlotWithSpots[]] | null;
          existingEvents: [EventRetrieved, ...EventRetrieved[]] | null;
        }>
      >((resolve) => {
        // resolve(Result.ok({ chunks: nonEmpty(availabilityDto.map(convertToSlotWithSpots)), existingEvents: availabilityExistingEvents ?? null })); // existingEvents are dismissed by getAvailableTimes;
        resolve(
          Result.ok({
            chunks: nonEmpty(availabilityDto.map(convertToSlotWithSpots)),
            existingEvents: existingEvents
              ? getExistingEvents(availabilityDto)
              : null,
          }),
        ); // existingEvents are dismissed by getAvailableTimes;
      }),
  );
  vi.spyOn(reservationSrv, 'getChunks').mockImplementation(
    () =>
      new Promise<
        Result<{
          chunks: [SlotWithSpots, ...SlotWithSpots[]] | null;
          existingEvents: [EventRetrieved, ...EventRetrieved[]] | null;
        }>
      >((resolve) => {
        // resolve(Result.ok({ chunks: nonEmpty(reservedDto.map(convertToSlotWithSpots)), existingEvents: reservedExistingEvents ?? null })); // existingEvents are dismissed by getAvailableTimes;
        resolve(
          Result.ok({
            chunks: nonEmpty(reservedDto.map(convertToSlotWithSpots)),
            existingEvents: existingEvents ? getExistingEvents(reservedDto) : null,
          }),
        ); // existingEvents are dismissed by getAvailableTimes;
      }),
  );

  const calendarsSrv = new CalendarsSrv({ availabilitySrv, reservationSrv, sendAnalytics });
  calendarsSrv.setCalendarIds({
    availabilityCalendarId: 'dummy',
    reservationCalendarId: 'dummy',
  });

  return calendarsSrv;
}

const lng = pickLng();
const error1 = new Error1();

describe(`getAvailableTimes`, () => {
  const validArgs = {
    identifier,
    slot: Slot.create({
      start: Dat.create({ value: nextDay }).value,
      end: Dat.create({ value: nextDay.plus({ days: 7 }) }).value,
    }).value,
    timeReference: TimeReference.create({
      hour: 0,
      minute: 0,
      zone: 'America/Cordoba',
    }).value,
    every: Every.create({ value: 10 }).value,
    duration: PositiveInt.create({ value: 10 }).value,
    reservationLimits: ReservationLimits.create({
      maxDaysAhead: MaxDaysAhead.create({ value: MaxDaysAhead.MAX }).value,
      minTimeBeforeService: N0.create({ value: 0 }).value,
    }).value,
    lng,
    maxTimes: PositiveInt.create({ value: 999999 }).value,
  };

  describe(`succeeds`, () => {
    test(`with existing times`, async () => {
      const availabilityDto = [
        {
          slot: {
            start: Dat.create({ value: nextDay }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 30 }) }).value,
          },
          spots: 1,
        },
        {
          slot: {
            start: Dat.create({ value: nextDay.plus({ minutes: 60 }) }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 90 }) }).value,
          },
          spots: 2,
        },
      ];
      const reservedDto = [
        {
          slot: {
            start: Dat.create({ value: nextDay.plus({ minutes: 20 }) }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 30 }) }).value,
          },
          spots: 1,
        },
        {
          slot: {
            start: Dat.create({ value: nextDay.plus({ minutes: 60 }) }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 70 }) }).value,
          },
          spots: 1,
        },
      ];

      const calendarsSrv = getCalendarsSrv({ availabilityDto, reservedDto });
      const result = await calendarsSrv.getAvailableTimes(validArgs);

      expect(result.isSuccess).toBe(true);
      if (!result.value) throw Error(`result.value is null`);
      expect(result.value.length).toBe(5);
      const dtoResult = result.value.map((time) => time.toDto());
      expect(dtoResult).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            time: Dat.create({ value: nextDay }).value.s,
            spots: 1,
          }),
          expect.objectContaining({
            time: Dat.create({ value: nextDay.plus({ minute: 10 }) }).value.s,
            spots: 1,
          }),
          expect.objectContaining({
            time: Dat.create({ value: nextDay.plus({ minute: 60 }) }).value.s,
            spots: 1,
          }),
          expect.objectContaining({
            time: Dat.create({ value: nextDay.plus({ minute: 70 }) }).value.s,
            spots: 2,
          }),
          expect.objectContaining({
            time: Dat.create({ value: nextDay.plus({ minute: 80 }) }).value.s,
            spots: 2,
          }),
        ]),
      );
    });

    test(`without finding times`, async () => {
      const availabilityDto = [
        {
          slot: {
            start: Dat.create({ value: nextDay }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 30 }) }).value,
          },
          spots: 1,
        },
        {
          slot: {
            start: Dat.create({ value: nextDay.plus({ minutes: 60 }) }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 90 }) }).value,
          },
          spots: 2,
        },
      ];
      const reservedDto = [
        {
          slot: {
            start: Dat.create({ value: nextDay }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 30 }) }).value,
          },
          spots: 1,
        },
        {
          slot: {
            start: Dat.create({ value: nextDay.plus({ minutes: 60 }) }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 90 }) }).value,
          },
          spots: 2,
        },
      ];

      const calendarsSrv = getCalendarsSrv({ availabilityDto, reservedDto });
      const result = await calendarsSrv.getAvailableTimes(validArgs);

      expect(result.isSuccess).toBe(true);
      expect(result.value).toBe(null);
    });
  });

  describe(`fails when`, () => {
    test(`availabilitySrv.getChunks errors out and it relays the error`, async () => {
      const availabilitySrv = new AvailabilitySrv({
        api: new CalendarApiFake(),
        sendAnalytics,
      });
      const reservationSrv = new ReservationSrv({
        api: new CalendarApiFake(),
        sendAnalytics,
      });

      vi.spyOn(availabilitySrv, 'getChunks').mockImplementation(
        () =>
          new Promise<
            Result<{
              chunks: [SlotWithSpots, ...SlotWithSpots[]] | null;
              existingEvents: [EventRetrieved, ...EventRetrieved[]] | null;
            }>
          >((resolve) => {
            resolve(Result.fail(error1));
          }),
      );

      const calendarsSrv = new CalendarsSrv({ availabilitySrv, reservationSrv, sendAnalytics });
      calendarsSrv.setCalendarIds({
        availabilityCalendarId: 'dummy',
        reservationCalendarId: 'dummy',
      });
      const result = await calendarsSrv.getAvailableTimes(validArgs);

      expect(result).toMatchObject({
        isFailure: true,
        error: {
          message: error1.message,
          type: error1.type,
          status: error1.status,
        },
      });
    });

    it(`reservationSrv.getChunks errors out and it relays the error`, async () => {
      const availabilitySrv = new AvailabilitySrv({
        api: new CalendarApiFake(),
        sendAnalytics,
      });
      const reservationSrv = new ReservationSrv({
        api: new CalendarApiFake(),
        sendAnalytics,
      });

      vi.spyOn(availabilitySrv, 'getChunks').mockImplementation(
        () =>
          new Promise<
            Result<{
              chunks: [SlotWithSpots, ...SlotWithSpots[]] | null;
              existingEvents: [EventRetrieved, ...EventRetrieved[]] | null;
            }>
          >((resolve) => {
            resolve(Result.ok({ chunks: null, existingEvents: null }));
          }),
      );
      vi.spyOn(reservationSrv, 'getChunks').mockImplementation(
        () =>
          new Promise<
            Result<{
              chunks: [SlotWithSpots, ...SlotWithSpots[]] | null;
              existingEvents: [EventRetrieved, ...EventRetrieved[]] | null;
            }>
          >((resolve) => {
            resolve(Result.fail(error1));
          }),
      );

      const calendarsSrv = new CalendarsSrv({ availabilitySrv, reservationSrv, sendAnalytics });
      calendarsSrv.setCalendarIds({
        availabilityCalendarId: 'dummy',
        reservationCalendarId: 'dummy',
      });
      const result = await calendarsSrv.getAvailableTimes(validArgs);

      expect(result).toMatchObject({
        isFailure: true,
        error: {
          message: error1.message,
          type: error1.type,
          status: error1.status,
        },
      });
    });
  });
});

describe(`getSpotsForReservation`, () => {
  const validArgs = {
    identifier,
    slot: Slot.create({
      start: Dat.create({ value: nextDay }).value,
      end: Dat.create({ value: nextDay.plus({ minutes: 30 })}).value,
    }).value,
    reservationLimits: ReservationLimits.create({
      maxDaysAhead: MaxDaysAhead.create({ value: MaxDaysAhead.MAX }).value,
      minTimeBeforeService: N0.create({ value: 0 }).value,
    }).value,
    lng,
  };

  describe(`succeeds`, () => {
    test(`without existing reservation`, async () => {
      // Spots available: 0-10 5, 10-15 3, 15-20 4, 20-25 5, 25-30 6 => 3
      const availabilityDto = [
        {
          slot: {
            start: Dat.create({ value: nextDay }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 15 }) }).value,
          },
          spots: 5,
        },
        {
          slot: {
            start: Dat.create({ value: nextDay.plus({ minutes: 15 }) }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 50 }) }).value,
          },
          spots: 6,
        },
      ];
      const reservedDto = [
        {
          slot: {
            start: Dat.create({ value: nextDay.plus({ minutes: 10 }) }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 20 }) }).value,
          },
          spots: 2,
        },
        {
          slot: {
            start: Dat.create({ value: nextDay.plus({ minutes: 20 }) }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 25 }) }).value,
          },
          spots: 1,
        },
      ];

      const calendarsSrv = getCalendarsSrv({
        availabilityDto,
        reservedDto,
        existingEvents: true,
      });
      const result = await calendarsSrv.getSpotsForReservation(validArgs);

      expect(result.isSuccess).toBe(true);
      expect(result.value.spots?.value).toBe(3);
      expect(result.value.existingReservation).toBe(undefined);
    });

    test(`with existing reservation`, async () => {
      const availabilityDto = [
        {
          slot: {
            start: Dat.create({ value: nextDay }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 30 }) }).value,
          },
          spots: 6,
        },
      ];
      const reservedDto = [
        {
          slot: {
            start: Dat.create({ value: nextDay }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 30 }) }).value,
          },
          spots: 2,
        },
      ];

      const calendarsSrv = getCalendarsSrv({
        availabilityDto,
        reservedDto,
        existingEvents: true,
      });
      const result = await calendarsSrv.getSpotsForReservation(validArgs);

      expect(result.isSuccess).toBe(true);
      expect(result.value.spots?.value).toBe(4);
      expect(
        result.value.existingReservation?.slot.start.equals(
          reservedDto[0].slot.start,
        ),
      ).toBe(true);
      expect(
        result.value.existingReservation?.slot.end.equals(reservedDto[0].slot.end),
      ).toBe(true);
    });

    test(`without finding spots`, async () => {
      const availabilityDto = [
        {
          slot: {
            start: Dat.create({ value: nextDay }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 15 }) }).value,
          },
          spots: 2,
        },
        {
          slot: {
            start: Dat.create({ value: nextDay.plus({ minutes: 15 }) }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 50 }) }).value,
          },
          spots: 1,
        },
      ];
      const reservedDto = [
        {
          slot: {
            start: Dat.create({ value: nextDay }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 20 }) }).value,
          },
          spots: 2,
        },
        {
          slot: {
            start: Dat.create({ value: nextDay.plus({ minutes: 20 }) }).value,
            end: Dat.create({ value: nextDay.plus({ minutes: 30 }) }).value,
          },
          spots: 1,
        },
      ];

      const calendarsSrv = getCalendarsSrv({
        availabilityDto,
        reservedDto,
        existingEvents: true,
      });
      const result = await calendarsSrv.getSpotsForReservation(validArgs);

      expect(result.isSuccess).toBe(true);
      expect(result.value.spots).toBe(null);
      expect(result.value.existingReservation).toBe(undefined);
    });
  });

  describe(`fails when`, () => {
    test(`availabilitySrv.getChunks errors out and it relays the error`, async () => {
      const availabilitySrv = new AvailabilitySrv({
        api: new CalendarApiFake(),
        sendAnalytics,
      });
      const reservationSrv = new ReservationSrv({
        api: new CalendarApiFake(),
        sendAnalytics,
      });

      vi.spyOn(availabilitySrv, 'getChunks').mockImplementation(
        () =>
          new Promise<
            Result<{
              chunks: [SlotWithSpots, ...SlotWithSpots[]] | null;
              existingEvents: [EventRetrieved, ...EventRetrieved[]] | null;
            }>
          >((resolve) => {
            resolve(Result.fail(error1));
          }),
      );

      const calendarsSrv = new CalendarsSrv({ availabilitySrv, reservationSrv, sendAnalytics });
      calendarsSrv.setCalendarIds({
        availabilityCalendarId: 'dummy',
        reservationCalendarId: 'dummy',
      });
      const result = await calendarsSrv.getSpotsForReservation(validArgs);

      expect(result).toMatchObject({
        isFailure: true,
        error: {
          message: error1.message,
          type: error1.type,
          status: error1.status,
        },
      });
    });

    it(`reservationSrv.getChunks errors out and it relays the error`, async () => {
      const availabilitySrv = new AvailabilitySrv({
        api: new CalendarApiFake(),
        sendAnalytics,
      });
      const reservationSrv = new ReservationSrv({
        api: new CalendarApiFake(),
        sendAnalytics,
      });

      vi.spyOn(availabilitySrv, 'getChunks').mockImplementation(
        () =>
          new Promise<
            Result<{
              chunks: [SlotWithSpots, ...SlotWithSpots[]] | null;
              existingEvents: [EventRetrieved, ...EventRetrieved[]] | null;
            }>
          >((resolve) => {
            resolve(Result.ok({ chunks: null, existingEvents: null }));
          }),
      );
      vi.spyOn(reservationSrv, 'getChunks').mockImplementation(
        () =>
          new Promise<
            Result<{
              chunks: [SlotWithSpots, ...SlotWithSpots[]] | null;
              existingEvents: [EventRetrieved, ...EventRetrieved[]] | null;
            }>
          >((resolve) => {
            resolve(Result.fail(error1));
          }),
      );

      const calendarsSrv = new CalendarsSrv({ availabilitySrv, reservationSrv, sendAnalytics });
      calendarsSrv.setCalendarIds({
        availabilityCalendarId: 'dummy',
        reservationCalendarId: 'dummy',
      });
      const result = await calendarsSrv.getSpotsForReservation(validArgs);

      expect(result).toMatchObject({
        isFailure: true,
        error: {
          message: error1.message,
          type: error1.type,
          status: error1.status,
        },
      });
    });
  });
});
