import { patch } from '@shared/core/utils';
import { BaseError } from '@shared/core/AppError';
import { Status } from '@shared/core/Status';
import { getLng, PossibleLngs } from '@shared/utils/utils';

export namespace CalendarApiErrors {
  export class NoName extends BaseError {
    public constructor(args: { calendarId: string; lng: PossibleLngs }) {
      const { calendarId, lng } = args
      const t = trans[getLng(lng)];
      super({
        message: `${t.noName} ${calendarId}`,
        status: Status.NOT_FOUND,
      });
    }
  }
  export class UnexpectedThrow extends BaseError {
    public constructor(args: { calendarId: string; lng: PossibleLngs; method: string }) {
      const { calendarId, lng, method } = args
      const t = trans[getLng(lng)];
      super({
        message: `${t.unexpectedThrow} ${calendarId} (${method} ${t.failed}).`,
        status: Status.BAD_GATEWAY,
      });
    }
  }
  export class NotOk extends BaseError {
    public constructor(args: { calendarId: string; lng: PossibleLngs }) {
      const { calendarId, lng } = args
      const t = trans[getLng(lng)];
      super({
        message: `${t.notOk} ${calendarId}`,
        status: Status.BAD_GATEWAY,
      });
    }
  }

  export class NotOkWhenGettingEvent extends BaseError {
    public constructor(args: { calendarId: string; id: string; lng: PossibleLngs }) {
      const { calendarId, id, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.notOkWhenGettingEvent({ calendarId, id }),
        status: Status.BAD_GATEWAY,
      });
    }
  }

  export class EventDeleted extends BaseError {
    public constructor(lng: PossibleLngs) {
      const t = trans[getLng(lng)];
      super({
        message: t.eventDeleted,
        status: Status.GONE,
      });
    }
  }

  export class CalendarNotFound extends BaseError {
    public constructor(args: { calendarId: string; email: string; lng: PossibleLngs }) {
      const { calendarId, email, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: `${t.calendarNotFound({calendarId, email})}`,
        status: Status.NOT_FOUND,
      });
    }
  }
  export class InvalidGrant extends BaseError {
    public constructor(args: { email: string; calendarId: string; lng: PossibleLngs; method: string }) {
      const { email, calendarId, lng, method } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.invalidGrant({ method, calendarId, email }),
        status: Status.UNAUTHORIZED,
      });
    }
  }
  export class RateLimitExceeded extends BaseError {
    public constructor(args: { method: string; lng: PossibleLngs }) {
      const { lng, method } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.rateLimitExceeded(method),
        status: Status.TOO_MANY_REQUESTS,
      });
    }
  }
  export class CalendarEventNotFound extends BaseError {
    public constructor(args: {
      calendarId: string;
      eventId: string;
      email: string;
      lng: PossibleLngs
    }) {
      const { calendarId, eventId, email, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.calendarEventNotFound({ calendarId, email, eventId }),
        status: Status.NOT_FOUND,
      });
    }
  }
}

const trans = {
  en: {
    startIsInvalid(v: string) {
      return `Start field is invalid: ${v}`;
    },
    businessNotFound(v: string) {
      return `Business with id ${v} not found`;
    },
    calendarNotFound(args: { calendarId: string; email: string }) {
      const { calendarId, email } = args;
      return `Check your calendar ${calendarId} exists and you granted permissions to ${email}`;
    },
    notOkWhenGettingEvent(args: { calendarId: string; id: string }) {
      const { calendarId, id } = args;
      return `An error occurred when trying to get event ${id} from calendar ${calendarId}`;
    },
    invalidGrant(args: { calendarId: string; email: string; method: string }) {
      const { calendarId, email, method } = args;
      return `You need to grant permissions to email ${email} for your calendar ${calendarId} (${method} failed).`;
    },
    calendarEventNotFound(args: { calendarId: string; email: string, eventId: string }) {
      const { calendarId, email, eventId } = args;
      return `Check you have event ${eventId} in your calendar ${calendarId}, and you granted permissions to ${email}`;
    },
    noName: 'No name found for calendar',
    unexpectedThrow: 'Unexpected error thrown by calendar',
    failed: 'failed',
    notOk: 'Google returned a not ok status for calendar',
    eventDeleted: 'Event was deleted.',
    rateLimitExceeded(method: string) {
      return `Google Calendar Rate limit exceeded (${method} failed).`;
    },
  },
  es: {
    startIsInvalid(v: string) {
      return `El campo de inicio es inválido: ${v}`;
    },
    businessNotFound(v: string) {
      return `Negocio con id ${v} no encontrado`;
    },
    calendarNotFound(args: { calendarId: string; email: string }) {
      const { calendarId, email } = args;
      return `Verifique que su calendario ${calendarId} existe y que compartió permisos a ${email}`;
    },
    notOkWhenGettingEvent(args: { calendarId: string; id: string }) {
      const { calendarId, id } = args;
      return `Ocurrió un error al intentar obtener el evento ${id} del calendario ${calendarId}`;
    },
    invalidGrant(args: { calendarId: string; email: string; method: string }) {
      const { calendarId, email, method } = args;
      return `Necesita otorgar permisos al email ${email} a su calendario ${calendarId} (${method} falló).`;
    },
    calendarEventNotFound(args: { calendarId: string; email: string, eventId: string }) {
      const { calendarId, email, eventId } = args;
      return `Verifique que tiene el evento ${eventId} en su calendario ${calendarId}, y que compartió permisos a ${email}`;
    },
    noName: 'No se encontró un nombre para el calendario',
    unexpectedThrow: 'Error inesperado lanzado por el calendario',
    failed: 'falló',
    notOk: 'Google devolvió un estado no ok para el calendario',
    eventDeleted: 'El evento fue borrado.',
    rateLimitExceeded(method: string) {
      return `Límite de peticiones a Google Calendar excedido (${method} falló).`;
    },
  },
};

patch({ CalendarApiErrors });
