import { Slot } from '../domain/Slot';
import { Result } from '@shared/core/Result';
import { ICalendarApi } from './ICalendarApi';
import { Identifier } from '../domain/Identifier';
import { SlotWithSpots } from '../domain/SlotWithSpots';
import { EventRetrieved } from '../domain/EventRetrieved';
import { Dat } from '@shared/core/Dat';
import { nonEmpty, PossibleLngs } from '@shared/utils/utils';
import { IAvailabilitySrv } from './ICalendarsSrv';
import { NextEvent } from '../domain/NextEvent';
import { ReservationLimitsT } from '../external';
import { IContextProvider } from '@shared/context/IContextProvider';
import { Event } from '../domain/Event';
import { tomorrowPlus } from '../utils/utils';

export class AvailabilitySrv implements IAvailabilitySrv {
  protected api: ICalendarApi;
  protected readonly sendAnalytics: IContextProvider['sendAnalytics'];

  public constructor(args: {
    api: ICalendarApi,
    sendAnalytics: IContextProvider['sendAnalytics'];
  }) {
    const { api, sendAnalytics } = args;
    this.api = api;
    this.sendAnalytics = sendAnalytics;
  }

  public setCalendarId(calendarId: string) {
    this.api.setCalendarId(calendarId);
  }

  // Chunkify, sort and consolidate by calling SlotWithSpots.chunkify
  public async getChunks(args: {
    identifier: Identifier;
    slot: Slot;
    reservationLimits: ReservationLimitsT;
    lng: PossibleLngs;
  }): Promise<
    Result<{
      chunks: [SlotWithSpots, ...SlotWithSpots[]] | null;
      existingEvents: [EventRetrieved, ...EventRetrieved[]] | null;
    }>
  > {
    const { identifier, reservationLimits, lng } = args;
    let { slot } = args;

    const nowTime = new Date().getTime();

    let correctedStart = slot.start;
    const earliestPossibleTime =
      nowTime + reservationLimits.minTimeBeforeService.value * 60 * 1000;
    if (slot.start.t < earliestPossibleTime) {
      correctedStart = Dat.create({ value: earliestPossibleTime }).value;
      console.log(
        `Slot start before the earliest possible time, so it's moved to the earliest possible time`,
        {
          originalStart: slot.start.s,
          correctedStart: correctedStart.s,
        },
      );
    }

    let correctedEnd = slot.end;
    const maxDateAhead = new Date(
      nowTime + reservationLimits.maxDaysAhead.value * 1000 * 60 * 60 * 24,
    );
    if (slot.end.t > maxDateAhead.getTime()) {
      correctedEnd = Dat.create({ value: maxDateAhead }).value;
      console.log(
        `Slot end after the max date ahead, so it's moved to the max date ahead`,
        {
          originalEnd: slot.end.s,
          correctedEnd: correctedEnd.s,
        },
      );
    }
    slot = Slot.create({
      start: correctedStart,
      end: correctedEnd,
    }).value;

    const eventsOrError = await this.api.listEvents({ identifier, slot, lng });
    if (eventsOrError.isFailure)
      return Result.fail(eventsOrError.error!);
    const apiEvents = eventsOrError.value;

    if (!apiEvents)
      return Result.ok({
        chunks: null,
        existingEvents: null,
      });

    const _chunks: SlotWithSpots[] = [];

    const validEvents: EventRetrieved[] = [];
    for (const event of apiEvents) {
      // Don't "return Result.fail(parsedOrError.error!)" in any of the following situations: no description or identifier not found in description. This scenario will happen if the identifier is put in the event title/name/summary, in that case, just dismiss the event.
      if (event.descriptionPlainText === null) {
        console.log(
          `Event ${event.id} with Identifier.query ${identifier.query} found when querying Google Calendar but without description`,
          { event, identifier },
        );
        await this.sendAnalytics({
          event,
          identifier,
        })
        continue;
      }
      const spots = Identifier.parse({
        identifier,
        eventDescriptionPlainText: event.descriptionPlainText,
      }, this.sendAnalytics);
      if (!spots) {
        const msg = `Identifier.query ${identifier.query} found when querying Google Calendar but not when searching in description`;
        console.log(
          msg,
          { event, identifier },
        );
        await this.sendAnalytics({
          msg,
          event,
          identifier,
        })
        continue;
      }

      validEvents.push(event); // Add event because for CreateReservationEvent use case we want to update the description of the event, for example from 0 reserved spots to 1 reserved spot

      if (spots.value === 0) {
        // Don't add this chunk as this is used by GetAvailableTimes & CreateReservationEvent, and a chunk with 0 spots won't have any effect
        console.log(
          `Event ${event.id} with Identifier.query ${identifier.query} found when querying Google Calendar but with 0 spots`,
          { event, identifier },
        );
        continue;
      }
      _chunks.push(
        SlotWithSpots.create({
          slot: event.slot,
          spots,
        }),
      );
    }
    const existingEvents = validEvents.length ? nonEmpty(validEvents) : null;
    // Chunkify, sort and consolidate by calling SlotWithSpots.chunkify
    const chunks = _chunks.length
      ? SlotWithSpots.chunkify({ slots: nonEmpty(_chunks), range: slot }, this.sendAnalytics)
      : null;

    return Result.ok({
      chunks,
      existingEvents,
    });
  }

  public async checkGetEvents(lng: PossibleLngs): Promise<
    Result<{
      name: string;
      nextEvents: [NextEvent, ...NextEvent[]] | null;
    }>
  > {
    const nameOrError = await this.api.getCalendarName(lng);
    if (nameOrError.isFailure) {
      const msg = `Error when getting calendar name`;
      console.log(msg, JSON.stringify(nameOrError, null, 2));
      await this.sendAnalytics({
        msg,
        nameOrError,
      });
      return Result.fail(nameOrError.error!);
    }
    const name = nameOrError.value;

    const apiEventsOrError = await this.api.nextEvents(lng);
    if (apiEventsOrError.isFailure) {
      const msg = 'Error when getting next events';
      console.log(msg, JSON.stringify(apiEventsOrError, null, 2));
      await this.sendAnalytics({
        msg,
        apiEventsOrError,
      });
      return Result.fail(apiEventsOrError.error!);
    }
    const apiEvents = apiEventsOrError.value;

    if (!apiEvents) return Result.ok({ name, nextEvents: null });

    const nextEvents = nonEmpty(
      apiEvents.map((event) => {
        let detected = null;
        if (event.descriptionPlainText)
          detected = Identifier.detectIdentifiersWithSpots(event.descriptionPlainText, this.sendAnalytics);
        return NextEvent.create({ event, detected });
      }),
    );

    return Result.ok({ name, nextEvents });
  }

  public async checkMakeChanges(lng: PossibleLngs): Promise<
    Result<{
      name: string;
      nextEvents: [NextEvent, ...NextEvent[]] | null;
    }>
  > {

    const gotOrError = await this.checkGetEvents(lng);
    if (gotOrError.isFailure) return Result.fail(gotOrError.error!);
    const { name, nextEvents } = gotOrError.value;

    const insertedOrError = await this.insertEvent({
      event: Event.create({
        name: 'test event name to check make changes permission',
        description: 'test event description to check make changes permission',
        slot: Slot.create({
          start: tomorrowPlus(),
          end: tomorrowPlus(30), // the event lasts 30m
        }).value,
      }),
      lng,
    });
    if (insertedOrError.isFailure) {
      const msg = 'Error when inserting event';
      const e = insertedOrError.error!;
      console.log(msg, e);
      await this.sendAnalytics({
        msg,
        e,
      });
      return Result.fail(e);
    }

    const deletedOrError = await this.deleteEvent({ id: insertedOrError.value.id, lng });
    if (deletedOrError.isFailure) {
      const msg = 'Error when deleting event';
      console.log(msg, deletedOrError);
      const e = deletedOrError.error!;
      await this.sendAnalytics({
        msg,
        e,
      });
      return Result.fail(e);
    }

    return Result.ok({ name, nextEvents });
  }

  public async insertEvent(args: { event: Event; lng: PossibleLngs }): Promise<Result<EventRetrieved>> {
    return await this.api.insertEvent(args);
  }

  public async deleteEvent(args: {
    id: string;
    lng: PossibleLngs;
  }): Promise<Result<void>> {
    return await this.api.deleteEvent(args);
  }
}
