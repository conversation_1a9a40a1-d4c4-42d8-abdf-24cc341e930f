import { Event } from '../domain/Event';
import { Result } from '@shared/core/Result';
import { Identifier } from '../domain/Identifier';
import { EventRetrieved } from '../domain/EventRetrieved';
import { Slot } from '../domain/Slot';
import { PossibleLngs } from '@shared/utils/utils';

export interface ICalendarApi {
  // Considero que debería devolver un Result ya que es posible que de error de autorización.
  getCalendarName(lng: PossibleLngs): Promise<Result<string>>;
  insertEvent(args: { event: Event; lng: PossibleLngs }): Promise<Result<EventRetrieved>>;
  updateEvent(args: {
    event: EventRetrieved;
    lng: PossibleLngs;
  }): Promise<Result<EventRetrieved>>;
  listEvents(args: {
    identifier: Identifier;
    slot: Slot;
    lng: PossibleLngs;
  }): Promise<Result<[EventRetrieved, ...EventRetrieved[]] | null>>;
  nextEvents(lng: PossibleLngs): Promise<Result<[EventRetrieved, ...EventRetrieved[]] | null>>;
  deleteEvent(args: { id: string, lng: PossibleLngs}): Promise<Result<void>>;
  setCalendarId(calendarId: string): void;
  getEvent(args: { id: string; lng: PossibleLngs }): Promise<Result<EventRetrieved>>;
}
