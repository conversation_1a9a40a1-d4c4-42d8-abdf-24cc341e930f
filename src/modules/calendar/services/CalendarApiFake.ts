import { Event } from '../domain/Event';
import { Result } from '@shared/core/Result';
import { ICalendarApi } from './ICalendarApi';
import { CalendarApiErrors } from './CalendarApiErrors';
import { Identifier } from '../domain/Identifier';
import { EventRetrieved } from '../domain/EventRetrieved';
import { Slot } from '../domain/Slot';
import { DateTime } from 'luxon';
import { ReservationOptionsInDb } from '../utils/testExternal';
import { PossibleLngs } from '@shared/utils/utils';

export type AllDayLong = { date: string };
export type TimeRange = { dateTime: string };

export type ApiEventData = {
  [k: string]: unknown;
  summary?: string;
  description?: string;
  start: {
    dateTime: string;
  };
  end: {
    dateTime: string;
  };
  id: string;
  status: string;
};

type ApiListEventTime = AllDayLong | TimeRange;

export type ApiListData = {
  timeZone: string;
  items:
    | {
        [k: string]: unknown;
        summary?: string;
        description?: string;
        start: ApiListEventTime;
        end: ApiListEventTime;
        id: string;
      }[]
    | [];
};

type SimpleResult = {
  status: number;
};
type ListResult = {
  status: number;
  data: ApiListData;
};
type InsertResult = {
  status: number;
  data: ApiEventData;
};
type GetCalendarResult = {
  status: number;
  data?: {
    summary: string;
  };
};
export type ApiResult =
  | SimpleResult
  | ListResult
  | InsertResult
  | GetCalendarResult;

const identifier = ReservationOptionsInDb[0].locIdentifier;
export const ApiResults: {
  listSuccess1: ListResult;
  listSuccess2: ListResult;
  listSuccess3noEvents: ListResult;
  deleteSuccess: SimpleResult;
  insertSuccess: InsertResult;
  deletedEvent: InsertResult;
  fail: SimpleResult;
  success: SimpleResult;
  getCalendarSuccess: GetCalendarResult;
} = {
  success: {
    status: 200,
  },
  fail: {
    status: 500,
  },
  listSuccess1: {
    status: 200,
    data: {
      timeZone: 'America/Argentina/Cordoba',
      items: [
        {
          summary: 'Available 1 all day long',
          description: 'description1',
          start: { date: '2023-12-16' },
          end: { date: '2023-12-17' },
          id: 'srpq60bjcca7r7knb0tvjrrk7o',
          random: 'random property',
        },
        {
          summary: 'Available 2',
          start: { dateTime: '2023-12-16T14:00:00-03:00' },
          end: { dateTime: '2023-12-16T14:25:00-03:00' },
          id: '2j5nojsfkr2pd0p7mj4rjohcnn',
        },
        {
          summary: 'Available 3',
          description: `#${identifier}`,
          start: {
            dateTime: DateTime.now()
              .setZone('America/Cordoba')
              .startOf('day')
              .plus({ day: 2 })
              .toISO()!,
          },
          end: {
            dateTime: DateTime.now()
              .setZone('America/Cordoba')
              .startOf('day')
              .plus({ day: 2, minutes: 30 })
              .toISO()!,
          },
          id: '2j5nojsfkr2pd0p7mj4rykwme',
        },
        {
          summary: 'Available 4',
          description: `#${identifier}:4`,
          start: {
            dateTime: DateTime.now()
              .setZone('America/Cordoba')
              .startOf('day')
              .plus({ day: 3 })
              .toISO()!,
          },
          end: {
            dateTime: DateTime.now()
              .setZone('America/Cordoba')
              .startOf('day')
              .plus({ day: 3, minutes: 30 })
              .toISO()!,
          },
          id: '2j5nojsfkr2pd0p7mj4ryujfkf',
        },
        {
          summary: 'Available 5',
          description: `#${identifier}:5`,
          start: {
            dateTime: DateTime.now()
              .setZone('America/Cordoba')
              .startOf('day')
              .plus({ day: 4 })
              .toISO()!,
          },
          end: {
            dateTime: DateTime.now()
              .setZone('America/Cordoba')
              .startOf('day')
              .plus({ day: 4, minutes: 30 })
              .toISO()!,
          },
          id: 'eventId4',
        },
      ],
    },
  },
  listSuccess2: {
    status: 200,
    data: {
      timeZone: 'America/Argentina/Cordoba',
      items: [
        {
          summary: 'Already taken 1',
          description: `#${identifier}:2`,
          start: {
            dateTime: DateTime.now()
              .setZone('America/Cordoba')
              .startOf('day')
              .plus({ day: 3 })
              .toISO()!,
          },
          end: {
            dateTime: DateTime.now()
              .setZone('America/Cordoba')
              .startOf('day')
              .plus({ day: 3, minutes: 30 })
              .toISO()!,
          },
          id: '2j5nojsfkr2pd0p7kjgkgkjh',
        },
        {
          summary: 'Already taken',
          description: `#${identifier}:5`,
          start: {
            dateTime: DateTime.now()
              .setZone('America/Cordoba')
              .startOf('day')
              .plus({ day: 4 })
              .toISO()!,
          },
          end: {
            dateTime: DateTime.now()
              .setZone('America/Cordoba')
              .startOf('day')
              .plus({ day: 4, minutes: 30 })
              .toISO()!,
          },
          id: '2j5nojsfkr2pd0p7kjgkgkjh',
        },
      ],
    },
  },
  listSuccess3noEvents: {
    status: 200,
    data: {
      timeZone: 'America/Argentina/Cordoba',
      items: [],
    },
  },
  deleteSuccess: {
    status: 204,
  },
  insertSuccess: {
    status: 200,
    data: {
      summary: 'Event title',
      description: `#${identifier}`,
      start: {
        dateTime: DateTime.now()
          .setZone('America/Cordoba')
          .plus({ day: 2 })
          .set({ millisecond: 0, second: 0, minute: 0 })
          .toISO()!, // e.g. 2024-04-19T09:00:00.000-03:00
      },
      end: {
        dateTime: DateTime.now()
          .setZone('America/Cordoba')
          .plus({ day: 2 })
          .set({ millisecond: 0, second: 0, minute: 30 })
          .toISO()!,
      },
      id: 'eventIdInsertedSuccessfully',
      status: 'confirmed',
    },
  },
  deletedEvent: {
    status: 200,
    data: {
      summary: 'Event title',
      start: {
        dateTime: DateTime.now()
          .setZone('America/Cordoba')
          .plus({ day: 2 })
          .set({ millisecond: 0, second: 0, minute: 0 })
          .toISO()!, // e.g. 2024-04-19T09:00:00.000-03:00
      },
      end: {
        dateTime: DateTime.now()
          .setZone('America/Cordoba')
          .plus({ day: 2 })
          .set({ millisecond: 0, second: 0, minute: 30 })
          .toISO()!,
      },
      id: 'eventIdDeletedSuccessfully',
      status: 'cancelled',
    },
  },
  getCalendarSuccess: {
    status: 200,
    data: {
      summary: 'fakeCalendarName',
    },
  },
};

export class CalendarApiFake implements ICalendarApi {
  private readonly common: {
    auth: { scopes: string[]; email: string; key: string };
    calendarId?: string;
  };
  private apiResult?: ApiResult = undefined;
  private listEventsCalls = 0;

  public constructor() {
    // Add all process.env used:
    const { calendar_clientEmail, calendar_clientKey } = process.env;
    if (!calendar_clientEmail || !calendar_clientKey) {
      console.log('process.env', process.env);
      throw new Error(`Undefined env var!`);
    }
    this.common = {
      auth: {
        email: calendar_clientEmail,
        key: calendar_clientKey,
        scopes: [
          // set the right scope
          'https://www.googleapis.com/auth/calendar',
          'https://www.googleapis.com/auth/calendar.events',
        ],
      },
    };
  }

  public setCalendarId(calendarId: string) {
    this.common.calendarId = calendarId;
  }

  public setApiResult(result: ApiResult) {
    this.apiResult = result;
  }

  public async getCalendarName(lng: PossibleLngs): Promise<Result<string>> {
    if (!this.common.calendarId) throw Error(`calendarId not set`);
    // const get = await this.api.calendars.get(this.common);
    const got = this.apiResult as GetCalendarResult;
    if (!isSuccess(got.status)) {
      console.log(`ERROR when getting calendar info`, got);
      return this.notOk(lng);
    }
    if (!got.data || got.data.summary === null || got.data.summary === undefined) {
      console.log(
        `ERROR when getting calendar info, there is no data.summary`,
        got.data,
      );
      return Result.fail(new CalendarApiErrors.NoName({calendarId: this.common.calendarId, lng }));
    }
    return Result.ok(got.data.summary);
  }

  public async insertEvent(args: { event: Event; lng: PossibleLngs }): Promise<Result<EventRetrieved>> {
    if (!this.common.calendarId) throw Error(`calendarId not set`);
    const { event, lng } = args;
    /*const insert = await this.api.events.insert({
      requestBody: {
        summary: event.name,
        description: event.description,
        start: {
          dateTime: event.slot.start.toISOString(),
        },
        end: {
          dateTime: event.slot.end.toISOString(),
        }
      },
      ...this.common,
    });*/

    const { name, ...rest } = event.toDto();
    const inserted = {
      status: 200,
      data: {
        ...rest,
        start: {
          dateTime: event.slot.start.l.setZone('America/Cordoba').toISO()!,
        },
        end: {
          dateTime: event.slot.end.l.setZone('America/Cordoba').toISO()!,
        },
        id: 'eventIdInsertCase',
        summary: name,
      },
    };

    if (!isSuccess(inserted.status)) {
      console.log(`ERROR when inserting event`, inserted);
      return this.notOk(lng);
    }
    return Result.ok(EventRetrieved.fromApi(inserted.data));
  }

  // Retrieves the Google calendar events that contain Identifier.query
  public async listEvents(args: {
    identifier: Identifier;
    slot: Slot;
    lng: PossibleLngs;
  }): Promise<Result<[EventRetrieved, ...EventRetrieved[]] | null>> {
    if (!this.common.calendarId) throw Error(`calendarId not set`);
    const { lng } = args;

    /*const list = await this.api.events.list({
      timeMin: slot.start.toISOString(),  // Includes the event if timeMin: equals event start or falls inside the event. Doesn't include the event if timeMin equals event end.
      timeMax: slot.end.toISOString(),  // Doesn't include the event if timeMax equals event start. Includes the event if timeMax: falls inside the event or after the event end.
      showDeleted: false,
      singleEvents: true,
      orderBy: 'startTime',
      q: identifier.query, // Finds the events if #<identifier> is in any place, for example: summary, location, description. It has some quirks, check domain/Identifier.ts:checkIdentifier
      ...this.common,
    });*/

    let listed;
    this.listEventsCalls++;
    switch (this.listEventsCalls % 2) {
      case 1:
        console.log('case 1');
        listed = ApiResults.listSuccess1;
        break;

      default:
        console.log('case default');
        listed = ApiResults.listSuccess2;
    }

    console.log(`Returned from api list`, listed);
    if (!isSuccess(listed.status)) {
      console.log(`ERROR when listing events`);
      return this.notOk(lng);
    }
    console.log(`items`, listed.data.items);
    if (!listed.data.items || !listed.data.items.length) return Result.ok(null);

    return Result.ok(EventRetrieved.listToDomain(listed.data));
  }

  public async nextEvents(lng: PossibleLngs): Promise<
    Result<[EventRetrieved, ...EventRetrieved[]] | null>
  > {
    if (!this.common.calendarId) throw Error(`calendarId not set`);

    let listed;
    this.listEventsCalls++;
    switch (this.listEventsCalls % 2) {
      case 1:
        console.log('case 1');
        listed = ApiResults.listSuccess1;
        break;

      default:
        console.log('case default');
        listed = ApiResults.listSuccess2;
    }

    console.log(`Returned from api list`, listed);
    if (!isSuccess(listed.status)) {
      console.log(`ERROR when listing events`);
      return this.notOk(lng);
    }
    console.log(`items`, listed.data.items);
    if (!listed.data.items || !listed.data.items.length) return Result.ok(null);

    return Result.ok(EventRetrieved.listToDomain(listed.data));
  }

  public async getEvent(args: { id: string; lng: PossibleLngs }): Promise<Result<EventRetrieved>> {
    if (!this.common.calendarId) throw Error(`calendarId not set`);

    const { id, lng } = args;
    switch (id) {
      case 'apiError':
        return Result.fail(
          new CalendarApiErrors.NotOkWhenGettingEvent({
            calendarId: this.common.calendarId,
            id,
            lng,
          }),
        );
      default:
        return Result.ok(EventRetrieved.fromApi(ApiResults.insertSuccess.data));
    }
  }

  public async updateEvent(args: {
    event: EventRetrieved;
    lng: PossibleLngs;
  }): Promise<Result<EventRetrieved>> {
    if (!this.common.calendarId) throw Error(`calendarId not set`);
    const { event, lng } = args;
    /*const updated = await this.api.events.update({
      ...this.common,
      eventId: event.id,
      requestBody: event,
    });*/

    const { name, ...rest } = event.toDto();
    const updated = {
      status: 200,
      data: {
        ...rest,
        start: {
          dateTime: event.slot.start.l.setZone('America/Cordoba').toISO()!,
        },
        end: {
          dateTime: event.slot.end.l.setZone('America/Cordoba').toISO()!,
        },
        id: 'eventIdUpdateCase',
        summary: name,
      },
    };

    if (!isSuccess(updated.status)) {
      console.log(`ERROR when updating event`, updated);
      return this.notOk(lng);
    }
    return Result.ok(EventRetrieved.fromApi(updated.data));
  }

  public async deleteEvent(args: { id: string, lng: PossibleLngs}): Promise<Result<void>> {
    const { lng } = args;
    if (!this.common.calendarId) throw Error(`calendarId not set`);
    /*const deleted = await this.api.events.delete({
      eventId,
      ...this.common,
    });*/
    const deleted = this.apiResult;

    if (!deleted) throw Error(`apiResult not set`);
    if (!isSuccess(deleted.status)) {
      return this.notOk(lng);
    }
    return Result.ok(undefined);
  }

  // Copied from CalendarApi
  private notOk(lng: PossibleLngs) {
    return Result.fail(new CalendarApiErrors.NotOk({ calendarId: this.common.calendarId!, lng }));
  }
}

function isSuccess(status: number) {
  return [200, 201, 204].includes(status);
}
