import { test } from 'vitest';

test(`Return chunks`, () => {
  type Slot = {
    start: Date;
    end: Date;
  };

  function getTimeChunks(slots: Slot[]): Slot[] {
    // Extracting all time points and sorting them
    let timePoints: Date[] = slots.reduce((acc, slot) => {
      acc.push(slot.start, slot.end);
      return acc;
    }, [] as Date[]);

    // Removing duplicates and sorting
    timePoints = Array.from(new Set(timePoints)).sort(
      (a, b) => a.getTime() - b.getTime(),
    );

    const chunks: Slot[] = [];
    for (let i = 0; i < timePoints.length - 1; i++) {
      const potentialChunk: Slot = {
        start: timePoints[i],
        end: timePoints[i + 1],
      };

      // Check if this chunk falls within any of the slots
      if (
        slots.some(
          (slot) =>
            slot.start <= potentialChunk.start && slot.end >= potentialChunk.end,
        )
      ) {
        chunks.push(potentialChunk);
      }
    }

    return chunks;
  }

  // Example usage:
  const inputArray: Slot[] = [
    {
      start: new Date('2024-01-06T09:00:00'),
      end: new Date('2024-01-06T10:00:00'),
    },
    {
      start: new Date('2024-01-06T09:30:00'),
      end: new Date('2024-01-06T10:30:00'),
    },
    {
      start: new Date('2024-01-06T11:00:00'),
      end: new Date('2024-01-06T12:00:00'),
    },
    {
      start: new Date('2024-01-06T13:00:00'),
      end: new Date('2024-01-06T14:00:00'),
    },
  ];

  const result: Slot[] = getTimeChunks(inputArray);
  console.log(result);
});

test(`Sum spots`, () => {
  type Slot = {
    start: Date;
    end: Date;
  };

  type SlotWithSpots = {
    slot: Slot;
    spots: number;
  };

  function createChunksWithSpots(
    slotsWithSpots: SlotWithSpots[],
  ): SlotWithSpots[] {
    // Extract all unique time points and sort them
    let timePoints: Date[] = slotsWithSpots.reduce((acc, slotWithSpots) => {
      acc.push(slotWithSpots.slot.start, slotWithSpots.slot.end);
      return acc;
    }, [] as Date[]);

    timePoints = Array.from(new Set(timePoints)).sort(
      (a, b) => a.getTime() - b.getTime(),
    );

    const chunks: SlotWithSpots[] = [];
    for (let i = 0; i < timePoints.length - 1; i++) {
      const potentialChunk: Slot = {
        start: timePoints[i],
        end: timePoints[i + 1],
      };
      let totalSpots = 0;

      // Sum spots for all slots that overlap with this chunk
      slotsWithSpots.forEach((slotWithSpots) => {
        if (
          slotWithSpots.slot.start <= potentialChunk.start &&
          slotWithSpots.slot.end >= potentialChunk.end
        ) {
          totalSpots += slotWithSpots.spots;
        }
      });

      if (totalSpots > 0) {
        chunks.push({ slot: potentialChunk, spots: totalSpots });
      }
    }

    return chunks;
  }

  const inputArray: SlotWithSpots[] = [
    {
      slot: {
        start: new Date('2024-01-06T09:00:00'),
        end: new Date('2024-01-06T10:00:00'),
      },
      spots: 3,
    },
    {
      slot: {
        start: new Date('2024-01-06T09:30:00'),
        end: new Date('2024-01-06T10:30:00'),
      },
      spots: 2,
    },
    {
      slot: {
        start: new Date('2024-01-06T11:00:00'),
        end: new Date('2024-01-06T12:00:00'),
      },
      spots: 1,
    },
  ];

  const result: SlotWithSpots[] = createChunksWithSpots(inputArray);
  console.log(result);
});

test(`Compute net spots subtracting occupied from available`, () => {
  type Slot = {
    start: Date;
    end: Date;
  };

  type SlotWithSpots = {
    slot: Slot;
    spots: number;
  };

  function calculateNetSpots(
    available: SlotWithSpots[],
    occupied: SlotWithSpots[],
  ): SlotWithSpots[] {
    // Merge and sort time points
    let timePoints: Date[] = [...available, ...occupied].reduce(
      (acc, slotWithSpots) => {
        acc.push(slotWithSpots.slot.start, slotWithSpots.slot.end);
        return acc;
      },
      [] as Date[],
    );

    timePoints = Array.from(new Set(timePoints)).sort(
      (a, b) => a.getTime() - b.getTime(),
    );

    const chunks: SlotWithSpots[] = [];
    for (let i = 0; i < timePoints.length - 1; i++) {
      const chunk: Slot = { start: timePoints[i], end: timePoints[i + 1] };
      let availableSpots = 0;
      let occupiedSpots = 0;

      // Calculate available and occupied spots for the chunk
      available.forEach((slotWithSpots) => {
        if (
          slotWithSpots.slot.start <= chunk.start &&
          slotWithSpots.slot.end >= chunk.end
        ) {
          availableSpots += slotWithSpots.spots;
        }
      });

      occupied.forEach((slotWithSpots) => {
        if (
          slotWithSpots.slot.start <= chunk.start &&
          slotWithSpots.slot.end >= chunk.end
        ) {
          occupiedSpots += slotWithSpots.spots;
        }
      });

      const netSpots = availableSpots - occupiedSpots;
      if (netSpots !== 0) {
        chunks.push({ slot: chunk, spots: netSpots });
      }
    }

    return chunks;
  }

  // Example usage
  const availableSlots: SlotWithSpots[] = [
    {
      slot: {
        start: new Date('2024-01-01T08:00:00'),
        end: new Date('2024-01-01T10:00:00'),
      },
      spots: 5,
    },
    {
      slot: {
        start: new Date('2024-01-01T10:00:00'),
        end: new Date('2024-01-01T12:00:00'),
      },
      spots: 4,
    },
  ];

  const occupiedSlots: SlotWithSpots[] = [
    {
      slot: {
        start: new Date('2024-01-01T09:00:00'),
        end: new Date('2024-01-01T11:00:00'),
      },
      spots: 2,
    },
  ];

  const result = calculateNetSpots(availableSlots, occupiedSlots);

  console.log(result);
});

test(`Don't include chunks with same start and end`, () => {
  type Slot = {
    start: Date;
    end: Date;
  };

  type SlotWithSpots = {
    slot: Slot;
    spots: number;
  };

  function calculateNetSpots(
    available: SlotWithSpots[],
    occupied: SlotWithSpots[],
  ): SlotWithSpots[] {
    // Merge and sort time points
    let timePoints: Date[] = [...available, ...occupied].reduce(
      (acc, slotWithSpots) => {
        acc.push(slotWithSpots.slot.start, slotWithSpots.slot.end);
        return acc;
      },
      [] as Date[],
    );

    timePoints = Array.from(new Set(timePoints)).sort(
      (a, b) => a.getTime() - b.getTime(),
    );

    const chunks: SlotWithSpots[] = [];
    for (let i = 0; i < timePoints.length - 1; i++) {
      const chunk: Slot = { start: timePoints[i], end: timePoints[i + 1] };

      // Skip chunks with the same start and end time
      if (chunk.start.getTime() === chunk.end.getTime()) {
        continue;
      }

      let availableSpots = 0;
      let occupiedSpots = 0;

      // Calculate available and occupied spots for the chunk
      available.forEach((slotWithSpots) => {
        if (
          slotWithSpots.slot.start <= chunk.start &&
          slotWithSpots.slot.end >= chunk.end
        ) {
          availableSpots += slotWithSpots.spots;
        }
      });

      occupied.forEach((slotWithSpots) => {
        if (
          slotWithSpots.slot.start <= chunk.start &&
          slotWithSpots.slot.end >= chunk.end
        ) {
          occupiedSpots += slotWithSpots.spots;
        }
      });

      const netSpots = availableSpots - occupiedSpots;
      if (netSpots !== 0) {
        chunks.push({ slot: chunk, spots: netSpots });
      }
    }

    return chunks;
  }

  // Example usage
  const availableSlots: SlotWithSpots[] = [
    {
      slot: {
        start: new Date('2024-01-01T08:00:00'),
        end: new Date('2024-01-01T10:00:00'),
      },
      spots: 5,
    },
    {
      slot: {
        start: new Date('2024-01-01T10:00:00'),
        end: new Date('2024-01-01T12:00:00'),
      },
      spots: 4,
    },
    {
      slot: {
        start: new Date('2024-01-01T13:30:00'),
        end: new Date('2024-01-01T14:00:00'),
      },
      spots: 4,
    },
    {
      slot: {
        start: new Date('2024-01-01T16:00:00'),
        end: new Date('2024-01-01T17:00:00'),
      },
      spots: 4,
    },
    {
      slot: {
        start: new Date('2024-01-01T17:30:00'),
        end: new Date('2024-01-01T18:00:00'),
      },
      spots: 2,
    },
  ];

  const occupiedSlots: SlotWithSpots[] = [
    {
      slot: {
        start: new Date('2024-01-01T09:00:00'),
        end: new Date('2024-01-01T11:00:00'),
      },
      spots: 2,
    },
    {
      slot: {
        start: new Date('2024-01-01T13:00:00'),
        end: new Date('2024-01-01T14:00:00'),
      },
      spots: 2,
    },
    {
      slot: {
        start: new Date('2024-01-01T17:00:00'),
        end: new Date('2024-01-01T18:00:00'),
      },
      spots: 2,
    },
  ];

  const result = calculateNetSpots(availableSlots, occupiedSlots);

  console.log(result);
});

test(`Consolidate adjacent slots with equal spots`, () => {
  type Slot = {
    start: Date;
    end: Date;
  };

  type SlotWithSpots = {
    slot: Slot;
    spots: number;
  };

  function consolidateSlots(slotsWithSpots: SlotWithSpots[]): SlotWithSpots[] {
    const consolidated: SlotWithSpots[] = [];

    for (let i = 0; i < slotsWithSpots.length; i++) {
      let current = slotsWithSpots[i];

      // Check if the current slot can be merged with the next one
      while (
        i < slotsWithSpots.length - 1 &&
        current.slot.end.getTime() ===
          slotsWithSpots[i + 1].slot.start.getTime() &&
        current.spots === slotsWithSpots[i + 1].spots
      ) {
        current = {
          slot: { start: current.slot.start, end: slotsWithSpots[i + 1].slot.end },
          spots: current.spots,
        };
        i++;
      }

      consolidated.push(current);
    }

    return consolidated;
  }

  // Example usage
  const slotsWithSpots: SlotWithSpots[] = [
    {
      slot: {
        start: new Date('2024-01-01T08:00:00'),
        end: new Date('2024-01-01T10:00:00'),
      },
      spots: 3,
    },
    {
      slot: {
        start: new Date('2024-01-01T10:00:00'),
        end: new Date('2024-01-01T12:00:00'),
      },
      spots: 3,
    },
    {
      slot: {
        start: new Date('2024-01-01T12:00:00'),
        end: new Date('2024-01-01T13:00:00'),
      },
      spots: 2,
    },
    {
      slot: {
        start: new Date('2024-01-01T14:00:00'),
        end: new Date('2024-01-01T15:00:00'),
      },
      spots: 1,
    },
  ];

  const result = consolidateSlots(slotsWithSpots);

  console.log(result);
});
