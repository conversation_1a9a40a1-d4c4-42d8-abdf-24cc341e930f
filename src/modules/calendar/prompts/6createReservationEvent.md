<details>
  <summary>Ask for improvements</summary>
I have the following TypeScript code, what improvements could you suggest?

```typescript
// src/modules/calendar/useCases/createReservationEvent/CreateReservationEvent.ts
import { Request, Response } from './CreateReservationEventDTOs';
import { ICalendarRepo } from '../../repos/ICalendarRepo';
import { formatError } from '@shared/core/AppError';
import { Status } from '@shared/core/Status';
import { ControllerResult } from '@shared/core/ControllerResult';
import {
  CreateReservationEventErrors,
} from './CreateReservationEventErrors';
import { Identifier } from '../../domain/Identifier';
import { ICalendarsSrv } from '../../services/ICalendarsSrv';
import { DateTime, IANAZone } from 'luxon';
import { Slot } from '../../domain/Slot';
import { Event } from '../../domain/Event';
import { EventRetrieved } from '../../domain/EventRetrieved';

type CreateReservationEventProps = {
  calendarRepo: ICalendarRepo;
  calendarsSrv: ICalendarsSrv;
};

export enum Action {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
}

export class CreateReservationEvent {
  private readonly calendarRepo: ICalendarRepo;
  private readonly calendarsSrv: ICalendarsSrv;

  public constructor({ calendarRepo, calendarsSrv }: CreateReservationEventProps) {
    this.calendarRepo = calendarRepo;
    this.calendarsSrv = calendarsSrv;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { userId, identifier: _identifier, name, start, end, customer, timeZone } = dto;

    const identifierOrError = Identifier.create(_identifier);
    if (identifierOrError.isFailure)
      return formatError(identifierOrError.error);
    const identifier = identifierOrError.value;

    const slotOrError = Slot.create({ start, end });
    if (slotOrError.isFailure)
      return formatError(slotOrError.error);
    const slot = slotOrError.value;

    const availabilityCalendar = await this.calendarRepo.getAvailability(userId);
    if (!availabilityCalendar)
      return formatError(new CreateReservationEventErrors.AvailabilityCalendarNotFoundForUserId(userId));
    const reservationCalendar = await this.calendarRepo.getReservations(userId);
    if (!reservationCalendar)
      return formatError(new CreateReservationEventErrors.ReservationCalendarNotFoundForUserId(userId));

    this.calendarsSrv.setCalendarIds({
      availabilityCalendarId: availabilityCalendar.id.toString(), reservationCalendarId: reservationCalendar.id.toString(),
    });

    const spotsOrError = await this.calendarsSrv.getSpotsForReservation({
      identifier,
      slot,
    });
    if (spotsOrError.isFailure) return formatError(spotsOrError.error);

    const { spots, existingReservation }  = spotsOrError.value;
    if (!spots)
      return formatError(new CreateReservationEventErrors.NoSpotsFound({
        identifier,
        start,
        end,
      }));

    const { firstName, lastName, phone, email } = customer;
    let customerReserved = `${firstName} ${lastName} (phone: ${phone}, email: ${email})`;
    if (IANAZone.isValidZone(timeZone)) // @ts-ignore
      customerReserved += ` reserved at ${DateTime.now().setZone(timeZone).startOf('second').toISO({ suppressMilliseconds: true }).toString()}`;

    let action: Action, event: Event | EventRetrieved;
    if (existingReservation) {

      const descriptionUpdated = `${customerReserved}\n${identifier.incrementSpots(existingReservation.description!)}`;

      const updatedOrError = await this.calendarsSrv.updateReservation(
        existingReservation.updateDescription(descriptionUpdated)
      );
      if (updatedOrError.isFailure)
        return formatError(updatedOrError.error);

      event = updatedOrError.value;

      action = Action.UPDATED;

    } else {

      const createdOrError = await this.calendarsSrv.createReservation(
        Event.create({
          name,
          description: `${customerReserved}\n${identifier.query}`,
          slot,
        })
      );

      if (createdOrError.isFailure)
        return formatError(createdOrError.error);

      event = createdOrError.value;

      action = Action.CREATED
    }

    return { status: Status.OK, result: {
        action,
        event: event.toRaw(),
      } };

  }
}
```

```typescript
// src/modules/calendar/domain/Identifier.ts
import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { BaseError } from '@shared/core/AppError';
import { IdentifierErrors } from './IdentifierErrors';
import { IdentifierWithSpots } from './IdentifierWithSpots';

interface IdentifierProps {
  value: string;  // loc1
  query: string;  // #loc1
}

export interface IdentifierRaw extends IdentifierProps {}

export class Identifier extends ValueObject<IdentifierProps, IdentifierRaw> {
  private __class = this.constructor.name;

  get value(): string {
    return this.props.value;
  }

  get query(): string {
    return this.props.query;
  }

  private constructor(props: IdentifierProps) {
    super(props);
  }

  public static create(identifier: string): Result<Identifier> {

    const check = checkIdentifier(identifier);
    if (check.isFailure) return Result.fail(check.error as BaseError);

    return Result.ok<Identifier>(new Identifier({
      value: identifier,
      query: `#${identifier}`,
    }));
  }

  public incrementSpots(existingReservationDescription: string): string {
    const spotsOrError = IdentifierWithSpots.parse({ identifier: this, eventDescription: existingReservationDescription });
    if (spotsOrError.isFailure) {
      const msg = `ERROR: Couldn't determine the already reserved spots when updating event`
      console.log(msg, {
        identifier: this,
        existingReservationDescription,
      });
      throw Error(msg);
    }
    const reservedSpots = spotsOrError.value.spots.increment();


    let regex = new RegExp(`${this.query}:[0-9]+`, 'g');
    const incremented = `${this.query}:${reservedSpots.value}`;
    const updatedDescription = existingReservationDescription.replaceAll(regex, incremented);
    regex = new RegExp(`${this.query}(?!:[0-9]+)`, 'g');
    return updatedDescription.replaceAll(regex, incremented);
  }

  public toRaw(): IdentifierRaw {
    return {
      value: this.props.value,
      query: this.props.query,
    }
  }
}

function checkIdentifier(identifierInput: string): Result<string> {
  // All these checks are related to how the search of events in Google Calendar works using parameter q from method googleapis.calendar.events.list
  // https://github.com/googleapis/google-api-nodejs-client/blob/904e5f00d7fba3955bcba22f5b218980647e2211/src/apis/calendar/v3.ts#L4524
  // Notes about the search with q:
  // It's case-insensitive: searching for #Hi will find #hi too.
  // Some characters and the place where they were located caused some trouble. For example:
  // #x:x_x is found
  // #_x is not found
  // #x:-x_--x-x is not found
  // #he::l__lo is found
  // #he:_l_lo is not found
  // That's why I limited the possible characters to only letters, numbers and "_". Also, checking their locations.

  const maxLength = 40;
  const minLength = 2;

  const trimmed = identifierInput.trim();

  const space = trimmed.match(/ /); // don't allow spaces. It's handled by this special error as printing spaces as the invalid characters isn't clear
  if (space) return Result.fail(new IdentifierErrors.SpacesNotAllowed());

  const upperCases = trimmed.match(/[A-Z]/g);
  if (upperCases)
    return Result.fail(
      new IdentifierErrors.UppercaseNotAllowed(upperCases),
    );

  const invalidChars = trimmed.match(/[^a-z0-9_]/g);
  if (invalidChars)
    return Result.fail(
      new IdentifierErrors.InvalidCharacters(invalidChars),
    );

  const atLeast1letter = trimmed.match(/[a-z]+/);
  if (!atLeast1letter)
    return Result.fail(
      new IdentifierErrors.ShouldHaveAtLeast1letter(),
    );

  const initialCharInvalid = trimmed.match(/^[^a-z0-9]/);
  if (initialCharInvalid)
    return Result.fail(
      new IdentifierErrors.FirstCharShouldBeLetterOrNumber(
        initialCharInvalid,
      ),
    );

  const lastCharInvalid = trimmed.match(/[^a-z0-9]$/);
  if (lastCharInvalid)
    return Result.fail(
      new IdentifierErrors.LastCharShouldBeLetterOrNumber(lastCharInvalid),
    );

  return Result.convertValue(trimmed)
    .ensure((value: string) => {
      return value.length >= minLength;
    }, new IdentifierErrors.TooShort(minLength))
    .ensure((value: string) => {
      return value.length <= maxLength;
    }, new IdentifierErrors.TooLong(maxLength))
    .onBoth((result) =>
      result.isSuccess ? Result.ok() : Result.fail(result.error as BaseError),
    );
}
```

```typescript
// src/modules/calendar/services/CalendarsSrv.ts
import { Result } from '@shared/core/Result';
import { BaseError } from '@shared/core/AppError';
import {
  GetSpotsForReservationArgs,
  GetAvailableTimesArgs,
  ICalendarsSrv,
} from './ICalendarsSrv';
import { SlotWithSpots } from '../domain/SlotWithSpots';
import { ICalendarSrv } from './ICalendarSrv';
import { TimeWithSpots } from '../domain/TimeWithSpots';
import { Identifier } from '../domain/Identifier';
import { Spots } from '../domain/Spots';
import { Event } from '../domain/Event';
import { Created } from '@shared/core/Created';
import { IdentifierWithSpots } from '../domain/IdentifierWithSpots';
import { EventRetrieved } from '../domain/EventRetrieved';
import { Slot } from '../domain/Slot';

export class CalendarsSrv implements ICalendarsSrv {
  private availabilitySrv?: ICalendarSrv;
  private reservationSrv?: ICalendarSrv;
  private readonly getCalendarSrv: (calendarId: string) => ICalendarSrv;
  private notInitialized = `${this.constructor.name} should be initialized before being used.`;

  public constructor(getCalendarSrv: (calendarId: string) => ICalendarSrv) {
    this.getCalendarSrv = getCalendarSrv;
  }

  public setCalendarIds(args: { availabilityCalendarId: string, reservationCalendarId: string }) {
    const { availabilityCalendarId, reservationCalendarId } = args;
    this.availabilitySrv = this.getCalendarSrv(availabilityCalendarId);
    this.reservationSrv = this.getCalendarSrv(reservationCalendarId);
  }

  public async getAvailableTimes(args: GetAvailableTimesArgs): Promise<Result<TimeWithSpots[]>> {

    const { identifier, slot, timeReference, every } = args;

    const availableChunksOrError = await this.getAvailableChunks({ identifier, slot });
    if (availableChunksOrError.isFailure)
      return Result.fail(availableChunksOrError.error as BaseError);
    const availableChunks = availableChunksOrError.value.chunks;

    return Result.ok(TimeWithSpots.getPossibleTimes({ timeReference, every, chunks: availableChunks}));

  }

  public async getSpotsForReservation(args: GetSpotsForReservationArgs): Promise<Result<{ spots: Spots | null, existingReservation: EventRetrieved | undefined }>> {
    if (!this.reservationSrv) throw Error(this.notInitialized);
    const { identifier, slot } = args;

    const availableChunksOrError = await this.getAvailableChunks({ identifier, slot });
    if (availableChunksOrError.isFailure)
      return Result.fail(availableChunksOrError.error as BaseError);
    const { chunks, existingEvents } = availableChunksOrError.value;

    const sameTime = existingEvents.filter(event => {
      return slot.start.getTime() === event.slot.start.getTime() && slot.end.getTime() === event.slot.end.getTime();
    });

    const existingReservation = sameTime.filter(event => {
      const { description } = event;
      if (!description) return false;
      const result = IdentifierWithSpots.parse({
        eventDescription: description,
        identifier,
      });
      return result.isSuccess;
    })[0];  // If the event has the same start and end time, and contains the event identifier, it's assumed that the first event that matches these conditions is the same reservation we are booking for, so we have to update that event instead of creating a new event.

    return Result.ok({
      spots: TimeWithSpots.getSpotsForReservation({ slot, chunks }),
      existingReservation,
    });

  }

  // Get the chunks (sorted) in which there are spots available
  private async getAvailableChunks(args: {
    identifier: Identifier;
    slot: Slot;
  }): Promise<Result<{ chunks: SlotWithSpots[]; existingEvents: EventRetrieved[] }>> {
    if (!this.availabilitySrv || !this.reservationSrv) throw Error(this.notInitialized);
    const { identifier, slot } = args;

    // Chunkify, sort and consolidate by calling availabilitySrv.getChunks > SlotWithSpots.chunkify
    const availabilityChunksOrError = await this.availabilitySrv.getChunks({
      identifier,
      slot,
    });
    if (availabilityChunksOrError.isFailure)
      return Result.fail(availabilityChunksOrError.error as BaseError);
    const availabilityChunks = availabilityChunksOrError.value.chunks;

    // Chunkify, sort and consolidate by calling reservationSrv.getChunks > SlotWithSpots.chunkify
    const reservationChunksOrError = await this.reservationSrv.getChunks({
      identifier,
      slot,
    });
    if (reservationChunksOrError.isFailure)
      return Result.fail(reservationChunksOrError.error as BaseError);
    const reservationChunks = reservationChunksOrError.value.chunks;

    return Result.ok({
      chunks: SlotWithSpots.getAvailableChunks({ availability: availabilityChunks, reserved: reservationChunks}),
      existingEvents: reservationChunksOrError.value.existingEvents,
    });

  }

  public async updateReservation(event: EventRetrieved): Promise<Result<EventRetrieved>> {
    if (!this.reservationSrv) throw Error(this.notInitialized);
    return await this.reservationSrv.updateEvent(event);
  }

  public async createReservation(event: Event): Promise<Result<EventRetrieved>> {
    if (!this.reservationSrv) throw Error(this.notInitialized);
    return await this.reservationSrv.insertEvent(event);
  }
}

```

```typescript
// src/modules/calendar/domain/IdentifierWithSpots.ts
import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { IdentifierWithSpotsErrors } from './IdentifierWithSpotsErrors';
import { Spots, SpotsRaw } from './Spots';
import { Identifier, IdentifierRaw } from './Identifier';

interface IdentifierWithSpotsProps {
  identifier: Identifier;
  spots: Spots;
}

interface IdentifierWithSpotsRaw {
  identifier: IdentifierRaw,
  spots: SpotsRaw,
}

export class IdentifierWithSpots extends ValueObject<IdentifierWithSpotsProps, IdentifierWithSpotsRaw> {
  private __class = this.constructor.name;
  get identifier(): Identifier {
    return this.props.identifier;
  }

  get spots(): Spots {
    return this.props.spots;
  }

  private constructor(props: IdentifierWithSpotsProps) {
    super(props);
  }

  // For the creation of IdentifierWithSpots, we search into the event description for the structure #<identifier>:<n>
  // In the availability calendar, <n> is the capacity of spots
  // In the reservation calendar, <n> is the number of spots taken
  // If no valid #<identifier>:<n> is found, it searches for just #<identifier> defaulting spots to 1
  public static parse(props: {
    identifier: Identifier;
    eventDescription: string;
  }): Result<IdentifierWithSpots> {
    const { identifier, eventDescription } = props;

    let spots;
    const regex = new RegExp(`${identifier.query}:[0-9]+`);
    const match = eventDescription.match(regex);
    label1: if (match) {
      const [ , _spots ] = match[0].split(':');
      const parsed = parseInt(_spots)
      if (isNaN(parsed)) break label1;

      const spotsOrError = Spots.create(parsed);
      if (spotsOrError.isFailure) break label1; else {
        spots = spotsOrError.value;
      }
    }

    // If we failed to found a valid #<identifier>:<n> structure search for #<identifier> and default it to #<identifier>:1
    if (!spots) {
      const regex = new RegExp(`${identifier.query}`);
      const match = eventDescription.match(regex);
      if (match) {
        spots = Spots.create(1).value;
      }
    }

    if (!spots)
      return Result.fail(new IdentifierWithSpotsErrors.NoValidIdentifierFound(identifier.query));

    return Result.ok<IdentifierWithSpots>(new IdentifierWithSpots({
      identifier,
      spots,
    }));
  }

  public toRaw(): IdentifierWithSpotsRaw {
    return {
      identifier: this.identifier.toRaw(),
      spots: this.spots.toRaw(),
    };
  }
}
```
</details>

<details>
  <summary>ChatGPT</summary>
No muy útil
</details>

---

<details>
  <summary>Focus on CreateReservationEvent</summary>
I want to focus on this part of `src/modules/calendar/useCases/createReservationEvent/CreateReservationEvent.ts`

```typescript
const { firstName, lastName, phone, email } = customer;
let customerReserved = `${firstName} ${lastName} (phone: ${phone}, email: ${email})`;
if (IANAZone.isValidZone(timeZone)) // @ts-ignore
  customerReserved += ` reserved at ${DateTime.now().setZone(timeZone).startOf('second').toISO({ suppressMilliseconds: true }).toString()}`;

let action: Action, event: Event | EventRetrieved;
if (existingReservation) {

  const descriptionUpdated = `${customerReserved}\n${identifier.incrementSpots(existingReservation.description!)}`;

  const updatedOrError = await this.calendarsSrv.updateReservation(
    existingReservation.updateDescription(descriptionUpdated)
  );
  if (updatedOrError.isFailure)
    return formatError(updatedOrError.error);

  event = updatedOrError.value;

  action = Action.UPDATED;

} else {

  const createdOrError = await this.calendarsSrv.createReservation(
    Event.create({
      name,
      description: `${customerReserved}\n${identifier.query}`,
      slot,
    })
  );

  if (createdOrError.isFailure)
    return formatError(createdOrError.error);

  event = createdOrError.value;

  action = Action.CREATED
}
```

What improvements could you suggest?

</details>

<details>
  <summary>ChatGPT</summary>
Focusing on the specified section of your `CreateReservationEvent.ts` file, there are several areas where improvements can be made for clarity, maintainability, and robustness:

### 1. Refactor Customer Reservation String Construction
The current method of constructing the `customerReserved` string is straightforward but could be made more readable and maintainable:

```typescript
private formatCustomerReservation(customer: Customer, timeZone: string): string {
  const { firstName, lastName, phone, email } = customer;
  let reservationInfo = `${firstName} ${lastName} (phone: ${phone}, email: ${email})`;

  if (IANAZone.isValidZone(timeZone)) {
    const reservationTime = DateTime.now().setZone(timeZone).startOf('second').toISO({ suppressMilliseconds: true });
    reservationInfo += ` reserved at ${reservationTime}`;
  }

  return reservationInfo;
}
```

### 2. Extract Logic for Updating and Creating Reservations
The `if...else` block that handles either updating an existing reservation or creating a new one can be refactored into separate methods for better readability and separation of concerns:

```typescript
private async handleExistingReservation(existingReservation: EventRetrieved, customerReserved: string, identifier: Identifier): Promise<ControllerResult<Response>> {
  const descriptionUpdated = `${customerReserved}\n${identifier.incrementSpots(existingReservation.description!)}`;
  const updatedOrError = await this.calendarsSrv.updateReservation(
    existingReservation.updateDescription(descriptionUpdated)
  );

  if (updatedOrError.isFailure) {
    return formatError(updatedOrError.error);
  }

  return {
    status: Status.OK,
    result: {
      action: Action.UPDATED,
      event: updatedOrError.value.toRaw(),
    },
  };
}

private async handleNewReservation(name: string, customerReserved: string, slot: Slot, identifier: Identifier): Promise<ControllerResult<Response>> {
  const createdOrError = await this.calendarsSrv.createReservation(
    Event.create({
      name,
      description: `${customerReserved}\n${identifier.query}`,
      slot,
    })
  );

  if (createdOrError.isFailure) {
    return formatError(createdOrError.error);
  }

  return {
    status: Status.OK,
    result: {
      action: Action.CREATED,
      event: createdOrError.value.toRaw(),
    },
  };
}
```

### 3. Main Execution Flow
The main execution flow becomes more readable:

```typescript
public async executeImpl(dto: Request): Promise<ControllerResult<Response>> {
  // ... other code ...

  const customerReserved = this.formatCustomerReservation(dto.customer, dto.timeZone);

  if (existingReservation) {
    return await this.handleExistingReservation(existingReservation, customerReserved, identifier);
  } else {
    return await this.handleNewReservation(dto.name, customerReserved, slot, identifier);
  }
}
```

### 4. TypeScript and ESLint Comments
- The use of `// @ts-ignore` should be minimized as it can mask potential type issues. Where necessary, use more specific TypeScript type assertions or refine the type definitions.
- If `IANAZone.isValidZone(timeZone)` is a recurring check, consider encapsulating it within a utility function or method for reusability.

### 5. Error Handling
- Ensure that all possible error scenarios are handled appropriately. If there's any additional business logic that needs to be executed in case of failures, it should be included in the error handling sections.

These improvements aim to make your code more modular, readable, and maintainable, following good practices in TypeScript programming.

</details>

---

<details>
  <summary>Moving handleExistingReservation and handleNewReservation into calendarsSrv?</summary>
While I think that moving methods `handleExistingReservation` and `handleNewReservation` to `calendarsSrv` would simplify `CreateReservationEvent.ts` and achieve better testability, as if that code is only used in `CreateReservationEvent.ts,` I'm not completely comfortable moving them to `calendarsSrv.` What do you think?
</details>

<details>
  <summary>ChatGPT</summary>
Your consideration about moving `handleExistingReservation` and `handleNewReservation` methods to `calendarsSrv` is valid and reflects a thoughtful approach to software architecture. Here are some points to consider in making your decision:

### 1. **Responsibility and Cohesion**
- **Service Layer Responsibilities**: The service layer (`calendarsSrv`) should ideally handle business logic that is common across multiple use cases. If `handleExistingReservation` and `handleNewReservation` are specific to the `CreateReservationEvent` use case, they might be better kept within that context to maintain cohesion.
- **Use Case Specificity**: If these methods are highly specific to `CreateReservationEvent` and unlikely to be reused elsewhere, keeping them within the use case class can make the class more self-contained and coherent.

### 2. **Testability**
- Moving logic to services can indeed enhance testability, particularly if it allows for more focused unit tests. If these methods are in `calendarsSrv`, you can mock `calendarsSrv` in tests for `CreateReservationEvent` to isolate testing the use case logic. However, this benefit needs to be weighed against the cohesion and clarity of your code structure.

### 3. **Code Reusability**
- If you anticipate that similar reservation creation or updating logic will be used in other parts of the application, extracting these methods to a service can reduce duplication. This is a strong argument for moving them to `calendarsSrv`.

### 4. **Layered Architecture**
- **Separation of Concerns**: Consider the separation of concerns principle. The `CreateReservationEvent` class should primarily be concerned with coordinating the flow of data and handling use case-specific logic. Complex business logic, particularly if it's reusable, is typically better situated in the service layer.
- **Domain Logic vs Application Logic**: Distinguish between domain logic (which belongs in domain services) and application logic (which belongs in application services or use cases). If `handleExistingReservation` and `handleNewReservation` are manipulating domain entities in ways that are fundamental to the business rules, they might be more appropriate in a domain service.

### 5. **Coupling**
- Consider the coupling between classes. If moving these methods to `calendarsSrv` introduces tight coupling or makes `calendarsSrv` too large and unwieldy, it might be better to keep them in `CreateReservationEvent`.

### Conclusion
In conclusion, the decision hinges on factors like the specific responsibilities of `calendarsSrv`, the likelihood of reuse of the methods in other contexts, the importance of keeping `CreateReservationEvent` cohesive and focused, and the architecture principles you are following. If these methods are highly specific to creating and updating reservations and are not expected to be reused outside this context, it may be more appropriate to keep them within `CreateReservationEvent`. However, if they involve significant business logic that could be leveraged in other parts of the application, moving them to `calendarsSrv` could be advantageous.
</details>

---

<details>
  <summary>Improvements on CreateReservationEvent.unit.ts?</summary>
I'm using the following unit tests. Would you recommend any improvement?

```typescript
// src/modules/calendar/useCases/createReservationEvent/CreateReservationEvent.unit.ts
import { Calendar } from '../../domain/Calendar';

process.env.calendar_clientEmail = 'dummy';
process.env.calendar_clientKey = 'dummy';
import { createEventRetrieved } from '../../utils/test';
import { CreateReservationEvent } from './CreateReservationEvent';
import { CalendarRepoFake, CalendarsInDb } from '../../repos/CalendarRepoFake';
import { CalendarsSrv } from '../../services/CalendarsSrv';
import { ICalendarSrv } from '../../services/ICalendarSrv';
import { CalendarSrv } from '../../services/CalendarSrv';
import { CalendarApiFake } from '../../services/CalendarApiFake';
import { DateTime } from 'luxon';
import { Result } from '@shared/core/Result';
import { EventRetrieved } from '../../domain/EventRetrieved';
import { Spots } from '../../domain/Spots';
import { Slot } from '../../domain/Slot';
import { Event } from '../../domain/Event';
import { UnexpectedError } from '@shared/core/AppError';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const identifier = 'loc1';
const nextDay = DateTime.now().plus({ day: 1 }).startOf('day');
const start = nextDay.toJSDate();
const end = nextDay.plus({ minutes: 30}).toJSDate();
const customer = {
  firstName: 'John',
  lastName: 'Smith',
  phone: '+549351284567',
  email: '<EMAIL>',
}
const reserved = 2;
const name = 'Brand new reservation';

const input = {
  identifier,
  start,
  end,
  timeZone: 'America/Cordoba',
  userId: CalendarsInDb[0].userId,
  name,
  customer,
}

const getCalendarSrv = (calendarId: string): ICalendarSrv => {
  const api = new CalendarApiFake(calendarId);
  return new CalendarSrv(api);
}
const calendarsSrv = new CalendarsSrv(getCalendarSrv);
const calendarRepo = new CalendarRepoFake();
const createReservationEvent = new CreateReservationEvent({
  calendarRepo,
  calendarsSrv,
});

describe(`succeeds`, () => {
  it(`creates an event`, async () => {

    const mockGetSpotsForReservation = () =>
      new Promise<Result<{spots: Spots | null, existingReservation: EventRetrieved | undefined}>>((resolve) => {
        resolve(Result.ok({
          spots: Spots.create(reserved).value,
          existingReservation: undefined,
        }));
      });
    jest.spyOn(calendarsSrv, 'getSpotsForReservation').mockImplementation(mockGetSpotsForReservation);

    const mockCreateReservation = (event: Event) =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        const regExp = new RegExp(`John Smith \\(phone: \\+549351284567, email: <EMAIL>\\) reserved at [0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}[\\\\+-][01]\\d:[0-5]\\d`
          + `\n#loc1`);
        expect(event.toRaw()).toMatchObject({
          name,
          description: expect.stringMatching(regExp),
          slot: {
            start,
            end,
          },
        })
        resolve(Result.ok(createEventRetrieved()));
      });
    jest.spyOn(calendarsSrv, 'createReservation').mockImplementation(mockCreateReservation);

    const createReservationEvent = new CreateReservationEvent({
      calendarRepo,
      calendarsSrv,
    });

    const response = await createReservationEvent.executeImpl(input);

    expect(response).toMatchObject({
      status: 200,
      result: {
        action: 'CREATED',
      },
    })
  });

  it(`updates an event with just #identifier:n in description`, async () => {

    const existingReservation = EventRetrieved.create({
      name: 'Existing reservation',
      description: `#${identifier}:${reserved}`,
      slot: Slot.create({ start, end }).value,
      id: chance.string(),
    });

    const mockGetSpotsForReservation = () =>
      new Promise<Result<{spots: Spots | null, existingReservation: EventRetrieved | undefined}>>((resolve) => {
        resolve(Result.ok({
          spots: Spots.create(reserved).value,
          existingReservation,
        }));
      });
    jest.spyOn(calendarsSrv, 'getSpotsForReservation').mockImplementation(mockGetSpotsForReservation);

    const mockUpdateReservation = (eventUpdated: EventRetrieved) =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        const regExp = new RegExp(`John Smith \\(phone: \\+549351284567, email: <EMAIL>\\) reserved at [0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}[\\\\+-][01]\\d:[0-5]\\d`
          + `\n#${identifier}:${reserved+1}`);
        expect(eventUpdated.description).toMatch(regExp);
        resolve(Result.ok(createEventRetrieved()));
      });
    jest.spyOn(calendarsSrv, 'updateReservation').mockImplementation(mockUpdateReservation);

    const createReservationEvent = new CreateReservationEvent({
      calendarRepo,
      calendarsSrv,
    });

    const response = await createReservationEvent.executeImpl(input);

    expect(response).toMatchObject({
      status: 200,
      result: {
        action: 'UPDATED',
      },
    });
  });

  it(`updates an event with a longer description`, async () => {

    const existingReservation = EventRetrieved.create({
      name: 'Existing reservation',
      description: `Original sentence #${identifier}:${reserved}`,
      slot: Slot.create({ start, end }).value,
      id: chance.string(),
    });

    const mockGetSpotsForReservation = () =>
      new Promise<Result<{spots: Spots | null, existingReservation: EventRetrieved | undefined}>>((resolve) => {
        resolve(Result.ok({
          spots: Spots.create(reserved).value,
          existingReservation,
        }));
      });
    jest.spyOn(calendarsSrv, 'getSpotsForReservation').mockImplementation(mockGetSpotsForReservation);

    const mockUpdateReservation = (eventUpdated: EventRetrieved) =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        const regExp = new RegExp(
          `John Smith \\(phone: \\+549351284567, email: <EMAIL>\\) reserved at [0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}[\\\\+-][01]\\d:[0-5]\\d`
          + `\nOriginal sentence #${identifier}:${reserved+1}`
        );
        expect(eventUpdated.description).toMatch(regExp);
        resolve(Result.ok(createEventRetrieved()));
      });
    jest.spyOn(calendarsSrv, 'updateReservation').mockImplementation(mockUpdateReservation);

    const createReservationEvent = new CreateReservationEvent({
      calendarRepo,
      calendarsSrv,
    });

    const response = await createReservationEvent.executeImpl(input);

    expect(response).toMatchObject({
      status: 200,
      result: {
        action: 'UPDATED',
      },
    });
  });
});

describe(`fails`, () => {
  test(`with an invalid identifier`, async () => {
    const response = await createReservationEvent.executeImpl({
      ...input,
      identifier: 'invalid id',
    });

    expect(response).toMatchObject({
      status: 400,
      result: {
        type: expect.stringContaining('IdentifierErrors.'),
        message: expect.any(String),
        status: 400,
      },
    });
  });

  test(`with invalid start-end values`, async () => {
    const response = await createReservationEvent.executeImpl({
      ...input,
      end: start,
    });

    expect(response).toMatchObject({
      status: 400,
      result: {
        type: expect.stringContaining('SlotErrors.'),
        message: expect.any(String),
        status: 400,
      },
    });
  });

  test(`when user isn't found in availability repo`, async () => {
    const response = await createReservationEvent.executeImpl({
      ...input,
      userId: 'non-existent',
    });

    expect(response).toMatchObject({
      status: 404,
      result: {
        type: 'CreateReservationEventErrors.AvailabilityCalendarNotFoundForUserId',
        message: expect.any(String),
        status: 404,
      },
    });
  });

  test(`when user isn't found in reservations repo`, async () => {
    const calendarRepo = new CalendarRepoFake();

    const mockGetReservations = (): Promise<Calendar | null>  => {
      return new Promise(resolve => {
        resolve(null);
      });
    };
    jest.spyOn(calendarRepo, 'getReservations').mockImplementation(mockGetReservations);

    const createReservationEvent = new CreateReservationEvent({
      calendarRepo,
      calendarsSrv,
    });

    const response = await createReservationEvent.executeImpl(input);

    expect(response).toMatchObject({
      status: 404,
      result: {
        type: 'CreateReservationEventErrors.ReservationCalendarNotFoundForUserId',
        message: expect.any(String),
        status: 404,
      },
    });
  });

  test(`when no spots are found`, async () => {
    const calendarsSrv = new CalendarsSrv(getCalendarSrv);
    const mockGetSpotsForReservation = () =>
      new Promise<Result<{spots: Spots | null, existingReservation: EventRetrieved | undefined}>>((resolve) => {
        resolve(Result.ok({
          spots: null,
          existingReservation: undefined,
        }));
      });
    jest.spyOn(calendarsSrv, 'getSpotsForReservation').mockImplementation(mockGetSpotsForReservation);

    const createReservationEvent = new CreateReservationEvent({
      calendarRepo,
      calendarsSrv,
    });

    const response = await createReservationEvent.executeImpl(input);

    expect(response).toMatchObject({
      status: 404,
      result: {
        type: 'CreateReservationEventErrors.NoSpotsFound',
        message: expect.any(String),
        status: 404,
      },
    });
  });

  test(`when calendarsSrv.updateReservation fails `, async () => {
    const calendarsSrv = new CalendarsSrv(getCalendarSrv);
    const existingReservation = EventRetrieved.create({
      name: 'Existing reservation',
      description: `#${identifier}:${reserved}`,
      slot: Slot.create({ start, end }).value,
      id: chance.string(),
    });

    const mockGetSpotsForReservation = () =>
      new Promise<Result<{spots: Spots | null, existingReservation: EventRetrieved | undefined}>>((resolve) => {
        resolve(Result.ok({
          spots: Spots.create(reserved).value,
          existingReservation,
        }));
      });
    jest.spyOn(calendarsSrv, 'getSpotsForReservation').mockImplementation(mockGetSpotsForReservation);
    const mockUpdateReservation = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.fail(new UnexpectedError()));
      });
    jest.spyOn(calendarsSrv, 'updateReservation').mockImplementation(mockUpdateReservation);

    const createReservationEvent = new CreateReservationEvent({
      calendarRepo,
      calendarsSrv,
    });

    const response = await createReservationEvent.executeImpl(input);

    expect(response).toMatchObject({
      status: 500,
      result: {
        type: 'UnexpectedError',
        message: expect.any(String),
        status: 500,
      },
    });
  });

  test(`when calendarsSrv.createReservation fails`, async () => {

    const mockGetSpotsForReservation = () =>
      new Promise<Result<{spots: Spots | null, existingReservation: EventRetrieved | undefined}>>((resolve) => {
        resolve(Result.ok({
          spots: Spots.create(reserved).value,
          existingReservation: undefined,
        }));
      });
    jest.spyOn(calendarsSrv, 'getSpotsForReservation').mockImplementation(mockGetSpotsForReservation);

    const mockCreateReservation = () =>
      new Promise<Result<EventRetrieved>>((resolve) => {
        resolve(Result.fail(new UnexpectedError()));
      });
    jest.spyOn(calendarsSrv, 'createReservation').mockImplementation(mockCreateReservation);

    const createReservationEvent = new CreateReservationEvent({
      calendarRepo,
      calendarsSrv,
    });

    const response = await createReservationEvent.executeImpl(input);

    expect(response).toMatchObject({
      status: 500,
      result: {
        type: 'UnexpectedError',
        message: expect.any(String),
        status: 500,
      },
    });
  });

});
```
</details>

<details>
  <summary>ChatGPT</summary>
Improving your unit tests involves applying best practices for readability, maintainability, and robustness. Below is a revised version of your tests with these improvements:

```typescript
import { CalendarRepoFake, CalendarsInDb } from '../../repos/CalendarRepoFake';
import { CalendarsSrv } from '../../services/CalendarsSrv';
import { CreateReservationEvent } from './CreateReservationEvent';
import { CalendarSrv } from '../../services/CalendarSrv';
import { CalendarApiFake } from '../../services/CalendarApiFake';
import { DateTime } from 'luxon';
import { Result } from '@shared/core/Result';
import { EventRetrieved } from '../../domain/EventRetrieved';
import { Spots } from '../../domain/Spots';
import { Slot } from '../../domain/Slot';
import { Event } from '../../domain/Event';
import { UnexpectedError } from '@shared/core/AppError';
const chance = require('chance').Chance();

// Common setup
const identifier = 'loc1';
const nextDay = DateTime.now().plus({ day: 1 }).startOf('day');
const start = nextDay.toJSDate();
const end = nextDay.plus({ minutes: 30 }).toJSDate();
const customer = { firstName: 'John', lastName: 'Smith', phone: '+549351284567', email: '<EMAIL>' };
const reserved = 2;
const name = 'Brand new reservation';
const input = { identifier, start, end, timeZone: 'America/Cordoba', userId: CalendarsInDb[0].userId, name, customer };

const getCalendarSrv = (calendarId: string) => new CalendarSrv(new CalendarApiFake(calendarId));
const calendarsSrv = new CalendarsSrv(getCalendarSrv);
const calendarRepo = new CalendarRepoFake();

let createReservationEvent: CreateReservationEvent;

beforeEach(() => {
  jest.clearAllMocks();
  createReservationEvent = new CreateReservationEvent({ calendarRepo, calendarsSrv });
});

describe('CreateReservationEvent', () => {
  describe('Success scenarios', () => {
    it('creates a new reservation event', async () => {
      // Mock setup
      jest.spyOn(calendarsSrv, 'getSpotsForReservation').mockResolvedValue(Result.ok({ spots: Spots.create(reserved).value, existingReservation: undefined }));
      jest.spyOn(calendarsSrv, 'createReservation').mockImplementation(mockCreateReservation);

      const response = await createReservationEvent.executeImpl(input);

      expect(response).toMatchObject({ status: 200, result: { action: 'CREATED' } });
    });

    // Similar structure for other success scenarios...

  });

  describe('Failure scenarios', () => {
    it('fails with an invalid identifier', async () => {
      const invalidInput = { ...input, identifier: 'invalid id' };
      const response = await createReservationEvent.executeImpl(invalidInput);

      expectErrorResponse(response, 'IdentifierErrors.', 400);
    });

    // Similar structure for other failure scenarios...

  });
});

function expectErrorResponse(response: any, errorTypeSubstring: string, statusCode: number) {
  expect(response).toMatchObject({
    status: statusCode,
    result: {
      type: expect.stringContaining(errorTypeSubstring),
      message: expect.any(String),
      status: statusCode,
    },
  });
}

function mockCreateReservation(event: Event) {
  return new Promise<Result<EventRetrieved>>((resolve) => {
    // Simplified RegExp for readability
    const descriptionRegExp = new RegExp(`\\(phone: ${customer.phone}, email: ${customer.email}\\) reserved at`);
    expect(event.toRaw()).toMatchObject({
      name,
      description: expect.stringMatching(descriptionRegExp),
      slot: { start, end },
    });
    resolve(Result.ok(createEventRetrieved()));
  });
}

function createEventRetrieved(): EventRetrieved {
  // Implementation of createEventRetrieved...
}
```

### Key Improvements:

1. **Common Setup**: Centralized the setup logic in `beforeEach` to reduce code duplication.

2. **Descriptive Test Naming**: Updated the test descriptions to be more specific about what each test is verifying.

3. **Simplified Mock Implementations**: The mock implementations are simplified for easier understanding and maintenance.

4. **Error Response Testing**: Created a utility function `expectErrorResponse` to standardize the assertion of error responses across different tests.

5. **Regular Expression Simplification**: Simplified the regular expressions used in the mock implementation for better readability.

6. **Centralized Mock Functions**: Created standalone functions for mocking implementations, reducing the complexity within individual tests.

Remember, unit tests are not just for ensuring that code works as expected, but also serve as documentation and examples of how the code should be used. Therefore, readability and maintainability are just as important as coverage and accuracy.
</details>

---

<details>
  <summary>prompt title</summary>
prompt
</details>

<details>
  <summary>ChatGPT</summary>
response
</details>

---

<details>
  <summary>prompt title</summary>
prompt
</details>

<details>
  <summary>ChatGPT</summary>
response
</details>

---

<details>
  <summary>prompt title</summary>
prompt
</details>

<details>
  <summary>ChatGPT</summary>
response
</details>
