import { test } from 'vitest';
import { DateTime, IANAZone } from 'luxon';

test(`isTimeInSlot`, () => {
  const result = isTimeInSlot(
    {
      // hour: 8,
      // hour: 5,
      // hour: 10,
      hour: 13,
      minute: 30,
      // zone: 'Australia/Sydney', // +11
      zone: 'Pacific/Kiritimati', // +14
    },
    {
      // start: new Date(1704797110415, { timeZone: 'Europe/London' }),
      // start: new Date('January 09, 2024, 21:00:00 UTC'), // Las 8:00 en Sydney (+11) son las 21:00 del día anterior en London
      // start: new Date('January 09, 2024, 18:00:00 UTC'), // Las 05:00 del 10/1 en Sydney son las 18:00 del 09/1 en London
      // start: new Date('January 09, 2024, 23:00:00 UTC'), // Las 10:00 del 10/1 en Sydney son las 23:00 del 09/1 en London
      start: new Date('January 09, 2024, 23:00:00 UTC'), // Las 13:00 del 10/1 en Kiritimati son las 23:00 del 09/1 en London
      // end: new Date('2024-02-01T20:00:00'),
      // end: new Date('January 10, 2024, 09:00:00 UTC'), //   Las 20:00 del 10/1 en Sidney son las 09:00 del 10/1 en London
      // end: new Date('January 10, 2024, 09:00:00 UTC'), //   Las 20:00 del 10/1 en Sidney son las 09:00 del 10/1 en London
      end: new Date('January 10, 2024, 06:00:00 UTC'), //   Las 20:00 del 10/1 en Kiritimati son las 06:00 del 10/1 en London
    },
  );

  console.log(result);

  interface TimeReference {
    hour: number;
    minute: number;
    zone: string;
  }

  interface Slot {
    start: Date;
    end: Date;
  }

  type IsWithinResult =
    | {
        isWithin: true;
        utcTime: Date;
      }
    | {
        isWithin: false;
      };

  function isTimeInSlot(timeRef: TimeReference, slot: Slot): IsWithinResult {
    if (!IANAZone.isValidZone(timeRef.zone)) {
      throw new Error('Invalid time zone');
    }

    // Convert Slot start and end to Luxon DateTime for comparison
    const startDateTime = DateTime.fromJSDate(slot.start).startOf('day');
    // const startDateTime = DateTime.fromJSDate(slot.start).minus({ days: 1 });
    const endDateTime = DateTime.fromJSDate(slot.end).endOf('day');
    // const endDateTime = DateTime.fromJSDate(slot.end).plus({ days: 1 });

    // Check each day in the slot range
    for (
      let day = startDateTime;
      day <= endDateTime;
      day = day.plus({ days: 1 })
    ) {
      // Create a DateTime object for the specific day at the specified hour and minute in the given time zone
      const localTimeInZone = day.setZone(timeRef.zone).set({
        hour: timeRef.hour,
        minute: timeRef.minute,
        second: 0,
        millisecond: 0,
      });

      // Convert the time to UTC
      const utcTime = localTimeInZone.toUTC();

      const datesAreEqual = utcTime === localTimeInZone;
      console.log('datesAreEqual', datesAreEqual);
      const unixAreEqual =
        utcTime.toUnixInteger() === localTimeInZone.toUnixInteger();
      console.log('unixAreEqual', unixAreEqual);
      const comparison1 =
        utcTime >= DateTime.fromJSDate(slot.start) &&
        utcTime <= DateTime.fromJSDate(slot.end);
      const comparison2 =
        localTimeInZone >= DateTime.fromJSDate(slot.start) &&
        localTimeInZone <= DateTime.fromJSDate(slot.end);
      const comparisonsAreEqual = comparison1 === comparison2;
      console.log('comparisonsAreEqual', comparisonsAreEqual);
      // Check if the UTC time is within the slot for that day
      if (
        utcTime >= DateTime.fromJSDate(slot.start) &&
        utcTime <= DateTime.fromJSDate(slot.end)
      ) {
        return {
          isWithin: true,
          utcTime: utcTime.toJSDate(),
        };
      }
    }

    return {
      isWithin: false,
    };
  }
});
