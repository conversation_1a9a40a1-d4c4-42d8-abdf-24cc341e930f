import { AnalyticsEventRepo } from '../../repos/AnalyticsEventRepo';
import { Send } from './Send';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize_analytics/models');

const repo = new AnalyticsEventRepo(models.AnalyticsEvent);
const controller = new Send({ repo });

export const handler = controller.executeImpl.bind(controller);