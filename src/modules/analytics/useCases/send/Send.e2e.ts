import { test, expect } from 'vitest';
import { Lambda } from '@aws-sdk/client-lambda';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Request } from './SendDTOs';
import { getRequest, rmAnalyticsEvent } from '../../utils/test';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize_analytics/models');

// Add all process.env used:
const { sendAnalyticsEvent } = process.env;
if (!sendAnalyticsEvent) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}

const lambdaInvoker = new LambdaInvoker(new Lambda({}));

function getExpectedEvent(request: Request) {
  return {
    ...request,
    id: expect.any(String),
    date: expect.any(Date),
    created_at: expect.any(Date),
    updated_at: expect.any(Date),
    deleted_at: null,
  };
}

test(`sends analytics event`, async () => {

  const request = getRequest();

  // Execute the useCase
  await lambdaInvoker.invokeSync<Request>(
    request,
    sendAnalyticsEvent,
  );

  // Query the database to verify the event was saved
  const events = await models.AnalyticsEvent.findAll({
    where: {
      logStream: request.logStream,
      logGroup: request.logGroup,
      requestId: request.requestId,
      cognitoIdentityId: request.cognitoIdentityId,
      // @ts-expect-error opt out typing
      'data.something': request.data.something,
    },
  });

  // Verify the event was saved correctly
  expect(events.length).toBe(1);
  const savedEvent = events[0].dataValues;
  
  expect(savedEvent).toEqual(getExpectedEvent(request));

  // Clean up
  await rmAnalyticsEvent({ id: savedEvent.id });
});
