import { test, expect, vi, beforeEach } from 'vitest';
import { Send } from './Send';
import { getRequest } from '../../utils/test';

const mockRepo = {
  insert: vi.fn().mockResolvedValue(undefined),
};
const controller = new Send({ repo: mockRepo });

// Reset mocks before each test
beforeEach(() => {
  vi.clearAllMocks();
});

test('successfully processes a valid event', async () => {
  await controller.executeImpl(getRequest());

  expect(mockRepo.insert).toHaveBeenCalledTimes(1);
});