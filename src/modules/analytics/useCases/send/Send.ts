import { Request } from './SendDTOs';
import { IAnalyticsEventRepo } from '../../repos/IAnalyticsEventRepo';
import { AnalyticsEvent } from '../../domain/AnalyticsEvent';

// Since AsynchronousController count on this very file to send analytics (looks for process.env.sendAnalyticsEvent), we can't use it because of circular dependency.
// export class Send extends AsynchronousController<Request> {
export class Send {
  private repo: IAnalyticsEventRepo;

  public constructor(args: {
    repo: IAnalyticsEventRepo,
  }) {
    const { repo } = args;
    this.repo = repo;
  }

  public async executeImpl(event: Request) {
    console.log(`${this.constructor.name}.executeImpl`, event);
    await this.repo.insert(AnalyticsEvent.create(event));
  }
}