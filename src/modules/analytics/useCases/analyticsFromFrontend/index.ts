import { AnalyticsFromFrontend } from './AnalyticsFromFrontend';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
import { AnalyticsFrontRepo } from '../../repos/AnalyticsFrontRepo';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize_analytics/models');

const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });
const repo = new AnalyticsFrontRepo(models.AnalyticsFront);
const controller = new AnalyticsFromFrontend({ repo, contextProvider });

export const handler = controller.execute.bind(controller);
