import { test, expect } from 'vitest';
import { gql } from 'graphql-tag';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import { Request } from './AnalyticsFromFrontendDTOs';
import { possibleLngs } from '@shared/utils/utils';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize_analytics/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const appsync = new AppSyncClient();
const query = gql`
  mutation (
    $userId: ID
    $uiLang: String!
    $label: String!
    $date: String!
    $appState: AWSJSON!
    $ua: AWSJSON!
    $data: AWSJSON!
  ) {
    analyticsFromFrontend(
      userId: $userId
      uiLang: $uiLang
      label: $label
      date: $date
      appState: $appState
      ua: $ua
      data: $data
    ) {
      statusCode
      message
    }
  }
`;

// Helper function to generate test request
function getAnalyticsFromFrontendRequest(): Request {
  return {
    userId: chance.bool() ? chance.guid() : null,
    data: JSON.stringify({  extra: chance.word() }),
    uiLang: chance.pickone(possibleLngs),
    ua: JSON.stringify({
      browser: {
        name: chance.word(),
        version: chance.string({ numeric: true, length: 2 }),
      },
      device: {
        type: chance.word(),
        vendor: chance.word(),
      },
    }),
    label: chance.word(),
    appState: JSON.stringify({ something: chance.word() }),
    date: new Date().toJSON(),
  };
}

// Helper function to clean up test data
async function rmAnalyticsFront({ id }: { id: string }) {
  await models.AnalyticsFront.destroy({
    where: { id },
    force: true,
  });
}

function getExpectedEvent(request: Request) {
  return {
    ...request,
    // @ts-expect-error opt out typing
    appState: JSON.parse(request.appState),
    // @ts-expect-error opt out typing
    ua: JSON.parse(request.ua),
    // @ts-expect-error opt out typing
    data: JSON.parse(request.data),
    id: expect.any(String),
    date: expect.any(Date),
    created_at: expect.any(Date),
    updated_at: expect.any(Date),
    deleted_at: null,
  };
}

test(`sends analytics event from frontend`, async () => {
  const request = getAnalyticsFromFrontendRequest();

  // Execute the useCase via GraphQL mutation
  const received = await appsync.send<Request>({
    query,
    variables: request,
  });

  // Verify the GraphQL response
  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.analyticsFromFrontend;
  expect(response.statusCode).toBe(202);
  expect(response.message).toBeDefined();

  // Query the database to verify the event was saved
  // @ts-expect-error opt out typing
  const ua = JSON.parse(request.ua);
  // @ts-expect-error opt out typing
  const appState = JSON.parse(request.appState);
  // @ts-expect-error opt out typing
  const data = JSON.parse(request.data);
  const events = await models.AnalyticsFront.findAll({
    where: {
      userId: request.userId,
      uiLang: request.uiLang,
      label: request.label,
      'ua.browser.name': ua.browser.name,
      'ua.browser.version': ua.browser.version,
      'ua.device.type': ua.device.type,
      'ua.device.vendor': ua.device.vendor,
      'appState.something': appState.something,
      'data.extra': data.extra,
    },
  });

  // Verify the event was saved correctly
  expect(events.length).toBe(1);
  const savedEvent = events[0].dataValues;

  expect(savedEvent).toEqual(getExpectedEvent(request));

  // Clean up
  await rmAnalyticsFront({ id: savedEvent.id });
});
