import { AsynchronousController } from '@shared/infra/Controllers';
import { Request } from './AnalyticsFromFrontendDTOs';
import { IContextProvider } from '@shared/context/IContextProvider';
import { IAnalyticsFrontRepo } from '../../repos/IAnalyticsFrontRepo';
import { AnalyticsFront } from '../../domain/AnalyticsFront';

export class AnalyticsFromFrontend extends AsynchronousController<Request> {
  private repo: IAnalyticsFrontRepo;

  public constructor(args: {
    repo: IAnalyticsFrontRepo;
    contextProvider: IContextProvider;
  }) {
    const { contextProvider, repo } = args;
    super({  contextProvider });
    this.repo = repo;
  }

  public async executeImpl(request: Request) {
    // console.log(`${this.constructor.name}.executeImpl`, request);
    await this.repo.insert(AnalyticsFront.create(request));
  }
}
