import { Request } from '../useCases/send/SendDTOs';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize_analytics/models');

// Helper function to clean up test data
export async function rmAnalyticsEvent(where: unknown) {
  await models.AnalyticsEvent.destroy({
    where,
    force: true,
  });
}

export function getRequest(): Request {
  return {
    logStream: chance.string({ alpha: true, numeric: true, length: 15 }),
    logGroup: chance.string({ alpha: true, numeric: true, length: 15 }),
    requestId: chance.string({ alpha: true, numeric: true, length: 15 }),
    cognitoIdentityId: chance.string({ alpha: true, numeric: true, length: 15 }),
    date: new Date().toJSON(),
    data: { something: chance.word() },
  }
}