import { EntityID } from '@shared/core/domain/EntityID';
import { Spread } from '@shared/utils/utils';
import { Entity } from '@shared/core/domain/Entity';

type AnalyticsEventInput = {
  logStream: string;
  logGroup: string;
  requestId: string;
  cognitoIdentityId: string | null;
  date: string;
  data: unknown;
};

type AnalyticsEventProps = AnalyticsEventInput;

export type AnalyticsEventDto = Spread<
  AnalyticsEventProps,
  {
    id: string;
  }
>;

export class AnalyticsEvent extends Entity<AnalyticsEventProps, AnalyticsEventDto> {
  private __class = this.constructor.name;

  private constructor(props: AnalyticsEventProps, id?: EntityID) {
    super(props, id);
  }

  public static create(input: AnalyticsEventInput): AnalyticsEvent {
    return new AnalyticsEvent(input);
  }

  public toDto(): AnalyticsEventDto {
    const {_id: {value: id}} = this;

    return {
      ...this.props,
      id: id.toString(),
    };
  }

}
