import { EntityID } from '@shared/core/domain/EntityID';
import { Spread } from '@shared/utils/utils';
import { Entity } from '@shared/core/domain/Entity';

type AnalyticsFrontInput = {
  label: string;
  userId: string | null;
  uiLang: string;
  date: string;
  appState: unknown;
  ua: unknown;
  data: unknown;
};

type AnalyticsFrontProps = AnalyticsFrontInput;

export type AnalyticsFrontDto = Spread<
  AnalyticsFrontProps,
  {
    id: string;
  }
>;

export class AnalyticsFront extends Entity<AnalyticsFrontProps, AnalyticsFrontDto> {
  private __class = this.constructor.name;

  private constructor(props: AnalyticsFrontProps, id?: EntityID) {
    super(props, id);
  }

  public static create(input: AnalyticsFrontInput): AnalyticsFront {
    return new AnalyticsFront(input);
  }

  public toDto(): AnalyticsFrontDto {
    const {_id: {value: id}} = this;

    return {
      ...this.props,
      id: id.toString(),
    };
  }

}
