import { IAnalyticsEventRepo } from './IAnalyticsEventRepo';
import { AnalyticsEvent } from '../domain/AnalyticsEvent';

export class AnalyticsEventRepo
  implements IAnalyticsEventRepo
{
  private AnalyticsEvent: any; // eslint-disable-line @typescript-eslint/no-explicit-any

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public constructor(AnalyticsEventModel: any) {
    if (AnalyticsEventModel.tableName !== 'analytics_events')
      throw Error(
        `Wrong model passed in: ${AnalyticsEventModel.tableName}, while correct is analytics_events`,
      );
    this.AnalyticsEvent = AnalyticsEventModel;
  }

  public async insert(event: AnalyticsEvent) {
    return this.AnalyticsEvent.create(event.toDto());
  }
}
