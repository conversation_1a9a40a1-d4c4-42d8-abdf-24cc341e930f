import { IAnalyticsFrontRepo } from './IAnalyticsFrontRepo';
import { AnalyticsFront } from '../domain/AnalyticsFront';

export class AnalyticsFrontRepo
  implements IAnalyticsFrontRepo
{
  private AnalyticsFront: any; // eslint-disable-line @typescript-eslint/no-explicit-any

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public constructor(AnalyticsFrontModel: any) {
    if (AnalyticsFrontModel.tableName !== 'analytics_fronts')
      throw Error(
        `Wrong model passed in: ${AnalyticsFrontModel.tableName}, while correct is analytics_fronts`,
      );
    this.AnalyticsFront = AnalyticsFrontModel;
  }

  public async insert(event: AnalyticsFront) {
    return this.AnalyticsFront.create(event.toDto());
  }
}
