import { expect, test, describe } from 'vitest';
import { getRelativeTime } from './utils';
import { Dat } from '@shared/core/Dat';
import { TimeZone } from '@shared/core/TimeZone';

describe('getRelativeTime', () => {
  test.only.each([
    // America/Cordoba GMT-3:00 / Asia/Calcutta GMT+5:30
    { timezone: 'America/Cordoba', lng: 'es' as const,expectedDay: 'hoy' },
    { timezone: 'Asia/Calcutta', lng: 'en' as const, expectedDay: 'today' },
    { timezone: 'America/Cordoba', lng: 'en' as const,expectedDay: 'today' },
    { timezone: 'Asia/Calcutta', lng: 'es' as const, expectedDay: 'hoy' },
  ])('formats day and time according to %s', ({ timezone, lng, expectedDay }) => {
    // Parameters are already destructured in the function signature
    const now = Dat.create().value;

    const got = getRelativeTime({
      date: now,
      lng,
      timezone: { value: timezone } as TimeZone,
    });

    expect(got).toContain(expectedDay);
    expect(got).toContain(now.l.setZone(timezone).toFormat('HH:mm'));
  });
});
