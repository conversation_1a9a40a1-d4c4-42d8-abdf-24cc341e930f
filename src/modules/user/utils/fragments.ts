import gql from 'graphql-tag';

export const ManuallyCreatedCustomerFragment = gql`
  fragment ManuallyCreatedCustomerFragment on ManuallyCreatedCustomer {
    id
    firstName
    lastName
    fullName
    notes
    createdBy
  }
`;
export const ManuallyCreatedCustomerWithTimestampsFragment = gql`
  fragment ManuallyCreatedCustomerWithTimestampsFragment on ManuallyCreatedCustomerWithTimestamps {
    id
    firstName
    lastName
    fullName
    notes
    createdBy
    created_at
    updated_at
    deleted_at
  }
`;

export const CustomerFragment = gql`
  fragment CustomerFragment on Customer {
    id
    firstName
    lastName
    email
    phone
    timezone
  }
`;