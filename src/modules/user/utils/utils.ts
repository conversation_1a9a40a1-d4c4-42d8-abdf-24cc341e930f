import { PossibleLngs } from '@shared/utils/utils';
import { Dat } from '@shared/core/Dat';
import { TimeZone } from '@shared/core/TimeZone';
import gql from 'graphql-tag';

export function getRelativeTime(args: {
  date: Dat;
  lng: PossibleLngs;
  timezone: TimeZone;
}) {
  const { date, lng, timezone } = args;
  const dateAccTz = date.l.setZone(timezone.value).setLocale(lng);
  const day = dateAccTz.toRelativeCalendar() || dateAccTz.toFormat('MMMM dd');
  const time = dateAccTz.toFormat('HH:mm');
  switch (lng) {
    case 'es':
      return `${day} a las ${time}`;
    default:
      return `${day} at ${time}`;

  }
}

export const BusinessUserForFrontendFragment = gql`
  fragment BusinessUserForFrontendFragment on BusinessUser {
    id
    firstName
    lastName
    lng
    email
    phone
    timezone
    phoneVerification {
      status
      blockedUntil
      newPhone
      codeExpiresAt
    }
  }
`;
