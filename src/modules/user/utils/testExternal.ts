import { GeneralSettingsRepo as _GSforReservationRepo } from '../../reservation/repos/GeneralSettingsRepo';
import { rmGS as _rmGSforReservation } from '../../reservation/utils/test';
import { ReservationsInDb as _ReservationsInDb } from '@shared/infra/database/sequelize/migrations/Reservations.json';
import { BalanceRepo as _BalanceRepo } from '../../balance/repos/BalanceRepo';
import { rmBalanceRow as _rmBalanceRow } from '../../balance/utils/test';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize/models');

export const GSforReservationRepo = new _GSforReservationRepo(
  models.GeneralSettingsForReservation,
);
export const rmGSforReservation = _rmGSforReservation;
export const ReservationsInDb = _ReservationsInDb;
export const BalanceRepo = new _BalanceRepo(
  models.BalanceRow,
);
export const rmBalanceRow = _rmBalanceRow;
