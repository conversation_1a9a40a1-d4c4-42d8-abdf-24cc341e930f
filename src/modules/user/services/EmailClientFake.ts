import { IEmailClient } from './IEmailClient';
import { Result2 } from '@shared/core/Result2';
import { PossibleLngs } from '@shared/utils/utils';

/**
 * Fake email client for testing purposes
 * Simulates email sending without actually sending emails
 */
export class EmailClientFake implements IEmailClient {
  public sentEmails: Array<{
    msg: string;
    emailAddress: string;
    subject: string;
    lng: PossibleLngs;
    timestamp: Date;
  }> = [];

  public shouldFail = false;
  public failureError: Error | null = null;

  public async send(params: {
    msg: string;
    emailAddress: string;
    subject: string;
    lng: PossibleLngs;
  }): Promise<Result2<undefined>> {
    const { msg, emailAddress, subject, lng } = params;

    if (this.shouldFail) {
      const error = this.failureError || new Error('Fake email client failure');
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return Result2.fail([error as any]);
    }

    // Store the sent email for verification in tests
    this.sentEmails.push({
      msg,
      emailAddress,
      subject,
      lng,
      timestamp: new Date(),
    });

    return Result2.ok(undefined);
  }

  /**
   * Reset the fake client state
   */
  public reset(): void {
    this.sentEmails = [];
    this.shouldFail = false;
    this.failureError = null;
  }

  /**
   * Configure the client to fail on next send
   */
  public setFailure(error?: Error): void {
    this.shouldFail = true;
    this.failureError = error || null;
  }

  /**
   * Get the last sent email
   */
  public getLastSentEmail() {
    return this.sentEmails[this.sentEmails.length - 1] || null;
  }

  /**
   * Get all sent emails
   */
  public getAllSentEmails() {
    return [...this.sentEmails];
  }

  /**
   * Check if an email was sent to a specific address
   */
  public wasEmailSentTo(emailAddress: string): boolean {
    return this.sentEmails.some(email => email.emailAddress === emailAddress);
  }
}
