// Set USE_REAL_AWS=true and TEST_TO_EMAIL_ADDRESS=<real email> in .env file to run this test
import { test, expect, vi, beforeEach, describe, it } from 'vitest';
import { AWSSESClient } from './AWSSESClient';
import { AWSSESClientErrors } from './AWSSESClientErrors';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { pickLng } from '@shared/utils/test';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const params = {
  msg: chance.string(),
  subject: chance.string(),
  lng: pickLng(),  
}

// Skip tests if USE_REAL_AWS is not set to true
const USE_REAL_AWS = process.env.USE_REAL_AWS === 'true';
if (!USE_REAL_AWS) {
  test.skip('Integration tests skipped - set USE_REAL_AWS=true to run', () => {
    console.log('Skipping AWS SES integration tests. Set USE_REAL_AWS=true to run them.');
  });
} else {
  describe('AWSSESClient Integration Tests', () => {
    let sesClient: AWSSESClient;
    let contextProvider: ContextProvider;
    
    beforeEach(() => {
      vi.clearAllMocks();
      
      contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
      sesClient = new AWSSESClient({ contextProvider });
      vi.spyOn(contextProvider, 'sendAnalytics').mockResolvedValue();
    });

    test('client initializes without errors', () => {
      expect(sesClient).toBeInstanceOf(AWSSESClient);
    });

    it.skip('sends a real email message', async () => {
      // IMPORTANT: This test will actually send an email and incur AWS charges
      // Use a valid email address that you can access
      const TEST_TO_EMAIL_ADDRESS = process.env.TEST_TO_EMAIL_ADDRESS;
      
      if (!TEST_TO_EMAIL_ADDRESS)
        throw 'TEST_TO_EMAIL_ADDRESS environment variable not set';

      const result = await sesClient.send({
        ...params,
        emailAddress: TEST_TO_EMAIL_ADDRESS,
      });
      
      // Check if the message was sent successfully
      expect(result.isSuccess).toBe(true);
    });

    it('handles invalid email address with real AWS', async () => {
      const result = await sesClient.send({
        ...params,
        emailAddress: 'invalid',
      });

      expect(result.isFailure).toBe(true);
      expect(result.errors?.[0]).toBeInstanceOf(AWSSESClientErrors.InvalidEmailAddress);
    });

    // All these scenarios are taken from the AWS SES console test email feature
    // https://us-east-1.console.aws.amazon.com/ses/home?region=us-east-1#/identities/santiagopergamo%40gmail.com/send-test-email
    // No scenario throws an error when using this.client.send
    /*it('handles scenario with real AWS', async () => {
      const result = await sesClient.send({
        ...params,
        // emailAddress: '<EMAIL>',
        // emailAddress: '<EMAIL>',
        // emailAddress: '<EMAIL>',
        // emailAddress: '<EMAIL>',
      });

      expect(result.isFailure).toBe(true);
      expect(result.errors?.[0]).toBeInstanceOf(expectedError);
    });*/

  });
}
