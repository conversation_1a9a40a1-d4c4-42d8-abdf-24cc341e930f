import { test, expect, vi, beforeEach } from 'vitest';
import { AWSSESClient } from './AWSSESClient';
import { AWSSESClientErrors } from './AWSSESClientErrors';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { pickLng } from '@shared/utils/test';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

// Mock AWS SDK
vi.mock('@aws-sdk/client-ses', () => {
  vi.stubEnv('AWS_SES_FROM_EMAIL', '<EMAIL>');
  return {
    SESClient: vi.fn().mockImplementation(() => ({
      send: vi.fn(),
    })),
    SendEmailCommand: vi.fn().mockImplementation((params) => ({
      params,
    })),
  };
});

// Import mocked modules
import { SESClient, SendEmailCommand } from '@aws-sdk/client-ses';

const params = {
  msg: chance.string(),
  emailAddress: chance.email(),
  subject: chance.string(),
  lng: pickLng(),
}

let awsSESClient: AWSSESClient;
let contextProvider: ContextProvider;
// eslint-disable-next-line @typescript-eslint/no-explicit-any
let mockSESSend: any;

beforeEach(() => {
  vi.clearAllMocks();

  // Mock environment variable
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  awsSESClient = new AWSSESClient({ contextProvider });
  vi.spyOn(contextProvider, 'sendAnalytics').mockResolvedValue();

  // Get the mocked SES client instance and its send method
  const MockedSESClient = vi.mocked(SESClient);
  // @ts-expect-error opt-out type checking for this mock
  const mockInstance = MockedSESClient.mock.results[MockedSESClient.mock.results.length - 1]?.value;
  mockSESSend = mockInstance?.send;
});

test('client initializes without errors', () => {
  expect(awsSESClient).toBeInstanceOf(AWSSESClient);
});

test('successfully sends an email message', async () => {
  // Mock successful response
  mockSESSend.mockResolvedValueOnce({ MessageId: 'test-message-id' });

  const result = await awsSESClient.send(params);

  // Verify result
  expect(result.isSuccess).toBe(true);

  // Verify AWS SES was called correctly
  expect(mockSESSend).toHaveBeenCalledTimes(1);
  expect(SendEmailCommand).toHaveBeenCalledWith({
    Source: '<EMAIL>',
    Destination: {
      ToAddresses: [params.emailAddress],
    },
    Message: {
      Subject: {
        Data: params.subject,
        Charset: 'UTF-8',
      },
      Body: {
        Text: {
          Data: params.msg,
          Charset: 'UTF-8',
        },
      },
    },
  });
});

test.each([
  {
    testName: 'handles invalid email address error',
    awsError: {
      name: 'InvalidParameterValue',
      message: 'Invalid email address format',
      $metadata: { requestId: 'test-request-id' },
    },
    emailAddress: 'invalid-email',
    expectedErrorClass: AWSSESClientErrors.InvalidEmailAddress,
    shouldVerifyAnalytics: true,
  },
  {
    testName: 'handles generic AWS error',
    awsError: {
      name: 'ServiceUnavailable',
      message: 'Service temporarily unavailable',
      $metadata: { requestId: 'test-request-id' },
    },
    emailAddress: '<EMAIL>',
    expectedErrorClass: AWSSESClientErrors.InternalError,
    shouldVerifyAnalytics: false,
  },
])('$testName', async ({ awsError, emailAddress, expectedErrorClass, shouldVerifyAnalytics }) => {
  mockSESSend.mockRejectedValueOnce(awsError);

  const result = await awsSESClient.send({
    ...params,
    emailAddress,
  });

  // Verify result
  expect(result.isFailure).toBe(true);
  expect(result.errors?.[0]).toBeInstanceOf(expectedErrorClass);

  // Verify analytics was called only for specific test cases
  if (shouldVerifyAnalytics) {
    expect(contextProvider.sendAnalytics).toHaveBeenCalledWith({
      error: awsError.name,
      errorMessage: awsError.message,
      emailAddress,
      metadata: awsError.$metadata,
    });
  }
});

test('throws error when AWS_SES_FROM_EMAIL environment variable is not set', async () => {
    // Since the env var validation happens at module load time,
    // we need to test this by dynamically importing the module without the env var
    vi.unstubAllEnvs();

    // Use vi.resetModules to clear the module cache
    vi.resetModules();

    await expect(async () => {
      // @ts-expect-error
      await import('./AWSSESClient');
    }).rejects.toThrow('Undefined env var AWS_SES_FROM_EMAIL!');
  });
