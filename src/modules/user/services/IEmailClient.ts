import { Result2 } from '@shared/core/Result2';
import { PossibleLngs } from '@shared/utils/utils';

/**
 * Interface for email client implementations
 */
export interface IEmailClient {
  /**
   * Sends an email message
   * @param params - The parameters for the email message
   * @returns A promise that resolves when the message is sent
   */
  send(params: {
    msg: string;
    emailAddress: string;
    subject: string;
    lng: PossibleLngs;
  }): Promise<Result2<undefined>>;
}
