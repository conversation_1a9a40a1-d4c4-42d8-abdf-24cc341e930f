import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { PossibleLngs } from '@shared/utils/utils';
import { Status } from '@shared/core/Status';

export namespace AWSSNSClientErrors {
  export class PhoneNumberOptedOut extends BaseError {
    public constructor(args: { lng: PossibleLngs }) {
      const { lng } = args;
      const t = trans[lng];
      super({
        message: t.phoneNumberOptedOut,
        status: Status.BAD_REQUEST,
      });
    }
  }
  
  export class InvalidPhoneNumber extends BaseError {
    public constructor(args: { lng: PossibleLngs }) {
      const { lng } = args;
      const t = trans[lng];
      super({
        message: t.invalidPhoneNumber,
        status: Status.BAD_REQUEST,
      });
    }
  }
  
  export class InternalError extends BaseError {
    public constructor(args: { lng: PossibleLngs }) {
      const { lng } = args;
      const t = trans[lng];
      super({
        message: t.internalError,
        status: Status.INTERNAL_ERROR,
      });
    }
  }
}

const trans = {
  en: {
    phoneNumberOptedOut: `This phone number has opted out of receiving SMS messages. Please contact your carrier to opt back in.`,
    invalidPhoneNumber: `The phone number format is invalid. Please use the international format (e.g., +1XXXXXXXXXX).`,
    internalError: `Unable to send verification code. Please try again later.`,
  },
  es: {
    phoneNumberOptedOut: `Este número de teléfono ha optado por no recibir mensajes SMS. Por favor, contacta a tu operador para volver a activar este servicio.`,
    invalidPhoneNumber: `El formato del número de teléfono no es válido. Por favor, utiliza el formato internacional (ej., +34XXXXXXXXX).`,
    internalError: `No se pudo enviar el código de verificación. Por favor, inténtalo de nuevo más tarde.`,
  },
};

patch({ AWSSNSClientErrors });