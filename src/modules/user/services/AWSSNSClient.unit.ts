import { test, expect, vi, beforeEach, describe } from 'vitest';
import { AWSSNSClient } from './AWSSNSClient';
import { AWSSNSClientErrors } from './AWSSNSClientErrors';
import { PossibleLngs } from '@shared/utils/utils';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { pickLng } from '@shared/utils/test';

// Mock AWS SDK
vi.mock('@aws-sdk/client-sns', () => {
  return {
    SNSClient: vi.fn().mockImplementation(() => ({
      send: vi.fn(),
    })),
    PublishCommand: vi.fn().mockImplementation((params) => ({
      params,
    })),
  };
});

// Import mocked modules
import { SNSClient, PublishCommand } from '@aws-sdk/client-sns';

const lng = pickLng();

describe('AWSSNSClient Unit Tests', () => {
  let awsSNSClient: AWSSNSClient;
  let contextProvider: ContextProvider;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  let mockSNSSend: any;

  beforeEach(() => {
    vi.clearAllMocks();
    contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
    awsSNSClient = new AWSSNSClient({ contextProvider });
    vi.spyOn(contextProvider, 'sendAnalytics').mockResolvedValue();

    // Get the mocked SNS client instance and its send method
    const MockedSNSClient = vi.mocked(SNSClient);
    // @ts-expect-error opt-out type checking for this mock
    const mockInstance = MockedSNSClient.mock.results[MockedSNSClient.mock.results.length - 1]?.value;
    mockSNSSend = mockInstance?.send;
  });

  test('client initializes without errors', () => {
    expect(awsSNSClient).toBeInstanceOf(AWSSNSClient);
  });

  test('successfully sends an SMS message', async () => {
    // Mock successful response
    mockSNSSend.mockResolvedValueOnce({ MessageId: 'test-message-id' });

    const params = {
      msg: 'Test message',
      phoneNumber: '+12345678901',
      lng,
    };

    const result = await awsSNSClient.send(params);

    // Verify result
    expect(result.isSuccess).toBe(true);

    // Verify AWS SNS was called correctly
    expect(mockSNSSend).toHaveBeenCalledTimes(1);
    expect(PublishCommand).toHaveBeenCalledWith({
      Message: params.msg,
      PhoneNumber: params.phoneNumber,
    });
  });

  test('handles invalid phone number error', async () => {
    // Mock AWS SNS error for invalid phone number
    const error = 'InvalidParameterException';
    const errorMessage = 'Invalid parameter: phone';
    const mockError = new Error(errorMessage);
    mockError.name = error;
    // Add AWS metadata
    Object.assign(mockError, {
      $metadata: {
        requestId: 'test-request-id',
        cfId: 'test-cf-id',
        extendedRequestId: 'test-extended-request-id',
      },
    });
    mockSNSSend.mockRejectedValueOnce(mockError);

    const params = {
      msg: 'Test message',
      phoneNumber: 'invalid-phone',
      lng,
    };

    const result = await awsSNSClient.send(params);

    // Verify result contains expected error
    expect(result.isFailure).toBe(true);
    expect(result.errors?.[0]).toBeInstanceOf(AWSSNSClientErrors.InvalidPhoneNumber);

    // Verify analytics was called
    expect(contextProvider.sendAnalytics).toHaveBeenCalledWith(expect.objectContaining({
      error,
      errorMessage,
      phoneNumber: params.phoneNumber,
      metadata: expect.any(Object),
    }));
  });

  test('handles opted out phone number error', async () => {
    // Mock AWS SNS error for opted out phone
    const mockError = new Error('Phone number opted out');
    mockError.name = 'EndpointDisabled';
    // Add AWS metadata
    Object.assign(mockError, {
      $metadata: {
        requestId: 'test-request-id',
        cfId: 'test-cf-id',
        extendedRequestId: 'test-extended-request-id',
      },
    });
    mockSNSSend.mockRejectedValueOnce(mockError);

    const params = {
      msg: 'Test message',
      phoneNumber: '+12345678901',
      lng: 'es' as PossibleLngs,
    };

    const result = await awsSNSClient.send(params);

    // Verify result contains expected error
    expect(result.isFailure).toBe(true);
    expect(result.errors?.[0]).toBeInstanceOf(AWSSNSClientErrors.PhoneNumberOptedOut);
  });

  test('handles generic AWS error', async () => {
    // Mock generic AWS error
    const mockError = new Error('Internal AWS error');
    mockError.name = 'InternalServiceError';
    // Add AWS metadata
    Object.assign(mockError, {
      $metadata: {
        requestId: 'test-request-id',
        cfId: 'test-cf-id',
        extendedRequestId: 'test-extended-request-id',
      },
    });
    mockSNSSend.mockRejectedValueOnce(mockError);

    const params = {
      msg: 'Test message',
      phoneNumber: '+12345678901',
      lng,
    };

    const result = await awsSNSClient.send(params);

    // Verify result contains generic error
    expect(result.isFailure).toBe(true);
    expect(result.errors?.[0]).toBeInstanceOf(AWSSNSClientErrors.InternalError);
  });
});