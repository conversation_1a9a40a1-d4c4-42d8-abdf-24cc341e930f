import { ISmsClient } from './ISmsClient';
import { Result2 } from '@shared/core/Result2';

/**
 * Fake SMS client for testing purposes
 * Simulates SMS sending without actually sending messages
 */
export class SmsClientFake implements ISmsClient {
  public sentMessages: Array<{
    msg: string;
    phoneNumber: string;
    timestamp: Date;
  }> = [];

  public shouldFail = false;
  public failureError: Error | null = null;

  public async send(params: {
    msg: string;
    phoneNumber: string;
  }): Promise<Result2<undefined>> {
    const { msg, phoneNumber } = params;

    if (this.shouldFail) {
      const error = this.failureError || new Error('Fake SMS client failure');
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      return Result2.fail([error as any]);
    }

    // Store the sent message for verification in tests
    this.sentMessages.push({
      msg,
      phoneNumber,
      timestamp: new Date(),
    });

    return Result2.ok(undefined);
  }

  /**
   * Reset the fake client state
   */
  public reset(): void {
    this.sentMessages = [];
    this.shouldFail = false;
    this.failureError = null;
  }

  /**
   * Configure the client to fail on next send
   */
  public setFailure(error?: Error): void {
    this.shouldFail = true;
    this.failureError = error || null;
  }

  /**
   * Get the last sent message
   */
  public getLastSentMessage() {
    return this.sentMessages[this.sentMessages.length - 1] || null;
  }

  /**
   * Get all sent messages
   */
  public getAllSentMessages() {
    return [...this.sentMessages];
  }

  /**
   * Check if a message was sent to a specific phone number
   */
  public wasMessageSentTo(phoneNumber: string): boolean {
    return this.sentMessages.some(message => message.phoneNumber === phoneNumber);
  }
}
