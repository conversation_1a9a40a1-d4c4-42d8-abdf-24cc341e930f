import { test, expect, beforeEach, describe } from 'vitest';
import { SmsClientFake } from './SmsClientFake';

describe('SmsClientFake Unit Tests', () => {
  let smsClientFake: SmsClientFake;

  beforeEach(() => {
    smsClientFake = new SmsClientFake();
  });

  test('client initializes without errors', () => {
    expect(smsClientFake).toBeInstanceOf(SmsClientFake);
    expect(smsClientFake.sentMessages).toEqual([]);
    expect(smsClientFake.shouldFail).toBe(false);
    expect(smsClientFake.failureError).toBeNull();
  });

  test('successfully sends an SMS message', async () => {
    const params = {
      msg: 'Test message',
      phoneNumber: '+12345678901',
    };

    const result = await smsClientFake.send(params);

    // Verify result
    expect(result.isSuccess).toBe(true);

    // Verify message was stored
    expect(smsClientFake.sentMessages).toHaveLength(1);
    expect(smsClientFake.sentMessages[0]).toMatchObject({
      msg: params.msg,
      phoneNumber: params.phoneNumber,
      timestamp: expect.any(Date),
    });
  });

  test('handles multiple messages', async () => {
    const params1 = {
      msg: 'First message',
      phoneNumber: '+12345678901',
    };
    const params2 = {
      msg: 'Second message',
      phoneNumber: '+12345678902',
    };

    await smsClientFake.send(params1);
    await smsClientFake.send(params2);

    expect(smsClientFake.sentMessages).toHaveLength(2);
    expect(smsClientFake.sentMessages[0].msg).toBe(params1.msg);
    expect(smsClientFake.sentMessages[1].msg).toBe(params2.msg);
  });

  test('handles failure when configured to fail', async () => {
    const customError = new Error('Custom SMS failure');
    smsClientFake.setFailure(customError);

    const params = {
      msg: 'Test message',
      phoneNumber: '+12345678901',
    };

    const result = await smsClientFake.send(params);

    // Verify result
    expect(result.isFailure).toBe(true);
    expect(result.errors).toEqual([customError]);

    // Verify no message was stored
    expect(smsClientFake.sentMessages).toHaveLength(0);
  });

  test('handles failure with default error', async () => {
    smsClientFake.setFailure();

    const params = {
      msg: 'Test message',
      phoneNumber: '+12345678901',
    };

    const result = await smsClientFake.send(params);

    // Verify result
    expect(result.isFailure).toBe(true);
    expect(result.errors?.[0]).toBeInstanceOf(Error);
    expect(result.errors?.[0].message).toBe('Fake SMS client failure');
  });

  test('reset clears all state', async () => {
    // Send a message and set failure
    await smsClientFake.send({
      msg: 'Test message',
      phoneNumber: '+12345678901',
    });
    smsClientFake.setFailure(new Error('Test error'));

    // Reset
    smsClientFake.reset();

    // Verify state is cleared
    expect(smsClientFake.sentMessages).toEqual([]);
    expect(smsClientFake.shouldFail).toBe(false);
    expect(smsClientFake.failureError).toBeNull();

    // Verify it works normally after reset
    const result = await smsClientFake.send({
      msg: 'After reset',
      phoneNumber: '+12345678901',
    });
    expect(result.isSuccess).toBe(true);
  });

  test('getLastSentMessage returns correct message', async () => {
    expect(smsClientFake.getLastSentMessage()).toBeNull();

    const params1 = {
      msg: 'First message',
      phoneNumber: '+12345678901',
    };
    const params2 = {
      msg: 'Second message',
      phoneNumber: '+12345678902',
    };

    await smsClientFake.send(params1);
    expect(smsClientFake.getLastSentMessage()?.msg).toBe(params1.msg);

    await smsClientFake.send(params2);
    expect(smsClientFake.getLastSentMessage()?.msg).toBe(params2.msg);
  });

  test('getAllSentMessages returns copy of messages', async () => {
    const params = {
      msg: 'Test message',
      phoneNumber: '+12345678901',
    };

    await smsClientFake.send(params);
    const messages = smsClientFake.getAllSentMessages();

    // Verify it's a copy (not the same reference)
    expect(messages).not.toBe(smsClientFake.sentMessages);
    expect(messages).toEqual(smsClientFake.sentMessages);
  });

  test('wasMessageSentTo correctly identifies sent messages', async () => {
    const phoneNumber1 = '+12345678901';
    const phoneNumber2 = '+12345678902';

    expect(smsClientFake.wasMessageSentTo(phoneNumber1)).toBe(false);

    await smsClientFake.send({
      msg: 'Test message',
      phoneNumber: phoneNumber1,
    });

    expect(smsClientFake.wasMessageSentTo(phoneNumber1)).toBe(true);
    expect(smsClientFake.wasMessageSentTo(phoneNumber2)).toBe(false);
  });
});
