import { PossibleLngs } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { IEmailClient } from './IEmailClient';

export class EmailSrv {
  private readonly emailClient: IEmailClient;

  public constructor(args: {
    emailClient: IEmailClient;
  }) {
    this.emailClient = args.emailClient;
  }

  /**
   * Sends a verification code via email
   * @param args.emailAddress - The email address to send the code to
   * @param args.code - The verification code to send
   * @param args.lng - The language for the message
   * @returns Result2<undefined> - Success or failure with errors
   */
  public async sendCode(args: { 
    emailAddress: string; 
    code: number; 
    lng: PossibleLngs 
  }): Promise<Result2<undefined>> {
    const { emailAddress, code, lng } = args;

    let subject: string;
    let msg: string;
    
    switch (lng) {
      case 'es':
        subject = 'Código de verificación';
        msg = `Tu código de verificación es: ${code}`;
        break;
      default:
        subject = 'Verification Code';
        msg = `Your verification code is: ${code}`;
        break;
    }

    return await this.emailClient.send({
      msg,
      emailAddress,
      subject,
      lng,
    });

  }
}
