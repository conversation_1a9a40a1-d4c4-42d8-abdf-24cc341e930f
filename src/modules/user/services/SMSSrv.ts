import { PossibleLngs } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { ISmsClient } from './ISmsClient';

export class SMSSrv {
  private readonly smsClient: ISmsClient;

  public constructor(args: {
    smsClient: ISmsClient;
  }) {
    this.smsClient = args.smsClient;
  }

  /**
   * Sends a verification code via SMS
   * @param args.phoneNumber - The phone number to send the code to
   * @param args.code - The verification code to send
   * @param args.lng - The language for the message
   * @returns Result2<undefined> - Success or failure with errors
   */
  public async sendCode(args: { 
    phoneNumber: string; 
    code: number; 
    lng: PossibleLngs 
  }): Promise<Result2<undefined>> {
    const { phoneNumber, code, lng } = args;

    let msg: string;
    switch (lng) {
      case 'es':
        msg = `Tu código de verificación es: ${code}`;
        break;
      default:
        msg = `Your verification code is: ${code}`;
        break;
    }

    return await this.smsClient.send({
      msg,
      phoneNumber,
    });

  }
}