import { DomainEvents } from '@shared/core/domain/DomainEvents';
import { CustomerManuallyCreatedEvent } from '../../domain/events/CustomerManuallyCreatedEvent';
import { IInvoker } from '@shared/infra/invocation/IInvoker';

export class CreateCustomerManuallyEvents {
  public static registration(invoker: IInvoker) {
    // Add all process.env used:
    const { distributeDomainEvents } = process.env;
    if (!distributeDomainEvents) {
      console.log('process.env', process.env);
      throw new Error(`Undefined env var!`);
    }

    DomainEvents.setInvoker(invoker);
    DomainEvents.register(
      `${distributeDomainEvents}`,
      CustomerManuallyCreatedEvent.name,
    );
  }
}
