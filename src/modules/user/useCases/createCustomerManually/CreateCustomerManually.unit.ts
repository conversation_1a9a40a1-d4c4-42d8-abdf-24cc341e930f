import { beforeEach, describe, expect, test, vi } from 'vitest';
import { CreateCustomerManually } from './CreateCustomerManually';
import { ManuallyCreatedCustomerRepoFake } from '../../repos/ManuallyCreatedCustomerRepoFake';
import { uuidFormat } from '@shared/utils/utils';
import {
  expectControllerErrorField,
  expectControllerMultipleErrors,
  orNull,
} from '@shared/utils/test';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { IManuallyCreatedCustomerRepo } from 'modules/user/repos/IManuallyCreatedCustomerRepo';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { BaseError } from '@shared/core/AppError';
import {
  ManuallyCreatedCustomerErrors,
} from '../../domain/ManuallyCreatedCustomerErrors';
import { ManuallyCreatedCustomersInDb } from '@shared/infra/database/sequelize/migrations/ManuallyCreatedCustomers.json';
import { CreateCustomerManuallyErrors } from './CreateCustomerManuallyErrors';
import { ContextProvider } from '@shared/context/ContextProvider';

vi.stubEnv('distributeDomainEvents', 'dummy');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

let repo: IManuallyCreatedCustomerRepo,
  createCustomerManually: CreateCustomerManually,
  contextProvider: ContextProvider;
beforeEach(() => {
  repo = new ManuallyCreatedCustomerRepoFake();
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  createCustomerManually = new CreateCustomerManually({
    repo,
    contextProvider,
  });
});

const validData = {
  firstName: chance.string({ alpha: true, numeric: true, length: 15 }),
  lastName: chance.string({ alpha: true, numeric: true, length: 15 }),
  notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
  createdBy: BusinessUsersInDb[0].id,
};

test(`creation`, async () => {
  const result = await createCustomerManually.executeImpl(validData);

  expect(result).toMatchObject({
    status: 201,
    result: expect.objectContaining({
      ...validData,
      id: expect.stringMatching(uuidFormat),
      fullName: `${validData.firstName} ${validData.lastName}`,
    }),
  });
});

describe(`fails`, () => {
  test.each([
    ['firstName', ''],
    ['lastName', ''],
    ['notes', ''],
  ])(`with invalid %s`, async (field, value) => {
    const response = await createCustomerManually.executeImpl({
      ...validData,
      [field]: value,
    });
    expectControllerErrorField({
      response,
      errorField: field,
      code: 400,
    });
  });

  test('with multiple field errors', async () => {
    const response = await createCustomerManually.executeImpl({
      ...validData,
      firstName: '',
      lastName: '',
      notes: '',
    });
    expect((response.result as BaseError[]).length).toBe(3);
    expectControllerMultipleErrors({
      response,
      errors: expect.arrayContaining([
        expect.objectContaining({ field: 'firstName' }),
        expect.objectContaining({ field: 'lastName' }),
        expect.objectContaining({ field: 'notes' }),
      ]),
    });
  });

  test('when firstName & lastName are nulls', async () => {
    const response = await createCustomerManually.executeImpl({
      ...validData,
      firstName: null,
      lastName: null,
    });
    expect((response.result as BaseError[]).length).toBe(2);
    const noNamesError = new ManuallyCreatedCustomerErrors.NoNames();
    expectControllerMultipleErrors({
      response,
      errors: expect.arrayContaining([
        noNamesError.setField('firstName'),
        noNamesError.clone('lastName'),
      ]),
    });
  });

  test('when fullName is already taken', async () => {
    const firstName = ManuallyCreatedCustomersInDb[1].firstName;
    const lastName = ManuallyCreatedCustomersInDb[1].lastName;
    const response = await createCustomerManually.executeImpl({
      ...validData,
      firstName,
      lastName,
    });
    expect((response.result as BaseError[]).length).toBe(2);
    const error = new CreateCustomerManuallyErrors.NameAlreadyTaken(`${firstName} ${lastName}`);
    expectControllerMultipleErrors({
      response,
      errors: expect.arrayContaining([
        error.setField('firstName'),
        error.clone('lastName'),
      ]),
    });
  });

  test('when fullName is already taken even though firstName & lastName are different from the existing one', async () => {
    const firstName = "Pascual";
    const lastName = "Abelardo Rapanti";
    const response = await createCustomerManually.executeImpl({
      ...validData,
      firstName,
      lastName,
    });
    expect((response.result as BaseError[]).length).toBe(2);
    const error = new CreateCustomerManuallyErrors.NameAlreadyTaken(`${firstName} ${lastName}`);
    expectControllerMultipleErrors({
      response,
      errors: expect.arrayContaining([
        error.setField('firstName'),
        error.clone('lastName'),
      ]),
    });
  });
});
