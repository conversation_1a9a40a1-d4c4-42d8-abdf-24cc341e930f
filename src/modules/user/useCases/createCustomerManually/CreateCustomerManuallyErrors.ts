import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { Status } from '@shared/core/Status';

export namespace CreateCustomerManuallyErrors {
  export class NameAlreadyTaken extends BaseError {
    public constructor(fullName: string) {
      super({
        message: `El nombre "${fullName}" ya se encuentra ocupado.`,
        status: Status.CONFLICT,
      });
    }
  }
}

patch({ CreateCustomerManuallyErrors });
