import { CreateCustomerManually } from './CreateCustomerManually';
import { ReturnUnexpectedError } from '@shared/decorators/ReturnUnexpectedError';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import setHooks from '@shared/infra/database/sequelize/hooks';

import { ManuallyCreatedCustomerRepo } from '../../repos/ManuallyCreatedCustomerRepo';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

setHooks();
const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });

const repo = new ManuallyCreatedCustomerRepo(models.ManuallyCreatedCustomer);

const controller = new CreateCustomerManually({
  repo,
  contextProvider,
});

const decorated = new ReturnUnexpectedError({
  wrapee: controller,
  contextProvider: new ContextProvider({ invoker }),
});
export const handler = decorated.execute.bind(decorated);
