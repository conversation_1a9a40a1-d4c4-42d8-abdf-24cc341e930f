import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import {
  dateFormat,
  expectMultipleErrorsAppSync,
  orNull,
} from '@shared/utils/test';
import { ManuallyCreatedCustomersInDb } from '@shared/infra/database/sequelize/migrations/ManuallyCreatedCustomers.json';
import { uuidFormat } from '@shared/utils/utils';
import { removeManuallyCreatedCustomer } from '../../utils/test';
import { ManuallyCreatedCustomerRepo } from '../../repos/ManuallyCreatedCustomerRepo';
import { Request } from './CreateCustomerManuallyDTOs';
import { ManuallyCreatedCustomerFragment } from '../../utils/fragments';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const appsync = new AppSyncClient();
const repo = new ManuallyCreatedCustomerRepo(models.ManuallyCreatedCustomer);

const input = {
  firstName: chance.string({ alpha: true, numeric: true, length: 15 }),
  lastName: chance.string({ alpha: true, numeric: true, length: 15 }),
  notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
  createdBy: ManuallyCreatedCustomersInDb[0].createdBy,
};

const query = gql`
  mutation (
    $firstName: String
    $lastName: String
    $notes: String
    $createdBy: ID!
  ) {
    createCustomerManually(
      firstName: $firstName
      lastName: $lastName
      notes: $notes
      createdBy: $createdBy
    ) {
      result {
        ...ManuallyCreatedCustomerFragment
      }
      time
    }
  }
  ${ManuallyCreatedCustomerFragment}
`;

it(`creates a customer`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: input,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.createCustomerManually;
  expect(response).toEqual({
    result: {
      ...input,
      id: expect.stringMatching(uuidFormat),
      fullName: `${input.firstName} ${input.lastName}`,
    },
    time: expect.stringMatching(dateFormat),
  });

  const saved = await repo.get({
    id: response.result.id,
    createdBy: response.result.createdBy,
  });
  expect(saved?.toDto()).toMatchObject({
    ...input,
    id: response.result.id,
    fullName: `${input.firstName} ${input.lastName}`,
  });

  await removeManuallyCreatedCustomer(response.result.id);
});

it(`fails when fullName is already taken`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      firstName: ManuallyCreatedCustomersInDb[0].firstName,
      lastName: ManuallyCreatedCustomersInDb[0].lastName,
      notes: null,
      createdBy: ManuallyCreatedCustomersInDb[0].createdBy,
    },
  });

  expect(received.status).toBe(200);
  expectMultipleErrorsAppSync({
    response: await received.json(),
    errors: [
      {
        field: 'firstName',
        type: 'CreateCustomerManuallyErrors.NameAlreadyTaken',
        message: expect.any(String),
        status: 409,
      },
      {
        field: 'lastName',
        type: 'CreateCustomerManuallyErrors.NameAlreadyTaken',
        message: expect.any(String),
        status: 409,
      },
    ],
  });
});
