import { Request, Response } from './CreateCustomerManuallyDTOs';
import { Name } from '../../domain/Name';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { BaseError, formatErrors } from '@shared/core/AppError';
import { IManuallyCreatedCustomerRepo } from '../../repos/IManuallyCreatedCustomerRepo';
import { ManuallyCreatedCustomer } from '../../domain/ManuallyCreatedCustomer';
import { CreateCustomerManuallyEvents } from './CreateCustomerManuallyEvents';
import { AppSyncController } from '@shared/infra/Controllers';
import { Notes } from '../../domain/Notes';
import { CreateCustomerManuallyErrors } from './CreateCustomerManuallyErrors';
import { Result2 } from '@shared/core/Result2';
import { IContextProvider } from '@shared/context/IContextProvider';

export class CreateCustomerManually extends AppSyncController<Request, Response> {
  private readonly repo: IManuallyCreatedCustomerRepo;
  public constructor(args: {
    repo: IManuallyCreatedCustomerRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
    CreateCustomerManuallyEvents.registration(contextProvider.invoker());
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { createdBy } = dto;

    const customerOrErrors = validateReq(dto);
    if (customerOrErrors.isFailure) return formatErrors(customerOrErrors.errors!);

    const customer = customerOrErrors.value;

    const existing = await this.repo.getByFullName({
      createdBy,
      fullName: customer.fullName,
    });
    if (existing) {
      const error = new CreateCustomerManuallyErrors.NameAlreadyTaken(customer.fullName);
      return formatErrors([
        error.setField('firstName'),
        error.clone('lastName'),
      ]);
    }

    return { status: Status.CREATED, result: await this.repo.create(customer) };
  }
}

// Exported for use in UpdateCustomerManually use case and frontend
export function validateReq(dto: Request): Result2<ManuallyCreatedCustomer> {
  const { firstName: _firstName, lastName: _lastName, notes: _notes, createdBy } = dto;

  const errors: BaseError[] = [];

  let firstName: null | Name = null;
  if (_firstName !== null) {
    const firstNameOrError = Name.create(_firstName);
    if (firstNameOrError.isFailure) errors.push(firstNameOrError.error!.setField('firstName'));
    else firstName = firstNameOrError.value;
  }

  let lastName: null | Name = null;
  if (_lastName !== null) {
    const lastNameOrError = Name.create(_lastName);
    if (lastNameOrError.isFailure) errors.push(lastNameOrError.error!.setField('lastName'));
    else lastName = lastNameOrError.value;
  }

  let notes: null | Notes = null;
  if (_notes !== null) {
    const notesOrError = Notes.create(_notes);
    if (notesOrError.isFailure) errors.push(notesOrError.error!.setField('notes'));
    else notes = notesOrError.value;
  }

  if (errors.length) return Result2.fail(errors);

  const customerOrErrors = ManuallyCreatedCustomer.create({
    firstName,
    lastName,
    notes,
    createdBy,
  });
  if (customerOrErrors.isFailure) return Result2.fail(customerOrErrors.errors!);

  const customer = customerOrErrors.value;

  return Result2.ok(customer);
}
