import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { dateFormat, nonExistingId } from '@shared/utils/test';
import { ManuallyCreatedCustomersInDb } from '@shared/infra/database/sequelize/migrations/ManuallyCreatedCustomers.json';
import { Request } from './GetManuallyCreatedCustomersDTOs';
import {
  ManuallyCreatedCustomerWithTimestampsFragment,
} from '../../utils/fragments';

const appsync = new AppSyncClient();

it(`gets customers`, async () => {
  const received = await appsync.send<Request>({
    query: gql`
      query ($createdBy: ID!) {
        getManuallyCreatedCustomers(createdBy: $createdBy) {
          result {
            ...ManuallyCreatedCustomerWithTimestampsFragment
          }
          time
        }
      }
      ${ManuallyCreatedCustomerWithTimestampsFragment}
    `,
    variables: {
      createdBy: ManuallyCreatedCustomersInDb[1].createdBy,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.getManuallyCreatedCustomers;
  expect(response.result.length).toBe(5); // If this fails check if migration src/shared/infra/database/sequelize/migrations/ManuallyCreatedCustomersMany.json is being loaded (instead of ManuallyCreatedCustomers.json).
  expect(response).toEqual({
    result: expect.arrayContaining([
      ManuallyCreatedCustomersInDb[1],
      ManuallyCreatedCustomersInDb[2],
      ManuallyCreatedCustomersInDb[4],
      ManuallyCreatedCustomersInDb[6],
      ManuallyCreatedCustomersInDb[7],
    ]),
    time: expect.stringMatching(dateFormat),
  });
});

it(`returns null when the customer isn't found`, async () => {
  const received = await appsync.send<Request>({
    query: gql`
      query ($createdBy: ID!) {
        getManuallyCreatedCustomers(createdBy: $createdBy) {
          result {
            __typename
          } # To avoid error, I need to put something here even when I'll receive null
          time
        }
      }
    `,
    variables: {
      createdBy: nonExistingId,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  expect(json.data.getManuallyCreatedCustomers).toMatchObject({
    result: null,
    time: expect.stringMatching(dateFormat),
  });
});
