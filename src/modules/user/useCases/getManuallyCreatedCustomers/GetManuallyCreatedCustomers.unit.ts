import { expect, beforeEach, it } from 'vitest';
import { Request } from './GetManuallyCreatedCustomersDTOs';
import { ManuallyCreatedCustomerRepoFake } from '../../repos/ManuallyCreatedCustomerRepoFake';
import { GetManuallyCreatedCustomers } from './GetManuallyCreatedCustomers';
import { ManuallyCreatedCustomersInDb } from '@shared/infra/database/sequelize/migrations/ManuallyCreatedCustomers.json';
import { ManuallyCreatedCustomerDto } from '../../domain/ManuallyCreatedCustomer';
import { nonExistingId } from '@shared/utils/test';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';

let repo, getManuallyCreatedCustomers: GetManuallyCreatedCustomers, contextProvider: ContextProvider;
beforeEach(() => {
  repo = new ManuallyCreatedCustomerRepoFake();
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  getManuallyCreatedCustomers = new GetManuallyCreatedCustomers({
    repo,
    contextProvider,
  });
});

const input: Request = {
  createdBy: ManuallyCreatedCustomersInDb[1].createdBy,
};

it(`get users`, async () => {
  const response = await getManuallyCreatedCustomers.executeImpl(input);

  expect(response.status).toBe(200);
  expect((response.result as ManuallyCreatedCustomerDto[]).length).toBe(5);
  expect(response.result).toEqual([
    ManuallyCreatedCustomersInDb[1],
    ManuallyCreatedCustomersInDb[2],
    ManuallyCreatedCustomersInDb[4],
    ManuallyCreatedCustomersInDb[6],
    ManuallyCreatedCustomersInDb[7],
  ]);
});

it(`returns null when no users are found for createdBy`, async () => {
  const response = await getManuallyCreatedCustomers.executeImpl({
    createdBy: nonExistingId,
  });

  expect(response).toEqual({
    status: 200,
    result: null,
  });
});
