import { DomainEvents } from '@shared/core/domain/DomainEvents';
import { BusinessUserCreatedEvent } from '../../domain/events/BusinessUserCreatedEvent';
import { IInvoker } from '@shared/infra/invocation/IInvoker';

export class CreateBusinessUserEvents {
  public static registration(invoker: IInvoker) {
    // Add all process.env used:
    const { distributeDomainEvents } = process.env;
    if (!distributeDomainEvents) {
      console.log('process.env', process.env);
      throw new Error(`Undefined env var!`);
    }

    DomainEvents.setInvoker(invoker);
    DomainEvents.register(
      `${distributeDomainEvents}`,
      BusinessUserCreatedEvent.name,
    );
  }
}
