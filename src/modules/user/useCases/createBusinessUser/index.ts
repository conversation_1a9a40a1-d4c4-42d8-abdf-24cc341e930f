import { CreateBusinessUser } from './CreateBusinessUser';
import { ReturnUnexpectedError } from '@shared/decorators/ReturnUnexpectedError';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import setHooks from '@shared/infra/database/sequelize/hooks';

import { BusinessUserRepo } from '../../repos/BusinessUserRepo';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

setHooks();
const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });

// const repo = new BusinessUserRepoFake();
const repo = new BusinessUserRepo(models.BusinessUser);

const controller = new CreateBusinessUser({
  repo,
  contextProvider,
});

const decorated = new ReturnUnexpectedError({
  wrapee: controller,
  contextProvider: new ContextProvider({ invoker }),
});
export const handler = decorated.execute.bind(decorated);
