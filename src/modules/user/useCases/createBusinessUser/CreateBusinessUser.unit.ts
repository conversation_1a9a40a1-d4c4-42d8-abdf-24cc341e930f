import { expect, beforeEach, vi, test, describe } from 'vitest';
vi.stubEnv('distributeDomainEvents', 'dummy');
import { CreateBusinessUser } from './CreateBusinessUser';
import { BusinessUserRepoFake } from '../../repos/BusinessUserRepoFake';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import {
  expectControllerErrorContaining,
  mockContext,
  pickLng,
} from '@shared/utils/test';
import { uuidFormat } from '@shared/utils/utils';
import { getTimezone } from '@shared/core/test';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

let businessUserRepo, createBusinessUser: CreateBusinessUser, contextProvider: ContextProvider;
beforeEach(() => {
  businessUserRepo = new BusinessUserRepoFake();
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  contextProvider.set(mockContext);
  createBusinessUser = new CreateBusinessUser({
    repo: businessUserRepo,
    contextProvider,
  });
});

const validData = {
  lng: pickLng(),
  email: chance.email(),
  timezone: getTimezone(),
};

const defaultProps = {
  firstName: null,
  lastName: null,
}

const success = {
  ...validData,
  ...defaultProps,
  id: expect.stringMatching(uuidFormat),
}

describe('succeeds for', () => {
  test(`valid data`, async () => {
    const result = await createBusinessUser.executeImpl(validData);

    expect(result).toMatchObject({
      status: 201,
      result: success,
    });
  });

  test.each([
    ['timezone', 'invalid', 'Etc/GMT'],
    ['lng', 'invalid', 'en'],
  ])(`invalid %s`, async (field, invalidValue, defaultValue) => {
    const result = await createBusinessUser.executeImpl({
      ...validData,
      [field]: invalidValue,
    });

    expect(result).toMatchObject({
      status: 201,
      result: {
        ...success,
        [field]: defaultValue,
        id: expect.stringMatching(uuidFormat),
      },
    });
  });
});

describe(`fails if`, () => {
  test.each([
    ['email', 'invalidEmail', 'EmailErrors.'],
  ])(`%s is the invalid value %s`, async (field, invalidValue, errorContains) => {
    const data = {
      ...validData,
      [field]: invalidValue,
    };

    const response = await createBusinessUser.executeImpl(data);

    expectControllerErrorContaining({
      response,
      errorContaining: errorContains,
      code: 400,
    });
  });

  test(`email is already taken`, async () => {
    const data = {
      ...validData,
      email: BusinessUsersInDb[0].email,
    };

    const result = await createBusinessUser.executeImpl(data);

    expect(result).toMatchObject({
      status: 409,
      result: [
        {
          message: expect.any(String),
          type: 'CreateBusinessUserErrors.EmailAlreadyTaken',
          status: 409,
        },
      ],
    });
  });
});
