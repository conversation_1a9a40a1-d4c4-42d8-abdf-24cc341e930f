import { Request, Response } from './CreateBusinessUserDTOs';
import { CreateBusinessUserErrors } from './CreateBusinessUserErrors';
import { Email } from '../../domain/Email';
import { BusinessUser } from '../../domain/BusinessUser';
import { IBusinessUserRepo } from '../../repos/IBusinessUserRepo';
import { CreateBusinessUserEvents } from './CreateBusinessUserEvents';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { formatErrors } from '@shared/core/AppError';
import { TimeZone } from '@shared/core/TimeZone';
import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';
import { UserLng } from '../../domain/UserLng';
const { CREATED } = Status;

export class CreateBusinessUser extends AppSyncController<Request, Response> {
  private readonly repo: IBusinessUserRepo;
  public constructor(args: {
    repo: IBusinessUserRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
    CreateBusinessUserEvents.registration(contextProvider.invoker());
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const emailOrError = Email.create(dto.email);

    if (emailOrError.isFailure)
      return formatErrors([emailOrError.error!.setField('email')]);
    const email = emailOrError.value;

    const timezoneOrError = TimeZone.create(dto.timezone);
    let timezone = TimeZone.default();
    if (timezoneOrError.isFailure) {
      // Since timezone is computed automatically on the frontend (no user input), send this error to analytics but don't stop the flow.
      await this.contextProvider.sendAnalytics(dto);
    } else
      timezone = timezoneOrError.value;

    const lngOrError = UserLng.create({ value: dto.lng });
    let lng = UserLng.default();
    if (lngOrError.isFailure) {
      // Since lng is computed automatically on the frontend (no user input), send this error to analytics but don't stop the flow.
      await this.contextProvider.sendAnalytics(dto);
    } else
      lng = lngOrError.value;

    const emailAlreadyTaken = await this.repo.exists(email);
    if (emailAlreadyTaken)
      return formatErrors([
        new CreateBusinessUserErrors.EmailAlreadyTaken(email.value),
      ]);

    const user = BusinessUser.create({
      email,
      lng,
      timezone,
    });

    await this.repo.create(user);

    return { status: CREATED, result: BusinessUser.forFrontend(user.toDto()) };
  }
}
