import { Request, Response } from './ChangeBusinessPhoneConfirmCodeDTOs';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { formatErrors } from '@shared/core/AppError';
import { IBusinessUserRepo } from '../../repos/IBusinessUserRepo';
import {
  ChangeBusinessPhoneConfirmCodeErrors,
} from './ChangeBusinessPhoneConfirmCodeErrors';
import { AppSyncController } from '@shared/infra/Controllers';
import { getLng } from '@shared/utils/utils';
import { IContextProvider } from '@shared/context/IContextProvider';
import { BusinessUser } from '../../domain/BusinessUser';
import { Code } from '../../domain/Code';

export class ChangeBusinessPhoneConfirmCode extends AppSyncController<Request, Response> {
  private readonly repo: IBusinessUserRepo;
  public constructor(args: {
    repo: IBusinessUserRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(request: Request): ControllerResult<Response> {

    const { lng: _lng, id, code: _code } = request;
    const lng = getLng(_lng);

    const codeOrError = Code.create({ value: _code, lng });
    if (codeOrError.isFailure) {
      for (const e of codeOrError.errors!)
        e.setField('code');
      return formatErrors(codeOrError.errors!);
    }
    const code = codeOrError.value;

    const user = await this.repo.get(id);
    if (!user)
      return formatErrors(
        [new ChangeBusinessPhoneConfirmCodeErrors.UserNotFound({ id, lng })],
      );

    const confirmedOrErrors = user.confirmPhoneCode({ code, lng });
    if (confirmedOrErrors.isFailure)
      return formatErrors(confirmedOrErrors.errors!);

    await this.repo.update(user);

    return {
      status: Status.OK,
      result: BusinessUser.forFrontend(user.toDto()),
    };
  }
}
