import { beforeEach, describe, expect, test, it } from 'vitest';
import { ChangeBusinessPhoneConfirmCode } from './ChangeBusinessPhoneConfirmCode';
import { BusinessUserRepoFake } from '../../repos/BusinessUserRepoFake';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { Request } from './ChangeBusinessPhoneConfirmCodeDTOs';
import { IBusinessUserRepo } from '../../repos/IBusinessUserRepo';
import {
  pickLng,
  nonExistingId,
  expectControllerError,
  expectControllerErrorContaining,
} from '@shared/utils/test';
import { Status } from '@shared/core/Status';
import { VerificationStatus } from '../../domain/BusinessUser';
import { Phone } from '../../domain/Phone';
import { getTimezone } from '@shared/core/test';

const lng = pickLng();
const newPhone = Phone.create({ value: '+549345123499', lng }).value;
const timezone = getTimezone();

let repo: IBusinessUserRepo, changeBusinessPhoneConfirmCode: ChangeBusinessPhoneConfirmCode, contextProvider: ContextProvider;
beforeEach(() => {
  repo = new BusinessUserRepoFake();
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  changeBusinessPhoneConfirmCode = new ChangeBusinessPhoneConfirmCode({
    repo,
    contextProvider,
  });
});

const businessUser = BusinessUsersInDb[0];
const id = businessUser.id;


test('succeeds with valid code', async () => {
  // Get the user and set up phone verification
  const user = await repo.get(id);

  // Add phone verification attempt to the user
  user!.addPhoneVerificationAttempt({
    lng,
    timezone, 
    newPhone ,
  });

  // Save the user with verification in progress
  await repo.update(user!);
  
  // Get the code that was generated
  const code = user!.phoneVerification.code!.value;
  
  // Create the request with the valid code
  const validData: Request = {
    id: id,
    code,
    lng,
  };
  
  // Execute the confirm code operation
  const result = await changeBusinessPhoneConfirmCode.executeImpl(validData);
  
  // Verify the result
  expect(result).toMatchObject({
    status: Status.OK,
    result: expect.objectContaining({
      id: id,
      phone: newPhone.value,
    }),
  });
  
  // Verify the user was updated in the repo
  const updatedUser = await repo.get(id);
  expect(updatedUser!.toDto().phoneVerification.status).toBe(VerificationStatus.SUCCESS);
  expect(updatedUser!.toDto().phone).toBe(newPhone.value);
});

describe('fails', () => {
  test('with invalid code', async () => {
    const user = await repo.get(id);

    user!.addPhoneVerificationAttempt({
      lng,
      timezone,
      newPhone,
    });

    // Save the user with verification in progress
    await repo.update(user!);

    // Create the request with an invalid code
    const result = await changeBusinessPhoneConfirmCode.executeImpl({
      id: id,
      code: 999, // invalid code
      lng: pickLng(),
    });

    expectControllerErrorContaining({
      response: result,
      errorContaining: 'CodeErrors.',
      code: 400,
    });

    // Verify the user status was updated to INVALID_CODE
    const updatedUser = await repo.get(id);
    expect(updatedUser!.toDto().phoneVerification.status).toBe(VerificationStatus.STARTED);
  });

  test('when user not found', async () => {
    const result = await changeBusinessPhoneConfirmCode.executeImpl({
      id: nonExistingId,
      code: 1234,
      lng: pickLng(),
    });

    expectControllerError({
      response: result,
      error: 'ChangeBusinessPhoneConfirmCodeErrors.UserNotFound',
      code: 404,
    });
  });

  it('if BusinessUser.confirmPhoneCode() fails', async () => {
    const result = await changeBusinessPhoneConfirmCode.executeImpl({
      id: BusinessUsersInDb[1].id,  // status: INITIAL (not STARTED), so user.confirmPhoneCode will fail
      code: 1234,
      lng,
    });

    expectControllerErrorContaining({
      response: result,
      errorContaining: 'BusinessUserErrors.',
      code: 400,
    });
  });
});
