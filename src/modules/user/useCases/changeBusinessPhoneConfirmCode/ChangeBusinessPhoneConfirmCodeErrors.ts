import { NotFound as _NotFound } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { PossibleLngs } from '@shared/utils/utils';

export namespace ChangeBusinessPhoneConfirmCodeErrors {
  export class UserNotFound extends _NotFound {
    public constructor(args: { id: string, lng: PossibleLngs }) {
      const { id, lng } = args;
      const t = trans[lng];
      super({
        message: t.notFound(id),
      });
    }
  }
}

const trans = {
  en: {
    notFound(v: string) {
      return `Business user ${v} not found`;
    },
  },
  es: {
    notFound(v: string) {
      return `Usuario ${v} no encontrado`;
    },
  },
};

patch({ ChangeBusinessPhoneConfirmCodeErrors });
