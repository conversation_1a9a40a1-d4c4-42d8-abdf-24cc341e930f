import { expect, it, test } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import { gql } from 'graphql-tag';
import { Request } from './ChangeBusinessPhoneConfirmCodeDTOs';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import {
  expectErrorAppSync,
  expectErrorAppSyncContaining,
  nonExistingId,
  pickLng,
} from '@shared/utils/test';
import { getTimezone } from '@shared/core/test';
import { BusinessUser, VerificationStatus } from '../../domain/BusinessUser';
import { BusinessUserRepo } from '../../repos/BusinessUserRepo';
import { Phone } from '../../domain/Phone';
import { BusinessUserForFrontendFragment } from '../../utils/utils';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');

const lng = pickLng();
const timezone = getTimezone();
const newPhone = Phone.create({ value: '+549345123499', lng }).value;

const repo = new BusinessUserRepo(models.BusinessUser);
const appsync = new AppSyncClient();

// GraphQL query for the changeBusinessPhoneConfirmCode mutation
const query = gql`
  mutation ($id: ID!, $code: Int!, $lng: String!) {
    changeBusinessPhoneConfirmCode(
      id: $id
      code: $code
      lng: $lng
    ) {
      result {
        ...BusinessUserForFrontendFragment
      }
      time
    }
  }
  ${BusinessUserForFrontendFragment}
`;

test(`success`, async () => {
  const originalBusinessUser = BusinessUsersInDb[9];
  const id = BusinessUsersInDb[9].id;
  // const code = Code.getRandom().value;

  // First fail trying to send a code without adding an attempt
  const variables = {
    id,
    code: 1234,
    lng,
  };

  let received = await appsync.send<Request>({
    query,
    variables,
  });
  expect(received.status).toBe(200);
  let json = await received.json();

  expectErrorAppSyncContaining({
    response: json,
    errorContaining: 'BusinessUserErrors.',
    status: 400,
  })

  const user = await repo.get(id);
  const code = user!.addPhoneVerificationAttempt({
    lng,
    timezone,
    newPhone,
  }).value.code.value;
  await repo.update(user!);

  received = await appsync.send<Request>({
    query,
    variables: {
      ...variables,
      code,
    },
  });
  expect(received.status).toBe(200);
  json = await received.json();
  const response = json.data.changeBusinessPhoneConfirmCode;
  expect(response.result).toMatchObject({
    id,
    phone: newPhone.value,
    phoneVerification: {
      status: VerificationStatus.SUCCESS,
    },
  });

  await repo.update(BusinessUser.assemble(BusinessUserRepo.mapDb2Dto(originalBusinessUser)));

});

it(`fails when user not found`, async () => {
  
  const received = await appsync.send<Request>({
    query,
    variables: {
      id: nonExistingId,
      code: 1234,
      lng,
    },
  });
  
  expect(received.status).toBe(200);
  const response = await received.json();
  
  expectErrorAppSync({
    response,
    query: 'changeBusinessPhoneConfirmCode',
    error: 'ChangeBusinessPhoneConfirmCodeErrors.UserNotFound',
    status: 404,
  });
});