import { Request, Response } from './GetBusinessUserFCDTOs';
import { IBusinessUserRepo } from '../../repos/IBusinessUserRepo';
import { FunctionController } from '@shared/infra/Controllers';

export class GetBusinessUserFC extends FunctionController<Request, Response> {
  private readonly businessUserRepo: IBusinessUserRepo;

  public constructor(businessUserRepo: IBusinessUserRepo) {
    super();
    this.businessUserRepo = businessUserRepo;
  }

  public async executeImpl(dto: Request) {
    const { id } = dto;

    const user = await this.businessUserRepo.get(id);
    if (!user) return null;

    return user.toDto();
  }
}
