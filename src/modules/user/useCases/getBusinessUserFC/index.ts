import { GetBusinessUserFC } from './GetBusinessUserFC';
import { BusinessUserRepo } from '../../repos/BusinessUserRepo';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

const repo = new BusinessUserRepo(models.BusinessUser);
const controller = new GetBusinessUserFC(repo);

export const handler = controller.executeImpl.bind(controller);
