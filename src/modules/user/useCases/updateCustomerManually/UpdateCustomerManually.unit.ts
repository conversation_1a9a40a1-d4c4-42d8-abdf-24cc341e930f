import { beforeEach, describe, expect, it, test, vi } from 'vitest';
import { UpdateCustomerManually } from './UpdateCustomerManually';
import { ManuallyCreatedCustomerRepoFake } from '../../repos/ManuallyCreatedCustomerRepoFake';
import {
  expectControllerError,
  expectController<PERSON>rror<PERSON>ield,
  expectControllerMultipleErrors,
  nonExistingId,
  orNull,
} from '@shared/utils/test';
import { ManuallyCreatedCustomersInDb } from '@shared/infra/database/sequelize/migrations/ManuallyCreatedCustomers.json';
import { IManuallyCreatedCustomerRepo } from 'modules/user/repos/IManuallyCreatedCustomerRepo';
import { BaseError } from '@shared/core/AppError';
import {
  ManuallyCreatedCustomerErrors,
} from '../../domain/ManuallyCreatedCustomerErrors';
import { UpdateCustomerManuallyErrors } from './UpdateCustomerManuallyErrors';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

let repo: IManuallyCreatedCustomerRepo,
  updateCustomerManually: UpdateCustomerManually,
  contextProvider: ContextProvider;
beforeEach(() => {
  repo = new ManuallyCreatedCustomerRepoFake();
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  updateCustomerManually = new UpdateCustomerManually({
    repo,
    contextProvider,
  });
});

const validData = {
  firstName: chance.string({ alpha: true, numeric: true, length: 15 }),
  lastName: chance.string({ alpha: true, numeric: true, length: 15 }),
  notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
  createdBy: ManuallyCreatedCustomersInDb[0].createdBy,
  id: ManuallyCreatedCustomersInDb[0].id,
};

describe('success', () => {
  test(`update`, async () => {
    const result = await updateCustomerManually.executeImpl(validData);

    expect(result).toMatchObject({
      status: 200,
      result: expect.objectContaining(validData),
    });
  });
  test(`update to the same fullName`, async () => {
    const customer = ManuallyCreatedCustomersInDb[0];
    const { firstName, lastName, createdBy, id } = customer;
    const input = {
      firstName,
      lastName,
      notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
      createdBy,
      id,
    }
    const result = await updateCustomerManually.executeImpl(input);

    expect(result).toMatchObject({
      status: 200,
      result: expect.objectContaining({
        ...input,
        fullName: ManuallyCreatedCustomersInDb[0].fullName,
      }),
    });
  });
  test(`update to a taken fullName but from different business`, async () => {
    const customer = ManuallyCreatedCustomersInDb[0]; // ManuallyCreatedCustomersInDb[0].createdBy = 01a82ed0-c39a-11ee-8adf-3f66c88fcbdd
    const { firstName: takenFirstName, lastName: takenLastName} = ManuallyCreatedCustomersInDb[1];  // ManuallyCreatedCustomersInDb[1].createdBy = 00ad8920-c36d-11ee-8adf-3f66c88fcbdd
    const { createdBy, id } = customer;
    const input = {
      firstName: takenFirstName,
      lastName: takenLastName,
      notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
      createdBy,
      id,
    }
    const result = await updateCustomerManually.executeImpl(input);

    expect(result).toMatchObject({
      status: 200,
      result: expect.objectContaining({
        ...input,
        fullName: ManuallyCreatedCustomersInDb[1].fullName,
      }),
    });
  });
});

describe(`fails`, () => {
  test.each([
    ['firstName', ''],
    ['lastName', ''],
    ['notes', ''],
  ])(`for an invalid %s`, async (field, value) => {
    const response = await updateCustomerManually.executeImpl({
      ...validData,
      [field]: value,
    });
    expectControllerErrorField({
      response,
      errorField: field,
      code: 400,
    });
  });

  test('with multiple errors', async () => {
    const response = await updateCustomerManually.executeImpl({
      ...validData,
      firstName: '',
      lastName: '',
      notes: '',
    });

    expect((response.result as BaseError[]).length).toBe(3);
    expectControllerMultipleErrors({
      response,
      errors: [
        expect.objectContaining({ field: 'firstName' }),
        expect.objectContaining({ field: 'lastName' }),
        expect.objectContaining({ field: 'notes' }),
      ],
    });
  });

  test(`when customer isn't found`, async () => {
    const response = await updateCustomerManually.executeImpl({
      ...validData,
      id: nonExistingId,
    });

    expectControllerError({
      response,
      error: 'UpdateCustomerManuallyErrors.NotFound',
      code: 404,
    });
  });

  it('ManuallyCreatedCustomer.update gives NoNames errors when names are null', async () => {

    const repo = new ManuallyCreatedCustomerRepoFake();
    const spyGet = vi.spyOn(repo, 'get');
    const contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
    const updateCustomerManually = new UpdateCustomerManually({
      repo,
      contextProvider,
    });

    const response = await updateCustomerManually.executeImpl({
      ...validData,
      firstName: null,
      lastName: null,
    });

    expect((response.result as BaseError[]).length).toBe(2);
    const noNamesError = new ManuallyCreatedCustomerErrors.NoNames();
    expectControllerMultipleErrors({
      response,
      errors: expect.arrayContaining([
        noNamesError.setField('firstName'),
        noNamesError.clone('lastName'),
      ]),
    });

    expect(spyGet).not.toHaveBeenCalled();
  });

  test('when fullName is already taken', async () => {
    const response = await updateCustomerManually.executeImpl({
      ...validData,
      firstName: ManuallyCreatedCustomersInDb[3].firstName,
      lastName: ManuallyCreatedCustomersInDb[3].lastName,
    });

    expect((response.result as BaseError[]).length).toBe(2);
    const fullNameAlreadyTaken = new UpdateCustomerManuallyErrors.FullNameAlreadyTaken({ fullName: ManuallyCreatedCustomersInDb[3].fullName });
    expectControllerMultipleErrors({
      response,
      errors: expect.arrayContaining([
        fullNameAlreadyTaken.setField('firstName'),
        fullNameAlreadyTaken.clone('lastName'),
      ]),
    });
  });
});
