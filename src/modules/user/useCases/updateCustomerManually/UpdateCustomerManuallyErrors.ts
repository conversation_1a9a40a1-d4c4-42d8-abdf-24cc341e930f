import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { Status } from '@shared/core/Status';

export namespace UpdateCustomerManuallyErrors {
  export class NotFound extends BaseError {
    public constructor() {
      super({
        message: `El cliente siendo editado no se encontró.`,
        status: Status.NOT_FOUND,
      });
    }
  }
  export class FullNameAlreadyTaken extends BaseError {
    public constructor(args: { fullName: string }) {
      const { fullName } = args;
      super({
        message: `El nombre "${fullName}" ya se encuentra ocupado.`,
        status: Status.CONFLICT,
      });
    }
  }
}

patch({ UpdateCustomerManuallyErrors });