import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import {
  dateFormat,
  expectMultipleErrorsAppSync,
  orNull,
} from '@shared/utils/test';
import {
  createCustomerManually,
  removeManuallyCreatedCustomer,
} from '../../utils/test';
import { ManuallyCreatedCustomerRepo } from '../../repos/ManuallyCreatedCustomerRepo';
import { Request } from './UpdateCustomerManuallyDTOs';
import {
  ManuallyCreatedCustomersInDb,
} from '../../../reservation/utils/testExternal';
import {
  ManuallyCreatedCustomerWithTimestampsFragment,
} from '../../utils/fragments';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const appsync = new AppSyncClient();
const repo = new ManuallyCreatedCustomerRepo(models.ManuallyCreatedCustomer);

const query = gql`
  mutation (
    $firstName: String
    $lastName: String
    $notes: String
    $createdBy: ID!
    $id: ID!
  ) {
    updateCustomerManually(
      firstName: $firstName
      lastName: $lastName
      notes: $notes
      createdBy: $createdBy
      id: $id
    ) {
      result {
        ...ManuallyCreatedCustomerWithTimestampsFragment
      }
      time
    }
  }
  ${ManuallyCreatedCustomerWithTimestampsFragment}
`;

it(`updates a customer`, async () => {
  // First create a customer
  const { customer } = createCustomerManually();
  await repo.create(customer);

  const input = {
    firstName: chance.string({ alpha: true, numeric: true, length: 15 }),
    lastName: chance.string({ alpha: true, numeric: true, length: 15 }),
    notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
    createdBy: customer.createdBy,
    id: customer.id.toString(),
  };

  const received = await appsync.send<Request>({
    query,
    variables: input,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data || !json.data.updateCustomerManually)
    console.log('UpdateCustomerManually.e2e.ts', json);
  const response = json.data.updateCustomerManually;
  const fullName = `${input.firstName} ${input.lastName}`;
  expect(response).toEqual({
    result: {
      ...input,
      fullName,
      created_at: expect.stringMatching(dateFormat),
      updated_at: expect.stringMatching(dateFormat),
      deleted_at: null,
    },
    time: expect.stringMatching(dateFormat),
  });

  const saved = await repo.get({
    id: customer.id.toString(),
    createdBy: customer.createdBy,
  });
  expect(saved?.toDto()).toMatchObject({
    ...input,
    fullName,
  });

  await removeManuallyCreatedCustomer(customer.id.toString());
});

it(`errors when fullName is already taken`, async () => {
  const customer = ManuallyCreatedCustomersInDb[0];
  const { firstName: takenFirstName, lastName: takenLastName} = ManuallyCreatedCustomersInDb[3];
  const input = {
    firstName: takenFirstName,
    lastName: takenLastName,
    notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
    createdBy: customer.createdBy,
    id: customer.id.toString(),
  };

  const received = await appsync.send<Request>({
    query,
    variables: input,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  expectMultipleErrorsAppSync({
    response: json,
    errors: [
      {
        field: 'firstName',
        message: expect.any(String),
        status: 409,
        type: 'UpdateCustomerManuallyErrors.FullNameAlreadyTaken',
      },
      {
        field: 'lastName',
        message: expect.any(String),
        status: 409,
        type: 'UpdateCustomerManuallyErrors.FullNameAlreadyTaken',
      },
    ],
  })
});