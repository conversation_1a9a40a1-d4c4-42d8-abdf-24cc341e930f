import { Request, Response } from './UpdateCustomerManuallyDTOs';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { formatErrors } from '@shared/core/AppError';
import { IManuallyCreatedCustomerRepo } from '../../repos/IManuallyCreatedCustomerRepo';
import { AppSyncController } from '@shared/infra/Controllers';
import { UpdateCustomerManuallyErrors } from './UpdateCustomerManuallyErrors';
import { ManuallyCreatedCustomerDto } from '../../domain/ManuallyCreatedCustomer';
import { validateReq } from '../createCustomerManually/CreateCustomerManually';
import { IContextProvider } from '@shared/context/IContextProvider';

export class UpdateCustomerManually extends AppSyncController<Request, Response> {
  private readonly repo: IManuallyCreatedCustomerRepo;
  public constructor(args: {
    repo: IManuallyCreatedCustomerRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { id, createdBy} = dto;

    // Use same validation as in createCustomerManually to check if customer can be created with dto. This way, if we encounter a ManuallyCreatedCustomerErrors.NoNames error, we detect it here and spare the call to the repo, since we don't have to wait for ManuallyCreatedCustomerErrors.update to be called.
    const customerCreatedOrErrors = validateReq(dto);
    if (customerCreatedOrErrors.isFailure) return formatErrors(customerCreatedOrErrors.errors!);

    const customerCreated = customerCreatedOrErrors.value;
    const { firstName, lastName, notes } = customerCreated;

    const customer = await this.repo.get({ id, createdBy });

    if (!customer) return formatErrors([new UpdateCustomerManuallyErrors.NotFound()]);

    const updatedOrErrors = customer.update({
      firstName,
      lastName,
      notes,
    });
    if (updatedOrErrors.isFailure) return formatErrors(updatedOrErrors.errors!);

    const updated = updatedOrErrors.value;

    const existent = await this.repo.getByFullName({ fullName: updated.fullName, createdBy });
    let updatedFromDb: ManuallyCreatedCustomerDto;
    if (!existent || (existent.length === 1 && existent[0].id.toString() === id)) {
      updatedFromDb = await this.repo.update(updated);
    } else {
      const error = new UpdateCustomerManuallyErrors.FullNameAlreadyTaken({ fullName: updated.fullName });
      return formatErrors([
        error.setField('firstName'),
        error.clone('lastName'),
      ]);
    }

    return { status: Status.OK, result: updatedFromDb };
  }
}
