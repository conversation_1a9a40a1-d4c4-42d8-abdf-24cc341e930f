import { CreateCustomerUser } from './CreateCustomerUser';
import { ReturnUnexpectedError } from '@shared/decorators/ReturnUnexpectedError';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import setHooks from '@shared/infra/database/sequelize/hooks';

// import { CustomerUserRepoFake } from '../../repos/CustomerUserRepoFake';
import { CustomerUserRepo } from '../../repos/CustomerUserRepo';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

setHooks();
const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });

// const repo = new CustomerUserRepoFake();
const repo = new CustomerUserRepo(models.CustomerUser);

const controller = new CreateCustomerUser({
  repo,
  contextProvider,
});

const decorated = new ReturnUnexpectedError({
  wrapee: controller,
  contextProvider: new ContextProvider({ invoker }),
});
export const handler = decorated.execute.bind(decorated);
