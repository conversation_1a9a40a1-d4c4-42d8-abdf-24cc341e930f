import { Request, Response } from './CreateCustomerUserDTOs';
import { CreateCustomerUserErrors } from './CreateCustomerUserErrors';
import { Email } from '../../domain/Email';
import { Name } from '../../domain/Name';
import { Result } from '@shared/core/Result';
import { CustomerUser } from '../../domain/CustomerUser';
import { ICustomerUserRepo } from '../../repos/ICustomerUserRepo';
import { CreateCustomerUserEvents } from './CreateCustomerUserEvents';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { formatErrors } from '@shared/core/AppError';
import { TimeZone } from '@shared/core/TimeZone';
import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';
const { CREATED } = Status;

export class CreateCustomerUser extends AppSyncController<Request, Response> {
  private readonly repo: ICustomerUserRepo;
  public constructor(args: {
    repo: ICustomerUserRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
    CreateCustomerUserEvents.registration(contextProvider.invoker());
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const emailOrError = Email.create(dto.email);
    const firstNameOrError = Name.create(dto.firstName);
    const lastNameOrError = Name.create(dto.lastName);
    const timezoneOrError = TimeZone.create(dto.timezone);

    const dtoResult = Result.combine([
      emailOrError,
      firstNameOrError,
      lastNameOrError,
      timezoneOrError,
    ]);

    if (dtoResult.isFailure) return formatErrors([dtoResult.error!]);
    const email = emailOrError.value;
    const firstName = firstNameOrError.value;
    const lastName = lastNameOrError.value;
    const timezone = timezoneOrError.value;

    const emailAlreadyTaken = await this.repo.exists(email);
    if (emailAlreadyTaken)
      return formatErrors([
        new CreateCustomerUserErrors.EmailAlreadyTaken(email.value),
      ]);

    const user = CustomerUser.create({
      email,
      firstName,
      lastName,
      timezone,
    });

    await this.repo.create(user);

    return { status: CREATED, result: user.toDto() };
  }
}
