import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { dateFormat, expectErrorAppSync } from '@shared/utils/test';
import { CustomerUsersInDb } from '@shared/infra/database/sequelize/migrations/CustomerUsers.json';
import { getTimezone } from '@shared/core/test';
import { uuidFormat } from '@shared/utils/utils';
import { removeCustomerUser } from '../../utils/test';
import { Request } from './CreateCustomerUserDTOs';
import { CustomerFragment } from '../../utils/fragments';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const appsync = new AppSyncClient();

const input = {
  firstName: chance.string({ alpha: true, numeric: true, length: 15 }),
  lastName: chance.string({ alpha: true, numeric: true, length: 15 }),
  email: chance.email(),
  timezone: getTimezone(),
};

const query = gql`
  mutation (
    $firstName: String!
    $lastName: String!
    $email: String!
    $timezone: String!
  ) {
    createCustomerUser(
      firstName: $firstName
      lastName: $lastName
      email: $email
      timezone: $timezone
    ) {
      result {
        ...CustomerFragment
      }
      time
    }
  }
  ${CustomerFragment}
`;

it(`creates a customer`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: input,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.createCustomerUser;
  expect(response).toEqual({
    result: {
      ...input,
      id: expect.stringMatching(uuidFormat),
      phone: null,
    },
    time: expect.stringMatching(dateFormat),
  });
  await removeCustomerUser(response.result.id);
});

it(`returns error when email is already taken`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: {
      ...input,
      email: CustomerUsersInDb[0].email,
    },
  });

  expect(received.status).toBe(200);
  const response = await received.json();
  expectErrorAppSync({
    response,
    query: 'createCustomerUser',
    error: 'CreateCustomerUserErrors.EmailAlreadyTaken',
    status: 409,
  });
});
