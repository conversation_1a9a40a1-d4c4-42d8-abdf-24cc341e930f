import { DomainEvents } from '@shared/core/domain/DomainEvents';
import { CustomerUserCreatedEvent } from '../../domain/events/CustomerUserCreatedEvent';
import { IInvoker } from '@shared/infra/invocation/IInvoker';

export class CreateCustomerUserEvents {
  public static registration(invoker: IInvoker) {
    // Add all process.env used:
    const { distributeDomainEvents } = process.env;
    if (!distributeDomainEvents) {
      console.log('process.env', process.env);
      throw new Error(`Undefined env var!`);
    }

    DomainEvents.setInvoker(invoker);
    DomainEvents.register(
      `${distributeDomainEvents}`,
      CustomerUserCreatedEvent.name,
    );
  }
}
