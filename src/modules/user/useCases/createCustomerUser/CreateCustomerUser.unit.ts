import { expect, test, beforeEach, vi } from 'vitest';
vi.stubEnv('distributeDomainEvents', 'dummy');
import { CreateCustomerUser } from './CreateCustomerUser';
import { CustomerUsersInDb } from '@shared/infra/database/sequelize/migrations/CustomerUsers.json';
import { CustomerUserRepoFake } from '../../repos/CustomerUserRepoFake';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { uuidFormat } from '@shared/utils/utils';
import { getTimezone } from '@shared/core/test';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

let repo, createCustomerUser: CreateCustomerUser, contextProvider: ContextProvider;
beforeEach(() => {
  repo = new CustomerUserRepoFake();
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  createCustomerUser = new CreateCustomerUser({
    repo,
    contextProvider,
  });
});

test(`CustomerUser creation`, async () => {
  const validData = {
    firstName: chance.string({ alpha: true, numeric: true, length: 15 }),
    lastName: chance.string({ alpha: true, numeric: true, length: 15 }),
    email: chance.email(),
    timezone: getTimezone(),
  };

  const result = await createCustomerUser.executeImpl(validData);

  expect(result).toMatchObject({
    status: 201,
    result: {
      id: expect.stringMatching(uuidFormat),
    },
  });
});

test(`CustomerUser creation fails for taken email`, async () => {
  const data = {
    firstName: chance.string({ alpha: true, numeric: true, length: 15 }),
    lastName: chance.string({ alpha: true, numeric: true, length: 15 }),
    email: CustomerUsersInDb[0].email,
    timezone: getTimezone(),
  };

  const result = await createCustomerUser.executeImpl(data);

  expect(result).toMatchObject({
    status: 409,
    result: [
      {
        message: expect.any(String),
        type: 'CreateCustomerUserErrors.EmailAlreadyTaken',
        status: 409,
      },
    ],
  });
});
