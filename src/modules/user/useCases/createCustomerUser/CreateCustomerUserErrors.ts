import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { Status } from '@shared/core/Status';

export namespace CreateCustomerUserErrors {
  export class EmailAlreadyTaken extends BaseError {
    public constructor(email: string) {
      super({
        message: `The email ${email} is already taken`,
        status: Status.CONFLICT,
      });
    }
  }
}

patch({ CreateCustomerUserErrors });
