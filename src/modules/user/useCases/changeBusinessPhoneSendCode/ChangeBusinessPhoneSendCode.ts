import { Request, Response } from './ChangeBusinessPhoneSendCodeDTOs';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { BaseError, formatErrors } from '@shared/core/AppError';
import { IBusinessUserRepo } from '../../repos/IBusinessUserRepo';
import {
  ChangeBusinessPhoneSendCodeErrors,
} from './ChangeBusinessPhoneSendCodeErrors';
import { AppSyncController } from '@shared/infra/Controllers';
import { getLng } from '@shared/utils/utils';
import { IContextProvider } from '@shared/context/IContextProvider';
import { Phone } from '../../domain/Phone';
import { TimeZone } from '@shared/core/TimeZone';
import { BusinessUser } from '../../domain/BusinessUser';
import { SMSSrv } from '../../services/SMSSrv';

export class ChangeBusinessPhoneSendCode extends AppSyncController<Request, Response> {
  private readonly repo: IBusinessUserRepo;
  private readonly smsSrv: SMSSrv;
  public constructor(args: {
    repo: IBusinessUserRepo;
    contextProvider: IContextProvider;
    smsService: SMSSrv;
  }) {
    const { repo, contextProvider, smsService } = args;
    super({ contextProvider });
    this.repo = repo;
    this.smsSrv = smsService;
  }

  public async executeImpl(request: Request): ControllerResult<Response> {

    const { lng: _lng, id, newPhone: _newPhone, timezone: _timezone } = request;

    const { errors, newPhone } = validateReq({ newPhone: _newPhone, lng: _lng });
    if (errors) return formatErrors(errors);

    let timezone: TimeZone | null = null;
    const timezoneOrError = TimeZone.create(_timezone);
    if (timezoneOrError.isFailure) {
      // Since timezone is computed automatically on the frontend (no user input), send this error to analytics but don't stop the flow.
      await this.contextProvider.sendAnalytics(request);
    } else
      timezone = timezoneOrError.value;

    const lng = getLng(_lng);

    const user = await this.repo.get(id);
    if (!user)
      return formatErrors([
        new ChangeBusinessPhoneSendCodeErrors.UserNotFound({ id, lng }),
      ]);

    if (user.phone && user.phone.equals(newPhone))
      return formatErrors([
        new ChangeBusinessPhoneSendCodeErrors.AlreadyUsingThatNumber({ lng }),
      ]);

    const codeOrErrors = user.addPhoneVerificationAttempt({ lng, timezone, newPhone });
    if (codeOrErrors.isFailure)
      return formatErrors(codeOrErrors.errors!);

    const { code, attemptsLeft } = codeOrErrors.value;

    // Send code to newPhone
    const smsResult = await this.smsSrv.sendCode({
      phoneNumber: newPhone.value,
      code: code.value,
      lng,
    });

    if (smsResult.isFailure) {
      return formatErrors(smsResult.errors!);
    }

    await this.repo.update(user);

    const dto = user.toDto();

    return { status: Status.OK, result: {
      attemptsLeft,
      user: BusinessUser.forFrontend(dto),
    } };
  }
}

export function validateReq(args: {
  newPhone: string;
  lng: string;
}): {
  errors: [BaseError, ...BaseError[]];
  newPhone: null;
} | {
  errors: null;
  newPhone: Phone;
} {
  const { newPhone: _newPhone, lng: _lng } = args;
  const lng = getLng(_lng);

  const errors: BaseError[] = [];
  const newPhoneOrError = Phone.create({ value: _newPhone, lng });
  if (newPhoneOrError.isFailure)
    errors.push(newPhoneOrError.error!.setField('newPhone'));

  if (errors.length) return {
    errors: errors as [BaseError, ...BaseError[]],
    newPhone: null,
  };

  return {
    errors: null,
    newPhone: newPhoneOrError.value,
  };
}
