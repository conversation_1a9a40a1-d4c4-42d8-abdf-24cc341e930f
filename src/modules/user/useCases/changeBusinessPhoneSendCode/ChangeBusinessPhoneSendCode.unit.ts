import { beforeEach, describe, expect, test, vi } from 'vitest';
import {
  ChangeBusinessPhoneSendCode,
  validateReq,
} from './ChangeBusinessPhoneSendCode';
import { BusinessUserRepoFake } from '../../repos/BusinessUserRepoFake';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { Request, Response } from './ChangeBusinessPhoneSendCodeDTOs';
import { IBusinessUserRepo } from '../../repos/IBusinessUserRepo';
import {
  pickLng,
  nonExistingId,
  expectControllerErrorContaining,
  expectControllerError,
  mockContext,
} from '@shared/utils/test';
import { getTimezone } from '@shared/core/test';
import { Status } from '@shared/core/Status';
import { BusinessUser } from '../../domain/BusinessUser';
import { Phone } from '../../domain/Phone';
import { SMSSrv } from '../../services/SMSSrv';
import { SmsClientFake } from '../../services/SmsClientFake';

let repo: IBusinessUserRepo, changeBusinessPhoneSendCode: ChangeBusinessPhoneSendCode, contextProvider: ContextProvider, smsSrv: SMSSrv;
beforeEach(() => {
  repo = new BusinessUserRepoFake();
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  contextProvider.set(mockContext);
  smsSrv = new SMSSrv({ smsClient: new SmsClientFake() });
  changeBusinessPhoneSendCode = new ChangeBusinessPhoneSendCode({
    repo,
    contextProvider,
    smsService: smsSrv,
  });
});

const businessUser = BusinessUsersInDb[0];
const validData: Request = {
  id: businessUser.id,
  newPhone: '+549345123499', // Different from current phone
  lng: pickLng(),
  timezone: getTimezone(),
};

describe('succeeds', () => {
  test('succeeds with valid data', async () => {
    const result = await changeBusinessPhoneSendCode.executeImpl(validData);

    expect(result).toMatchObject({
      status: Status.OK,
      result: {
        attemptsLeft: BusinessUser.MAX_VERIFICATION_ATTEMPTS_IN_24_HOURS - 1,
        user: {
          id: businessUser.id,
        },
      },
    });
    // @ts-expect-error: code should be hidden in the response
    expect((result.result as Response).user.phoneVerification.code).not.toBeDefined();
  });

  test('succeeds with invalid timezone', async () => {
    const result = await changeBusinessPhoneSendCode.executeImpl({
      ...validData,
      timezone: 'invalid',
    });

    expect(result).toMatchObject({
      status: Status.OK,
      result: {
        attemptsLeft: BusinessUser.MAX_VERIFICATION_ATTEMPTS_IN_24_HOURS - 1,
        user: {
          id: businessUser.id,
        },
      },
    });
    // @ts-expect-error: code should be hidden in the response
    expect((result.result as Response).user.phoneVerification.code).not.toBeDefined();
  });

  test('adds phone verification attempt to user', async () => {
    const updateSpy = vi.spyOn(repo, 'update');

    const result = await changeBusinessPhoneSendCode.executeImpl(validData);
    const originalPhoneVerificationAttempts = businessUser.phoneVerificationAttempts?.length || 0;

    expect(updateSpy).toHaveBeenCalledOnce();
    const updatedUser = updateSpy.mock.calls[0][0];
    expect(updatedUser.toDto().phoneVerification.attempts!.length).toBe(originalPhoneVerificationAttempts + 1);
    expect(updatedUser.toDto().phoneVerification.code).toEqual(expect.any(Number));

    expect(result).toMatchObject({
      status: Status.OK,
      result: {
        attemptsLeft: BusinessUser.MAX_VERIFICATION_ATTEMPTS_IN_24_HOURS - 1,
        user: {
          id: businessUser.id,
        },
      },
    });
    // @ts-expect-error: code should be hidden in the response
    expect((result.result as Response).user.phoneVerification.code).not.toBeDefined();
  });

  test('returns correct attempts left count', async () => {
    // First attempt
    const result1 = await changeBusinessPhoneSendCode.executeImpl(validData);
    const attemptsLeft1 = (result1.result as Response).attemptsLeft;

    // Second attempt
    const result2 = await changeBusinessPhoneSendCode.executeImpl(validData);
    const attemptsLeft2 = (result2.result as Response).attemptsLeft;

    expect(attemptsLeft2).toBe(attemptsLeft1 - 1);
  });
});

describe('fails', () => {

  test('with invalid phone number', async () => {
    const result = await changeBusinessPhoneSendCode.executeImpl({
      ...validData,
      newPhone: 'invalid-phone',
    });

    expectControllerErrorContaining({
      response: result,
      errorContaining: 'PhoneErrors',
      code: 400,
      field: 'newPhone',
    });
  });

  test(`when user isn't found`, async () => {
    const result = await changeBusinessPhoneSendCode.executeImpl({
      ...validData,
      id: nonExistingId,
    });

    expectControllerError({
      response: result,
      error: 'ChangeBusinessPhoneSendCodeErrors.UserNotFound',
      code: 404,
    });
  });

  test('when trying to use the same phone number', async () => {
    const result = await changeBusinessPhoneSendCode.executeImpl({
      ...validData,
      newPhone: businessUser.phone!, // Same as current phone
    });

    expectControllerError({
      response: result,
      error: 'ChangeBusinessPhoneSendCodeErrors.AlreadyUsingThatNumber',
      code: 400,
    });
  });
});

describe('validateReq', () => {
  test('succeeds with valid data', () => {
    const result = validateReq(validData);

    expect(result.errors).toBeNull();
    expect(result.newPhone!.equals(Phone.create({ value: validData.newPhone }).value)).toBe(true);
  });

  test('fails with invalid phone number', () => {
    const result = validateReq({
      ...validData,
      newPhone: '+54935132799351234562',
    });

    expect(result.errors![0]).toMatchObject({
      field: 'newPhone',
      type: expect.stringContaining('PhoneErrors.'),
      message: expect.any(String),
      status: 400,
    });
    expect(result.newPhone).toBeNull();
  });
});
