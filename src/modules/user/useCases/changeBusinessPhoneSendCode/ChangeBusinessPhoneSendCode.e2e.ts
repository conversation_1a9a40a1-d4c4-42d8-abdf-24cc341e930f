import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import { gql } from 'graphql-tag';
import { Request } from './ChangeBusinessPhoneSendCodeDTOs';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { expectErrorAppSync, pickLng } from '@shared/utils/test';
import { getTimezone } from '@shared/core/test';
import { BusinessUser, VerificationStatus } from '../../domain/BusinessUser';
import {
  expectInAbout24hrs,
  expectInAboutAMinute,
  getPhone,
} from '../../utils/test';
import { BusinessUserRepo } from '../../repos/BusinessUserRepo';
import { BusinessUserForFrontendFragment } from '../../utils/utils';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');

const repo = new BusinessUserRepo(models.BusinessUser);

const appsync = new AppSyncClient();
const query = gql`
  mutation ($id: ID!, $newPhone: String!, $lng: String!, $timezone: String!) {
    changeBusinessPhoneSendCode(
      id: $id
      newPhone: $newPhone
      lng: $lng
      timezone: $timezone
    ) {
      result {
        attemptsLeft
        user {
          ...BusinessUserForFrontendFragment
        }
      }
      time
    }
  }
  ${BusinessUserForFrontendFragment}
`;

it(`sends verification code for phone change`, async () => {

  const businessUser = BusinessUsersInDb[8];

  const getInput = () => ({
    id: businessUser.id,
    newPhone: getPhone().value,
    lng: pickLng(),
    timezone: getTimezone(),
  });

  // First request
  let variables = getInput();
  let received = await appsync.send<Request>({
    query,
    variables,
  });

  expect(received.status).toBe(200);
  const json = await received.json();

  let response = json.data.changeBusinessPhoneSendCode;
  expect(response.result.attemptsLeft).toBe(BusinessUser.MAX_VERIFICATION_ATTEMPTS_IN_24_HOURS - 1);
  expect(response.result.user).toMatchObject({
    id: businessUser.id,
    phone: businessUser.phone,
    phoneVerification: {
      status: VerificationStatus.STARTED,
      newPhone: variables.newPhone,
      blockedUntil: null,
    },
  });
  expect(response.result.user.phoneVerification.code).not.toBeDefined();
  expectInAboutAMinute(new Date(response.result.user.phoneVerification.codeExpiresAt).getTime());

  // Second request
  variables = getInput();
  received = await appsync.send<Request>({
    query,
    variables,
  });
  response = (await received.json()).data.changeBusinessPhoneSendCode;
  expect(response.result.attemptsLeft).toBe(BusinessUser.MAX_VERIFICATION_ATTEMPTS_IN_24_HOURS - 2);
  expect(response.result.user).toMatchObject({
    id: businessUser.id,
    phone: businessUser.phone,
    phoneVerification: {
      status: VerificationStatus.STARTED,
      newPhone: variables.newPhone,
      blockedUntil: null,
    },
  });
  expect(response.result.user.phoneVerification.code).not.toBeDefined();
  expectInAboutAMinute(new Date(response.result.user.phoneVerification.codeExpiresAt).getTime());

  // Third request
  const lastSuccessfullReq = getInput();
  received = await appsync.send<Request>({
    query,
    variables: lastSuccessfullReq,
  });
  response = (await received.json()).data.changeBusinessPhoneSendCode;
  expect(response.result.attemptsLeft).toBe(0);
  expect(response.result.user).toMatchObject({
    id: businessUser.id,
    phone: businessUser.phone,
    phoneVerification: {
      status: VerificationStatus.STARTED,
      newPhone: lastSuccessfullReq.newPhone,
    },
  });
  expect(response.result.user.phoneVerification.code).not.toBeDefined();
  expectInAboutAMinute(new Date(response.result.user.phoneVerification.codeExpiresAt).getTime());
  expectInAbout24hrs(new Date(response.result.user.phoneVerification.blockedUntil).getTime());

  // Fourth request
  variables = getInput();
  received = await appsync.send<Request>({
    query,
    variables,
  });
  response = await received.json();
  expectErrorAppSync({
    response,
    query: 'changeBusinessPhoneSendCode',
    error: 'BusinessUserErrors.NoAttemptsLeft',
    status: 400,
  });

  const saved = await repo.get(businessUser.id);
  expect(saved!.toDto().phoneVerification).toMatchObject({
    status: 'STARTED',
    newPhone: lastSuccessfullReq.newPhone,
  })

  // Reverse changes
  await repo.update(BusinessUser.assemble(BusinessUserRepo.mapDb2Dto(businessUser)));

});

it(`fails when using the already existing phone`, async () => {
  const input: Request = {
    id: BusinessUsersInDb[0].id,
    newPhone: BusinessUsersInDb[0].phone!,
    lng: pickLng(),
    timezone: getTimezone(),
  };

  const received = await appsync.send<Request>({
    query,
    variables: input,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  
  expectErrorAppSync({
    response: json,
    query: 'changeBusinessPhoneSendCode',
    error: 'ChangeBusinessPhoneSendCodeErrors.AlreadyUsingThatNumber',
    status: 400,
  });
});