import { BaseError, NotFound as _NotFound } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { PossibleLngs } from '@shared/utils/utils';
import { Status } from '@shared/core/Status';

export namespace ChangeBusinessPhoneSendCodeErrors {
  export class UserNotFound extends _NotFound {
    public constructor(args: { id: string, lng: PossibleLngs }) {
      const { id, lng } = args;
      const t = trans[lng];
      super({
        message: t.notFound(id),
      });
    }
  }
  export class AlreadyUsingThatNumber extends BaseError {
    public constructor(args: { lng: PossibleLngs }) {
      const { lng } = args;
      const t = trans[lng];
      super({
        message: t.alreadyUsingThatNumber(),
        status: Status.BAD_REQUEST,
      });
    }
  }
}

const trans = {
  en: {
    notFound(v: string) {
      return `Business user ${v} not found`;
    },
    alreadyUsingThatNumber() {
      return `You are already using that phone number.`;
    },
  },
  es: {
    notFound(v: string) {
      return `Usuario ${v} no encontrado`;
    },
    alreadyUsingThatNumber() {
      return `Ya estás usando ese número de teléfono.`;
    },
  },
};

patch({ ChangeBusinessPhoneSendCodeErrors });
