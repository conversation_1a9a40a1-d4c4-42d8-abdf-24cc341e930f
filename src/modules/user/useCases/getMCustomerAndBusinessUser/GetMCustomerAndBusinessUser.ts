import { Request, Response } from './GetMCustomerAndBusinessUserDTOs';
import { IBusinessUserRepo } from '../../repos/IBusinessUserRepo';
import { IManuallyCreatedCustomerRepo } from '../../repos/IManuallyCreatedCustomerRepo';
import { FunctionController2 } from '@shared/infra/Controllers';
import { Result2 } from '@shared/core/Result2';
import {
  GetMCustomerAndBusinessUserErrors,
} from './GetMCustomerAndBusinessUserErrors';
import { BaseError } from '@shared/core/AppError';

// This use case is used by CreateReservationByBusiness use case in module reservation.
export class GetMCustomerAndBusinessUser extends FunctionController2<
  Request,
  Response
> {
  private readonly customerRepo: IManuallyCreatedCustomerRepo;
  private readonly businessRepo: IBusinessUserRepo;

  public constructor(args: {
    customerRepo: IManuallyCreatedCustomerRepo;
    businessRepo: IBusinessUserRepo;
  }) {
    super();
    const { customerRepo, businessRepo } = args;
    this.customerRepo = customerRepo;
    this.businessRepo = businessRepo;
  }

  public async executeImpl(dto: Request) {
    const { customerId, businessId } = dto;

    const [businessOrNull, customerOrNull] = await Promise.all([
      this.businessRepo.get(businessId),
      this.customerRepo.get({ id: customerId, createdBy: businessId }),
    ]);

    const errors: BaseError[] = []
    if (!businessOrNull)
      errors.push(new GetMCustomerAndBusinessUserErrors.BusinessNotFound(businessId).setField('businessId'));
    if (!customerOrNull)
      errors.push(new GetMCustomerAndBusinessUserErrors.MCustomerNotFound({ id: customerId, createdBy: businessId }).setField('customerId'));

    if (errors.length)
      return Result2.fail(errors);

    return Result2.ok({
      customer: customerOrNull!.toDto(),
      business: businessOrNull!.toDto(),
    });
  }
}
