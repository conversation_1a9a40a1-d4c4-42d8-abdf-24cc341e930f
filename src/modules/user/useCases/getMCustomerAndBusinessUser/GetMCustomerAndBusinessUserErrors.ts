import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { Status } from '@shared/core/Status';

export namespace GetMCustomerAndBusinessUserErrors {
  export class BusinessNotFound extends BaseError {
    public constructor(id: string) {
      super({
        message: `Business with id "${id}" not found.`,
        status: Status.NOT_FOUND,
      });
    }
  }
  export class MCustomerNotFound extends BaseError {
    public constructor(args: { id: string, createdBy: string }) {
      const { id, createdBy } = args;
      super({
        message: `Manually created customer with id "${id}" not found for business with id "${createdBy}".`,
        status: Status.NOT_FOUND,
      });
    }
  }
}

patch({ GetMCustomerAndBusinessUserErrors });
