import { GetMCustomerAndBusinessUser } from './GetMCustomerAndBusinessUser';
import { BusinessUserRepo } from '../../repos/BusinessUserRepo';
import { ManuallyCreatedCustomerRepo } from '../../repos/ManuallyCreatedCustomerRepo';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('./../../../../shared/infra/database/sequelize/models'); // if I use @shared, src/modules/reservation/useCases/createReservationByBusiness/CreateReservationByBusiness.unit.ts fails

const customerRepo = new ManuallyCreatedCustomerRepo(
  models.ManuallyCreatedCustomer,
);
const businessRepo = new BusinessUserRepo(models.BusinessUser);
const controller = new GetMCustomerAndBusinessUser({ customerRepo, businessRepo });

export const handler = controller.executeImpl.bind(controller);