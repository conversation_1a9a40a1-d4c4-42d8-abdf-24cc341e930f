import { expect, beforeEach, it, describe, test } from 'vitest';
import { Request } from './GetMCustomerAndBusinessUserDTOs';
import { BusinessUserRepoFake } from '../../repos/BusinessUserRepoFake';
import { GetMCustomerAndBusinessUser } from './GetMCustomerAndBusinessUser';
import { dateFormat, nonExistingId } from '@shared/utils/test';
import { rmTimestamps } from '@shared/utils/utils';
import { ManuallyCreatedCustomerRepoFake } from '../../repos/ManuallyCreatedCustomerRepoFake';
import { ManuallyCreatedCustomersInDb } from '@shared/infra/database/sequelize/migrations/ManuallyCreatedCustomers.json';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import {
  GetMCustomerAndBusinessUserErrors,
} from './GetMCustomerAndBusinessUserErrors';
import { BusinessUserRepo } from '../../repos/BusinessUserRepo';

let customerRepo,
  businessRepo,
  getMCustomerAndBusinessUser: GetMCustomerAndBusinessUser;
beforeEach(() => {
  customerRepo = new ManuallyCreatedCustomerRepoFake();
  businessRepo = new BusinessUserRepoFake();
  getMCustomerAndBusinessUser = new GetMCustomerAndBusinessUser({
    customerRepo,
    businessRepo,
  });
});

const businessInDb = BusinessUsersInDb[0];
const input: Request = {
  customerId: ManuallyCreatedCustomersInDb[1].id, // ManuallyCreatedCustomersInDb[1].createdBy = BusinessUsersInDb[0].id
  businessId: businessInDb.id,
};

it.only(`gets a customer and business user`, async () => {
  const result = await getMCustomerAndBusinessUser.executeImpl(input);

  const businessDto = BusinessUserRepo.mapDb2Dto(businessInDb);
  expect(result).toMatchObject({
    isSuccess: true,
    value: {
      customer: rmTimestamps(ManuallyCreatedCustomersInDb[1]),
      business: {
        ...businessDto,
        phoneVerification: {
          ...businessDto.phoneVerification,
          attempts: expect.arrayContaining([
            expect.stringMatching(dateFormat),
          ]),
          codeExpiresAt: expect.stringMatching(businessDto.phoneVerification.codeExpiresAt!.replace('.000', '')),
        },
      },
    },
  });
});

describe('returns failure when', () => {

  test(`business isn't found`, async () => {
    const input2 = {
      ...input,
      businessId: nonExistingId,
    }
    const result = await getMCustomerAndBusinessUser.executeImpl(input2);

    expect(result).toMatchObject({
      isFailure: true,
      errors: [
        new GetMCustomerAndBusinessUserErrors.BusinessNotFound(nonExistingId).setField('businessId'),
        new GetMCustomerAndBusinessUserErrors.MCustomerNotFound({ id: input2.customerId, createdBy: nonExistingId }).setField('customerId'),
      ],
    });
  });

  it(`customer isn't found`, async () => {
    const result = await getMCustomerAndBusinessUser.executeImpl({
      ...input,
      customerId: nonExistingId,
    });

    expect(result).toMatchObject({
      isFailure: true,
      errors: [new GetMCustomerAndBusinessUserErrors.MCustomerNotFound({ id: nonExistingId, createdBy: input.businessId }).setField('customerId')],
    });
  });
});