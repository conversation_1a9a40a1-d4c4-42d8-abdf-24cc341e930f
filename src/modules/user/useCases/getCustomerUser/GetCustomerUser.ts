import { Request, Response } from './GetCustomerUserDTOs';
import { ICustomerUserRepo } from '../../repos/ICustomerUserRepo';
import { Status } from '@shared/core/Status';
import { ControllerResult } from '@shared/core/ControllerResult';
import { CustomerUser } from '../../domain/CustomerUser';
import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';

const { OK } = Status;

export class GetCustomerUser extends AppSyncController<Request, Response> {
  private readonly customerUserRepo: ICustomerUserRepo;

  public constructor(args: {
    customerUserRepo: ICustomerUserRepo;
    contextProvider: IContextProvider;
  }) {
    const { customerUserRepo, contextProvider } = args;
    super({ contextProvider });
    this.customerUserRepo = customerUserRepo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { id } = dto;
    const userOrNull = await this.customerUserRepo.get(id);
    if (!userOrNull) return { status: OK, result: null };

    const user = userOrNull as CustomerUser;

    return { status: OK, result: user.toDto() };
  }
}
