import { expect, beforeEach, it } from 'vitest';
import { Request } from './GetCustomerUserDTOs';
import { CustomerUsersInDb } from '@shared/infra/database/sequelize/migrations/CustomerUsers.json';
import { CustomerUserRepoFake } from '../../repos/CustomerUserRepoFake';
import { GetCustomerUser } from './GetCustomerUser';
import { rmTimestamps } from '@shared/utils/utils';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';

let customerUserRepo, getCustomerUser: GetCustomerUser, contextProvider: ContextProvider;
beforeEach(() => {
  customerUserRepo = new CustomerUserRepoFake();
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  getCustomerUser = new GetCustomerUser({
    customerUserRepo,
    contextProvider,
  });
});

it(`gets a user`, async () => {
  const validData: Request = {
    id: CustomerUsersInDb[0].id,
  };

  const response = await getCustomerUser.executeImpl(validData);

  expect(response).toMatchObject({
    status: 200,
    result: rmTimestamps(CustomerUsersInDb[0]),
  });
});

it(`returns null when id isn't found`, async () => {
  const response = await getCustomerUser.executeImpl({
    id: 'non-existent user',
  });

  expect(response).toMatchObject({
    status: 200,
    result: null,
  });
});
