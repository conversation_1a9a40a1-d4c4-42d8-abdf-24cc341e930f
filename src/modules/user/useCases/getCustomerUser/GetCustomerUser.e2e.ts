import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { dateFormat, nonExistingId } from '@shared/utils/test';
import { CustomerUsersInDb } from '@shared/infra/database/sequelize/migrations/CustomerUsers.json';
import { rmTimestamps } from '@shared/utils/utils';
import { Request } from './GetCustomerUserDTOs';
import { CustomerFragment } from '../../utils/fragments';

const appsync = new AppSyncClient();

it(`gets a customer`, async () => {
  const received = await appsync.send<Request>({
    query: gql`
      query ($id: ID!) {
        getCustomerUser(id: $id) {
          result {
            ...CustomerFragment
          }
          time
        }
      }
      ${CustomerFragment}
    `,
    variables: {
      id: CustomerUsersInDb[0].id,
    },
  });

  expect(received.status).toBe(200);
  const response = (await received.json()).data.getCustomerUser;
  expect(response).toEqual({
    result: rmTimestamps(CustomerUsersInDb[0]),
    time: expect.stringMatching(dateFormat),
  });
});

it(`returns null when the customer isn't found`, async () => {
  const received = await appsync.send<Request>({
    query: gql`
      query ($id: ID!) {
        getCustomerUser(id: $id) {
          result {
            __typename
          } # To avoid error, I need to put something here even when I'll receive null
          time
        }
      }
    `,
    variables: {
      id: nonExistingId,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  expect(json.data.getCustomerUser).toEqual({
    result: null,
    time: expect.stringMatching(dateFormat),
  });
});
