import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { dateFormat } from '@shared/utils/test';
import { ManuallyCreatedCustomersInDb } from '@shared/infra/database/sequelize/migrations/ManuallyCreatedCustomers.json';
import { rmTimestamps } from '@shared/utils/utils';
import { Request } from './GetManuallyCreatedCustomerDTOs';
import {
  ManuallyCreatedCustomerWithTimestampsFragment,
} from '../../utils/fragments';

const appsync = new AppSyncClient();

it(`gets a customer`, async () => {
  const received = await appsync.send<Request>({
    query: gql`
      query ($id: ID!, $createdBy: ID!) {
        getManuallyCreatedCustomer(id: $id, createdBy: $createdBy) {
          result {
            ...ManuallyCreatedCustomerWithTimestampsFragment
          }
          time
        }
      }
      ${ManuallyCreatedCustomerWithTimestampsFragment}
    `,
    variables: {
      id: ManuallyCreatedCustomersInDb[0].id,
      createdBy: ManuallyCreatedCustomersInDb[0].createdBy,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  const response = json.data.getManuallyCreatedCustomer;
  expect(response).toMatchObject({
    result: rmTimestamps(ManuallyCreatedCustomersInDb[0]),
    time: expect.stringMatching(dateFormat),
  });
});

it(`returns null when the customer isn't found`, async () => {
  const received = await appsync.send<Request>({
    query: gql`
      query ($id: ID!, $createdBy: ID!) {
        getManuallyCreatedCustomer(id: $id, createdBy: $createdBy) {
          result {
            __typename
          } # To avoid error, I need to put something here even when I'll receive null
          time
        }
      }
    `,
    variables: {
      id: ManuallyCreatedCustomersInDb[0].id,
      createdBy: ManuallyCreatedCustomersInDb[1].createdBy,
    },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  expect(json.data.getManuallyCreatedCustomer).toMatchObject({
    result: null,
    time: expect.stringMatching(dateFormat),
  });
});
