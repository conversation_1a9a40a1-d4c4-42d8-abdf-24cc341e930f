import { Request, Response } from './GetManuallyCreatedCustomerDTOs';
import { IManuallyCreatedCustomerRepo } from '../../repos/IManuallyCreatedCustomerRepo';
import { Status } from '@shared/core/Status';
import { ControllerResult } from '@shared/core/ControllerResult';
import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';

const { OK } = Status;

export class GetManuallyCreatedCustomer extends AppSyncController<
  Request,
  Response
> {
  private readonly repo: IManuallyCreatedCustomerRepo;

  public constructor(args: {
    repo: IManuallyCreatedCustomerRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { id, createdBy } = dto;
    const customerOrNull = await this.repo.get({ id, createdBy });
    if (!customerOrNull) return { status: OK, result: null };

    return { status: OK, result: customerOrNull.toDto() };
  }
}
