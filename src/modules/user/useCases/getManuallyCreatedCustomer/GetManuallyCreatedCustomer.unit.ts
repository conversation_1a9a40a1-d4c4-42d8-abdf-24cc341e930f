import { expect, beforeEach, it } from 'vitest';
import { Request } from './GetManuallyCreatedCustomerDTOs';
import { ManuallyCreatedCustomerRepoFake } from '../../repos/ManuallyCreatedCustomerRepoFake';
import { GetManuallyCreatedCustomer } from './GetManuallyCreatedCustomer';
import { ManuallyCreatedCustomersInDb } from '@shared/infra/database/sequelize/migrations/ManuallyCreatedCustomers.json';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';

let repo, getManuallyCreatedCustomer: GetManuallyCreatedCustomer, contextProvider: ContextProvider;
beforeEach(() => {
  repo = new ManuallyCreatedCustomerRepoFake();
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  getManuallyCreatedCustomer = new GetManuallyCreatedCustomer({
    repo,
    contextProvider,
  });
});

const input: Request = {
  id: ManuallyCreatedCustomersInDb[0].id,
  createdBy: ManuallyCreatedCustomersInDb[0].createdBy,
};

it(`gets a user`, async () => {
  const response = await getManuallyCreatedCustomer.executeImpl(input);

  expect(response).toMatchObject({
    status: 200,
    result: ManuallyCreatedCustomersInDb[0],
  });
});

it(`returns null when id isn't found`, async () => {
  const response = await getManuallyCreatedCustomer.executeImpl({
    ...input,
    createdBy: 'non-existent user',
  });

  expect(response).toMatchObject({
    status: 200,
    result: null,
  });
});
