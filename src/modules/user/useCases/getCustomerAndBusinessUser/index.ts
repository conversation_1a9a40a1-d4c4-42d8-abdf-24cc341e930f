import { GetCustomerAndBusinessUser } from './GetCustomerAndBusinessUser';
import { CustomerUserRepo } from '../../repos/CustomerUserRepo';
import { BusinessUserRepo } from '../../repos/BusinessUserRepo';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('./../../../../shared/infra/database/sequelize/models');  // Si uso @shared fallan unit tests

const customerRepo = new CustomerUserRepo(models.CustomerUser);
const businessRepo = new BusinessUserRepo(models.BusinessUser);
const controller = new GetCustomerAndBusinessUser({ customerRepo, businessRepo });

export const handler = controller.executeImpl.bind(controller);