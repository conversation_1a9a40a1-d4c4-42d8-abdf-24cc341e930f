import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { Status } from '@shared/core/Status';

export namespace GetCustomerAndBusinessUserErrors {
  export class BusinessNotFound extends BaseError {
    public constructor(id: string) {
      super({
        message: `Business with id "${id}" not found.`,
        status: Status.NOT_FOUND,
      });
    }
  }
  export class CustomerNotFound extends BaseError {
    public constructor(id: string) {
      super({
        message: `Customer with id "${id}" not found.`,
        status: Status.NOT_FOUND,
      });
    }
  }
}

patch({ GetCustomerAndBusinessUserErrors });
