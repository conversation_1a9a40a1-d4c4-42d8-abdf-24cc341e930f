import { expect, beforeEach, it, describe } from 'vitest';
import { Request } from './GetCustomerAndBusinessUserDTOs';
import { CustomerUserRepoFake } from '../../repos/CustomerUserRepoFake';
import { CustomerUsersInDb } from '@shared/infra/database/sequelize/migrations/CustomerUsers.json';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { BusinessUserRepoFake } from '../../repos/BusinessUserRepoFake';
import { GetCustomerAndBusinessUser } from './GetCustomerAndBusinessUser';
import { dateFormat, nonExistingId } from '@shared/utils/test';
import {
  GetCustomerAndBusinessUserErrors,
} from './GetCustomerAndBusinessUserErrors';
import { BusinessUserRepo } from '../../repos/BusinessUserRepo';
import { rmTimestamps } from '@shared/utils/utils';

let customerRepo,
  businessRepo,
  getCustomerAndBusinessUser: GetCustomerAndBusinessUser;
beforeEach(() => {
  customerRepo = new CustomerUserRepoFake();
  businessRepo = new BusinessUserRepoFake();
  getCustomerAndBusinessUser = new GetCustomerAndBusinessUser({
    customerRepo,
    businessRepo,
  });
});

const businessInDb = BusinessUsersInDb[0];
const input: Request = {
  customerId: CustomerUsersInDb[0].id,
  businessId: businessInDb.id,
};

it(`gets a customer and business user`, async () => {
  const result = await getCustomerAndBusinessUser.executeImpl(input);

  const businessDto = BusinessUserRepo.mapDb2Dto(businessInDb);
  expect(result).toMatchObject({
    isSuccess: true,
    value: {
      customer: rmTimestamps(CustomerUsersInDb[0]),
      business: {
        ...businessDto,
        phoneVerification: {
          ...businessDto.phoneVerification,
          attempts: expect.arrayContaining([
            expect.stringMatching(dateFormat),
          ]),
          codeExpiresAt: expect.stringMatching(businessDto.phoneVerification.codeExpiresAt!.replace('.000', '')),
        },
      },
    },
  });
});

describe('returns failure when', () => {
  it(`business isn't found`, async () => {
    const result = await getCustomerAndBusinessUser.executeImpl({
      ...input,
      businessId: nonExistingId,
    });

    expect(result).toMatchObject({
      isFailure: true,
      errors: [
        new GetCustomerAndBusinessUserErrors.BusinessNotFound(nonExistingId).setField('businessId'),
      ],
    });
  });

  it(`customer isn't found`, async () => {
    const result = await getCustomerAndBusinessUser.executeImpl({
      ...input,
      customerId: nonExistingId,
    });

    expect(result).toMatchObject({
      isFailure: true,
      errors: [
        new GetCustomerAndBusinessUserErrors.CustomerNotFound(nonExistingId).setField('customerId'),
      ],
    });
  });
});