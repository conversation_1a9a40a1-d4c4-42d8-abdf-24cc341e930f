import { Request, Response } from './GetCustomerAndBusinessUserDTOs';
import { ICustomerUserRepo } from '../../repos/ICustomerUserRepo';
import { IBusinessUserRepo } from '../../repos/IBusinessUserRepo';
import { FunctionController2 } from '@shared/infra/Controllers';
import { Result2 } from '@shared/core/Result2';
import { BaseError } from '@shared/core/AppError';
import {
  GetCustomerAndBusinessUserErrors,
} from './GetCustomerAndBusinessUserErrors';

// This use case is used by CreateReservation use case in module reservation.
export class GetCustomerAndBusinessUser extends FunctionController2<
  Request,
  Response
> {
  private readonly customerRepo: ICustomerUserRepo;
  private readonly businessRepo: IBusinessUserRepo;

  public constructor(args: {
    customerRepo: ICustomerUserRepo;
    businessRepo: IBusinessUserRepo;
  }) {
    super();
    const { customerRepo, businessRepo } = args;
    this.customerRepo = customerRepo;
    this.businessRepo = businessRepo;
  }

  public async executeImpl(dto: Request) {
    const { customerId, businessId } = dto;

    const [businessOrNull, customerOrNull] = await Promise.all([
      this.businessRepo.get(businessId),
      this.customerRepo.get(customerId),
    ]);

    const errors: BaseError[] = []
    if (!businessOrNull)
      errors.push(new GetCustomerAndBusinessUserErrors.BusinessNotFound(businessId).setField('businessId'));
    if (!customerOrNull)
      errors.push(new GetCustomerAndBusinessUserErrors.CustomerNotFound(customerId).setField('customerId'));

    if (errors.length)
      return Result2.fail(errors);

    return Result2.ok({
      customer: customerOrNull!.toDto(),
      business: businessOrNull!.toDto(),
    });
  }
}
