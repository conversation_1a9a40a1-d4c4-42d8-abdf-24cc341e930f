import { BaseError } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';
import { Status } from '@shared/core/Status';
import { getLng, OptionalLng } from '@shared/utils/utils';

export namespace UpdateBusinessUserErrors {
  export class NotFound extends BaseError {
    public constructor(args: { id: string } & OptionalLng) {
      const { id, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.notFound(id),
        status: Status.NOT_FOUND,
      });
    }
  }
}

const trans = {
  en: {
    notFound(v: string) {
      return `Business user ${v} not found`;
    },
  },
  es: {
    notFound(v: string) {
      return `Usuario ${v} no encontrado`;
    },
  },
};

patch({ UpdateBusinessUserErrors });
