import { beforeEach, describe, expect, test } from 'vitest';
import { UpdateBusinessUser } from './UpdateBusinessUser';
import { BusinessUserRepoFake } from '../../repos/BusinessUserRepoFake';
import {
  expectControllerErrorField,
  orNull,
  pickLng,
} from '@shared/utils/test';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { IBusinessUserRepo } from 'modules/user/repos/IBusinessUserRepo';
import { getTimezone } from '@shared/core/test';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { Name } from '../../domain/Name';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

let repo: IBusinessUserRepo, updateBusinessUser: UpdateBusinessUser, contextProvider: ContextProvider;
beforeEach(() => {
  repo = new BusinessUserRepoFake();
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  updateBusinessUser = new UpdateBusinessUser({
    repo,
    contextProvider,
  });
});

const validData = {
  id: BusinessUsersInDb[0].id,
  firstName: orNull(chance.string({ alpha: true, numeric: true, length: 15 })),
  lastName: orNull(chance.string({ alpha: true, numeric: true, length: 15 })),
  userLng: pickLng(),
  lng: pickLng(),
  timezone: getTimezone(),
};

test(`succeeds`, async () => {
  const result = await updateBusinessUser.executeImpl({
    ...validData,
    lng: 'invalid', // We waive this field, it doesn't cause the request to fail
  });

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const { userLng, lng, ...rest } = validData;
  expect(result).toMatchObject({
    status: 200,
    result: expect.objectContaining({
      ...rest,
      lng: userLng,
    }),
  });
});

describe(`fails for an invalid`, () => {
  test.each([
    ['userLng', 'fr'],
    ['firstName', 'a'.repeat(Name.maxLength + 1)],
    ['lastName', 'a'.repeat(Name.maxLength + 1)],
    ['timezone', 'Europe/Cordoba'],
  ])(`%s`, async (errorField, value) => {
    const response = await updateBusinessUser.executeImpl({
      ...validData,
      [errorField]: value,
    });
    expectControllerErrorField({
      response,
      errorField,
      code: 400,
    });
  });
});
