import { Request, Response } from './UpdateBusinessUserDTOs';
import { Name } from '../../domain/Name';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { BaseError, formatErrors } from '@shared/core/AppError';
import { IBusinessUserRepo } from '../../repos/IBusinessUserRepo';
import { UpdateBusinessUserErrors } from './UpdateBusinessUserErrors';
import { TimeZone } from '@shared/core/TimeZone';
import { AppSyncController } from '@shared/infra/Controllers';
import { getLng } from '@shared/utils/utils';
import { IContextProvider } from '@shared/context/IContextProvider';
import { BusinessUser } from '../../domain/BusinessUser';
import { UserLng } from '../../domain/UserLng';

export class UpdateBusinessUser extends AppSyncController<Request, Response> {
  private readonly repo: IBusinessUserRepo;
  public constructor(args: {
    repo: IBusinessUserRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    
    const { errors, firstName, lastName, userLng, timezone } = validateReq({
      firstName: dto.firstName,
      lastName: dto.lastName,
      userLng: dto.userLng,
      timezone: dto.timezone,
      lng: dto.lng,
    });

    if (errors) return formatErrors(errors);

    const lng = getLng(dto.lng);
    const id = dto.id;

    const user = await this.repo.get(id);
    if (!user)
      return formatErrors([new UpdateBusinessUserErrors.NotFound({ id, lng })]);

    user.update({
      firstName,
      lastName,
      lng: userLng,
      timezone,
    });

    await this.repo.update(user);

    return { status: Status.OK, result: BusinessUser.forFrontend(user.toDto()) };
  }
}

// Exported for reuse in frontend
export function validateReq(args: {
  firstName: string | null;
  lastName: string | null;
  userLng: string;
  timezone: string;
  lng: string;
}): {
  errors: null;
  firstName: Name | null;
  lastName: Name | null;
  timezone: TimeZone;
  userLng: UserLng;
} | {
  errors: [BaseError, ...BaseError[]];
  firstName: null;
  lastName: null;
  timezone: null;
  userLng: null;
} {
  const {
    firstName: _firstName,
    lastName: _lastName,
    userLng: _userLng,
    lng: _lng,
    timezone: _timezone,
  } = args;

  const lng = getLng(_lng);

  const errors: BaseError[] = [];
  const userLngOrError = UserLng.create({ value: _userLng, lng });
  if (userLngOrError.isFailure)
    errors.push(userLngOrError.error!.setField('userLng'));

  let firstName: Name | null = null;
  if (_firstName) {
    const firstNameOrError = Name.create(_firstName);
    if (firstNameOrError.isFailure) {
      errors.push((firstNameOrError.error!).setField('firstName'));
    } else {
      firstName = firstNameOrError.value;
    }
  }

  let lastName: Name | null = null;
  if (_lastName) {
    const lastNameOrError = Name.create(_lastName);
    if (lastNameOrError.isFailure) {
      errors.push((lastNameOrError.error!).setField('lastName'));
    } else {
      lastName = lastNameOrError.value;
    }
  }

  const timezoneOrErrors = TimeZone.create(_timezone);
  if (timezoneOrErrors.isFailure)
    errors.push((timezoneOrErrors.error!).setField('timezone'));

  if (errors.length) {
    return {
      errors: errors as [BaseError, ...BaseError[]],
      firstName: null,
      lastName: null,
      timezone: null,
      userLng: null,
    };
  }

  const userLng = userLngOrError.value;
  const timezone = timezoneOrErrors.value;

  return {
    errors: null,
    firstName,
    lastName,
    timezone,
    userLng,
  };
}
