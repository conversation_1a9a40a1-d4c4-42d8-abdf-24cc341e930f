import { expect, beforeEach, it, vi } from 'vitest';
import { Request } from './GetBusinessUserDTOs';
import { BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { BusinessUserRepoFake } from '../../repos/BusinessUserRepoFake';
import { GetBusinessUser } from './GetBusinessUser';
import { GetBusinessUserFC } from '../getBusinessUserFC/GetBusinessUserFC';
import { ContextProvider } from '@shared/context/ContextProvider';
import { LambdaInvokerFake } from '@shared/infra/invocation/LambdaInvokerFake';
import { BusinessUserRepo } from '../../repos/BusinessUserRepo';
import { BusinessUser } from '../../domain/BusinessUser';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

let businessUserRepo: BusinessUserRepoFake, getBusinessUserFC, getBusinessUser: GetBusinessUser, contextProvider: ContextProvider;
beforeEach(() => {
  vi.clearAllMocks();
  businessUserRepo = new BusinessUserRepoFake();
  contextProvider = new ContextProvider({ invoker: new LambdaInvokerFake() });
  getBusinessUserFC = new GetBusinessUserFC(businessUserRepo);
  getBusinessUser = new GetBusinessUser({
    getBusinessUserFC: getBusinessUserFC.executeImpl,
    contextProvider,
  });
});

it(`relays request to, and response from, the function controller`, async () => {
  const validData: Request = {
    id: BusinessUsersInDb[0].id,
  };
  const returnedByFC = chance.bool() ? BusinessUserRepo.mapDb2Dto(BusinessUsersInDb[0]) : null;

  const getBusinessUserFC = new GetBusinessUserFC(businessUserRepo);
  vi.spyOn(getBusinessUserFC, 'executeImpl').mockImplementation(({ id }) => {
    expect(id).toBe(validData.id);
    return Promise.resolve(returnedByFC);
  });
  getBusinessUser = new GetBusinessUser({
    getBusinessUserFC: getBusinessUserFC.executeImpl,
    contextProvider,
  });

  const response = await getBusinessUser.executeImpl(validData);

  expect(response).toMatchObject({
    status: 200,
    result: returnedByFC? BusinessUser.forFrontend(returnedByFC) : null,
  });
});
