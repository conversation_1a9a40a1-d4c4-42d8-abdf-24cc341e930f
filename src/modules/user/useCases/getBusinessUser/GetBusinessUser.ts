import { Request, Response } from './GetBusinessUserDTOs';
import { Status } from '@shared/core/Status';
import { ControllerResult } from '@shared/core/ControllerResult';
import { AppSyncController, FCHandler } from '@shared/infra/Controllers';
import {
  Request as GetBusinessUserFCReq,
  Response as GetBusinessUserFCRes,
} from '../getBusinessUserFC/GetBusinessUserFCDTOs';
import { IContextProvider } from '@shared/context/IContextProvider';
import { BusinessUser } from '../../domain/BusinessUser';

const { OK } = Status;

export class GetBusinessUser extends AppSyncController<Request, Response> {
  private readonly getBusinessUserFC: FCHandler<GetBusinessUserFCReq, GetBusinessUserFCRes>;

  public constructor(args: {
    getBusinessUserFC: FCHandler<GetBusinessUserFCReq, GetBusinessUserFCRes>;
    contextProvider: IContextProvider;
  }) {
    const { getBusinessUserFC, contextProvider } = args;
    super({ contextProvider });
    this.getBusinessUserFC = getBusinessUserFC;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    const { id } = dto;

    const userDtoOrNull = await this.getBusinessUserFC({ id });
    if (!userDtoOrNull) return { status: OK, result: null };

    return { status: OK, result: BusinessUser.forFrontend(userDtoOrNull) };
  }
}
