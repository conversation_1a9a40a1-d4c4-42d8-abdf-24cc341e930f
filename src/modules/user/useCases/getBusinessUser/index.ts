import { GetBusinessUser } from './GetBusinessUser';
import { ReturnUnexpectedError } from '@shared/decorators/ReturnUnexpectedError';
import { GuardUuid } from '@shared/decorators/GuardUuid';
import { handler as getBusinessUserFChandler } from '../getBusinessUserFC';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';

const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });

const controller = new GetBusinessUser({
  getBusinessUserFC: getBusinessUserFChandler,
  contextProvider,
});

const decorated1 = new GuardUuid({ controller, uuids: ['id'] });
const decorated2 = new ReturnUnexpectedError({
  wrapee: decorated1,
  contextProvider: new ContextProvider({ invoker }),
});
export const handler = decorated2.execute.bind(decorated2);
