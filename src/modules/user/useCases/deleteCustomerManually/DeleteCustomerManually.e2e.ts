import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { createCustomerManually } from '../../utils/test';
import { ManuallyCreatedCustomerRepo } from '../../repos/ManuallyCreatedCustomerRepo';
import { dateFormat } from '@shared/utils/test';
import { Request } from './DeleteCustomerManuallyDTOs';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');

const appsync = new AppSyncClient();
const repo = new ManuallyCreatedCustomerRepo(models.ManuallyCreatedCustomer);

const query = gql`
  mutation ($createdBy: ID!, $id: ID!) {
    deleteCustomerManually(createdBy: $createdBy, id: $id) {
      result
      time
    }
  }
`;

it(`deletes a customer`, async () => {
  // First create a customer
  const { customer } = createCustomerManually();
  await repo.create(customer);

  const input = {
    createdBy: customer.createdBy,
    id: customer.id.toString(),
  };

  const received = await appsync.send<Request>({
    query,
    variables: input,
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data.deleteCustomerManually)
    console.log('DeleteCustomerManually.e2e.ts', json);
  const response = json.data.deleteCustomerManually;
  expect(response).toEqual({
    result: 1,
    time: expect.stringMatching(dateFormat),
  });

  const saved = await repo.get({
    id: customer.id.toString(),
    createdBy: customer.createdBy,
  });
  expect(saved).toBe(null);
});
