import { Request, Response } from './DeleteCustomerManuallyDTOs';
import { ControllerResult } from '@shared/core/ControllerResult';
import { Status } from '@shared/core/Status';
import { IManuallyCreatedCustomerRepo } from '../../repos/IManuallyCreatedCustomerRepo';
import { AppSyncController } from '@shared/infra/Controllers';
import { IContextProvider } from '@shared/context/IContextProvider';

export class DeleteCustomerManually extends AppSyncController<Request, Response> {
  private readonly repo: IManuallyCreatedCustomerRepo;
  public constructor(args: {
    repo: IManuallyCreatedCustomerRepo;
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(dto: Request): ControllerResult<Response> {
    return {
      status: Status.OK,
      result: await this.repo.delete(dto),
    };
  }
}
