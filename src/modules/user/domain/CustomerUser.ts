import { AggregateRoot } from '@shared/core/domain/AggregateRoot';
import { EntityID } from '@shared/core/domain/EntityID';
import { Email } from './Email';
import { Name } from './Name';
import { CustomerUserCreatedEvent } from './events/CustomerUserCreatedEvent';
import { Spread } from '@shared/utils/utils';
import { TimeZone } from '@shared/core/TimeZone';
import { Phone } from './Phone';

type CustomerUserInput = {
  firstName: Name;
  lastName: Name;
  email: Email;
  timezone: TimeZone; // default timezone during registration, frontend sends whatever the browser timezone is.
};

export type CustomerUserProps = CustomerUserInput & {
  phone: Phone | null; // Only email is asked for account creation, so at first we don't have their phone
};

export type CustomerUserDto = Spread<
  CustomerUserProps,
  {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    timezone: string;
    phone: string | null;
  }
>;

export class CustomerUser extends AggregateRoot<
  CustomerUserProps,
  CustomerUserDto
> {
  private __class = this.constructor.name;

  get firstName(): Name {
    return this.props.firstName;
  }

  get lastName(): Name {
    return this.props.lastName;
  }

  get email(): Email {
    return this.props.email;
  }

  get phone(): Phone | null {
    return this.props.phone;
  }

  get timezone(): TimeZone {
    return this.props.timezone;
  }

  private constructor(props: CustomerUserProps, id?: EntityID) {
    super(props, id);
  }

  public static create(props: CustomerUserInput): CustomerUser {
    const user = new CustomerUser({
      ...props,
      phone: null,
    });
    user.addDomainEvent(new CustomerUserCreatedEvent(user.toDto()));
    return user;
  }

  public changePhone(phone: Phone): void {
    this.props.phone = phone;
  }

  public toDto(): CustomerUserDto {
    const { firstName, lastName, email, timezone, phone, ...rest } = this.props;

    return {
      ...rest,
      id: this.id.toString(),
      firstName: firstName.value,
      lastName: lastName.value,
      email: email.value,
      timezone: timezone.value,
      phone: phone?.value || null,
    };
  }

  public static assemble(dto: CustomerUserDto): CustomerUser {
    const { id, firstName, lastName, email, timezone, phone, ...rest } = dto;
    return new CustomerUser(
      {
        ...rest,
        firstName: Name.create(firstName).value,
        lastName: Name.create(lastName).value,
        email: Email.create(email).value,
        timezone: TimeZone.create(timezone).value,
        phone: phone ? Phone.create({ value: phone }).value : null,
      },
      new EntityID(id),
    );
  }
}
