import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { NotesErrors } from './NotesErrors';

type NotesDto = string | null;

interface NotesProps {
  value: NotesDto;
}

export class Notes extends ValueObject<NotesProps, NotesDto> {
  private __class = this.constructor.name;
  public static maxLength = 200;
  public static minLength = 1;

  get value(): string | null {
    return this.props.value;
  }

  private constructor(props: NotesProps) {
    super(props);
  }

  public static create(value: string): Result<Notes> {
    const trimmed = value.trim();
    const trimmed2 = trimmed.replace(/\s+/g, ' '); // replace duplicate spaces by just one
    return Result.convertValue(trimmed2)
      .ensure((value: string) => {
        return value.length >= this.minLength;
      }, new NotesErrors.TooShort(this.minLength))
      .ensure((value: string) => {
        return value.length <= this.maxLength;
      }, new NotesErrors.TooLong({ maxLength: this.maxLength, entered: trimmed2.length }))
      .onBoth((result) =>
        result.isSuccess
          ? Result.ok<Notes>(new Notes({ value: result.value }))
          : Result.fail(result.error!),
      );
  }

  public toDto(): NotesDto {
    return this.value;
  }
}
