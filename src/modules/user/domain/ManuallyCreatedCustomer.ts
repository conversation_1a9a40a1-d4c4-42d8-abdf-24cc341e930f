import { AggregateRoot } from '@shared/core/domain/AggregateRoot';
import { EntityID } from '@shared/core/domain/EntityID';
import { Name } from './Name';
import { Spread } from '@shared/utils/utils';
import { CustomerManuallyCreatedEvent } from './events/CustomerManuallyCreatedEvent';
import { Notes } from './Notes';
import { ManuallyCreatedCustomerErrors } from './ManuallyCreatedCustomerErrors';
import { Result2 } from '@shared/core/Result2';
import { BaseError } from '@shared/core/AppError';

type ManuallyCreatedCustomerUpdateInput = {
  firstName: Name | null;
  lastName: Name | null;
  notes: Notes | null;
};

type ManuallyCreatedCustomerCreateInput = ManuallyCreatedCustomerUpdateInput & {
  createdBy: string;
};

export type ManuallyCreatedCustomerProps = ManuallyCreatedCustomerCreateInput & {
  fullName: string;
};

export type ManuallyCreatedCustomerDto = Spread<
  ManuallyCreatedCustomerProps,
  {
    id: string;
    firstName: string | null;
    lastName: string | null;
    notes: string | null;
  }
>;

export class ManuallyCreatedCustomer extends AggregateRoot<
  ManuallyCreatedCustomerProps,
  ManuallyCreatedCustomerDto
> {
  private __class = this.constructor.name;

  get firstName(): Name | null {
    return this.props.firstName;
  }

  get lastName(): Name | null {
    return this.props.lastName;
  }

  get fullName(): string {
    return this.props.fullName;
  }

  get notes(): Notes | null {
    return this.props.notes;
  }

  get createdBy(): string {
    return this.props.createdBy;
  }

  private constructor(props: ManuallyCreatedCustomerProps, id?: EntityID) {
    super(props, id);
  }

  public static create(
    input: ManuallyCreatedCustomerCreateInput,
  ): Result2<ManuallyCreatedCustomer> {

    const errors = ManuallyCreatedCustomer.checkNoNames(input);
    if (errors.length) return Result2.fail(errors);

    const customer = new ManuallyCreatedCustomer({
      ...input,
      fullName: ManuallyCreatedCustomer.getFullName({
        firstName: input.firstName,
        lastName: input.lastName,
      }),
    });
    customer.addDomainEvent(new CustomerManuallyCreatedEvent(customer.toDto()));
    return Result2.ok(customer);
  }

  public update(
    input: ManuallyCreatedCustomerUpdateInput,
  ): Result2<ManuallyCreatedCustomer> {

    const errors = ManuallyCreatedCustomer.checkNoNames(input);
    if (errors.length) return Result2.fail(errors);

    return Result2.ok(
      new ManuallyCreatedCustomer(
        {
          ...this.props,
          ...input,
          fullName: ManuallyCreatedCustomer.getFullName({
            firstName: input.firstName,
            lastName: input.lastName,
          }),
        },
        this.id,
      ),
    );
  }

  private static getFullName(args: { firstName: Name | null, lastName: Name | null }): string {
    const { firstName, lastName } = args;
    let fullName;
    if (firstName)  {
      fullName = firstName.value;
      if (lastName) fullName += ` ${lastName.value}`;
    } else {
      fullName = lastName!.value
    }
    return fullName;
  }

  private static checkNoNames(input: ManuallyCreatedCustomerUpdateInput): BaseError[] {
    const errors: BaseError[] = [];

    if (!input.firstName && !input.lastName) {
      const error = new ManuallyCreatedCustomerErrors.NoNames();
      errors.push(
        error.setField('firstName'),
        error.clone('lastName'),
      );
    }
    return errors;
  }

  public toDto(): ManuallyCreatedCustomerDto {
    const { firstName, lastName, notes, ...rest } = this.props;

    return {
      ...rest,
      id: this.id.toString(),
      firstName: firstName? firstName.value : null,
      lastName: lastName? lastName.value : null,
      notes: notes ? notes.value : null,
    };
  }

  public static assemble(
    dto: ManuallyCreatedCustomerDto,
  ): ManuallyCreatedCustomer {
    const { id, firstName, lastName, notes, ...rest } = dto;
    return new ManuallyCreatedCustomer(
      {
        ...rest,
        firstName: firstName === null? null : Name.create(firstName).value,
        lastName: lastName === null? null : Name.create(lastName).value,
        notes: notes === null? null : Notes.create(notes).value,
      },
      new EntityID(id),
    );
  }
}
