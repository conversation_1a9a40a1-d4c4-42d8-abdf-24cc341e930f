import { expect, test, describe } from 'vitest';
import { UserLng } from './UserLng';
import { possibleLngs } from '@shared/utils/utils';
import { expectErrorResult } from '@shared/utils/test';

describe('UserLng', () => {
  test('all possible languages are valid', () => {
    possibleLngs.forEach(lng => {
      const result = UserLng.create({ value: lng });
      expect(result.isSuccess).toBe(true);
      expect(result.value.value).toBe(lng);
    });
  });

  test('creation with valid language with whitespace', () => {
    const result = UserLng.create({ value: '  en  ' });

    expect(result.isSuccess).toBe(true);
    expect(result.value.value).toBe('en');
  });

  test('creation with invalid language', () => {
    const result = UserLng.create({ value: 'fr' });

    expectErrorResult({
      result,
      error: 'UserLngErrors.InvalidLanguage',
      code: 400,
    });
  });

  test('creation with empty string', () => {
    const result = UserLng.create({ value: '' });

    expectErrorResult({
      result,
      error: 'UserLngErrors.InvalidLanguage',
      code: 400,
    });
  });

  test('creation with invalid language (case sensitive)', () => {
    const result = UserLng.create({ value: 'EN' });

    expectErrorResult({
      result,
      error: 'UserLngErrors.InvalidLanguage',
      code: 400,
    });
  });

  test('toDto returns the language value', () => {
    const lng = UserLng.create({ value: 'es' }).value;

    expect(lng.toDto()).toBe('es');
  });

  test('default returns English', () => {
    const defaultLng = UserLng.default();

    expect(defaultLng.value).toBe('en');
  });

  test('equality works correctly', () => {
    const lng1 = UserLng.create({ value: 'en' }).value;
    const lng2 = UserLng.create({ value: 'en' }).value;
    const lng3 = UserLng.create({ value: 'es' }).value;

    expect(lng1.equals(lng2)).toBe(true);
    expect(lng1.equals(lng3)).toBe(false);
  });
});
