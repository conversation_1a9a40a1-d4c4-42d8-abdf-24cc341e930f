import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { PossibleLngs } from '@shared/utils/utils';

export namespace PhoneErrors {
  export class NotValid extends BadRequest {
    public constructor(args: { error: string, lng: PossibleLngs }) {
      const { error, lng } = args;
      const t = trans[lng];
      super({
        message: `${t.notValid}: ${error}`,
      });
    }
  }
}

patch({ PhoneErrors });

const trans = {
  en: {
    notValid: `Phone isn't valid`,
  },
  es: {
    notValid: 'El teléfono no es válido',
  },
};
