import { expect, test } from 'vitest';
import { Name } from './Name';
import { CustomerUser } from './CustomerUser';
import { Email } from './Email';
import { orNull } from '@shared/utils/test';
import { TimeZone } from '@shared/core/TimeZone';
import { getTimezone } from '@shared/core/test';
import { Phone } from './Phone';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

export function getInputs() {
  const dto = {
    firstName: chance.string({ alpha: true, numeric: true, length: 15 }),
    lastName: chance.string({ alpha: true, numeric: true, length: 15 }),
    email: chance.email(),
    timezone: getTimezone(),
  };

  const args = {
    firstName: Name.create(dto.firstName).value,
    lastName: Name.create(dto.lastName).value,
    email: Email.create(dto.email).value,
    timezone: TimeZone.create(dto.timezone).value,
  };

  return { dto, args };
}

test(`create and assemble with phone null`, () => {
  const { dto, args } = getInputs();

  const defaultProps = {
    phone: null,
  };

  const created = CustomerUser.create(args);

  expect(created.toDto()).toMatchObject({
    ...dto,
    ...defaultProps,
    id: created.id.toString(),
  });

  const assembled = CustomerUser.assemble({
    id: created.id.toString(),
    ...dto,
    ...defaultProps,
  });

  expect(assembled.equals(created)).toBe(true);
});

test(`change phone`, () => {
  const { args } = getInputs();
  const created = CustomerUser.create(args);
  const phoneDto = '+12125551234';
  const phone = Phone.create({ value: phoneDto }).value;
  created.changePhone(phone);
  expect(created.toDto()).toMatchObject({
    phone: phone.value,
  });
});

test(`assemble from db possibly with phone`, () => {
  const { dto } = getInputs();
  const inDB = {
    ...dto,
    id: chance.guid({ version: 4 }),
    phone: orNull('+12125551234'),
  };

  const assembled = CustomerUser.assemble(inDB);

  expect(assembled.toDto()).toMatchObject(inDB);
});
