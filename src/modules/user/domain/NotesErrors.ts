import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';

export namespace NotesErrors {
  export class TooShort extends BadRequest {
    public constructor(minLength: number) {
      super({ message: `Los comentarios deben tener al menos ${minLength} ${minLength > 1 ? 'caracteres' : 'carácter'}.` });
    }
  }

  export class TooLong extends BadRequest {
    public constructor(args: {
      maxLength: number;
      entered: number;
    }) {
      const { maxLength, entered } = args;
      super({ message: `Los comentarios no pueden ser más de ${maxLength} caracteres, mientras que fueron ingresados ${entered}.` });
    }
  }
}

patch({ NotesErrors });
