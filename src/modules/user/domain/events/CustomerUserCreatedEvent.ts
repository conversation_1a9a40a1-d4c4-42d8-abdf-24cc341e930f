import { DomainEventTypes } from '@shared/events/DomainEventTypes';
import { CustomerUserDto } from '../CustomerUser';
import { DomainEventBase } from '@shared/events/DomainEventBase';

export class CustomerUserCreatedEvent extends DomainEventBase {
  public user;

  public constructor(user: CustomerUserDto) {
    super({
      aggregateId: user.id,
      type: DomainEventTypes.CustomerUserCreatedEvent,
    });
    this.user = user;
  }
}
