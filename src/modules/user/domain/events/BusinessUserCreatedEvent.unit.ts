import { expect, test } from 'vitest';
import { createBusinessUser } from '../../utils/test';

test(`BusinessUserCreatedEvent is added to user during creation`, () => {
  const { user } = createBusinessUser();

  expect(user.domainEvents.length).toBe(1);
  const domainEvent = user.domainEvents[0];
  expect(domainEvent.type).toBe('BusinessUserCreatedEvent');
  expect(user.id.toString()).toBe(domainEvent.aggregateId);
});
