import { DomainEventTypes } from '@shared/events/DomainEventTypes';
import { BusinessUserDto } from '../BusinessUser';
import {
  DomainEventBase,
  DomainEventBaseDto,
} from '@shared/events/DomainEventBase';

export type BusinessUserCreatedEventDto = DomainEventBaseDto & {
  user: BusinessUserDto;
}

export class BusinessUserCreatedEvent extends DomainEventBase {
  public user;

  public constructor(user: BusinessUserDto) {
    super({
      aggregateId: user.id,
      type: DomainEventTypes.BusinessUserCreatedEvent,
    });
    this.user = user;
  }

  public toDto() {
    return {
      ...this._toDto(),
      user: this.user,
    };
  }
}
