import { expect, test } from 'vitest';
import { createCustomerUser } from '../../utils/test';

test(`CustomerUserCreatedEvent is added to user during creation`, () => {
  const { user } = createCustomerUser();

  expect(user.domainEvents.length).toBe(1);
  const domainEvent = user.domainEvents[0];
  expect(domainEvent.type).toBe('CustomerUserCreatedEvent');
  expect(user.id.toString()).toBe(domainEvent.aggregateId);
});
