import { DomainEventTypes } from '@shared/events/DomainEventTypes';
import { ManuallyCreatedCustomerDto } from '../ManuallyCreatedCustomer';
import { DomainEventBase } from '@shared/events/DomainEventBase';

export class CustomerManuallyCreatedEvent extends DomainEventBase {
  public customer;

  public constructor(customer: ManuallyCreatedCustomerDto) {
    super({
      aggregateId: customer.id,
      type: DomainEventTypes.CustomerManuallyCreatedEvent,
    });
    this.customer = customer;
  }
}
