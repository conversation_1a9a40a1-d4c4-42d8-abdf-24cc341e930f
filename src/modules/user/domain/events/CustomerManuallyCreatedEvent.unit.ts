import { expect, test } from 'vitest';
import { createCustomerManually } from '../../utils/test';

test(`CustomerUserCreatedEvent is added to user during creation`, () => {
  const { customer } = createCustomerManually();

  expect(customer.domainEvents.length).toBe(1);
  const domainEvent = customer.domainEvents[0];
  expect(domainEvent).toMatchObject({
    type: 'CustomerManuallyCreatedEvent',
    aggregateId: customer.id.toString(),
    customer: customer.toDto(),
  });
});
