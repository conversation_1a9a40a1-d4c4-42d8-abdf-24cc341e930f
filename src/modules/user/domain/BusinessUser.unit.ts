import { expect, test, describe, beforeEach, it } from 'vitest';
import { Name } from './Name';
import {
  BusinessUser, getEmailVerificationNull,
  getPhoneVerificationNull,
  VerificationStatus,
} from './BusinessUser';
import { Email } from './Email';
import { TimeZone } from '@shared/core/TimeZone';
import { expectErrorsResult, orNull, pickLng } from '@shared/utils/test';
import { getTimezone } from '@shared/core/test';
import { Phone } from './Phone';
import { Dat } from '@shared/core/Dat';
import { PossibleLngs, possibleLngs } from '@shared/utils/utils';
import { expectInAbout24hrs, expectInAboutAMinute, getPhone } from '../utils/test';
import { Code } from './Code';
import { DatSec } from '@shared/core/DatSec';
import { UserLng } from './UserLng';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

let timezone: TimeZone, newPhone: Phone, lng: PossibleLngs;

beforeEach(() => {
  timezone = getTimezone();
  newPhone = getPhone();
  lng = chance.pickone(possibleLngs);
});

export function getInputs() {
  const firstName: string | null = orNull(chance.string({ alpha: true, numeric: true, length: 15 }));
  const lastName: string | null = orNull(chance.string({ alpha: true, numeric: true, length: 15 }));
  const dto = {
    firstName,
    lastName,
    lng: pickLng(),
    email: chance.email(),
    timezone: getTimezone(),
  };

  const args = {
    firstName: firstName? Name.create(firstName).value : null,
    lastName: lastName? Name.create(lastName).value : null,
    lng: UserLng.create({ value: dto.lng }).value,
    email: Email.create(dto.email).value,
    timezone: TimeZone.create(dto.timezone).value,
  };

  return { dto, args };
}

const { dto, args } = getInputs();

test(`create & assemble back with phone null`, () => {
  const defaultProps = {
    firstName: null,
    lastName: null,
    phone: null,
    phoneVerification: getPhoneVerificationNull(),
    emailVerification: getEmailVerificationNull(),
  };

  const user = BusinessUser.create(args);

  expect(user.toDto()).toMatchObject({
    ...dto,
    ...defaultProps,
    id: user.id.toString(),
  });

   const assembled = BusinessUser.assemble({
    id: user.id.toString(),
    ...dto,
    ...defaultProps,
  });

  expect(assembled.equals(user)).toBe(true);
});

describe('assemble', () => {

  const validData = {
    ...dto,
    id: chance.guid({ version: 4 }),
    phone: orNull('+***********'), // Valid US number format
    phoneVerification: {
      status: chance.pickone(Object.values(VerificationStatus)),
      attempts: orNull([
        Dat.create().value.s,
        Dat.create().value.s,
      ]),
      blockedUntil: orNull(Dat.create().value.s),
      newPhone: orNull('+***********'),
      codeExpiresAt: orNull(DatSec.create().value.s),
      code: orNull(chance.integer({ min: 1000, max: 9999 })),
    },
    emailVerification: {
      status: chance.pickone(Object.values(VerificationStatus)),
      attempts: orNull([
        Dat.create().value.s,
        Dat.create().value.s,
      ]),
      blockedUntil: orNull(Dat.create().value.s),
      newEmail: orNull(chance.email()),
      codeExpiresAt: orNull(DatSec.create().value.s),
      code: orNull(chance.integer({ min: 1000, max: 9999 })),
    },
  };

  it(`succeeds`, () => {
    const assembled = BusinessUser.assemble(validData);

    expect(assembled.toDto()).toMatchObject(validData);
  });

  it(`throws when verification status is invalid`, () => {
    expect(() => BusinessUser.assemble({
      ...validData,
      phoneVerification: {
        ...validData.phoneVerification,
        status: 'INVALID_STATUS',
      },
    })).toThrow();
  });

});

test(`update`, () => {
  const user = BusinessUser.create(args);

  const firstName: string | null = orNull(chance.string());
  const lastName: string | null = orNull(chance.string());
  const lng = pickLng();
  const timezone = getTimezone();
  const updateDto = {
    firstName,
    lastName,
    timezone,
    lng,
  };
  const updateArg = {
    firstName: firstName? Name.create(firstName).value : null,
    lastName: lastName? Name.create(lastName).value : null,
    timezone: TimeZone.create(timezone).value,
    lng: UserLng.create({ value: lng }).value,
  };
  user.update(updateArg)
  expect(user.toDto()).toMatchObject(updateDto);
});

describe('addPhoneVerificationAttempt',  () => {

  test(`when phoneVerification.blockedUntil is set and not expired`, () => {
    const user = BusinessUser.create(args);
    const initialStatus = user.props.phoneVerification.status;

    // Set blocked 1 hour in the future
    const blockedUntil = Dat.create({ value: new Date().getTime() + 60 * 60 * 1000 }).value;
    user.props.phoneVerification = {
      ...getPhoneVerificationNull(),
      blockedUntil,
    };

    const result = user.addPhoneVerificationAttempt({ lng, timezone, newPhone });

    expectErrorsResult({
      result,
      error: 'BusinessUserErrors.NoAttemptsLeft',
      code: 400,
    });
    expect(user.props.phoneVerification.status).toBe(initialStatus);
    expect(user.props.phoneVerification.attempts).toBeNull();
  });

  test(`when phoneVerification.blockedUntil is set, but expired, and there were no attempts in the last 24 hours`, () => {
    const user = BusinessUser.create(args);

    // Set blocked 1 hour in the past, so block is expired
    const blockedUntil = Dat.create({ value: new Date().getTime() - 60 * 60 * 1000 }).value;
    const now = new Date().getTime();

    // Create an attempt 25 hrs ago
    const attempt = Dat.create({ value: now - 25 * 60 * 60 * 1000 }).value; // 25 hours ago

    // Set the attempts in the user object
    user.props.phoneVerification = {
      ...getPhoneVerificationNull(),
      attempts: [attempt],
      blockedUntil: blockedUntil,
    };

    const result = user.addPhoneVerificationAttempt({ lng, timezone, newPhone });

    expect(result).toMatchObject({
      isSuccess: true,
      value: {
        code: user.props.phoneVerification.code!,
        attemptsLeft: BusinessUser.MAX_VERIFICATION_ATTEMPTS_IN_24_HOURS - 1,
      },
    });
    expect(user.props.phoneVerification).toMatchObject({
      status: VerificationStatus.STARTED,
      attempts: [expect.anything(), expect.anything()],
      blockedUntil, // keeps the existing blockedUntil
    });
    expectVerificationData({ user, newPhone });
  });

  test(`when there were attempts in the last 24 hours`, () => {
    const user = BusinessUser.create(args);

    // Set blocked 1 hour in the past, so it's expired
    const blockedUntil = Dat.create({ value: new Date().getTime() - 60 * 60 * 1000 }).value;
    const now = new Date().getTime();

    // Create an attempt from 25 hrs ago
    const attempt1 = Dat.create({ value: now - 25 * 60 * 60 * 1000 }).value; // 25 hours ago
    // Create an attempt from 23 hrs ago
    const attempt2 = Dat.create({ value: now - 23 * 60 * 60 * 1000 }).value; // 23 hours ago

    // Set the attempts in the user object
    user.props.phoneVerification = {
      ...getPhoneVerificationNull(),
      attempts: [attempt1, attempt2],
      blockedUntil,
    };

    const result = user.addPhoneVerificationAttempt({ lng, timezone, newPhone });

    expect(result).toMatchObject({
      isSuccess: true,
      value: {
        code: user.props.phoneVerification.code!,
        attemptsLeft: BusinessUser.MAX_VERIFICATION_ATTEMPTS_IN_24_HOURS - 2,
      },
    });
    expect(user.props.phoneVerification).toMatchObject({
      status: VerificationStatus.STARTED,
      attempts: [expect.anything(), expect.anything(), expect.anything()],
      blockedUntil, // keeps the existing blockedUntil
    });
    expectVerificationData({ user, newPhone });
  });

  test(`when the attempt exhausts the max attempts in the last 24 hours`, () => {
    const user = BusinessUser.create(args);

    // Create 2 attempts
    user.addPhoneVerificationAttempt({ lng, timezone, newPhone: getPhone() });
    user.addPhoneVerificationAttempt({ lng, timezone, newPhone: getPhone() });

    const result = user.addPhoneVerificationAttempt({ lng, timezone, newPhone });

    expect(result).toMatchObject({
      isSuccess: true,
      value: {
        code: user.props.phoneVerification.code!,
        attemptsLeft: 0,
      },
    });
    expect(user.props.phoneVerification).toMatchObject({
      status: VerificationStatus.STARTED,
      attempts: null,
    });
    expectInAbout24hrs(user.props.phoneVerification.blockedUntil!.t);
    expectVerificationData({ user, newPhone });
  });

  test(`when phoneVerification has null data`, () => {
    const user = BusinessUser.create(args);

    const result = user.addPhoneVerificationAttempt({ lng, timezone, newPhone });

    expect(result).toMatchObject({
      isSuccess: true,
      value: {
        code: user.props.phoneVerification.code!,
        attemptsLeft: BusinessUser.MAX_VERIFICATION_ATTEMPTS_IN_24_HOURS - 1,
      },
    });
    expect(user.props.phoneVerification).toMatchObject({
      status: VerificationStatus.STARTED,
      attempts: [expect.anything()],
      blockedUntil: null,
    });
    expectVerificationData({ user, newPhone });
  });

  test(`when all verification attempts are used, it holds last valid attempted phone`, () => {
    const user = BusinessUser.create(args);

    // Create 3 recent attempts
    user.addPhoneVerificationAttempt({ lng, timezone, newPhone: getPhone() });
    user.addPhoneVerificationAttempt({ lng, timezone, newPhone: getPhone() });
    user.addPhoneVerificationAttempt({ lng, timezone, newPhone });

    const result = user.addPhoneVerificationAttempt({ lng, timezone, newPhone: getPhone() });

    expectErrorsResult({
      result,
      error: 'BusinessUserErrors.NoAttemptsLeft',
      code: 400,
    });
    expect(user.props.phoneVerification).toMatchObject({
      status: VerificationStatus.STARTED,
      attempts: null,
    });
    expectInAbout24hrs(user.props.phoneVerification.blockedUntil!.t);
    expectVerificationData({ user, newPhone });
  });
})

describe('confirmPhoneCode', () => {

  test('succeeds with valid code', () => {
    const user = BusinessUser.create(args);
    user.addPhoneVerificationAttempt({ lng, timezone, newPhone });

    const result = user.confirmPhoneCode({ code: user.phoneVerification.code!, lng });

    expect(result.isSuccess).toBe(true);
    expect(user.phone?.value).toBe(newPhone.value);
    expect(user.props.phoneVerification.status).toBe(VerificationStatus.SUCCESS);
  });

  test('fails when no verification is in progress', () => {
    const user = BusinessUser.create(args);

    const result = user.confirmPhoneCode({ code: Code.getRandom(), lng });

    expectErrorsResult({
      result,
      error: 'BusinessUserErrors.NoVerificationInProgress',
      code: 400,
    });
  });

  test('throws when verification is in progress but code and new phone is missing', () => {
    const user = BusinessUser.create(args);
    user.props.phoneVerification.status = VerificationStatus.STARTED;

    expect(() => user.confirmPhoneCode({ code: Code.getRandom(), lng })).toThrow();
  });

  test('fails when code expired', () => {
    const user = BusinessUser.create(args);
    user.addPhoneVerificationAttempt({ lng, timezone, newPhone });
    // Set code expiration to the past
    const pastDate = new Date();
    pastDate.setMinutes(pastDate.getMinutes() - 1); // 1 minute in the past
    user.props.phoneVerification.codeExpiresAt = DatSec.create({ value: pastDate }).value;

    const result = user.confirmPhoneCode({ code: Code.getRandom(), lng });

    expectErrorsResult({
      result,
      error: 'BusinessUserErrors.CodeExpired',
      code: 400,
    });
    expect(user.phoneVerification.status).toBe(VerificationStatus.CODE_EXPIRED);
  });

  test('fails with invalid code', () => {
    const user = BusinessUser.create(args);
    user.addPhoneVerificationAttempt({ lng, timezone, newPhone });

    const result = user.confirmPhoneCode({ code: Code.getRandom(), lng });

    expectErrorsResult({
      result,
      error: 'BusinessUserErrors.InvalidCode',
      code: 400,
    });
    expect(user.phoneVerification.status).toBe(VerificationStatus.INVALID_CODE);
  });
})

function expectCodeWithinRange(code: Code) {
  expect(code.value).toBeGreaterThanOrEqual(1000);
  expect(code.value).toBeLessThanOrEqual(9999);
}

function expectVerificationData(args: { user: BusinessUser, newPhone: Phone}) {
  const { user: { props: { phoneVerification } }, newPhone } = args;
  expect(phoneVerification.status).toBe(VerificationStatus.STARTED);
  expect(phoneVerification.newPhone!.value).toBe(newPhone.value);
  expectCodeWithinRange(phoneVerification.code!);
  expect(dateFormatSec.test(phoneVerification.codeExpiresAt!.s)).toBe(true);
  expectInAboutAMinute(phoneVerification.codeExpiresAt!.t);
}

const dateFormatSec = new RegExp(
  `[0-9]{4}-[0-1][0-9]-[0-3][0-9][Tt][0-2][0-9]:[0-5][0-9]:[0-5][0-9][Zz]`,
);