import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { UserLngErrors } from './UserLngErrors';
import { PossibleLngs, possibleLngs, getLng, OptionalLng } from '@shared/utils/utils';

type UserLngDto = string;

interface UserLngProps {
  value: PossibleLngs;
}

export class UserLng extends ValueObject<UserLngProps, UserLngDto> {
  private __class = this.constructor.name;

  get value(): PossibleLngs {
    return this.props.value;
  }

  private constructor(props: UserLngProps) {
    super(props);
  }

  public static create(args: { value: string } & OptionalLng): Result<UserLng> {
    const { value, lng: _lng } = args;

    const trimmed = value.trim();
    
    // Check if the value is one of the possible languages
    if (!possibleLngs.includes(trimmed as PossibleLngs)) {
      return Result.fail(new UserLngErrors.InvalidLanguage({ lng: getLng(_lng) }));
    }

    return Result.ok<UserLng>(new UserLng({ value: trimmed as PossibleLngs }));
  }

  public toDto(): UserLngDto {
    return this.props.value;
  }

  public static default(): UserLng {
    return UserLng.create({ value: 'en' }).value;
  }
}
