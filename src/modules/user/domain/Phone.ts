import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { PhoneErrors } from './PhoneErrors';
import { parsePhoneNumberWithError } from 'libphonenumber-js'
import { getLng, OptionalLng } from '@shared/utils/utils';

type PhoneDto = string;

interface PhoneProps {
  value: PhoneDto;
}

export class Phone extends ValueObject<PhoneProps, PhoneDto> {
  private __class = this.constructor.name;

  get value(): string {
    return this.props.value;
  }

  private constructor(props: PhoneProps) {
    super(props);
  }

  public static create(args: { value: string } & OptionalLng): Result<Phone> {
    const { value, lng: _lng } = args;
    const lng = getLng(_lng);
    try {
      const phoneNumber = parsePhoneNumberWithError(value);
      return Result.ok(new Phone({ value: phoneNumber.number }));
    } catch (error) {
      return Result.fail(new PhoneErrors.NotValid({
        // @ts-expect-error opt out ts
        error: error.message,
        lng,
      }));
    }
  }

  public toDto(): PhoneDto {
    return this.props.value;
  }
}
