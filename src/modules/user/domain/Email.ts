import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { EmailErrors } from './EmailErrors';

type EmailDto = string;

interface EmailProps {
  value: EmailDto;
}

export class Email extends ValueObject<EmailProps, EmailDto> {
  private __class = this.constructor.name;
  public static maxLength = 70;

  get value(): string {
    return this.props.value;
  }

  private constructor(props: EmailProps) {
    super(props);
  }

  public static create(email: string): Result<Email> {
    const trimmed = email.trim();
    const validEmail = /^\w+([.-\\+]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/.test(
      trimmed,
    );
    if (!validEmail) return Result.fail(new EmailErrors.NotValid());

    return Result.convertValue(trimmed)
      .ensure((value: string) => {
        return value.length <= this.maxLength;
      }, new EmailErrors.TooLong(this.maxLength))
      .onBoth((result) =>
        result.isSuccess
          ? Result.ok<Email>(new Email({ value: result.value }))
          : Result.fail(result.error!),
      );
  }

  public toDto(): EmailDto {
    return this.value;
  }
}
