import { expect, test } from 'vitest';
import { Notes } from './Notes';

test(`Creation`, () => {
  const result = Notes.create(' test  - notes ');
  expect(result.isSuccess).toBe(true);
  const username = result.value;
  expect(username.value).toBe('test - notes');
});

test(`Creation fails with a short string`, () => {
  const result = Notes.create('');

  expect(result).toMatchObject({
    isFailure: true,
    error: {
      type: 'NotesErrors.TooShort',
      message: expect.any(String),
      status: 400,
    },
  });
});

test(`Creation fails with a long string`, () => {
  const tooLong = 'abcdefghijklmnopqrstuvwxyz-abcdefghijklmnopqrstuvwxyz-abcdefghijklmnopqrstuvwxyz-abcdefghijklmnopqrstuvwxyz-abcdefghijklmnopqrstuvwxyz-abcdefghijklmnopqrstuvwxyz-abcdefghijklmnopqrstuvwxyz-abcdefghijkl';
  const result = Notes.create(
    tooLong,
  );

  expect(result).toMatchObject({
    isFailure: true,
    error: {
      type: 'NotesErrors.TooLong',
      message: expect.any(String),
      status: 400,
    },
  });
});
