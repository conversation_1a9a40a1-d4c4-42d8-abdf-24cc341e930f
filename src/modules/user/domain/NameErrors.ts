import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';

export namespace NameErrors {
  export class TooShort extends BadRequest {
    public constructor(minLength: number) {
      super({ message: `Debe tener al menos ${minLength} ${minLength > 1 ? 'caracteres' : 'carácter'}.` });
    }
  }

  export class Too<PERSON>ong extends BadRequest {
    public constructor(args: {
      maxLength: number;
      entered: number;
    }) {
      const { maxLength, entered } = args;
      super({ message: `No puede tener más de ${maxLength} caracteres, mientras que fueron ingresados ${entered}.` });
    }
  }
}

patch({ NameErrors });
