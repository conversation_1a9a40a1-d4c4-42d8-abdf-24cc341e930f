import { expect, test, describe, it } from 'vitest';
import { Name } from './Name';
import { ManuallyCreatedCustomer } from './ManuallyCreatedCustomer';
import { orNull } from '@shared/utils/test';
import { Notes } from './Notes';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const noNamesErrors = [
  {
    type: 'ManuallyCreatedCustomerErrors.NoNames',
    field: 'firstName',
    message: expect.any(String),
    status: 400,
  },
  {
    type: 'ManuallyCreatedCustomerErrors.NoNames',
    field: 'lastName',
    message: expect.any(String),
    status: 400,
  },
];

describe('create and assemble back with', () => {
  test(`firstName & lastName`, () => {
    const dto = {
      firstName: chance.string({ alpha: true, numeric: true, length: 50 }),
      lastName: chance.string({ alpha: true, numeric: true, length: 50 }),
      createdBy: chance.guid({ version: 4 }),
      notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
    };

    const args = {
      firstName: Name.create(dto.firstName).value,
      lastName: Name.create(dto.lastName).value,
      notes: dto.notes ? Notes.create(dto.notes).value : null,
    };
    const createdOrError = ManuallyCreatedCustomer.create({
      ...dto,
      ...args,
    });

    expect(createdOrError.isSuccess).toBe(true);
    const created = createdOrError.value;

    const fullName = `${dto.firstName} ${dto.lastName}`;
    expect(created.toDto()).toMatchObject({
      id: created.id.toString(),
      ...dto,
      fullName,
    });

    const assembled = ManuallyCreatedCustomer.assemble({
      id: created.id.toString(),
      ...dto,
      fullName,
    });

    expect(assembled.equals(created)).toBe(true);

    expect(created.domainEvents.length).toBe(1);
    expect(created.domainEvents[0]).toMatchObject({
      aggregateId: created.id.toString(),
      type: 'CustomerManuallyCreatedEvent',
      customer: created.toDto(),
    });
  });
  test(`only firstName`, () => {
    const dto = {
      firstName: chance.string({ alpha: true, numeric: true, length: 50 }),
      lastName: null,
      createdBy: chance.guid({ version: 4 }),
      notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
    };

    const args = {
      firstName: Name.create(dto.firstName).value,
      lastName: dto.lastName,
      notes: dto.notes ? Notes.create(dto.notes).value : null,
    };
    const createdOrError = ManuallyCreatedCustomer.create({
      ...dto,
      ...args,
    });

    expect(createdOrError.isSuccess).toBe(true);
    const created = createdOrError.value;

    const fullName = `${dto.firstName}`;
    expect(created.toDto()).toMatchObject({
      id: created.id.toString(),
      ...dto,
      fullName,
    });

    const assembled = ManuallyCreatedCustomer.assemble({
      id: created.id.toString(),
      ...dto,
      fullName,
    });

    expect(assembled.equals(created)).toBe(true);
  });
  test(`only lastName`, () => {
    const dto = {
      firstName: null,
      lastName: chance.string({ alpha: true, numeric: true, length: 50 }),
      createdBy: chance.guid({ version: 4 }),
      notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
    };

    const args = {
      firstName: dto.firstName,
      lastName: Name.create(dto.lastName).value,
      notes: dto.notes ? Notes.create(dto.notes).value : null,
    };
    const createdOrError = ManuallyCreatedCustomer.create({
      ...dto,
      ...args,
    });

    expect(createdOrError.isSuccess).toBe(true);
    const created = createdOrError.value;

    const fullName = `${dto.lastName}`;
    expect(created.toDto()).toMatchObject({
      id: created.id.toString(),
      ...dto,
      fullName,
    });

    const assembled = ManuallyCreatedCustomer.assemble({
      id: created.id.toString(),
      ...dto,
      fullName,
    });

    expect(assembled.equals(created)).toBe(true);
  });
});

test('creation fails when both firstName and lastName are null', () => {
  const errors = ManuallyCreatedCustomer.create({
    firstName: null,
    lastName: null,
    createdBy: chance.guid({ version: 4 }),
    notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
  });

  expect(errors).toMatchObject({
    isFailure: true,
    errors: noNamesErrors,
  })
});

describe('update', () => {
    const dto = {
      firstName: chance.string({ alpha: true, numeric: true, length: 15 }),
      lastName: chance.string({ alpha: true, numeric: true, length: 15 }),
      createdBy: chance.guid({ version: 4 }),
      notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
    };
    const vos = {
      firstName: Name.create(dto.firstName).value,
      lastName: Name.create(dto.lastName).value,
      notes: dto.notes === null? null : Notes.create(dto.notes).value,
    };
    const created = ManuallyCreatedCustomer.create({
      ...dto,
      ...vos,
    }).value;

  it(`succeeds`, () => {

    const dto = {
      firstName: chance.string({ alpha: true, numeric: true, length: 15 }),
      lastName: chance.string({ alpha: true, numeric: true, length: 15 }),
      createdBy: chance.guid({ version: 4 }),
      notes: orNull(chance.string({ alpha: true, numeric: true, length: 200 })),
    };

    const vos = {
      firstName: Name.create(dto.firstName).value,
      lastName: Name.create(dto.lastName).value,
      notes: dto.notes === null? null : Notes.create(dto.notes).value,
    };

    const updatedOrErrors = created.update({
      ...dto,
      ...vos,
    });
    expect(updatedOrErrors.isSuccess).toBe(true);
    const updated = updatedOrErrors.value;

    expect(updated.toDto()).toMatchObject({
      ...dto,
      id: created.id.toString(),
      fullName: `${dto.firstName} ${dto.lastName}`,
    });
  });

  it(`fails when both firstName & lastName are null`, () => {

    const errors = created.update({
      firstName: null,
      lastName: null,
      notes: orNull(Notes.create(chance.string({ alpha: true, numeric: true, length: 200 })).value),
    });
    expect(errors).toMatchObject({
      isFailure: true,
      errors: noNamesErrors,
    })
  });
});

