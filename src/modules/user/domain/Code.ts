import { ValueObject } from '@shared/core/domain/ValueObject';
import { CodeErrors } from './CodeErrors';
import { getLng, OptionalLng } from '@shared/utils/utils';
import { PositiveInt } from '@shared/core/PositiveInt';
import { Result2 } from '@shared/core/Result2';

interface CodeProps {
  value: number;
}

type CodeDto = number;

export class Code extends ValueObject<CodeProps, CodeDto> {
  private __class = this.constructor.name;

  get value(): number {
    return this.props.value;
  }

  private constructor(props: CodeProps) {
    super(props);
  }

  public static create(args: { value: number } & OptionalLng): Result2<Code> {
    const { value, lng: _lng } = args;
    const lng = getLng(_lng);

    const positiveIntOrError = PositiveInt.create({ value, lng });
    if (positiveIntOrError.isFailure)
      return Result2.fail(positiveIntOrError.errors!);

    // Check if the value is a 4-digit number (between 1000 and 9999)
    if (value < 1000 || value > 9999) {
      return Result2.fail([new CodeErrors.NotFourDigits({ value, lng })]);
    }

    return Result2.ok<Code>(new Code({ value }));
  }

  /**
   * Generates a random 4-digit code
   */
  public static getRandom(): Code {
    // Generate a random 4-digit code (between 1000 and 9999)
    const randomCode = Math.floor(Math.random() * 9000) + 1000;
    return Code.create({ value: randomCode }).value;
  }

  public toDto(): CodeDto {
    return this.value;
  }
}
