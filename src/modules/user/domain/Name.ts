import { ValueObject } from '@shared/core/domain/ValueObject';
import { Result } from '@shared/core/Result';
import { NameErrors } from './NameErrors';

type NameDto = string;

interface NameProps {
  value: NameDto;
}

export class Name extends ValueObject<NameProps, NameDto> {
  private __class = this.constructor.name;
  public static maxLength = 50;
  public static minLength = 1;

  get value(): string {
    return this.props.value;
  }

  private constructor(props: NameProps) {
    super(props);
  }

  public static create(name: string): Result<Name> {
    const trimmed = name.trim();
    const trimmed2 = trimmed.replace(/\s+/g, ' '); // replace duplicate spaces by just one
    return Result.convertValue(trimmed2)
      .ensure((value: string) => {
        return value.length >= this.minLength;
      }, new NameErrors.TooShort(this.minLength))
      .ensure((value: string) => {
        return value.length <= this.maxLength;
      }, new NameErrors.TooLong({ maxLength: this.maxLength, entered: trimmed2.length }))
      .onBoth((result) =>
        result.isSuccess
          ? Result.ok<Name>(new Name({ value: result.value }))
          : Result.fail(result.error!),
      );
  }

  public toDto(): NameDto {
    return this.value;
  }
}
