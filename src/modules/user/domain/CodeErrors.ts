import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { getLng, OptionalLng } from '@shared/utils/utils';

export namespace CodeErrors {
  export class NotFourDigits extends BadRequest {
    public constructor(args: { value: number } & OptionalLng) {
      const { value, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: `${t.notFourDigits}: ${value}`,
      });
    }
  }

  export class NotAnInteger extends BadRequest {
    public constructor(args: { value: number } & OptionalLng) {
      const { value, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: `${t.notAnInteger}: ${value}`,
      });
    }
  }
}

const trans = {
  en: {
    notFourDigits: 'Code must be a 4-digit number, but this was entered',
    notAnInteger: 'Code must be an integer, but this was entered',
  },
  es: {
    notFourDigits: 'El código debe ser un número de 4 dígitos, pero esto fue ingresado',
    notAnInteger: 'El código debe ser un número entero, pero esto fue ingresado',
  },
};

patch({ CodeErrors });
