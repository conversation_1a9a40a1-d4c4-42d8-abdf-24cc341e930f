import { expect, it, describe } from 'vitest';
import { Code } from './Code';
import {
  expectErrorsResult,
  expectErrorsResultContaining,
} from '@shared/utils/test';

describe('Code', () => {
  it('creates a valid 4-digit code', () => {
    const result = Code.create({ value: 1234 });

    expect(result.isSuccess).toBe(true);
    expect(result.value.value).toBe(1234);
  });

  it('fails if value is not a positive integer', () => {
    const result = Code.create({ value: 0 });

    expectErrorsResultContaining({
      result,
      errorContaining: 'PositiveIntErrors.',
      code: 400,
    })
  });

  it('fails if value is less than 1000', () => {
    const result = Code.create({ value: 999 });

    expectErrorsResult({
      result,
      error: 'CodeErrors.NotFourDigits',
      code: 400,
    });
  });

  it('fails if value is greater than 9999', () => {
    const result = Code.create({ value: 10000 });

    expectErrorsResult({
      result,
      error: 'CodeErrors.NotFourDigits',
      code: 400,
    });
  });

  it('converts to DTO correctly', () => {
    const result = Code.create({ value: 1234 });

    expect(result.isSuccess).toBe(true);
    expect(result.value.toDto()).toBe(1234);
  });

  describe('getRandom', () => {
    it('generates different codes on multiple calls', () => {
      // Generate multiple codes and check they're not all the same
      const codes = new Set<number>();
      const numCodes = 10;

      for (let i = 0; i < numCodes; i++)
        codes.add(Code.getRandom().value);

      // With 10 random codes, it's extremely unlikely they would all be the same
      // This test could theoretically fail, but the probability is extremely low
      expect(codes.size).toBe(numCodes);
    });
  });
});
