import { expect, test, describe } from 'vitest';
import { BusinessUserErrors } from './BusinessUserErrors';
import { Dat } from '@shared/core/Dat';
import { pickLng } from '@shared/utils/test';
import { getTimezone } from '@shared/core/test';

describe('BusinessUserErrors.NoAttemptsLeft handles', () => {
  const now = new Date().getTime();
  test.each([
    ['Spanish "hoy"', 'es' as const, now, 'hoy' ],
    ['English "today"', 'en' as const, now, 'today' ],
    ['Spanish "mañana"', 'es' as const, now + 24 * 60 * 60 * 1000, 'mañana' ],  // 1 day in the future
    ['English "tomorrow"', 'en' as const, now + 24 * 60 * 60 * 1000, 'tomorrow' ],  // 1 day in the future
  ])('%s', (_title, lng, blockedUntil, expectedRelativeDay) => {
    const error = new BusinessUserErrors.NoAttemptsLeft({
      blockedUntil: Dat.create({ value: blockedUntil }).value, // 1 min in the future,
      lng,
      timezone: getTimezone(),
    });

    const errorDto = error.toDto();
    expect(errorDto.message).toContain(expectedRelativeDay);
    expect(errorDto.message).not.toContain('Z (GMT)');
  });

  test('null timezone', () => {
    const error = new BusinessUserErrors.NoAttemptsLeft({
      blockedUntil: Dat.create({ value: new Date().getTime() + 60 * 60 * 1000 }).value, // 1 hour in the future,
      lng: pickLng(),
      timezone: null,
    });

    const errorDto = error.toDto();
    expect(errorDto.message).toContain('Z (GMT)');
  });
});
