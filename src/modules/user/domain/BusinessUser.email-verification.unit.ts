import { expect, test, describe, beforeEach } from 'vitest';
import {
  BusinessUser,
  getEmailVerificationNull,
  VerificationStatus,
} from './BusinessUser';
import { Email } from './Email';
import { TimeZone } from '@shared/core/TimeZone';
import { expectErrorsResult, pickLng } from '@shared/utils/test';
import { getTimezone } from '@shared/core/test';
import { Dat } from '@shared/core/Dat';
import { expectInAbout24hrs, expectInAboutAMinute } from '../utils/test';
import { Code } from './Code';
import { DatSec } from '@shared/core/DatSec';
import { UserLng } from './UserLng';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const lng = pickLng();

let user: BusinessUser, newEmail: Email, timezone: TimeZone;
beforeEach(() => {
  newEmail = Email.create(chance.email()).value;
  timezone = getTimezone();

  user = BusinessUser.create({
    email: Email.create(chance.email()).value,
    timezone: getTimezone(),
    lng: UserLng.create({ value: pickLng() }).value,
  });
});

function expectVerificationData(args: { user: BusinessUser, newEmail: Email }) {
  const { user, newEmail } = args;
  expect(user.emailVerification.status).toBe(VerificationStatus.STARTED);
  expect(user.emailVerification.newEmail?.value).toBe(newEmail.value);
  expect(user.emailVerification.code).toBeDefined();
  expect(user.emailVerification.code?.value).toBeGreaterThanOrEqual(1000);
  expect(user.emailVerification.code?.value).toBeLessThanOrEqual(9999);
  expectInAboutAMinute(user.emailVerification.codeExpiresAt!.t);
}

describe('addEmailVerificationAttempt', () => {

  test(`when emailVerification.blockedUntil is set and not expired`, () => {
    const initialStatus = user.props.emailVerification.status;

    // Set blocked 1 hour in the future
    const blockedUntil = Dat.create({ value: new Date().getTime() + 60 * 60 * 1000 }).value;
    user.props.emailVerification = {
      ...getEmailVerificationNull(),
      blockedUntil,
    };

    const result = user.addEmailVerificationAttempt({ lng, timezone, newEmail });

    expectErrorsResult({
      result,
      error: 'BusinessUserErrors.NoAttemptsLeft',
      code: 400,
    });
    expect(user.props.emailVerification.status).toBe(initialStatus);
    expect(user.props.emailVerification.attempts).toBeNull();
  });

  test(`when emailVerification has null data`, () => {
    const result = user.addEmailVerificationAttempt({ lng, timezone, newEmail });

    expect(result).toMatchObject({
      isSuccess: true,
      value: {
        code: user.props.emailVerification.code!,
        attemptsLeft: BusinessUser.MAX_VERIFICATION_ATTEMPTS_IN_24_HOURS - 1,
      },
    });
    expect(user.props.emailVerification).toMatchObject({
      status: VerificationStatus.STARTED,
      attempts: [expect.anything()],
      blockedUntil: null,
    });
    expectVerificationData({ user, newEmail });
  });

  test(`when the attempt exhausts the max attempts in the last 24 hours`, () => {
    // Create 2 attempts
    user.addEmailVerificationAttempt({ lng, timezone, newEmail: Email.create('<EMAIL>').value });
    user.addEmailVerificationAttempt({ lng, timezone, newEmail: Email.create('<EMAIL>').value });

    const result = user.addEmailVerificationAttempt({ lng, timezone, newEmail });

    expect(result).toMatchObject({
      isSuccess: true,
      value: {
        code: user.props.emailVerification.code!,
        attemptsLeft: 0,
      },
    });
    expect(user.props.emailVerification).toMatchObject({
      status: VerificationStatus.STARTED,
      attempts: null,
    });
    expectInAbout24hrs(user.props.emailVerification.blockedUntil!.t);
    expectVerificationData({ user, newEmail });
  });
});

describe('confirmEmailCode', () => {

  test(`succeeds with valid code`, () => {
    const { code } = user.addEmailVerificationAttempt({ lng, timezone, newEmail }).value;

    const result = user.confirmEmailCode({ code, lng });

    expect(result.isSuccess).toBe(true);
    expect(user.props.emailVerification.status).toBe(VerificationStatus.SUCCESS);
    expect(user.props.email.value).toBe(newEmail.value);
  });

  test(`fails when no verification in progress`, () => {
    const code = Code.create({ value: 1234, lng }).value;
    const result = user.confirmEmailCode({ code, lng });

    expectErrorsResult({
      result,
      error: 'BusinessUserErrors.NoVerificationInProgress',
      code: 400,
    });
  });

  test(`fails when code is expired`, () => {
    const { code } = user.addEmailVerificationAttempt({ lng, timezone, newEmail }).value;
    
    // Manually expire the code
    const pastTime = DatSec.create({ value: new Date().getTime() - 60 * 1000 }).value; // 1 minute ago
    user.props.emailVerification.codeExpiresAt = pastTime;

    const result = user.confirmEmailCode({ code, lng });

    expectErrorsResult({
      result,
      error: 'BusinessUserErrors.CodeExpired',
      code: 400,
    });
    expect(user.props.emailVerification.status).toBe(VerificationStatus.CODE_EXPIRED);
  });

  test(`fails when code is invalid`, () => {
    user.addEmailVerificationAttempt({ lng, timezone, newEmail });
    
    const wrongCode = Code.create({ value: 9999, lng }).value;
    const result = user.confirmEmailCode({ code: wrongCode, lng });

    expectErrorsResult({
      result,
      error: 'BusinessUserErrors.InvalidCode',
      code: 400,
    });
    expect(user.props.emailVerification.status).toBe(VerificationStatus.INVALID_CODE);
  });
});
