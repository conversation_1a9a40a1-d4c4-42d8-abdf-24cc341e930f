import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';

export namespace EmailErrors {
  export class EmailNotDefined extends BadRequest {
    public constructor() {
      super({ message: 'Email is not defined' });
    }
  }

  export class NotString extends BadRequest {
    public constructor() {
      super({ message: 'Email should be a string' });
    }
  }

  export class NotValid extends BadRequest {
    public constructor() {
      super({ message: `Email isn't valid` });
    }
  }

  export class TooLong extends BadRequest {
    public constructor(maxLength: number) {
      super({ message: `Email should be at most ${maxLength} characters long` });
    }
  }
}

patch({ EmailErrors });
