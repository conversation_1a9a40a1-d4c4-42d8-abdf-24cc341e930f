import { expect, test } from 'vitest';
import { Email } from './Email';

test(`Creation`, () => {
  const result = Email.create('<EMAIL>');

  expect(result.isSuccess).toBe(true);
  const userEmail = result.value;
  expect(userEmail.value).toBe('<EMAIL>');
});

test(`Fails with invalid email`, () => {
  const result = Email.create('john@gmail.b');

  expect(result).toMatchObject({
    isFailure: true,
    error: {
      type: 'EmailErrors.NotValid',
      message: expect.any(String),
      status: 400,
    },
  });
});

test(`Fails with a long value`, () => {
  const result = Email.create(
    '<EMAIL>',
  );

  expect(result).toMatchObject({
    isFailure: true,
    error: {
      type: 'EmailErrors.TooLong',
      message: expect.any(String),
      status: 400,
    },
  });
});
