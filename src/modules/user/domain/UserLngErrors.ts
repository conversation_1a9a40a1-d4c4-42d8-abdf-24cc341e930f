import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { possibleLngs, PossibleLngs } from '@shared/utils/utils';

export namespace UserLngErrors {
  export class InvalidLanguage extends BadRequest {
    public constructor(args: { lng: PossibleLngs }) {
      const { lng } = args;
      const t = trans[lng];
      super({
        message: `${t.invalidLanguage}`,
      });
    }
  }
}

patch({ UserLngErrors });

const trans = {
  en: {
    invalidLanguage: `Language isn't valid. Possible values are ${possibleLngs.join(', ')}`,
  },
  es: {
    invalidLanguage: `El idioma no es válido. Valores admitidos: ${possibleLngs.join(', ')}`,
  },
};
