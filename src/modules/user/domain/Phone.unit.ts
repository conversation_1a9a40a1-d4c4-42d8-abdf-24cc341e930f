import { describe, expect, test } from 'vitest';
import { Phone } from './Phone';
import { possibleLngs } from '@shared/utils/utils';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const lng = chance.bool() ? chance.pickone(possibleLngs) : undefined;

test(`Creation`, () => {
  const result = Phone.create({ value: ' + (121) 33 73  4253   ', lng });
  expect(result.isSuccess).toBe(true);
  const phone = result.value;
  expect(phone.value).toBe('+12133734253');
});

describe('Creation fails with', () => {
  test.each([ // Errors taken from https://gitlab.com/catamphetamine/libphonenumber-js/-/blob/master/README.md?ref_type=heads#parsephonenumberstring-defaultcountry-string--options-object-phonenumber
    ['NOT_A_NUMBER', 'abcde'],
    ['NOT_A_NUMBER', '+'],
    ['INVALID_COUNTRY', '+9991112223333'],
    ['TOO_SHORT', '+12'],
    ['TOO_LONG', '1'.repeat(251)],
  ])(`%s`, (error, value) => {
    const result = Phone.create({ value, lng });

    expect(result).toMatchObject({
      isFailure: true,
      error: {
        type: 'PhoneErrors.NotValid',
        message: expect.stringContaining(error),
        status: 400,
      },
    });
  });
});
