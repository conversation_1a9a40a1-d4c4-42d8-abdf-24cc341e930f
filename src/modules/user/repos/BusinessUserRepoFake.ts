import { Email } from '../domain/Email';
import { BusinessUser } from '../domain/BusinessUser';
import { BusinessUserInDb, BusinessUserRepo } from './BusinessUserRepo';
import { IBusinessUserRepo } from './IBusinessUserRepo';
import { BusinessUsersInDb as json } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';
import { Dat } from '@shared/core/Dat';

export class BusinessUserRepoFake implements IBusinessUserRepo {
  private BusinessUsersInDb: BusinessUserInDb[];

  public constructor() {
    this.BusinessUsersInDb = [...json];
  }

  public async exists(email: Email): Promise<boolean> {
    const usersInDb = this.BusinessUsersInDb.filter(
      (user) => user.email === email.value,
    );
    return !!usersInDb.length;
  }

  public async create(user: BusinessUser) {
    const now = new Date().toISOString();
    const dto = user.toDto();
    const db = BusinessUserRepo.mapDto2Db(dto);

    this.BusinessUsersInDb.push({
      ...db,
      created_at: now,
      updated_at: now,
      deleted_at: null,
    });
  }

  public async update(user: BusinessUser) {
    const dto = user.toDto();
    const db = BusinessUserRepo.mapDto2Db(dto);
    const idx = this.BusinessUsersInDb.findIndex(
      (user) => user.id === user.id.toString(),
    );
    const now = Dat.create().value.s;
    this.BusinessUsersInDb[idx] = {
      ...this.BusinessUsersInDb[idx], // to keep same created_at
      ...db,
      updated_at: now,
    };
  }

  public async get(_id: string): Promise<BusinessUser | null> {
    const userInDb = this.BusinessUsersInDb.filter((user) => user.id === _id)[0];
    if (!userInDb) return null;

    // Convert from flat DB structure to nested DTO structure
    const dto = BusinessUserRepo.mapDb2Dto(userInDb);
    return BusinessUser.assemble(dto);
  }
}
