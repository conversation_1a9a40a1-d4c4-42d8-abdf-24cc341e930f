import { IBusinessUserRepo } from './IBusinessUserRepo';
import { Email } from '../domain/Email';
import { BusinessUser, BusinessUserDto } from '../domain/BusinessUser';
import { Repository } from '@shared/core/Repository';
import { Timestamps } from '@shared/infra/database/sequelize/config/Timestamps';

type BusinessUserToDb = Omit<BusinessUserDto, 'phoneVerification' | 'emailVerification'> & {
  phoneVerificationStatus: string;
  phoneVerificationAttempts: string[] | null;
  phoneVerificationBlockedUntil: string | null;
  phoneVerificationNewPhone: string | null;
  phoneVerificationCode: number | null;
  phoneVerificationCodeExpiresAt: string | null;
  emailVerificationStatus: string;
  emailVerificationAttempts: string[] | null;
  emailVerificationBlockedUntil: string | null;
  emailVerificationNewEmail: string | null;
  emailVerificationCode: number | null;
  emailVerificationCodeExpiresAt: string | null;
};

// BusinessUserInDb represents the database structure with flat properties
export type BusinessUserInDb = BusinessUserToDb & Timestamps;

export class BusinessUserRepo
  extends Repository<BusinessUser>
  implements IBusinessUserRepo
{
  private BusinessUser: any; // eslint-disable-line @typescript-eslint/no-explicit-any

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public constructor(BusinessUserModel: any) {
    super();
    if (BusinessUserModel.tableName !== 'business_users')
      throw Error(
        `Wrong model passed in: ${BusinessUserModel.tableName}, while correct is business_users`,
      );
    this.BusinessUser = BusinessUserModel;
  }

  public async exists(email: Email): Promise<boolean> {
    const found = await this.BusinessUser.findOne({
      where: { email: email.value },
      transaction: this.transaction,
    });
    return !!found;
  }

  public async create(user: BusinessUser) {
    const dto = user.toDto();
    const db = BusinessUserRepo.mapDto2Db(dto);

    return this.BusinessUser.create(db, {
      transaction: this.transaction,
    });
  }

  public async update(user: BusinessUser) {
    const dto = user.toDto();
    const db = BusinessUserRepo.mapDto2Db(dto);

    return this.BusinessUser.update(db, {
      where: {
        id: user.id.toString(),
      },
      transaction: this.transaction,
    });
  }

  // Needed by create reservation use case, as only it's sent customerId.
  public async get(id: string): Promise<BusinessUser | null> {
    const found = await this.BusinessUser.findOne({
      where: { id },
      transaction: this.transaction,
    });
    if (!found) return null;

    return BusinessUser.assemble(BusinessUserRepo.mapDb2Dto(found.dataValues));
  }

  public static mapDb2Dto = (db: BusinessUserInDb): BusinessUserDto => {
    const {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      created_at,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      updated_at,
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      deleted_at,
      phoneVerificationAttempts,
      phoneVerificationBlockedUntil,
      phoneVerificationNewPhone,
      phoneVerificationCode,
      phoneVerificationCodeExpiresAt,
      phoneVerificationStatus,
      emailVerificationStatus,
      emailVerificationAttempts,
      emailVerificationBlockedUntil,
      emailVerificationNewEmail,
      emailVerificationCode,
      emailVerificationCodeExpiresAt,
      ...rest
    } = db;

    // Create the nested phoneVerification object
    const phoneVerification = {
      status: phoneVerificationStatus,
      attempts: phoneVerificationAttempts,
      blockedUntil: phoneVerificationBlockedUntil,
      newPhone: phoneVerificationNewPhone,
      code: phoneVerificationCode,
      codeExpiresAt: phoneVerificationCodeExpiresAt,
    };
    const emailVerification = {
      status: emailVerificationStatus,
      attempts: emailVerificationAttempts,
      blockedUntil: emailVerificationBlockedUntil,
      newEmail: emailVerificationNewEmail,
      code: emailVerificationCode,
      codeExpiresAt: emailVerificationCodeExpiresAt,
    }

    return {
      ...rest,
      phoneVerification,
      emailVerification,
    };
  };

  public static mapDto2Db = (dto: BusinessUserDto): BusinessUserToDb => {
    const { phoneVerification, emailVerification, ...rest } = dto;

    return {
      ...rest,
      phoneVerificationStatus: phoneVerification.status,
      phoneVerificationAttempts: phoneVerification.attempts || null,
      phoneVerificationBlockedUntil: phoneVerification.blockedUntil || null,
      phoneVerificationNewPhone: phoneVerification.newPhone || null,
      phoneVerificationCode: phoneVerification.code || null,
      phoneVerificationCodeExpiresAt: phoneVerification.codeExpiresAt || null,
      emailVerificationStatus: emailVerification.status,
      emailVerificationAttempts: emailVerification.attempts || null,
      emailVerificationBlockedUntil: emailVerification.blockedUntil || null,
      emailVerificationNewEmail: emailVerification.newEmail || null,
      emailVerificationCode: emailVerification.code || null,
      emailVerificationCodeExpiresAt: emailVerification.codeExpiresAt || null,
    };
  }

}
