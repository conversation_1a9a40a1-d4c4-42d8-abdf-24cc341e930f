import {
  ManuallyCreatedCustomer,
  ManuallyCreatedCustomerDto,
} from '../domain/ManuallyCreatedCustomer';
import { IManuallyCreatedCustomerRepo } from './IManuallyCreatedCustomerRepo';
import { Repository } from '@shared/core/Repository';
import { Timestamps } from '@shared/infra/database/sequelize/config/Timestamps';
import { nonEmpty } from '@shared/utils/utils';

type ManuallyCreatedCustomerToDb = ManuallyCreatedCustomerDto;
export type ManuallyCreatedCustomerInDb = ManuallyCreatedCustomerToDb & Timestamps;

export class ManuallyCreatedCustomerRepo
  extends Repository<ManuallyCreatedCustomer>
  implements IManuallyCreatedCustomerRepo
{
  private ManuallyCreatedCustomer: any; // eslint-disable-line @typescript-eslint/no-explicit-any

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public constructor(ManuallyCreatedCustomerModel: any) {
    super();
    if (ManuallyCreatedCustomerModel.tableName !== 'manually_created_customers')
      throw Error(
        `Wrong model passed in: ${ManuallyCreatedCustomerModel.tableName}, while correct is manually_created_customers`,
      );
    this.ManuallyCreatedCustomer = ManuallyCreatedCustomerModel;
  }

  public async create(customer: ManuallyCreatedCustomer) {
    const dto = customer.toDto();
    await this.ManuallyCreatedCustomer.create(dto, { // dto coincides with ManuallyCreatedCustomerInDb
      transaction: this.transaction,
    });
    return dto;
  }

  public async update(customer: ManuallyCreatedCustomer) {
    const dto = customer.toDto();
    await this.ManuallyCreatedCustomer.update(dto, { // dto coincides with ManuallyCreatedCustomerInDb
      where: {
        id: customer.id.toString(),
        createdBy: customer.createdBy,
      },
      transaction: this.transaction,
    });
    return dto;
  }

  public async get(args: {
    id: string;
    createdBy: string;
  }): Promise<ManuallyCreatedCustomer | null> {
    const { id, createdBy } = args;
    const found = await this.ManuallyCreatedCustomer.findOne({
      where: { id, createdBy },
      transaction: this.transaction,
    });
    if (!found) return null;

    return ManuallyCreatedCustomer.assemble(found.dataValues);
  }

  public async getByFullName(args: {
    createdBy: string;
    fullName: string;
  }): Promise<[ManuallyCreatedCustomer, ...ManuallyCreatedCustomer[]] | null> {
    const { createdBy, fullName } = args;
    const found: {
      dataValues: ManuallyCreatedCustomerInDb;
    }[] = await this.ManuallyCreatedCustomer.findAll({
      where: { createdBy, fullName },
      transaction: this.transaction,
    });
    if (!found.length) return null;

    const customersInDb = found.map((f) => f.dataValues);

    const dtos = customersInDb.map(ManuallyCreatedCustomerRepo.mapDb2Dto);
    return nonEmpty(dtos.map(ManuallyCreatedCustomer.assemble));
  }

  public static mapDb2Dto(db: ManuallyCreatedCustomerInDb): ManuallyCreatedCustomerDto {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { created_at, updated_at, deleted_at, ...rest } = db;
    return {
      ...rest,
      id: db.id.toString(),
    };
  }

  public async getAll(
    createdBy: string,
  ): Promise<[ManuallyCreatedCustomer, ...ManuallyCreatedCustomer[]] | null> {
    const found = await this.ManuallyCreatedCustomer.findAll({
      where: { createdBy },
      transaction: this.transaction,
    });
    if (!found.length) return null;

    return found.map((f: { dataValues: ManuallyCreatedCustomerInDb }) =>
      ManuallyCreatedCustomer.assemble(f.dataValues),
    );
  }

  public async delete(args: { id: string; createdBy: string }): Promise<number> {
    const { id, createdBy } = args;
    return this.ManuallyCreatedCustomer.destroy({
      where: {
        id,
        createdBy,
      },
      transaction: this.transaction,
    });
  }
}
