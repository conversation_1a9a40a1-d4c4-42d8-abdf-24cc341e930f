import { ManuallyCreatedCustomer } from '../domain/ManuallyCreatedCustomer';
import { IManuallyCreatedCustomerRepo } from './IManuallyCreatedCustomerRepo';
import { ManuallyCreatedCustomerInDb } from './ManuallyCreatedCustomerRepo';
import { ManuallyCreatedCustomersInDb as json } from '@shared/infra/database/sequelize/migrations/ManuallyCreatedCustomers.json';
import { Dat } from '@shared/core/Dat';
import { nonEmpty } from '@shared/utils/utils';

export class ManuallyCreatedCustomerRepoFake
  implements IManuallyCreatedCustomerRepo
{
  private ManuallyCreatedCustomersInDb: ManuallyCreatedCustomerInDb[];

  public constructor() {
    this.ManuallyCreatedCustomersInDb = [...json];
  }

  public async create(customer: ManuallyCreatedCustomer) {
    const now = new Date().toISOString();
    const dto = {
      ...customer.toDto(),
      created_at: now,
      updated_at: now,
      deleted_at: null,
    }
    this.ManuallyCreatedCustomersInDb.push(dto);  // dto coincides with ManuallyCreatedCustomerInDb
    return dto;
  }

  public async update(updating: ManuallyCreatedCustomer) {
    const idx = this.ManuallyCreatedCustomersInDb.findIndex(
      (existing) =>
        existing.id === updating.id.toString() && existing.createdBy === updating.createdBy,
    );
    const toDB = {
      ...this.ManuallyCreatedCustomersInDb[idx], // keep the same created_at
      ...updating.toDto(),
      updated_at: Dat.create().value.s,
    }
    this.ManuallyCreatedCustomersInDb[idx] = toDB;
    return toDB;  // toDB coincides with ManuallyCreatedCustomerDto
  }

  public async get(args: {
    id: string;
    createdBy: string;
  }): Promise<ManuallyCreatedCustomer | null> {
    const { id, createdBy } = args;
    const customerInDb = this.ManuallyCreatedCustomersInDb.filter(
      (customer) => customer.id === id && customer.createdBy === createdBy,
    )[0];
    if (!customerInDb) return null;

    return ManuallyCreatedCustomer.assemble(customerInDb);
  }

  public async getByFullName(args: {
    createdBy: string;
    fullName: string;
  }): Promise<[ManuallyCreatedCustomer, ...ManuallyCreatedCustomer[]]  | null> {
    const { createdBy, fullName } = args;
    const customersInDb = this.ManuallyCreatedCustomersInDb.filter(
      (customer) => customer.fullName === fullName && customer.createdBy === createdBy,
    );
    if (!customersInDb.length) return null;

    return nonEmpty(customersInDb.map(ManuallyCreatedCustomer.assemble));
  }

  public async getAll(
    createdBy: string,
  ): Promise<[ManuallyCreatedCustomer, ...ManuallyCreatedCustomer[]] | null> {
    const customersInDb = this.ManuallyCreatedCustomersInDb.filter(
      (customer) => customer.createdBy === createdBy && !customer.deleted_at,
    );
    if (!customersInDb.length) return null;

    return nonEmpty(customersInDb.map(ManuallyCreatedCustomer.assemble));
  }

  public async delete(args: { id: string; createdBy: string }): Promise<number> {
    const { id, createdBy } = args;
    const initialLength = this.ManuallyCreatedCustomersInDb.length;
    this.ManuallyCreatedCustomersInDb = this.ManuallyCreatedCustomersInDb.filter(
      (customer) => !(customer.id === id && customer.createdBy === createdBy),
    );
    return initialLength - this.ManuallyCreatedCustomersInDb.length;
  }
}
