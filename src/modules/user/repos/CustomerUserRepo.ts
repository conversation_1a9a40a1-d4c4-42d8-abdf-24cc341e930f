import { ICustomerUserRepo } from './ICustomerUserRepo';
import { Email } from '../domain/Email';
import { CustomerUser, CustomerUserDto } from '../domain/CustomerUser';
import { Repository } from '@shared/core/Repository';
import { Timestamps } from '@shared/infra/database/sequelize/config/Timestamps';
import { Op } from 'sequelize';
import { nonEmpty } from '@shared/utils/utils';

type CustomerUserToDb = CustomerUserDto;
export type CustomerUserInDb = CustomerUserToDb & Timestamps;

export class CustomerUserRepo
  extends Repository<CustomerUser>
  implements ICustomerUserRepo
{
  private CustomerUser: any; // eslint-disable-line @typescript-eslint/no-explicit-any

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public constructor(CustomerUserModel: any) {
    super();
    if (CustomerUserModel.tableName !== 'customer_users')
      throw Error(
        `Wrong model passed in: ${CustomerUserModel.tableName}, while correct is customer_users`,
      );
    this.CustomerUser = CustomerUserModel;
  }

  public async exists(email: Email): Promise<boolean> {
    const found = await this.CustomerUser.findOne({
      where: { email: email.value },
      transaction: this.transaction,
    });
    return !!found;
  }

  public async create(user: CustomerUser) {
    return this.CustomerUser.create(user.toDto(), {
      transaction: this.transaction,
    });
  }

  // Needed by create reservation use case, as only it's sent customerId.
  public async get(id: string): Promise<CustomerUser | null> {
    const found = await this.CustomerUser.findOne({
      where: { id },
      transaction: this.transaction,
    });
    if (!found) return null;

    return CustomerUser.assemble(found.dataValues);
  }

  public async getByIds(ids: string[]): Promise<[CustomerUser, ...CustomerUser[]] | null> {
    const found: {
      dataValues: CustomerUserInDb;
    }[] = await this.CustomerUser.findAll({
      where: {
        [Op.or]: ids.map(id => ({ id })),
      },
      transaction: this.transaction,
    });
    if (!found.length) return null;

    const customersInDb = found.map((f) => f.dataValues);

    return nonEmpty(customersInDb.map(CustomerUser.assemble));  // dtos = customersInDb
  }
}
