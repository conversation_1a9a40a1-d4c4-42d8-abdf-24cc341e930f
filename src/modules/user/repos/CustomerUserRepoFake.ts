import { ICustomerUserRepo } from './ICustomerUserRepo';
import { Email } from '../domain/Email';
import { CustomerUser } from '../domain/CustomerUser';
import { CustomerUserInDb } from './CustomerUserRepo';
import { CustomerUsersInDb as json } from '@shared/infra/database/sequelize/migrations/CustomerUsers.json';
import { nonEmpty } from '@shared/utils/utils';

export class CustomerUserRepoFake implements ICustomerUserRepo {
  private CustomerUsersInDb: CustomerUserInDb[];

  public constructor() {
    this.CustomerUsersInDb = [...json];
  }

  public async exists(email: Email): Promise<boolean> {
    const usersInDb = this.CustomerUsersInDb.filter(
      (user) => user.email === email.value,
    );
    return !!usersInDb.length;
  }

  public async create(user: CustomerUser) {
    const now = new Date().toISOString();
    this.CustomerUsersInDb.push({
      ...user.toDto(),
      created_at: now,
      updated_at: now,
      deleted_at: null,
    });
  }

  public async get(id: string): Promise<CustomerUser | null> {
    const userInDb = this.CustomerUsersInDb.filter(
      (customer) => customer.id === id,
    )[0];
    if (!userInDb) return null;

    return CustomerUser.assemble(userInDb);
  }

  public async getByIds(ids: string[]): Promise<[CustomerUser, ...CustomerUser[]] | null> {
    const usersInDb = this.CustomerUsersInDb.filter((user) =>
      ids.includes(user.id),
    );
    if (!usersInDb.length) return null;

    return nonEmpty(usersInDb.map(CustomerUser.assemble));
  }
}
