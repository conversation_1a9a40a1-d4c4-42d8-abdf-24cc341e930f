import {
  ManuallyCreatedCustomer,
  ManuallyCreatedCustomerDto,
} from '../domain/ManuallyCreatedCustomer';

export declare interface IManuallyCreatedCustomerRepo {
  create(customer: ManuallyCreatedCustomer): Promise<ManuallyCreatedCustomerDto>;
  update(customer: ManuallyCreatedCustomer): Promise<ManuallyCreatedCustomerDto>;
  get(args: {
    createdBy: string;
    id: string;
  }): Promise<ManuallyCreatedCustomer | null>; // Necesito el get para el update
  getAll(
    createdBy: string,
  ): Promise<[ManuallyCreatedCustomer, ...ManuallyCreatedCustomer[]] | null>;
  delete(args: { createdBy: string; id: string }): Promise<number>;
  getByFullName(args: {
    createdBy: string;
    fullName: string;
  }): Promise<[ManuallyCreatedCustomer, ...ManuallyCreatedCustomer[]]  | null>;
}
