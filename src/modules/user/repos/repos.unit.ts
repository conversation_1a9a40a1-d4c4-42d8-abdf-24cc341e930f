import { test, expect } from 'vitest';
import { BusinessUserRepo } from './BusinessUserRepo';
import { CustomerUserRepo } from './CustomerUserRepo';
import { ManuallyCreatedCustomerRepo } from './ManuallyCreatedCustomerRepo';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize/models');

test(`BusinessUserRepo receives business_users table`, () => {
  expect(() => new BusinessUserRepo(models.Calendar)).toThrow();
  expect(() => new BusinessUserRepo(models.BusinessUser)).not.toThrow();
});

test(`CustomerUserRepo receives customer_users table`, () => {
  expect(() => new CustomerUserRepo(models.Calendar)).toThrow();
  expect(() => new CustomerUserRepo(models.CustomerUser)).not.toThrow();
});

test(`ManuallyCreatedCustomerRepo receives manually_created_customers table`, () => {
  expect(() => new ManuallyCreatedCustomerRepo(models.Calendar)).toThrow();
  expect(
    () => new ManuallyCreatedCustomerRepo(models.ManuallyCreatedCustomer),
  ).not.toThrow();
});
