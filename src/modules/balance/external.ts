import { ReservationDto as _ReservationDto } from '../reservation/domain/Reservation';
import { BusinessUserCreatedEventDto as _BusinessUserCreatedEventDto } from '../user/domain/events/BusinessUserCreatedEvent';
import { ReservationByBusinessEventDto as _ReservationByBusinessEventDto } from '../reservation/domain/events/ReservationByBusinessEvent';
import { ReservationByCustomerEventDto as _ReservationByCustomerEventDto } from '../reservation/domain/events/ReservationByCustomerEvent';
import { CancelReservationByCustomerEventDto as _CancelReservationByCustomerEventDto } from '../reservation/domain/events/CancelReservationByCustomerEvent';
import { CancelReservationByBusinessEventDto as _CancelReservationByBusinessEventDto } from '../reservation/domain/events/CancelReservationByBusinessEvent';
import { CustomerUserDto as _CustomerUserDto } from '../user/domain/CustomerUser';

export type ReservationDto = _ReservationDto;
export type BusinessUserCreatedEventDto = _BusinessUserCreatedEventDto;
export type ReservationByBusinessEventDto = _ReservationByBusinessEventDto;
export type ReservationByCustomerEventDto = _ReservationByCustomerEventDto;
export type CancelReservationByCustomerEventDto = _CancelReservationByCustomerEventDto;
export type CancelReservationByBusinessEventDto = _CancelReservationByBusinessEventDto;
export type CustomerUserDto = _CustomerUserDto;