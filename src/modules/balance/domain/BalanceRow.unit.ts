import { beforeEach, describe, expect, test } from 'vitest';
import { BalanceRow, Movement } from './BalanceRow';
import { dateFormat } from '@shared/utils/test';
import { PositiveInt } from '@shared/core/PositiveInt';
import {
  createReservation,
  ReservationDto,
} from '../utils/testExternal';
import { Dat } from '@shared/core/Dat';
import { getFullName } from '../utils/utils';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const userId = chance.guid();

let reservation: ReservationDto | null, movement: Movement;
beforeEach(() => {
  movement = chance.pickone(Object.keys(Movement)) as Movement;
  reservation = [Movement.RESERVATION_BY_CUSTOMER, Movement.RESERVATION_BY_BUSINESS, Movement.CANCELLATION_BY_CUSTOMER, Movement.CANCELLATION_BY_BUSINESS].includes(movement) ? createReservation().dto : null;
})

test(`BalanceRow.create() starts balance with ${BalanceRow.INITIAL_FREE_BALANCE_AMOUNT}`, () => {
  const balanceRow = BalanceRow.create({ userId });

  expect(balanceRow.toDto()).toEqual({
    movement: Movement.INITIAL_FREE_BALANCE,
    change: BalanceRow.INITIAL_FREE_BALANCE_AMOUNT,
    balance: BalanceRow.INITIAL_FREE_BALANCE_AMOUNT,
    reservationId: null,
    customerId: null,
    customerFullName: null,
    srvName: null,
    date: expect.stringMatching(dateFormat),
    index: 0,
    userId,
  });
});

describe('movement of type', () => {
  test.each([
    Movement.PURCHASE as const,
    Movement.MONTHLY_FREE_QUOTA_TOP_UP as const,
  ])(`%s increases balance by the inputted change`, (movement: Movement.PURCHASE | Movement.MONTHLY_FREE_QUOTA_TOP_UP) => {
    const initialBalance = chance.integer({min: 1, max: 999999});
    const initialIndex = chance.integer({min: 1, max: 9999999});
    const balanceRow = assembleBalance({
      balance: initialBalance,
      index: initialIndex,
    });

    const change = chance.integer({min: 1, max: 999999});
    balanceRow.move({
      movement,
      change: PositiveInt.create({value: change}).value,
    });

    expect(balanceRow.toDto()).toEqual({
      movement,
      change,
      balance: initialBalance + change,
      reservationId: null,
      customerId: null,
      customerFullName: null,
      srvName: null,
      date: expect.stringMatching(dateFormat),
      index: initialIndex + 1,
      userId,
    });
  });

  test.each([
    Movement.RESERVATION_BY_CUSTOMER as const,
    Movement.RESERVATION_BY_BUSINESS as const,
  ])(`%s decreases balance by 1`, (movement) => {
    const initialBalance = chance.integer({min: 1, max: 99999});
    const initialIndex = chance.integer({min: 1, max: 999999});
    const balanceRow = assembleBalance({
      balance: initialBalance,
      index: initialIndex,
    });

    const reservation = createReservation().dto;
    balanceRow.move({
      movement,
      reservation,
    });

    expect(balanceRow.toDto()).toEqual({
      movement,
      change: -1,
      balance: initialBalance - 1,
      reservationId: reservation.id,
      customerId: reservation.customerId,
      customerFullName: getFullName({
        firstName: reservation.customerFirstName,
        lastName: reservation.customerLastName,
      }),
      srvName: reservation.srvName,
      date: expect.stringMatching(dateFormat),
      index: initialIndex + 1,
      userId,
    });
  });

  test(`${Movement.CANCELLATION_BY_BUSINESS} doesn't decrease balance`, () => {
    const initialBalance = chance.integer({min: 1, max: 99999});
    const initialIndex = chance.integer({min: 1, max: 99999});
    const balanceRow = assembleBalance({
      balance: initialBalance,
      index: initialIndex,
    });

    const movement = Movement.CANCELLATION_BY_BUSINESS;
    const reservation = createReservation().dto;
    balanceRow.move({
      movement,
      reservation,
    });

    expect(balanceRow.toDto()).toEqual({
      movement,
      change: 0,
      balance: initialBalance,
      reservationId: reservation.id,
      customerId: reservation.customerId,
      customerFullName: getFullName({
        firstName: reservation.customerFirstName,
        lastName: reservation.customerLastName,
      }),
      srvName: reservation.srvName,
      date: expect.stringMatching(dateFormat),
      index: initialIndex + 1,
      userId,
    });
  });

  test(`${Movement.CANCELLATION_BY_CUSTOMER} increases balance by 1`, () => {
    const initialBalance = chance.integer({min: 1, max: 99999999});
    const initialIndex = chance.integer({min: 1, max: 99999999});
    const balanceRow = assembleBalance({
      balance: initialBalance,
      index: initialIndex,
    });

    const movement = Movement.CANCELLATION_BY_CUSTOMER;
    const reservation = createReservation().dto;
    balanceRow.move({
      movement,
      reservation,
    });

    expect(balanceRow.toDto()).toEqual({
      movement,
      change: 1,
      balance: initialBalance + 1,
      reservationId: reservation.id,
      customerId: reservation.customerId,
      customerFullName: getFullName({
        firstName: reservation.customerFirstName,
        lastName: reservation.customerLastName,
      }),
      srvName: reservation.srvName,
      date: expect.stringMatching(dateFormat),
      index: initialIndex + 1,
      userId,
    });
  });
});

test(`move throws if it's called more than once`, () => {
  const balanceRow = assembleBalance({
    balance: chance.integer({min: 0, max: 99999999}),
    index: chance.integer({min: 0, max: 99999999}),
  });
  balanceRow.move({
    movement: Movement.PURCHASE,
    change: PositiveInt.create({value: chance.integer({min: 1, max: 99999999})}).value,
  });
  expect(() => balanceRow.move({
    movement: Movement.PURCHASE,
    change: PositiveInt.create({value: chance.integer({min: 1, max: 99999999})}).value,
  })).toThrow();
});

test('isPositive', () => {
  let balanceRow = assembleBalance({
    balance: chance.integer({min: 1, max: 99999999}),
    index: chance.integer({min: 1, max: 99999999}),
  });
  expect(balanceRow.isPositive()).toBe(true);
  balanceRow = assembleBalance({
    balance: 0,
    index: chance.integer({min: 1, max: 99999999}),
  });
  expect(balanceRow.isPositive()).toBe(false);
});

test(`assemble throws if movement is invalid`, () => {
  expect(() => BalanceRow.assemble({
    movement: 'invalid',
    reservationId: chance.guid(),
    customerId: chance.guid(),
    customerFullName: chance.name(),
    srvName: chance.word(),
    balance: chance.integer({min: 0, max: 99999999}),
    change: chance.integer(),
    date: Dat.create().value.s,
    userId,
    index: chance.integer({min: 0, max: 99999999}),
  })).toThrow();
});

function assembleBalance(args: {balance: number, index: number}) {
  const { balance, index } = args;
  return BalanceRow.assemble({
    movement,
    reservationId: reservation?.id ?? null,
    customerId: reservation?.customerId ?? null,
    customerFullName: reservation?.customerFirstName || reservation?.customerLastName ? getFullName({
      firstName: reservation?.customerFirstName ?? null,
      lastName: reservation?.customerLastName ?? null,
    }) : null,
    srvName: reservation?.srvName ?? null,
    balance,
    change: chance.integer(),
    date: Dat.create().value.s,
    userId,
    index,
  });
}