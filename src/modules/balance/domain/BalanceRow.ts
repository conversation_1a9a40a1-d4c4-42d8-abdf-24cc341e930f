import { EntityID } from '@shared/core/domain/EntityID';
import { Dat } from '@shared/core/Dat';
import { Spread } from '@shared/utils/utils';
import { N0 } from '@shared/core/N0';
import { CustomerUserDto, ReservationDto } from '../external';
import { PositiveInt } from '@shared/core/PositiveInt';
import { Integer } from '@shared/core/Integer';
import { AggregateRootCK } from '@shared/core/domain/AggregateRootCK';
import { getFullName } from '../utils/utils';

export enum Movement {
  INITIAL_FREE_BALANCE = 'INITIAL_FREE_BALANCE',
  // When setting PURCHASE, it comes with change field
  MONTHLY_FREE_QUOTA_TOP_UP = 'MONTHLY_FREE_QUOTA_TOP_UP',
  PURCHASE = 'PURCHASE',
  // When setting the following fields, they come with fields: reservationId, customerFullName and srvName
  RESERVATION_BY_BUSINESS = 'RESERVATION_BY_BUSINESS',
  RESERVATION_BY_CUSTOMER = 'RESERVATION_BY_CUSTOMER',
  CANCELLATION_BY_BUSINESS = 'CANCELLATION_BY_BUSINESS',
  CANCELLATION_BY_CUSTOMER = 'CANCELLATION_BY_CUSTOMER',
}

type BalanceRowInput = {
  // userId and index form a composite key
  userId: string; // linked to the BusinessUser entity in module user
};

type BalanceRowProps = {
  balance: Integer;  // use Integer instead of N0 to allow negative balance in case multiple reservations occur simultaneously, while the balance is 0.
  // after creation and before calling movement, movement and change are null
  movement: Movement;
  change: Integer;

  // for movement = RESERVATION | BUSINESS_CANCELLATION | CUSTOMER_CANCELLATION these fields exist, otherwise they're null
  reservationId: ReservationDto['id'] | null,
  customerId: CustomerUserDto['id'] | null,
  customerFullName: string | null,
  srvName: ReservationDto['srvName'] | null,

  date: Dat;
};

export type BalanceRowDto = Spread<
  BalanceRowProps & BalanceRowInput,
  {
    index: number;
    balance: number;
    movement: string;
    change: number;
    date: string;
  }
>;

export type MoveArgs = {
  movement: Movement.MONTHLY_FREE_QUOTA_TOP_UP; // The monthly free quota top-up row is added to the balance only when an increase is needed. This prevents unused accounts from accumulating balance rows due to monthly free quota top-ups.
  change: PositiveInt;
} | {
  movement: Movement.PURCHASE;
  change: PositiveInt;
} | {
  movement: Movement.RESERVATION_BY_BUSINESS;
  reservation: ReservationDto;
} | {
  movement: Movement.RESERVATION_BY_CUSTOMER;
  reservation: ReservationDto;
} | {
  movement: Movement.CANCELLATION_BY_BUSINESS;
  reservation: ReservationDto;
} | {
  movement: Movement.CANCELLATION_BY_CUSTOMER;
  reservation: ReservationDto;
};

export class BalanceRow extends AggregateRootCK<BalanceRowProps, BalanceRowDto> {
  private __class = this.constructor.name;
  private moveAlreadyCalled = false;
  public static MONTHLY_FREE_QUOTA = 100;
  public static INITIAL_FREE_BALANCE_AMOUNT = BalanceRow.MONTHLY_FREE_QUOTA; // although being private suffices, I declare it public for unit test

  get userId(): string {
    return this._id.toString();
  }

  get index(): N0 {
    return this._id2;
  }

  get balance(): Integer {
    return this.props.balance;
  }

  get movement(): Movement {
    return this.props.movement;
  }

  get change(): Integer {
    return this.props.change;
  }

  get reservationId(): string | null {
    return this.props.reservationId;
  }
  get customerId(): string | null {
    return this.props.customerId;
  }
  get customerFullName(): string | null {
    return this.props.customerFullName;
  }
  get srvName(): string | null {
    return this.props.srvName;
  }

  get date(): Dat {
    return this.props.date;
  }

  private constructor(props: BalanceRowProps, compositeKey: {
    userId: EntityID,
    index: N0
  }) {
    const {userId, index} = compositeKey;
    super(props, userId, index);
  }

  public static create(input: BalanceRowInput): BalanceRow {
    const { userId } = input;
    const initialBalance = BalanceRow.INITIAL_FREE_BALANCE_AMOUNT;

    return new BalanceRow(
      {
        balance: Integer.create({ value: initialBalance }).value,
        movement: Movement.INITIAL_FREE_BALANCE,
        change: Integer.create({value: initialBalance}).value,

        reservationId: null,
        customerId: null,
        customerFullName: null,
        srvName: null,

        date: Dat.create().value,
      },
      {
        userId: new EntityID(userId),
        index: N0.create({ value: 0 }).value,
      },
    );
  }

  public isPositive(): boolean {
    return this.balance.value > 0;
  }

  public move(args: MoveArgs) {

    if (this.moveAlreadyCalled) // Prevents index _id2 from being increased more than once
      throw Error(`move() has already been called`);
    this.moveAlreadyCalled = true;

    this._id2.add(PositiveInt.create({ value: 1 }).value);

    const movement = args.movement;
    this.props.movement = movement;
    this.props.date = Dat.create().value;
    switch (movement) {
      case Movement.PURCHASE:
      case Movement.MONTHLY_FREE_QUOTA_TOP_UP: {
        const change = args.change;
        this.props.change = Integer.create({value: change.value}).value;
        this.props.balance.add(this.props.change);
        this.props.reservationId = null;
        this.props.customerId = null;
        this.props.customerFullName = null;
        this.props.srvName = null;
        break;
      }
      case Movement.RESERVATION_BY_CUSTOMER:
      case Movement.RESERVATION_BY_BUSINESS: {
        const { reservation } = args;
        this.props.reservationId = reservation.id;
        this.props.customerId = reservation.customerId;
        this.props.customerFullName = getFullName({
          firstName: reservation.customerFirstName,
          lastName: reservation.customerLastName,
        });
        this.props.srvName = reservation.srvName;
        this.props.change = Integer.create({value: -1}).value;
        this.props.balance.add(this.props.change);
        break;
      }

      case Movement.CANCELLATION_BY_BUSINESS: {
        const { reservation } = args;
        this.props.reservationId = reservation.id;
        this.props.customerId = reservation.customerId;
        this.props.customerFullName = getFullName({
          firstName: reservation.customerFirstName,
          lastName: reservation.customerLastName,
        });
        this.props.srvName = reservation.srvName;
        this.props.change = Integer.create({value: 0}).value;
        break;
      }

      case Movement.CANCELLATION_BY_CUSTOMER: {
        const { reservation } = args;
        this.props.reservationId = reservation.id;
        this.props.customerId = reservation.customerId;
        this.props.customerFullName = getFullName({
          firstName: reservation.customerFirstName,
          lastName: reservation.customerLastName,
        });
        this.props.srvName = reservation.srvName;
        this.props.change = Integer.create({value: 1}).value;
        this.props.balance.add(this.props.change);
        break;
      }
    }
  }

  public toDto(): BalanceRowDto {
    const {id, id2, balance, movement, change, date} = this;

    return {
      ...this.props,
      userId: id.toString(),
      index: id2.value,
      balance: balance.value,
      movement: Movement[movement],
      change: change.value,
      date: date.s,
    };
  }

  public static assemble(dto: BalanceRowDto): BalanceRow {
    const {
      userId,
      index,
      balance,
      movement,
      change,
      date,
      ...rest
    } = dto;

    if (!isValidMovement(movement))
      throw Error(`Invalid movement ${movement}`);

    return new BalanceRow(
      {
        ...rest,
        balance: Integer.create({value: balance}).value,
        movement,
        change: Integer.create({value: change}).value,
        date: Dat.create({value: date}).value,
      },
      {
        userId: new EntityID(userId),
        index: N0.create({value: index}).value,
      },
    );
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isValidMovement(test: any): test is Movement {
  return Object.keys(Movement).includes(test);
}
