// Funciones usadas en los tests que dependen de otros módulos externos.
import {
  createBusinessUser as _createBusinessUser,
} from '../../user/utils/test';
import {
  createReservation as _createReservation,
} from '../../reservation/utils/testExternal';
import { ReservationDto as _ReservationDto }
from '../../reservation/domain/Reservation';
import { BusinessUsersInDb as _BusinessUsersInDb } from '@shared/infra/database/sequelize/migrations/BusinessUsers.json';

export const createBusinessUser = _createBusinessUser;
export const createReservation = _createReservation;
export type ReservationDto = _ReservationDto;
export const BusinessUsersInDb = _BusinessUsersInDb;