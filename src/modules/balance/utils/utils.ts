import { backOff } from 'exponential-backoff';
import { commonBackOffOptions } from '@shared/utils/utils';
import { BalanceRow } from '../domain/BalanceRow';
import { IContextProvider } from '@shared/context/IContextProvider';

export async function tryWithBackoff(args: {
  fn: () => Promise<BalanceRow>;
  sendAnalytics: IContextProvider['sendAnalytics'];
}) {
  const { fn, sendAnalytics } = args;
  try {
    return await backOff(async () => {

      return await fn();

    }, backOffOptions(sendAnalytics));

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (e: any) {
    await sendAnalytics({
      e,
    });
    if (isSequelizeUniqueError(e))
      console.log(`Sequelize unique error couldn't be resolved after backoff`);
    else
      console.log(`A different error from sequelize unique couldn't be resolved after backoff`, e);
    throw Error(e);
  }
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function isSequelizeUniqueError(e: any) {
  if (!e || typeof e.name !== 'string') return false;
  const name = e.name;
  return /sequelize/i.test(name) && /unique/i.test(name);
}

const backOffOptions = (sendAnalytics: IContextProvider['sendAnalytics']) => ({
  ...commonBackOffOptions,
  retry: async (e: unknown, attempt: number) => {
    const uniqueError = isSequelizeUniqueError(e);
    await sendAnalytics({
      e,
      attempt,
      uniqueError,
    });
    if (uniqueError) {
      console.log(`Sequelize unique error persists in balanceRepo.insert, backOff attempt number ${attempt}`, e);
      return true;
    } else {
      console.log(`A different error from sequelize unique in balanceRepo.insert, backOff stops at attempt number ${attempt}`, e);
      return false;
    }
  },
});

export function getFullName(args: {
  firstName: string | null;
  lastName: string | null;
}) {
  const { firstName, lastName } = args;

  if (firstName === null && lastName === null) {
    throw Error('firstName and lastName are both null');
  }

  return `${firstName? firstName : ''}` +
    `${firstName && lastName? ' ' : ''}` +
    `${lastName? lastName : ''}`;
}