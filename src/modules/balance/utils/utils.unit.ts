import { expect, it, vi, describe } from 'vitest';
import { tryWithBackoff } from './utils';
import { ErrorSequelizeUnique } from './test';
import { BalanceRow } from '../domain/BalanceRow';
import { invokerDummy as invoker } from '@shared/utils/test';
import { ContextProvider } from '@shared/context/ContextProvider';
import { Context } from '@shared/context/IContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const contextProvider = new ContextProvider({ invoker });
contextProvider.set({
  awsRequestId: 'test-request-id',
  logGroupName: '/aws/lambda/test-group',
  logStreamName: 'test-stream',
  identity: {
    cognitoIdentityId: 'test-identity-id',
  },
} as Context);
const sendAnalytics = contextProvider.sendAnalytics.bind(contextProvider);

const row = BalanceRow.create({ userId: chance.guid() });

describe('tryWithBackoff', () => {
  it('succeeds if fn() throws up to 7 attempts and the 8th attempt succeeds', async () => {
    let numberOfCalls = 0;
    const input = {
      fn: async () => row,
    };
    let success = false;
    const spyOnFn = vi.spyOn(input, 'fn').mockImplementation(async () => {
      if (numberOfCalls < 7) {
        numberOfCalls++;
        throw new ErrorSequelizeUnique();
      }
      success = true;
      return row;
    });

    await tryWithBackoff({
      fn: input.fn,
      sendAnalytics,
    });

    expect(spyOnFn).toHaveBeenCalledTimes(8);
    expect(success).toBe(true);
  });

  it('throws if fn() throws always', async () => {
    const input = {
      fn: async () => row,
    };
    const spyOnFn = vi.spyOn(input, 'fn').mockImplementation(async () => {
      throw new ErrorSequelizeUnique();
    });

    await expect(tryWithBackoff({ fn: input.fn, sendAnalytics })).rejects.toThrow();
    expect(spyOnFn).toHaveBeenCalledTimes(8);
  });

  it(`throws on the first attempt if the error isn't a sequelize unique error`, async () => {
    const input = {
      fn: async () => row,
      sendAnalytics,
    };
    const spyOnFn = vi.spyOn(input, 'fn').mockImplementation(async () => {
      throw new Error();
    });

    await expect(tryWithBackoff(input)).rejects.toThrow();
    expect(spyOnFn).toHaveBeenCalledTimes(1);
  });
})