// Funciones usadas en los tests que no dependen de otros módulos externos
import { BalanceRow, Movement } from '../domain/BalanceRow';
import { createReservation } from './testExternal';
import { PositiveInt } from '@shared/core/PositiveInt';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize/models');

export const BalanceRowModel = models.BalanceRow;

export async function rmBalanceRow(where: { userId: string, index: number }) {
  return BalanceRowModel.destroy({ where });
}

export async function rmBalanceRows(where: { userId: string }) {
  return BalanceRowModel.destroy({ where });
}

export class ErrorSequelizeUnique extends Error {
  public name = 'SequelizeUniqueConstraintError';
  public constructor() {
    super('Custom sequelize unique error')
  }
}

export function getNextRow(args: {
  previousRow: BalanceRow;
  movement: Movement;
  change?: number;
}) {
  const { previousRow, movement, change } = args;
  const previousRowDto = previousRow.toDto()
  const clone = BalanceRow.assemble(previousRowDto); // clone previousRow
  clone.move({
    // @ts-expect-error
    movement: Movement[movement],
    ...[Movement.CANCELLATION_BY_BUSINESS, Movement.RESERVATION_BY_BUSINESS, Movement.RESERVATION_BY_CUSTOMER, Movement.CANCELLATION_BY_BUSINESS, Movement.CANCELLATION_BY_CUSTOMER].includes(movement)? { reservation: createReservation({ userId: previousRowDto.userId }).dto} : {},
    ...[Movement.MONTHLY_FREE_QUOTA_TOP_UP, Movement.PURCHASE].includes(movement)? { change: PositiveInt.create({ value: change! }).value } : {},
  });
  return clone;
}