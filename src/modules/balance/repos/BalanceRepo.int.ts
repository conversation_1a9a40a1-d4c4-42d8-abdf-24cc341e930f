import { expect, it, describe } from 'vitest';
import { BalanceRepo } from './BalanceRepo';
import { BalanceRowsInDb } from '@shared/infra/database/sequelize/migrations/BalanceRows.json';
import { BalanceRow, Movement } from '../domain/BalanceRow';
import { DateTime } from 'luxon';
import { BalanceRowModel, getNextRow, rmBalanceRows } from '../utils/test';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const balanceRepo = new BalanceRepo(models.BalanceRow);

describe('distinctUsers', () => {
  it('returns a list of unique user IDs', async () => {
    // Get the expected unique user IDs from the test data
    const expectedUserIds = [...new Set(BalanceRowsInDb.map(row => row.userId))];

    // Call the method being tested
    const distinctUsers = await balanceRepo.distinctUsers();

    // Verify there are no duplicates in the result
    const uniqueResultIds = [...new Set(distinctUsers)];
    expect(uniqueResultIds.length).toBe(distinctUsers.length);

    // Verify each expected user ID is in the result
    expectedUserIds.forEach(userId => {
      expect(distinctUsers).toContain(userId);
    });
  });
});

describe('refillableConsumptionOfLastMonth', () => {
  it('calculates the refillable consumption correctly', async () => {
    const userId = chance.guid();

    // Create test data with different movements
    const rows = [];
    rows.push(
      BalanceRow.create({ userId })
    );
    // Refillable count: +1
    rows.push(getNextRow({ previousRow: rows[rows.length - 1],
      movement: Movement.RESERVATION_BY_CUSTOMER,
    }));

    // +2
    rows.push(getNextRow({ previousRow: rows[rows.length - 1],
      movement: Movement.RESERVATION_BY_BUSINESS,
    }));

    // +1 (-1 from refillable count)
    rows.push(getNextRow({ previousRow: rows[rows.length - 1],
      movement: Movement.CANCELLATION_BY_CUSTOMER,
    }));

    // +1 (no effect on refillable count)
    rows.push(getNextRow({ previousRow: rows[rows.length - 1],
      movement: Movement.CANCELLATION_BY_BUSINESS,
    }));

    // +1 (no effect on refillable count)
    rows.push(getNextRow({ previousRow: rows[rows.length - 1],
      movement: Movement.PURCHASE,
      change: 10,
    }));

    // +1 (no effect on refillable count)
    rows.push(getNextRow({ previousRow: rows[rows.length - 1],
      movement: Movement.MONTHLY_FREE_QUOTA_TOP_UP,
      change: 20,
    }));

    // +2
    rows.push(getNextRow({ previousRow: rows[rows.length - 1],
      movement: Movement.RESERVATION_BY_BUSINESS,
    }));

    await Promise.all(
      rows.map((row) => balanceRepo.insert(row))
    );

    const now = DateTime.now().setZone('UTC');
    const lastMonth = now.minus({ months: 1 });
    await BalanceRowModel.update(
      { date: lastMonth.toJSDate() },
      { where: { userId } }
    );

    // Call the method being tested
    const refillableConsumption = await balanceRepo.refillableConsumptionOfLastMonth(userId);

    expect(refillableConsumption).toBe(2);

    // Clean up
    await rmBalanceRows({ userId });
  });
});