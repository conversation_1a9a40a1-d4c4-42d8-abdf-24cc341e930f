import { IBalanceRepo } from './IBalanceRepo';
import { BalanceRow, Movement } from '../domain/BalanceRow';
import { BalanceRowInDb } from './BalanceRepo';
import { BalanceRowsInDb as json } from '@shared/infra/database/sequelize/migrations/BalanceRows.json';
import { nonEmpty } from '@shared/utils/utils';
import { DateTime } from 'luxon';

export class BalanceRepoFake implements IBalanceRepo {
  private readonly BalanceRowsInDb: BalanceRowInDb[];

  public constructor() {
    this.BalanceRowsInDb = [...json]; // Load a brand-new copy of the json for every fake repo
  }

  public async insert(balanceRow: BalanceRow) {
    const now = new Date().toISOString();
    const newRow = {
      ...balanceRow.toDto(),
      created_at: now,
      updated_at: now,
    }
    this.BalanceRowsInDb.push(newRow);
    return BalanceRow.assemble(newRow);
  }

  public async list(userId: string): Promise<[BalanceRow, ...BalanceRow[]]> {
    const balanceRowsInDb = this.BalanceRowsInDb.filter(
      (res) => res.userId === userId,
    );

    return nonEmpty(balanceRowsInDb.map(BalanceRow.assemble));
  }

  public async last(userId: string): Promise<BalanceRow> {
    const balanceRowsInDb = this.BalanceRowsInDb.filter(
      (res) => res.userId === userId,
    )
    const last = balanceRowsInDb.sort((a, b) => b.index - a.index)[0];

    return BalanceRow.assemble(last);
  }

  public async distinctUsers() {
    const users = this.BalanceRowsInDb.map((row) => row.userId);
    const uniqueUsers = [...new Set(users)];
    return nonEmpty(uniqueUsers);
  }

  public async refillableConsumptionOfLastMonth(userId: string) {
    const now = DateTime.now().setZone('UTC');
    const lastMonth = now.minus({ months: 1 });
    const lastMonthStart = DateTime.fromObject(
      { year: lastMonth.year, month: lastMonth.month, day: 1 },
      { zone: 'UTC' }
    ).startOf('day');
    const lastMonthEnd = lastMonthStart.plus({ months: 1 }).minus({ days: 1 }).endOf('day');

    let found = this.BalanceRowsInDb.filter(
      (row) => row.userId === userId
      && new Date(row.date).getTime() > lastMonthStart.toMillis()
      && new Date(row.date).getTime() < lastMonthEnd.toMillis()
      && (row.movement === Movement.RESERVATION_BY_CUSTOMER || row.movement === Movement.RESERVATION_BY_BUSINESS),
    );
    const consumptions = found.map((f) => f.change);
    const montlhyConsumption = consumptions.reduce((acc, curr) => acc + curr, 0);

    found = this.BalanceRowsInDb.filter(
      (row) => row.userId === userId
        && new Date(row.date).getTime() > lastMonthStart.toMillis()
        && new Date(row.date).getTime() < lastMonthEnd.toMillis()
        && (row.movement === Movement.CANCELLATION_BY_CUSTOMER),
    );
    const cancellationsByCustomers = found.map((f) => f.change);
    const monthlyCancellationsByCustomers = cancellationsByCustomers.reduce((acc, curr) => acc + curr, 0);

    return montlhyConsumption - monthlyCancellationsByCustomers;
  }
}
