import {
  BalanceRow,
  BalanceRowDto,
  Movement,
} from '../domain/BalanceRow';
import { IBalanceRepo } from './IBalanceRepo';
import { Repository } from '@shared/core/Repository';
import { Timestamps } from '@shared/infra/database/sequelize/config/Timestamps';
import { nonEmpty, Spread } from '@shared/utils/utils';
import { DateTime } from 'luxon';
import { Op } from 'sequelize';
import { Dat } from '@shared/core/Dat';

type BalanceRowToDb = Spread<BalanceRowDto, {
  movement: string; // movement is null at the creation, but it always should have a move() that populates movement before saving
}>;

export type BalanceRowInDb = BalanceRowToDb & Omit<Timestamps, 'deleted_at'>;

export class BalanceRepo
  extends Repository<BalanceRow>
  implements IBalanceRepo
{
  private BalanceRow: any; // eslint-disable-line @typescript-eslint/no-explicit-any

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  public constructor(BalanceRowModel: any) {
    super();
    if (BalanceRowModel.tableName !== 'balance_rows')
      throw Error(
        `Wrong model passed in: ${BalanceRowModel.tableName}, while correct is balance_rows`,
      );
    this.BalanceRow = BalanceRowModel;
  }

  public async list(
    userId: string,
  ): Promise<[BalanceRow, ...BalanceRow[]]> {
    const found: {
      dataValues: BalanceRowInDb;
    }[] = await this.BalanceRow.findAll({
      where: {
        userId,
      },
      transaction: this.transaction,
    });

    const inDd = found.map((f) => f.dataValues);

    return nonEmpty(inDd.map(BalanceRow.assemble));
  }

  public async insert(balanceRow: BalanceRow) {
    const created: { dataValues: BalanceRowInDb } = await this.BalanceRow.create(balanceRow.toDto(), {
      transaction: this.transaction,
    });
    return BalanceRow.assemble(created.dataValues);
  }

  public async last(userId: string): Promise<BalanceRow> {
    const last: {
      dataValues: BalanceRowInDb;
    } = await this.BalanceRow.findOne({
      where: {
        userId,
      },
      order: [['index', 'DESC' ]],
      transaction: this.transaction,
    });

    return BalanceRow.assemble(last.dataValues);
  }

  public async distinctUsers() {
    const found: {
      dataValues: { userId: string };
    }[] = await this.BalanceRow.findAll({
      attributes: ['userId'],
      group: ['userId'],
      transaction: this.transaction,
    });

    const inDd = found.map((f) => f.dataValues.userId);

    return nonEmpty(inDd);
  }

  // Sum up all the consumptions of the last month with movement = Movement.RESERVATION_BY_BUSINESS and RESERVATION_BY_CUSTOMER
  public async refillableConsumptionOfLastMonth(userId: string) {
    const now = DateTime.now().setZone('UTC');
    const lastMonth = now.minus({ months: 1 });
    const lastMonthStart = DateTime.fromObject(
      { year: lastMonth.year, month: lastMonth.month, day: 1 },
      { zone: 'UTC' }
    ).startOf('day');
    const lastMonthEnd = lastMonthStart.plus({ months: 1 }).minus({ days: 1 }).endOf('day');
    const between = [
      Dat.create({ value: lastMonthStart }).value.j,
      Dat.create({ value: lastMonthEnd }).value.j,
    ]

    let found: {
      dataValues: BalanceRowInDb;
    }[] = await this.BalanceRow.findAll({
      where: {
        userId,
        date: {
          [Op.between]: between,
        },
        movement: {
          [Op.in]: [
            Movement.RESERVATION_BY_BUSINESS,
            Movement.RESERVATION_BY_CUSTOMER,
          ],
        },
      },
      transaction: this.transaction,
    });
    let inDd = found.map((f) => f.dataValues);
    const consumptions = inDd.map((f) => - f.change); // since consumptions are negative, invert the sign
    const montlhyConsumption = consumptions.reduce((acc, curr) => acc + curr, 0);

    found = await this.BalanceRow.findAll({
      where: {
        userId,
        date: {
          [Op.between]: between,
        },
        movement: {
          [Op.in]: [
            Movement.CANCELLATION_BY_CUSTOMER,
          ],
        },
      },
      transaction: this.transaction,
    });
    inDd = found.map((f) => f.dataValues);
    const cancellationsByCustomers = inDd.map((f) => f.change);
    const monthlyCancellationsByCustomers = cancellationsByCustomers.reduce((acc, curr) => acc + curr, 0);

    return montlhyConsumption - monthlyCancellationsByCustomers;
  }
}
