import { test, expect } from 'vitest';
import { BalanceRepo } from './BalanceRepo';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../shared/infra/database/sequelize/models');

test(`BalanceRepo receives balance_rows table`, () => {
  expect(() => new BalanceRepo(models.Calendar)).toThrow();
  expect(
    () => new BalanceRepo(models.BalanceRow),
  ).not.toThrow();
});