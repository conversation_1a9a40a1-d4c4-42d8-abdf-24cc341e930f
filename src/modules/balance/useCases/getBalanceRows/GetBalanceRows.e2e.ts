import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { BalanceRowsInDb } from '@shared/infra/database/sequelize/migrations/BalanceRows.json';
import { dateFormat } from '@shared/utils/test';
import { Request } from './GetBalanceRowsDTOs';
import { BalanceRow } from '../../domain/BalanceRow';
import { BalanceRowWithTimestampsFragment } from '../../utils/fragments';

const appsync = new AppSyncClient();

const query = gql`
  query ($userId: ID!) {
    getBalanceRows(userId: $userId) {
      result {
        ...BalanceRowWithTimestampsFragment
      }
      time
    }
  }
  ${BalanceRowWithTimestampsFragment}
`;

const timestamps = {
  created_at: expect.stringMatching(dateFormat),
  updated_at: expect.stringMatching(dateFormat),
};

const { userId } = BalanceRowsInDb[0];

it(`gets balance rows`, async () => {
  const received = await appsync.send<Request>({
    query,
    variables: { userId },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log('GetBalanceRows 1', json);
  const response = json.data.getBalanceRows;
  if (!response) console.log('json', json);

  expect(response.result.length).toBe(12);
  const res = BalanceRowsInDb.filter(b => b.userId === userId).map(BalanceRow.assemble);
  const resExpected = res.map(b => ({
    ...b.toDto(),
    ...timestamps,
  }));
  expect(response.result).toEqual(resExpected);
});