import { AppSyncController } from '@shared/infra/Controllers';
import {
  Request,
  Response,
} from './GetBalanceRowsDTOs';
import { IBalanceRepo } from '../../repos/IBalanceRepo';
import { IContextProvider } from '@shared/context/IContextProvider';

export class GetBalanceRows extends AppSyncController<Request, Response> {
  private readonly balanceRepo: IBalanceRepo;

  public constructor(args: {
    balanceRepo: IBalanceRepo;
    contextProvider: IContextProvider;
  }) {
    const { balanceRepo, contextProvider } = args;
    super({ contextProvider });
    this.balanceRepo = balanceRepo;
  }

  public async executeImpl(event: Request) {
    const { userId } = event;

    const list = await this.balanceRepo.list(userId);
    return {
      status: 200,
      result: list.map(b => b.toDto()),
    };
  }
}