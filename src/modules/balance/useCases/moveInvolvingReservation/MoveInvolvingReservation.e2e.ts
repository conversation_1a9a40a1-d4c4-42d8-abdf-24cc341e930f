import { expect, it } from 'vitest';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import { BalanceRepo } from '../../repos/BalanceRepo';
import { Request } from './MoveInvolvingReservationDTOs';
import {
  createReservation,
} from '../../utils/testExternal';
import { DomainEventTypes } from '@shared/events/DomainEventTypes';
import { BalanceRowsInDb } from '@shared/infra/database/sequelize/migrations/BalanceRows.json';
import { dateFormat } from '@shared/utils/test';
import { Movement } from '../../domain/BalanceRow';
import { rmBalanceRow } from '../../utils/test';
import { getFullName } from '../../utils/utils';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');

const { moveInvolvingReservation } = process.env;
if (!moveInvolvingReservation) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}

const lambdaInvoker = new LambdaInvoker(new Lambda({}));
const repo = new BalanceRepo(models.BalanceRow);

const userId = BalanceRowsInDb[1].userId; // 01a82ed0-c39a-11ee-8adf-3f66c88fcbdd
const reservation = createReservation({userId}).dto;

it(`inserts a RESERVATION_BY_CUSTOMER movement`, async () => {
  const request: Request = {
    dateTimeOccurred: '2024-02-13T23:42Z',
    type: DomainEventTypes.ReservationByCustomerEvent,
    aggregateId: reservation.id,
    reservation,
  };

  await lambdaInvoker.invokeSync<Request>(
    request,
    moveInvolvingReservation,
  );

  const balance = await repo.last(userId);
  expect(balance.toDto()).toEqual({
    userId,
    index: 2,
    balance: 98,
    movement: Movement.RESERVATION_BY_CUSTOMER,
    change: -1,
    reservationId: reservation.id,
    customerId: reservation.customerId,
    customerFullName: getFullName({
      firstName: reservation.customerFirstName,
      lastName: reservation.customerLastName,
    }),
    srvName: reservation.srvName,
    date: expect.stringMatching(dateFormat),
    created_at: expect.any(Date),
    updated_at: expect.any(Date),
  });

  await rmBalanceRow({
    userId,
    index: 2,
  })

});