import { MoveInvolvingReservation } from './MoveInvolvingReservation';
import { BalanceRepo } from '../../repos/BalanceRepo';
import { BalanceSrv } from '../../services/BalanceSrv';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });
const sendAnalytics = contextProvider.sendAnalytics.bind(contextProvider);

const repo = new BalanceRepo(models.BalanceRow);

const srv = new BalanceSrv({ repo, sendAnalytics });
const controller = new MoveInvolvingReservation({ srv, contextProvider });

export const handler = controller.execute.bind(controller);
