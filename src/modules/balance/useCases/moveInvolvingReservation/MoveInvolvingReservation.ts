import { AsynchronousController } from '@shared/infra/Controllers';
import { Request } from './MoveInvolvingReservationDTOs';
import { Movement } from '../../domain/BalanceRow';
import { DomainEventTypes } from '@shared/events/DomainEventTypes';
import { IBalanceSrv } from '../../services/IBalanceSrv';
import { IContextProvider } from '@shared/context/IContextProvider';

export class MoveInvolvingReservation extends AsynchronousController<Request> {
  private srv: IBalanceSrv;

  public constructor(args: {
    srv: IBalanceSrv,
    contextProvider: IContextProvider;
  }) {
    const { srv, contextProvider } = args;
    super({ contextProvider });
    this.srv = srv;
  }

  public async executeImpl(event: Request): Promise<void> {
    const { type, reservation } = event;

    let movement: Movement;

    switch (type) {
      case DomainEventTypes.ReservationByBusinessEvent: {
        movement = Movement.RESERVATION_BY_BUSINESS;
        break;
      }
      case DomainEventTypes.ReservationByCustomerEvent: {
        movement = Movement.RESERVATION_BY_CUSTOMER;
        break;
      }
      case DomainEventTypes.CancelReservationByCustomerEvent: {
        movement = Movement.CANCELLATION_BY_CUSTOMER;
        break;
      }
      case DomainEventTypes.CancelReservationByBusinessEvent: {
        movement = Movement.CANCELLATION_BY_BUSINESS;
        break;
      }

      default:
        console.log({event});
        throw new Error(`Unsupported event type: ${type}`);

    }
    await this.srv.insertWithBackoff({
      userId: reservation.userId,
      moveArgs: {
        movement,
        reservation,
      },
    });

  }
}