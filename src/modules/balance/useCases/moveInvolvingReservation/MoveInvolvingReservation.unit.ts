import { beforeEach, it, vi, expect, describe } from 'vitest';
import { BalanceRepoFake } from '../../repos/BalanceRepoFake';
import { MoveInvolvingReservation } from './MoveInvolvingReservation';
import { BusinessUsersInDb, createReservation } from '../../utils/testExternal';
import { Movement } from '../../domain/BalanceRow';
import { DomainEventTypes } from '@shared/events/DomainEventTypes';
import { getFullName } from '../../utils/utils';
import { BalanceSrv } from '../../services/BalanceSrv';
import { invokerDummy } from '@shared/utils/test';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

let repo: BalanceRepoFake,
  controller: MoveInvolvingReservation;

beforeEach(() => {
  repo = new BalanceRepoFake();
  const contextProvider = new ContextProvider({ invoker: invokerDummy });
  const sendAnalytics = contextProvider.sendAnalytics.bind(contextProvider);
  const srv = new BalanceSrv({ repo, sendAnalytics });
  controller = new MoveInvolvingReservation({ srv, contextProvider });
});

const userId = BusinessUsersInDb[0].id;
const reservation = createReservation({userId}).dto;

describe('inserts a movement of type', () => {
  it.each([
    [Movement.RESERVATION_BY_BUSINESS, DomainEventTypes.ReservationByBusinessEvent],
    [Movement.RESERVATION_BY_CUSTOMER, DomainEventTypes.ReservationByCustomerEvent],
    [Movement.CANCELLATION_BY_CUSTOMER, DomainEventTypes.CancelReservationByCustomerEvent],
    [Movement.CANCELLATION_BY_BUSINESS, DomainEventTypes.CancelReservationByBusinessEvent],
  ])('%s', async (movement, event) => {
    vi.spyOn(repo, 'insert').mockImplementation(async (row) => {
      expect(row).toMatchObject({
        movement,
        reservationId: reservation.id,
        customerFullName: getFullName({
          firstName: reservation.customerFirstName,
          lastName: reservation.customerLastName,
        }),
        srvName: reservation.srvName,
      });
      return row;
    });

    await controller.executeImpl({
      type: event,
      reservation,
      aggregateId: reservation.id,
      dateTimeOccurred: chance.date(),
    })
  });
});

it('throws if movement is unknown', async () => {
  await expect(
    controller.executeImpl({
      type: 'Unknown',
      reservation,
      aggregateId: reservation.id,
      dateTimeOccurred: chance.date(),
    })
  ).rejects.toThrow();
});