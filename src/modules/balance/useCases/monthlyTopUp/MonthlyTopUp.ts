import { AsynchronousController } from '@shared/infra/Controllers';
import { IBalanceRepo } from '../../repos/IBalanceRepo';
import { BalanceRow, Movement } from '../../domain/BalanceRow';
import { PositiveInt } from '@shared/core/PositiveInt';
import { IBalanceSrv } from '../../services/IBalanceSrv';
import { IContextProvider } from '@shared/context/IContextProvider';

export class MonthlyTopUp extends AsynchronousController<Request> {
  private readonly repo: IBalanceRepo;
  private readonly srv: IBalanceSrv;

  public constructor(args: {
    repo: IBalanceRepo,
    srv: IBalanceSrv,
    contextProvider: IContextProvider;
  }) {
    const { repo, srv, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
    this.srv = srv;
  }

  public async executeImpl() {
    const userIds = await this.repo.distinctUsers();

    await Promise.all(
      userIds.map(async (userId) => {
        await refill({
          userId,
          balanceRepo: this.repo,
          balanceSrv: this.srv,
        })
      })
    );

  }
}

// Exported for testing
export async function refill(args: {
  userId: string;
  balanceRepo: IBalanceRepo;
  balanceSrv: IBalanceSrv;
}) {
  const { userId, balanceRepo, balanceSrv } = args;
  const refillable = await balanceRepo.refillableConsumptionOfLastMonth(userId);
  if (refillable > 0) {
    const refill = Math.min(refillable, BalanceRow.MONTHLY_FREE_QUOTA);
    const change = PositiveInt.create({ value: refill }).value;

    console.log(`Free monthly top up for ${userId}: ${refill}`);
    await balanceSrv.insertWithBackoff({
      userId,
      moveArgs: {
        movement: Movement.MONTHLY_FREE_QUOTA_TOP_UP,
        change,
      },
    })

  } else
    console.log(`No refillable consumption for userId: ${userId}, refillable: ${refillable}`);
}