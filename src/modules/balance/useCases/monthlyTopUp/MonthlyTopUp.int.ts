import { expect, it, vi, beforeEach, describe } from 'vitest';
import { BalanceRepo } from '../../repos/BalanceRepo';
import { BalanceRow } from '../../domain/BalanceRow';
import { rmBalanceRows } from '../../utils/test';
import { refill } from './MonthlyTopUp';
import { BalanceSrv } from '../../services/BalanceSrv';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

describe('refill', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  const sendAnalytics = vi.fn();

  it(`refills last month's refillable consumption, if it’s positive and does not exceed the monthly free quota`, async () => {

    const balanceRepo = new BalanceRepo(models.BalanceRow);
    const balanceSrv = new BalanceSrv({ repo: balanceRepo, sendAnalytics });

    const refillable = chance.integer({min: 1, max: BalanceRow.MONTHLY_FREE_QUOTA });
    vi.spyOn(balanceRepo, 'refillableConsumptionOfLastMonth').mockImplementation(
      async () => refillable
    );

    const userId = chance.guid();
    // Add initial balance to the user
    await balanceRepo.insert(BalanceRow.create({ userId }));

    await refill({
      userId,
      balanceRepo,
      balanceSrv,
    });

    const last = await balanceRepo.last(userId);
    expect(last.toDto()).toMatchObject({
      userId,
      change: refillable,
    });

    await rmBalanceRows({
      userId,
    });

  });

  it(`doesn't refill beyond the MONTHLY_FREE_QUOTA`, async () => {

    const balanceRepo = new BalanceRepo(models.BalanceRow);
    const balanceSrv = new BalanceSrv({ repo: balanceRepo, sendAnalytics });

    const refillable = chance.integer({min: BalanceRow.MONTHLY_FREE_QUOTA + 1, max: 2 * BalanceRow.MONTHLY_FREE_QUOTA });
    vi.spyOn(balanceRepo, 'refillableConsumptionOfLastMonth').mockImplementation(
      async () => refillable
    );

    const userId = chance.guid();
    // Add initial balance to the user
    await balanceRepo.insert(BalanceRow.create({ userId }));

    await refill({
      userId,
      balanceRepo,
      balanceSrv,
    });

    const last = await balanceRepo.last(userId);
    expect(last.toDto()).toMatchObject({
      userId,
      change: BalanceRow.MONTHLY_FREE_QUOTA,
    });

    await rmBalanceRows({
      userId,
    });

  });
});