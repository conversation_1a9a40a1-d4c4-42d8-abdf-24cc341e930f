import { IsBalanceEnough } from './IsBalanceEnough';
import { BalanceRepo } from '../../repos/BalanceRepo';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models'); // If I use @shared, unit tests give error

const balanceRepo = new BalanceRepo(models.BalanceRow);
const controller = new IsBalanceEnough({
  balanceRepo,
});

export const handler = controller.executeImpl.bind(controller);