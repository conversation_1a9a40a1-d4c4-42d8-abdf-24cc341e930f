import { expect, it } from 'vitest';
import { IsBalanceEnough } from './IsBalanceEnough';
import { BalanceRepoFake } from '../../repos/BalanceRepoFake';
import { BalanceRowsInDb } from '@shared/infra/database/sequelize/migrations/BalanceRows.json';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const balanceRepo = new BalanceRepoFake();
const isBalanceEnough = new IsBalanceEnough({
  balanceRepo,
});

const userId = '00ad8920-c36d-11ee-8adf-3f66c88fcbdd';
// last row for userId 00ad8920-c36d-11ee-8adf-3f66c88fcbdd in BalanceRows.json is BalanceRowsInDb[13]
const existingBalance = BalanceRowsInDb[13].balance;

it('returns true when balance is enough', async () => {
  const requiredBalance = chance.integer({ min: 1, max: existingBalance - 1 });

  const isEnoughOrErrors = await isBalanceEnough.executeImpl({
    userId,
    requiredBalance,
  });

  expect(isEnoughOrErrors).toBe(true);
});
it(`returns false when balance isn't enough`, async () => {
  const requiredBalance = chance.integer({ min: existingBalance });

  const isEnoughOrErrors = await isBalanceEnough.executeImpl({
    userId,
    requiredBalance,
  });

  expect(isEnoughOrErrors).toBe(false);
});
it(`returns true when required balance equals existing balance`, async () => {
  const isEnoughOrErrors = await isBalanceEnough.executeImpl({
    userId,
    requiredBalance: existingBalance,
  });

  expect(isEnoughOrErrors).toBe(true);
});