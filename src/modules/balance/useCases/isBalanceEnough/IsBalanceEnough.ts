import { FunctionController } from '@shared/infra/Controllers';
import {
  Request,
  Response,
} from './IsBalanceEnoughDTOs';
import { IBalanceRepo } from '../../repos/IBalanceRepo';

export class IsBalanceEnough extends FunctionController<Request, Response> {
  private readonly balanceRepo: IBalanceRepo;

  public constructor(args: {
    balanceRepo: IBalanceRepo;
  }) {
    super();
    const { balanceRepo } = args;
    this.balanceRepo = balanceRepo;
  }

  public async executeImpl(event: Request) {
    const { userId, requiredBalance } = event;

    const last = await this.balanceRepo.last(userId);
    return last.balance.value >= requiredBalance;
  }
}