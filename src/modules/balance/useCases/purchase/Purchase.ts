import { AppSyncController } from '@shared/infra/Controllers';
import { Request, Response } from './PurchaseDTOs';
import { Movement } from '../../domain/BalanceRow';
import { PositiveInt } from '@shared/core/PositiveInt';
import { BaseError, formatErrors } from '@shared/core/AppError';
import { Status } from '@shared/core/Status';
import { getLng } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { IBalanceSrv } from '../../services/IBalanceSrv';
import { IContextProvider } from '@shared/context/IContextProvider';

export class Purchase extends AppSyncController<Request, Response> {
  private srv: IBalanceSrv;

  public constructor(args: {
    srv: IBalanceSrv;
    contextProvider: IContextProvider;
  }) {
    const { srv, contextProvider } = args;
    super({ contextProvider });
    this.srv = srv;
  }

  public async executeImpl(request: Request) {
    const { userId, lng: _lng, ...rest } = request;

    const lng = getLng(_lng);
    const vosOrErrors = validateReq({ ...rest, lng });
    if (vosOrErrors.isFailure)
      return formatErrors(vosOrErrors.errors!);

    const { credits } = vosOrErrors.value;

    const inserted = await this.srv.insertWithBackoff({
      userId,
      moveArgs: {
        movement: Movement.PURCHASE,
        change: credits,
      },
    });

    return {
      result: inserted.toDto(),
      status: Status.CREATED,
    };
  }
}

// Exported for use in frontend
export function validateReq(dto: Omit<Request, 'userId'>): Result2<{
  credits: PositiveInt;
}> {
  const errors: BaseError[] = [];
  const lng = getLng(dto.lng);

  const creditsOrErrors = PositiveInt.create({ value: dto.credits, lng });
  if (creditsOrErrors.isFailure) {
    if (!creditsOrErrors.errors) throw Error(`creditsOrErrors.errors is not defined when in failure?`);
    for (let i = 0; i < creditsOrErrors.errors.length; i++) {
      errors.push((creditsOrErrors.errors[i]).setField(`credits.${i}`));
    }
  }

  if (errors.length) {
    return Result2.fail(errors);
  }

  const credits = creditsOrErrors.value;

  return Result2.ok({
    credits,
  });
}