import { Purchase } from './Purchase';
import { BalanceRepo } from '../../repos/BalanceRepo';
import { GuardUuid } from '@shared/decorators/GuardUuid';
import { ReturnUnexpectedError } from '@shared/decorators/ReturnUnexpectedError';
import { BalanceSrv } from '../../services/BalanceSrv';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });
const sendAnalytics = contextProvider.sendAnalytics.bind(contextProvider);

const repo = new BalanceRepo(models.BalanceRow);
const srv = new BalanceSrv({ repo, sendAnalytics });
const controller = new Purchase({ srv, contextProvider });

const decorated1 = new GuardUuid({ controller, uuids: ['userId'] });
const decorated2 = new ReturnUnexpectedError({
  wrapee: decorated1,
  contextProvider: new ContextProvider({ invoker }),
});
export const handler = decorated2.execute.bind(decorated2);
