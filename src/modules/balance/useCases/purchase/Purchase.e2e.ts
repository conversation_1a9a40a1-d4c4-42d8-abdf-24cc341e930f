import { expect, it } from 'vitest';
import { AppSyncClient } from '@shared/infra/appsync/AppSyncClient';
import gql from 'graphql-tag';
import { BalanceRowsInDb } from '@shared/infra/database/sequelize/migrations/BalanceRows.json';
import { Request } from './PurchaseDTOs';
import { Movement } from '../../domain/BalanceRow';
import { dateFormat, pickLng } from '@shared/utils/test';
import { BalanceRowWithTimestampsFragment } from '../../utils/fragments';
import { rmBalanceRow } from '../../utils/test';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const lng = pickLng();
const appsync = new AppSyncClient();

const query = gql`
  mutation ($userId: ID!, $credits: Int!, $lng: String!) {
    purchase(userId: $userId, credits: $credits, lng: $lng) {
      result {
        ...BalanceRowWithTimestampsFragment
      }
      time
    }
  }
  ${BalanceRowWithTimestampsFragment}
`;

const { userId } = BalanceRowsInDb[0];

it(`purchases`, async () => {
  const credits = chance.integer({ min: 1, max: 1000 });
  const received = await appsync.send<Request>({
    query,
    variables: { userId, credits, lng },
  });

  expect(received.status).toBe(200);
  const json = await received.json();
  if (!json.data) console.log('Purchase 1', json);
  const response = json.data.purchase;
  if (!response) console.log('json', json);

  expect(response).toEqual({
    result: {
      userId,
      index: expect.any(Number),
      balance: expect.any(Number),
      movement: Movement.PURCHASE,
      change: credits,
      date: expect.stringMatching(dateFormat),
      reservationId: null,
      customerId: null,
      customerFullName: null,
      srvName: null,
      created_at: expect.stringMatching(dateFormat),
      updated_at: expect.stringMatching(dateFormat),
    },
    time: expect.stringMatching(dateFormat),
  });

  // Clean up
  await rmBalanceRow({
    userId,
    index: response.result.index,
  });
});

it(`errors if credits isn't a positive integer`, async () => {
  const credits = chance.bool()? chance.integer({ max: 0, min: -99999 }) : 0;
  const received = await appsync.send<Request>({
    query,
    variables: { userId, credits, lng },
  });

  expect(received.status).toBe(200);
  const json = await received.json();

  expect(json).toMatchObject({
    data: null,
    errors: [
      {
        errorType: expect.any(String),
        message: expect.any(String),
        errorInfo: {
          time: expect.stringMatching(dateFormat),
          errors: expect.arrayContaining([
            {
            message: expect.any(String),
            status: expect.any(Number),
            type: expect.any(String),
            field: expect.stringContaining('credits'),
          },
          ]),
        },
      },
    ],
  });
});