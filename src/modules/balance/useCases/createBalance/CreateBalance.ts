import { AsynchronousController } from '@shared/infra/Controllers';
import { Request } from './CreateBalanceDTOs';
import { IBalanceRepo } from '../../repos/IBalanceRepo';
import { BalanceRow } from '../../domain/BalanceRow';
import { IContextProvider } from '@shared/context/IContextProvider';

export class CreateBalance extends AsynchronousController<Request> {
  private repo: IBalanceRepo;

  public constructor(args: {
    repo: IBalanceRepo,
    contextProvider: IContextProvider;
  }) {
    const { repo, contextProvider } = args;
    super({ contextProvider });
    this.repo = repo;
  }

  public async executeImpl(event: Request) {
    const { aggregateId } = event;
    const row = BalanceRow.create({
      userId: aggregateId,
    });
    await this.repo.insert(row);
  }
}