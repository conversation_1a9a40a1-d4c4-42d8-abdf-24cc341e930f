import { expect, test } from 'vitest';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Request } from './CreateBalanceDTOs';
import { DomainEventTypes } from '@shared/events/DomainEventTypes';
import { createBusinessUser } from '../../utils/testExternal';
import { BalanceRepo } from '../../repos/BalanceRepo';
import { rmBalanceRow } from '../../utils/test';
import { Lambda } from '@aws-sdk/client-lambda';
import { dateFormat } from '@shared/utils/test';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('../../../../shared/infra/database/sequelize/models');
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const { createBalance } = process.env;
if (!createBalance) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}

const lambdaInvoker = new LambdaInvoker(new Lambda({}));
const repo = new BalanceRepo(models.BalanceRow);

const { BusinessUserCreatedEvent } = DomainEventTypes;

test(`initializes data for new user`, async () => {
  const userId = chance.guid({ version: 4 });
  const request: Request = {
    dateTimeOccurred: '2024-02-13T23:42Z',
    type: BusinessUserCreatedEvent,
    aggregateId: userId,
    user: createBusinessUser().dto,
  };

  await lambdaInvoker.invokeSync<Request>(
    request,
    createBalance,
  );

  const list = await repo.list(userId);
  expect(list.length).toBe(1);
  const dto = list[0].toDto();
  expect(dto).toMatchObject({
    userId,
    index: 0,
    balance: 100,
    movement: 'INITIAL_FREE_BALANCE',
    change: 100,
    reservationId: null,
    customerId: null,
    customerFullName: null,
    srvName: null,
    date: expect.stringMatching(dateFormat),
    created_at: expect.any(Date),
    updated_at: expect.any(Date),
  });

  await rmBalanceRow({ userId, index: 0 });
});
