import { CreateBalance } from './CreateBalance';
import { BalanceRepo } from '../../repos/BalanceRepo';
import { LambdaInvoker } from '@shared/infra/invocation/LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const models = require('@shared/infra/database/sequelize/models');

const invoker = new LambdaInvoker(new Lambda({}));
const contextProvider = new ContextProvider({ invoker });

const repo = new BalanceRepo(models.BalanceRow);
const controller = new CreateBalance({
  repo,
  contextProvider,
});

export const handler = controller.execute.bind(controller);