import { IBalanceRepo } from '../repos/IBalanceRepo';
import { MoveArgs } from '../domain/BalanceRow';
import { tryWithBackoff } from '../utils/utils';
import { IBalanceSrv } from './IBalanceSrv';
import { IContextProvider } from '@shared/context/IContextProvider';

export class BalanceSrv implements IBalanceSrv {
  private repo: IBalanceRepo;
  private readonly sendAnalytics: IContextProvider['sendAnalytics'];

  public constructor(args: {
    repo: IBalanceRepo;
    sendAnalytics: IContextProvider['sendAnalytics'];
  }) {
    const { repo, sendAnalytics } = args;
    this.repo = repo;
    this.sendAnalytics = sendAnalytics;
  }

  public insertWithBackoff = async (args: {
    userId: string,
    moveArgs: MoveArgs,
  }) => {
    const { userId, moveArgs } = args;

    return await tryWithBackoff({
      fn: async () => {
      const row = await this.repo.last(userId);
      row.move(moveArgs);
      console.log('Balance row to insert:', row.toDto());
      return await this.repo.insert(row);
    },
      sendAnalytics: this.sendAnalytics,
  });
  }

}