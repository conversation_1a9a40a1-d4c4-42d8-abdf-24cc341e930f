export function logAndThrow(args: { msg: string; data: unknown }): never {
  const {msg, data} = args;
  console.log(msg, data);
  throw Error(msg);
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function areObjectsEqual(obj1: any, obj2: any) {
  if (obj1 === obj2) {
    return true;
  }

  if (isNull(obj1) || isNull(obj2)) {
    return false;
  }

  if (obj1.constructor.name !== obj2.constructor.name) return false;

  const obj1flatten = flattenObject(obj1);
  const obj2flatten = flattenObject(obj2);
  return deepEqual(obj1flatten, obj2flatten);
}

// Más correcto sería poner isValidDate, pero para ser conciso dejo isDate
export function isDate(value: unknown): boolean {
  return (
    !!value &&
    // @ts-expect-error
    typeof value.getTime === 'function' &&
    // @ts-expect-error
    !Number.isNaN(value.getTime())
  );
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function flattenObject(obj: any, prefix?: string) {
  const flattened = Object();

  for (const key of Object.keys(obj)) {
    const value = obj[key];
    const prefixedKey = prefix ? prefix + key : key;

    if (isDate(value)) {
      flattened[prefixedKey] = value.getTime();
      continue;
    }

    if (isNull(value)) {
      flattened[prefixedKey] = value;
      continue;
    }

    if (Array.isArray(value)) {
      if (value.length === 0) {
        flattened[prefixedKey] = value;
        continue;
      }
      for (let i = 0; i < value.length; i++) {
        if (isNull(value[i])) {
          flattened[prefixedKey + i] = value;
          continue;
        }
        Object.assign(flattened, flattenObject(value[i], prefixedKey));
      }
    }

    if (typeof value === 'object') {
      Object.assign(flattened, flattenObject(value, prefixedKey));
    } else {
      flattened[prefixedKey] = value;
    }
  }
  return flattened;
}

function isNull(some: unknown) {
  return some === null || some === undefined;
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
function deepEqual(object1: any, object2: any) {
  const keys1 = Object.keys(object1);
  const keys2 = Object.keys(object2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    const val1 = object1[key];
    const val2 = object2[key];
    // Here we know that val1 and val2 are not arrays, as we checked it in flattenObject before, so isObject is safe. If that's not the case, and the next code would fail if val1 or val2 are arrays, we would need to add an Array.isArray check.
    const areObjects = isObject(val1) && isObject(val2);
    if ((areObjects && !deepEqual(val1, val2)) || (!areObjects && val1 !== val2)) {
      return false;
    }
  }

  return true;
}

export function isObject(object: unknown) {
  return !isNull(object) && typeof object === 'object';
}

export const uuidFormat = new RegExp(
  `^[0-9a-f]{8}\\b-[0-9a-f]{4}\\b-[0-9a-f]{4}\\b-[0-9a-f]{4}\\b-[0-9a-f]{12}$`,
);

type Diff<T, U> = T extends U ? never : T; // Remove types from T that are assignable to U

// Type of { ...L, ...R }
export type Spread<L, R> =
// Properties in L that don't exist in R
  Pick<L, Diff<keyof L, keyof R>> & R;

type DbRecord = {
  deleted_at: string | null;
  created_at: string;
  updated_at: string;
  [key: string]: unknown;
};

export function rmTimestamps(record: DbRecord): Record<string, unknown> {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const {created_at, updated_at, deleted_at, ...rest} = record;
  return rest;
}

export function nonEmpty<T>(input: T[]) {
  if (!input.length) throw Error('Array is empty');
  return input as [T, ...T[]];
}

export const isPrimitive = (value: unknown) => !(value instanceof Object);

export const possibleLngs = ['en', 'es'] as const;  // check luxon DateTime.setLocale(lng) works when adding new lngs, since setLocale is used in src/modules/user/useCases/changeBusinessPhoneSendCode/ChangeBusinessPhoneSendCodeErrors.ts
export type PossibleLngs = (typeof possibleLngs)[number];

export function getLng(_lng?: unknown): PossibleLngs {
  if (typeof _lng !== 'string') return 'en';
  switch (_lng) {
    case 'es':
    case 'en':
      return _lng;
    default:
      return 'en';
  }
}

export type OptionalLng = { lng?: PossibleLngs };

export const commonBackOffOptions = {
  // https://github.com/coveooss/exponential-backoff?tab=readme-ov-file#readme
  // Entiendo que la primera vez que se hace el request no hay delay (default delayFirstAttempt = false), en el 1er reintento el delay es 100ms (default startingDelay = 100), y empieza a hacer backoff con múltiplo 2 (default timeMultiple = 2). Es decir, 1er intento 0ms, 2do intento 100ms, 3ero 200ms, 4to 400ms, 5to 800ms, 6to 1,6s, 7mo 3,2s, 8vo 6,4s => Pongo un máximo de 8 intentos
  numOfAttempts: 8,
  jitter: 'full' as const,
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isRateLimitExceeded(e: any) {
  return e && typeof e.message === 'string' && /rate/i.test(e.message);
}

// Since this function is exported to the frontend, it's placed here. Otherwise, it would be placed in test.ts
// eslint-disable-next-line @typescript-eslint/no-unused-vars
export const sendAnalyticsDummy = (_data: unknown): Promise<void> => Promise.resolve();