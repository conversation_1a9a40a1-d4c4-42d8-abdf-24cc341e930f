import { expect } from 'vitest';
import { BaseError, BaseErrorDto } from '@shared/core/AppError';
import { StatusError, StatusSuccess } from '@shared/core/Status';
import { Slug } from '@shared/core/Slug';
import { PossibleLngs, possibleLngs } from '@shared/utils/utils';
import { Result2 } from '@shared/core/Result2';
import { IInvoker } from '@shared/infra/invocation/IInvoker';
import { Context } from 'aws-lambda';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

export const mockContext: Context = {
  awsRequestId: 'test-request-id',
  logGroupName: '/aws/lambda/test-group',
  logStreamName: 'test-stream',
  identity: {
    cognitoIdentityId: 'test-identity-id',
  },
} as unknown as Context;

export const invokerDummy = {
  invokeAsync: () => undefined,
} as unknown as IInvoker;

export function expectErrorResult(args: {
  result: unknown;
  error: string;
  code: StatusError;
}) {
  const { result, error, code } = args;
  expect(result).toMatchObject({
    isFailure: true,
    error: {
      type: error,
      message: expect.any(String),
      status: code,
    },
  });
}

export function expectErrorsResult(args: {
  result: unknown;
  error: string;
  code: StatusError;
  field?: string;
}) {
  const { result, error, code, field } = args;
  expect(result).toMatchObject({
    isFailure: true,
    errors: [{
      type: error,
      message: expect.any(String),
      status: code,
      ... field? { field } : {},
    }],
  });
}

export function expectMultipleErrorsResult(args: {
  result: Result2<unknown>;
  errors: BaseError[];
}) {
  const { result, errors } = args;
  expect(result).toMatchObject({
    isFailure: true,
    errors,
  });
}

export function expectErrorResultContaining(args: {
  result: unknown;
  errorContaining: string;
  code: StatusError;
}) {
  const { result, errorContaining, code } = args;
  expect(result).toMatchObject({
    isFailure: true,
    error: {
      type: expect.stringContaining(errorContaining),
      message: expect.any(String),
      status: code,
    },
  });
}

export function expectErrorsResultContaining(args: {
  result: unknown;
  errorContaining: string;
  code: StatusError;
}) {
  const { result, errorContaining, code } = args;
  expect(result).toMatchObject({
    isFailure: true,
    errors: [{
      type: expect.stringContaining(errorContaining),
      message: expect.any(String),
      status: code,
    }],
  });
}

export function expectErrorFieldContaining(args: {
  result: Result2<unknown>;
  errorFieldContaining: string;
  code: StatusError;
}) {
  const { result, errorFieldContaining, code } = args;
  expect(result.isFailure).toBe(true);
  if (!result.errors) throw Error(`result.errors is not defined when in failure?`);
  for (const error of result.errors) {
    expect(error).toEqual({
      field: expect.stringMatching(new RegExp(`^${errorFieldContaining}\\.`)),
      type: expect.any(String),
      message: expect.any(String),
      status: code,
    });
  }
}

export function expectErrorField(args: {
  result: unknown;
  errorField: string;
  code: StatusError;
}) {
  const { result, errorField, code } = args;
  expect(result).toMatchObject({
    isFailure: true,
    errors: [{
      field: errorField,
      type: expect.any(String),
      message: expect.any(String),
      status: code,
    }],
  });
}

// copiado de src/shared/core/ControllerResult.ts
type _ControllerResult<T> =
  | {
      status: StatusError;
      result: BaseError | [BaseError, ...BaseError[]];
    }
  | {
      status: StatusSuccess;
      result?: T;
    };
export function expectControllerError<Response>(args: {
  response: _ControllerResult<Response>;
  error: string;
  code: StatusError;
}) {
  const { response, error, code } = args;
  expect(response).toMatchObject({
    status: code,
    result: [
      {
        type: error,
        message: expect.any(String),
        status: code,
      },
    ],
  });
}

export function expectControllerErrorField<Response>(args: {
  response: _ControllerResult<Response>;
  errorField: string;
  code: StatusError;
  type?: string;
}) {
  const { response, errorField, code, type } = args;
  expect(response).toEqual({
    status: code,
    result: [
      {
        field: errorField,
        type: type ?? expect.any(String),
        message: expect.any(String),
        status: code,
      },
    ],
  });
}

export function expectControllerMultipleErrors<Response>(args: {
  response: _ControllerResult<Response>;
  errors: BaseErrorDto[];
}) {
  const { response, errors } = args;
  expect(response).toMatchObject({
    status: 400,
    result: errors,
  });
}

export function expectControllerErrorContaining<Response>(args: {
  response: _ControllerResult<Response>;
  errorContaining: string;
  code: StatusError;
  field?: string;
}) {
  const { response, errorContaining, code, field } = args;
  expect(response).toMatchObject({
    status: code,
    result: [
      {
        type: expect.stringContaining(errorContaining),
        message: expect.any(String),
        status: code,
        ...field? { field } : {},
      },
    ],
  });
}

export function expectErrorAppSync(args: {
  response: unknown;
  query: string;
  error: string;
  status: number;
  field?: string;
}) {
  const { response, error, status, field} = args;
  expect(response).toMatchObject({
    data: null,
    errors: [
      {
        errorType: error,
        message: expect.any(String),
        errorInfo: {
          time: expect.stringMatching(dateFormat),
          errors: [
            {
              message: expect.any(String),
              status,
              type: error,
              ...field? { field } : {},
            },
          ],
        },
      },
    ],
  });
}

export function expectMultipleErrorsAppSync(args: {
  response: unknown;
  errors: [BaseErrorDto, ...BaseErrorDto[]];
}) {
  const { response, errors } = args;
  expect(response).toMatchObject({
    /*data: {
      [query]: null,
    },*/
    data: null,
    errors: [
      {
        errorType: 'MultipleErrors',
        message: 'Multiple errors',
        errorInfo: {
          time: expect.stringMatching(dateFormat),
          errors,
        },
      },
    ],
  });
}

export function expectErrorAppSyncContaining(args: {
  response: unknown;
  errorContaining: string;
  status: number;
}) {
  const { response, errorContaining, status } = args;
  expect(response).toMatchObject({
    data: null,
    errors: [
      {
        errorType: expect.stringContaining(errorContaining),
        message: expect.any(String),
        errorInfo: {
          time: expect.stringMatching(dateFormat),
          errors: [
            {
              message: expect.any(String),
              status,
              type: expect.stringContaining(errorContaining),
            },
          ],
        },
      },
    ],
  });
}

export function expectError2(response: unknown) {
  expect(response).toMatchObject({
    isFailure: true,
    error: {
      type: 'Error2',
      message: 'Error2 message',
      status: 400,
    },
  });
}

export const dateFormat = new RegExp(
  `[0-9]{4}-[0-1][0-9]-[0-3][0-9][Tt][0-2][0-9]:[0-5][0-9](:[0-5][0-9])?(\\.[0-9]+)?[Zz](([+-])([01]\\d|2[0-3]):?([0-5]\\d)?)?`,
);

export const nonExistingId = '00000000-0000-0000-0000-000000000000';

export function orNull<T>(value: T): T | null {
  return chance.bool() ? value : null;
}

export function createSlug() {
  const string = chance.string({
    length: 8,
    casing: 'lower',
    alpha: true,
    numeric: true,
  });
  const slug = Slug.create({
    slug: string,
    lng: pickLng(),
  }).value;
  return { string, slug };
}

export function pickLng() {
  return chance.pickone(possibleLngs) as PossibleLngs;
}

export class Error1 extends BaseError {
  public constructor() {
    super({
      message: chance.sentence(),
      status: chance.pickone(Object.values(StatusError)),
    });
  }
}

export class Error2 extends BaseError {
  public constructor() {
    super({
      message: 'Error2 message',
      status: StatusError.BAD_REQUEST,
    });
  }
}

export function getAppsyncCtx<Args, TResult>(
  args: Args,
  result: TResult,
  error?: { message: string; type: string },
) {
  return {
    identity: null,
    args,
    arguments: args,
    result,
    source: {},
    info: {
      fieldName: '',
      parentTypeName: '',
      variables: args,
      selectionSetList: [],
      selectionSetGraphQL: '',
    },
    prev: null,
    stash: {},
    error,
    request: {headers: {}, domainName: null},
  };
}