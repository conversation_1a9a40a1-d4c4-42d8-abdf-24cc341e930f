import { expect, it, describe } from 'vitest';
import { UtilsClass } from './UtilsClass';

describe(`roundToTwoDecimals`, () => {
  const { roundToTwoDecimals } = UtilsClass;

  it(`rounds to two decimals`, () => {
    expect(roundToTwoDecimals({ input: 1.123 }).value).toBe(1.12);
    expect(roundToTwoDecimals({ input: 1.125 }).value).toBe(1.13);
    expect(roundToTwoDecimals({ input: -1.125 }).value).toBe(-1.13);
    expect(roundToTwoDecimals({ input: UtilsClass.MAX_ROUNDING - 0.005 }).value).toBe(
      UtilsClass.MAX_ROUNDING,
    );
    expect(roundToTwoDecimals({ input: -(UtilsClass.MAX_ROUNDING - 0.005) }).value).toBe(
      -UtilsClass.MAX_ROUNDING,
    );
    expect(roundToTwoDecimals({ input: UtilsClass.MAX_ROUNDING - 0.006 }).value).toBe(
      UtilsClass.MAX_ROUNDING - 0.01,
    );
    expect(roundToTwoDecimals({ input: -(UtilsClass.MAX_ROUNDING - 0.006) }).value).toBe(
      -(UtilsClass.MAX_ROUNDING - 0.01),
    );
  });

  it(`fails with a number that is too big`, () => {
    expect(roundToTwoDecimals({ input: UtilsClass.MAX_ROUNDING + 1 })).toMatchObject({
      isFailure: true,
      error: {
        message: expect.any(String),
        type: 'UtilsClassErrors.NumberTooBig',
      },
    });
  });
});
