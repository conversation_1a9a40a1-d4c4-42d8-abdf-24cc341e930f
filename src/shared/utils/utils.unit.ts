import { expect, test, it, describe } from 'vitest';
import { areObjectsEqual, isDate, isPrimitive, nonEmpty } from './utils';
import { expectTypeOf } from 'expect-type';

describe(`areObjectsEqual`, () => {
  test(`simple objects`, () => {
    expect(areObjectsEqual({ a: 1 }, { a: 1 })).toBe(true);

    expect(areObjectsEqual({ a: 1 }, { b: 1 })).toBe(false);

    expect(areObjectsEqual({ a: 1 }, { a: 2 })).toBe(false);
  });

  test(`complex objects`, () => {
    const date = new Date();
    expect(
      areObjectsEqual(
        {
          a: 1,
          b: 's',
          c: null,
          d: undefined,
          e: date,
          f: [],
          g: [1, 'e', null, []],
          h: { inner1: 5, inner2: {} },
        },
        {
          a: 1,
          b: 's',
          c: null,
          d: undefined,
          e: date,
          f: [],
          g: [1, 'e', null, []],
          h: { inner1: 5, inner2: {} },
        },
      ),
    ).toBe(true);

    expect(
      areObjectsEqual(
        {
          a: 1,
          b: 's',
          c: null,
          d: undefined,
          e: date,
          f: [],
          g: [1, 'e', null, []],
          h: { inner1: 5, inner2: {} },
        },
        {
          a: 1,
          b: 's',
          c: null,
          d: undefined,
          e: date,
          f: [],
          g: [1, 'e', null, []],
          h: { inner1: 6, inner2: {} },
        },
      ),
    ).toBe(false);
  });
});

describe(`isDate`, () => {
  it(`detects an invalid Date`, () => {
    const invalidDate = new Date('12-12-24T08:30:15.000-03:00');
    const result = isDate(invalidDate);
    expect(result).toBe(false);
  });
});

describe(`nonEmpty`, () => {
  it(`casts a possible empty array to a non-empty array`, () => {
    const array = [1];
    const result = nonEmpty(array);
    expectTypeOf(result).toEqualTypeOf<[number, ...number[]]>();
  });
  it(`throws whe the array is empty`, () => {
    const array: number[] = [];
    expect(() => nonEmpty(array)).toThrow();
  });
});

describe(`isPrimitive`, () => {
  test(`is primitive`, () => {
    expect(isPrimitive(1)).toBe(true);
    expect(isPrimitive('1')).toBe(true);
    expect(isPrimitive(true)).toBe(true);
    expect(isPrimitive(null)).toBe(true);
    expect(isPrimitive(undefined)).toBe(true);
  });

  test(`is not primitive`, () => {
    expect(isPrimitive({})).toBe(false);
    expect(isPrimitive([])).toBe(false);
    expect(isPrimitive(new Date())).toBe(false);
  });
});
