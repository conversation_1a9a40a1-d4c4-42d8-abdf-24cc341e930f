import { patch } from '@shared/core/utils';
import { BadRequest } from '@shared/core/AppError';
import { getLng, OptionalLng } from '@shared/utils/utils';

export namespace UtilsClassErrors {
  export class NumberTooBig extends BadRequest {
    public constructor(args: { input: number } & OptionalLng) {
      const { input, lng } = args;
      const t = trans[getLng(lng)];
      super({
        message: t.numberTooBig(input),
      });
    }
  }
}

const trans = {
  en: {
    numberTooBig(v: number) {
      return `Number ${v} is too big and not safe to round to two decimals.`;
    },
  },
  es: {
    numberTooBig(v: number) {
      return `El número ${v} es demasiado grande y no es seguro redondearlo a dos decimales.`;
    },
  },
};

patch({ UtilsClassErrors });
