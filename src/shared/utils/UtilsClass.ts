import { Result } from '@shared/core/Result';
import { UtilsClassErrors } from '@shared/utils/UtilsClassErrors';
import { OptionalLng } from '@shared/utils/utils';

// I need to put UtilsClass outside utils.ts because of circular dependency when running AppError.unit.ts
export class UtilsClass {
  public static MAX_ROUNDING = Math.trunc(Number.MAX_SAFE_INTEGER / 10000);
  // I put MAX_ROUNDING in order to have MAX_ROUNDING - 0.005 be correctly represented in JavaScript.
  public static roundToTwoDecimals(args: { input: number } & OptionalLng): Result<number> {
    const { input, lng } = args;
    const absolute = Math.abs(input); // e.g.: absolute = 1.125

    if (absolute > UtilsClass.MAX_ROUNDING)
      return Result.fail(new UtilsClassErrors.NumberTooBig({ input, lng }));

    const hundredths = absolute * 100; // hundredths = 112.5
    let result = Math.trunc(hundredths) / 100; // result = 1.12
    const remainder = hundredths - Math.floor(hundredths); // remainder = 0.5
    if (remainder >= 0.5) {
      result += 0.01; // result = 1.1300000000000001
    }
    result = Number(result.toFixed(2)); // result = 1.13

    return input < 0 ? Result.ok(-result) : Result.ok(result);
  }
}
