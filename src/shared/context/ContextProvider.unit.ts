import { expect, test, describe, vi, beforeEach } from 'vitest';
import { ContextProvider } from './ContextProvider';
import { IInvoker } from '@shared/infra/invocation/IInvoker';
import env from '../../../vitest.unit.env';
import { mockContext } from '@shared/utils/test';

describe('ContextProvider', () => {
  let invoker: IInvoker;
  let contextProvider: ContextProvider;

  beforeEach(() => {
    // Create a mock invoker
    invoker = {
      invokeAsync: vi.fn(),
    } as unknown as IInvoker;

    // Create a new ContextProvider instance for each test
    contextProvider = new ContextProvider({ invoker });
  });

  test('set should store the context', () => {
    contextProvider.set(mockContext);
    expect(contextProvider.sendAnalytics({ test: 'data' })).resolves.not.toThrow();
  });

  test('clear should remove the context', async () => {
    // First set the context
    contextProvider.set(mockContext);
    
    // Then clear it
    contextProvider.clear();
    
    // After clearing, sendAnalytics should throw because context is null
    await expect(contextProvider.sendAnalytics({ test: 'data' }))
      .rejects
      .toThrow('Context not set');
  });

  describe('sendAnalytics', () => {
    test('should throw error when context is not set', async () => {
      await expect(contextProvider.sendAnalytics({ test: 'data' }))
        .rejects
        .toThrow('Context not set');
    });

    test('should call invoker.invokeAsync with correct parameters when context is set', async () => {
      // Set the context
      contextProvider.set(mockContext);
      
      // Test data to send
      const testData = { test: 'data' };
      
      // Call sendAnalytics
      await contextProvider.sendAnalytics(testData);
      
      // Verify invoker.invokeAsync was called with the correct parameters
      expect(invoker.invokeAsync).toHaveBeenCalledWith(
        {
          logStream: mockContext.logStreamName,
          logGroup: 'test-group', // This is the last part of logGroupName after splitting by '/'
          requestId: mockContext.awsRequestId,
          cognitoIdentityId: mockContext.identity?.cognitoIdentityId,
          date: expect.any(String),
          data: testData,
        },
        env.sendAnalyticsEvent
      );
    });

    test('should handle context without identity', async () => {
      // Create a context without identity
      const contextWithoutIdentity = {
        ...mockContext,
        identity: undefined,
      };
      
      // Set the context
      contextProvider.set(contextWithoutIdentity);
      
      // Call sendAnalytics
      await contextProvider.sendAnalytics({ test: 'data' });
      
      // Verify invoker.invokeAsync was called with null for cognitoIdentityId
      expect(invoker.invokeAsync).toHaveBeenCalledWith(
        expect.objectContaining({
          cognitoIdentityId: null,
        }),
        env.sendAnalyticsEvent
      );
    });
  });
});
