import { IContextProvider, Context } from './IContextProvider';
import { IInvoker } from '@shared/infra/invocation/IInvoker';
import { Request as SendAnalyticsRequest } from '../../modules/analytics/useCases/send/SendDTOs';

const { sendAnalyticsEvent } = process.env;
if (!sendAnalyticsEvent) {
  console.log('process.env ContextProvide.ts', process.env);
  throw new Error(`Undefined env var!`);
}

export class ContextProvider implements IContextProvider {
  private context: Context | null = null;
  private readonly _invoker: IInvoker;

  public constructor(args: {
    invoker: IInvoker,
  }) {
    const { invoker } = args;
    this._invoker = invoker;
  }

  public set(context: Context): void {
    this.context = context;
  }

  public clear(): void {
    this.context = null;
  }

  public invoker(): IInvoker {
    return this._invoker;
  }

  public async sendAnalytics(data: unknown) {
    if (!this.context)
      throw new Error('Context not set');

    await this._invoker.invokeAsync<SendAnalyticsRequest>({
      logStream: this.context.logStreamName,
      logGroup: this.context.logGroupName.split('/').pop()!,
      requestId: this.context.awsRequestId,
      cognitoIdentityId: this.context.identity?.cognitoIdentityId ?? null,
      data,
      date: new Date().toJSON(),
    }, sendAnalyticsEvent!);

  }
}