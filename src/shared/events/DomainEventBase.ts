import { DomainEventTypes } from './DomainEventTypes';
import { Dat } from '@shared/core/Dat';

type DomainEventBaseInput = {
  aggregateId: string;
  type: DomainEventTypes;
};

export type DomainEventBaseDto = {
  dateTimeOccurred: string;
  type: string;
  aggregateId: string;
}

export abstract class DomainEventBase {
  public dateTimeOccurred: string;
  public type: DomainEventTypes;
  public aggregateId: string;

  protected constructor(input: DomainEventBaseInput) {
    const { aggregateId, type } = input;
    this.dateTimeOccurred = Dat.create().value.s;
    this.aggregateId = aggregateId;
    this.type = type;
  }

  protected _toDto(): DomainEventBaseDto {
    return {
      dateTimeOccurred: this.dateTimeOccurred,
      type: DomainEventTypes[this.type],
      aggregateId: this.aggregateId,
    };
  }
}
