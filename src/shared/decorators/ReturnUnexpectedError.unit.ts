import { expect, test, vi } from 'vitest';
import { ExeResponse } from './IDecorator';
import { ReturnUnexpectedError } from './ReturnUnexpectedError';
import { Context } from 'aws-lambda';
import { ContextProvider } from '@shared/context/ContextProvider';
import {
  invokerDummy as invoker,
} from '@shared/utils/test';
import {
  sendAnalyticsDummy as sendAnalytics,
} from '@shared/utils/utils';

const contextProvider = {
  set: vi.fn(),
  clear: vi.fn(),
  sendAnalytics,
  invoker,
} as unknown as ContextProvider;

test('Return unexpected error when sequelize transaction errors', async () => {
  const dummyController = {
    execute(): ExeResponse {
      throw Error('Simulated error');
    },
  };
  const decorated = new ReturnUnexpectedError({
    wrapee: dummyController,
    contextProvider,
  });
  const result = await decorated.execute(Object(), {
    logGroupName: 'logGroupName',
    awsRequestId: 'awsRequestId',
    logStreamName: 'logStreamName',
    identity: {
      cognitoIdentityId: 'cognitoIdentityId',
    },
  } as Context);

  expect(result).toMatchObject({
    errorType: 'UnexpectedError',
    errors: [{
      type: 'UnexpectedError',
      status: 500,
      logGroup: 'logGroupName',
      requestId: 'awsRequestId',
    }],
  });
});
