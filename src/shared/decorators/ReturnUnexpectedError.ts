import { AppSyncResolverEvent, Context } from 'aws-lambda';
import { Envelope } from '../core/Envelope';
import { ExeResponse, IDecorator } from './IDecorator';
import { UnexpectedError } from '@shared/decorators/UnexpectedError';
import { ContextProvider } from '@shared/context/ContextProvider';

export class ReturnUnexpectedError<Request> implements IDecorator<Request> {
  public wrapee: IDecorator<Request>['wrapee'];
  private readonly contextProvider: ContextProvider;

  public constructor(args: {
    wrapee: IDecorator<Request>['wrapee'],
    contextProvider: ContextProvider;
  }) {
    const { wrapee, contextProvider } = args;
    this.wrapee = wrapee;
    this.contextProvider = contextProvider;
  }

  public async execute(
    event: AppSyncResolverEvent<Request>,
    context: Context,
  ): ExeResponse {
    console.log(`${this.constructor.name}.execute`, { event, context });

    try {
      return await this.wrapee.execute(event, context);
    } catch (error) {
      console.log(
        `An unexpected error occurred @ ${this.constructor.name}.execute`,
        error,
      );
      console.log(`Context`, context);
      console.log(`Event`, event);
      const { logGroupName, awsRequestId: requestId } = context;
      // @ts-expect-error lng may exist
      const lng = event.arguments?.lng? event.arguments.lng : undefined;
      const logGroup = logGroupName.split('/').pop()!;
      const unexpected = new UnexpectedError({
        logGroup,
        requestId,
        lng,
      });
      console.log({unexpected});

      this.contextProvider.set(context);
      await this.contextProvider.sendAnalytics({
        unexpected,
      });
      this.contextProvider.clear();

      return Envelope.errors([unexpected]);
    }
  }
}
