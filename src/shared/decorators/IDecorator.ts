import { AppSyncResolverEvent, Context } from 'aws-lambda';
import { Envelope } from '../core/Envelope';

export type ExeResponse = Promise<
  // ...if A<PERSON><PERSON><PERSON><PERSON><PERSON>roll<PERSON> completed the use case successfully
  | Envelope<unknown>
  // ...if <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>roll<PERSON> couldn't complete the use case because of one or multiple expected possible errors
  | Envelope<undefined>
>;

export interface IDecorator<Request> {
  execute(event: AppSyncResolverEvent<Request>, context: Context): ExeResponse;
  wrapee: {
    execute: IDecorator<Request>['execute'];
  };
}
