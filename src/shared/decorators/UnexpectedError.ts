import { StatusError } from '@shared/core/Status';
import { getLng, OptionalLng } from '@shared/utils/utils';

const type = 'UnexpectedError';
const status = StatusError.INTERNAL_ERROR;

export type UnexpectedErrorDto = {
  type: typeof type;
  message: string;
  status: typeof status;
  logGroup: string;
  requestId: string;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export function isUnexpectedError(test: any): test is UnexpectedError {
  const {
    type: _type,
    message,
    status: _status,
    logGroup,
    requestId,
  } = test;
  return _type === type &&
    (message === trans.en.message || message === trans.es.message) &&
    _status === status &&
    typeof logGroup === 'string' &&
    typeof requestId === 'string';
}

export class UnexpectedError {
  public readonly type = type;
  public message: string;
  public readonly status = status;
  public logGroup: string;
  public requestId: string;

  public toDto(): UnexpectedErrorDto {
    const { type, message, status, logGroup, requestId } = this;
    return {
      type,
      message,
      status,
      logGroup,
      requestId,
    };
  }

  public constructor(args: {
    logGroup: string;
    requestId: string;
  } & OptionalLng) {
    const { logGroup, requestId, lng } = args;
    if (!logGroup || !requestId) throw Error(`logGroup and requestId are required`);
    this.logGroup = logGroup;
    this.requestId = requestId;
    const t = trans[getLng(lng)];
    this.message = t.message;
  }

  /*public static assemble(args: UnexpectedErrorDto & OptionalLng) {
    return new UnexpectedError(args);
  }*/
}

const trans = {
  en: {
    message: `Unexpected error`,
  },
  es: {
    message: `Error inesperado`,
  },
};