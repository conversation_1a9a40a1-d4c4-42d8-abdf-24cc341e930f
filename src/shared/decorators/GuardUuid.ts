import { AppSyncResolverEvent, Context } from 'aws-lambda';
import { Envelope } from '../core/Envelope';
import { ExeResponse, IDecorator } from './IDecorator';
import { Guard } from '@shared/core/Guard';
import { GuardUuidErrors } from '@shared/decorators/GuardUuidErrors';
import { BaseError } from '@shared/core/AppError';

// For those AppSyncController that receive ids mapping to DataType.UUID in Sequelize, we should guard them to avoid SequelizeDatabaseError.
// For those ids that map to DataTypes.TEXT, this is not necessary.
export class GuardUuid<Request> implements IDecorator<Request> {
  public wrapee: IDecorator<Request>['wrapee'];
  private uuids: string[];

  public constructor(args: {
    controller: IDecorator<Request>['wrapee'];
    uuids: string[];
  }) {
    const { controller, uuids } = args;
    if (!uuids.length) throw Error('uuids must have at least one element');
    this.uuids = uuids;
    this.wrapee = controller;
  }

  public async execute(
    event: AppSyncResolverEvent<Request>,
    context: Context,
  ): ExeResponse {
    // console.log(`${this.constructor.name}.execute`);

    const errors: BaseError[] = [];
    for (const field of this.uuids) {
      // @ts-expect-error
      const value = event.arguments[field];
      if (typeof value !== 'string') {
        console.log({ uuids: this.uuids, arguments: event.arguments, field});
        errors.push(new GuardUuidErrors.NotString(field));
      } else {
        const guardUuid = Guard.isUuid(value, new GuardUuidErrors.Format(field));
        if (guardUuid.isFailure)
          errors.push(guardUuid.error!);
      }
    }

    if (errors.length)
      return Envelope.errors(errors as [BaseError, ...BaseError[]]);

    return await this.wrapee.execute(event, context);
  }
}
