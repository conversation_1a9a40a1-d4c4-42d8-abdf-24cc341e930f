import { test, expect, describe, it } from 'vitest';
import { isUnexpectedError, UnexpectedError } from './UnexpectedError';
import { BadRequest } from '@shared/core/AppError';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const logGroup = chance.string({ alpha: true, numeric: true, length: 15 });
const requestId = chance.string({ alpha: true, numeric: true, length: 15 });
const status = 500;
const type = 'UnexpectedError';
const enMsg = 'Unexpected error';
const esMsg = 'Error inesperado';

describe('creation', () => {
  describe('succeeds', () => {
    test('with English language', () => {
      const lng = 'en';
      const error = new UnexpectedError({
        logGroup,
        requestId,
        lng,
      });

      /*expect(error.toDto()).toEqual(UnexpectedError.assemble({
        lng,*/
      expect(error.toDto()).toEqual({
        type,
        message: enMsg,
        status,
        logGroup,
        requestId,
      });
    });

    test('with Spanish language', () => {
      const lng = 'es';
      const error = new UnexpectedError({
        logGroup,
        requestId,
        lng,
      });
      /*expect(error.toDto()).toEqual(UnexpectedError.assemble({
        lng,*/
      expect(error.toDto()).toEqual({
        type,
        message: esMsg,
        status,
        logGroup,
        requestId,
      });
    });

    test('without language defaulting to English', () => {
      const error = new UnexpectedError({
        logGroup,
        requestId,
      });
      // expect(error.toDto()).toEqual(UnexpectedError.assemble({
      expect(error.toDto()).toEqual({
        type,
        message: enMsg,
        status,
        logGroup,
        requestId,
      });
    });
  });

  it(`fails when logGroup or requestId isn't defined`, () => {
    let logGroup = chance.string({ alpha: true, numeric: true, length: 15 });
    let requestId = chance.string({ alpha: true, numeric: true, length: 15 });
    if (chance.bool())
      logGroup = '';
    else
      requestId = '';

    expect(() => new UnexpectedError({
     logGroup,
     requestId,
    })).toThrowError();
  });

})

describe('isUnexpectedError', () => {
  it('succeeds for UnexpectedError', () => {
    const error = new UnexpectedError({
      logGroup,
      requestId,
    });
    expect(isUnexpectedError(error)).toBe(true);
  });

  test('fails for a different error', () => {
    class BadRequest1 extends BadRequest {
      public constructor() {
        super({ message: 'test message' });
      }
    }
    const badReq = new BadRequest1();
    expect(isUnexpectedError(badReq)).toBe(false);
  })

});
