import { expect, it, describe, test } from 'vitest';
import { ExeResponse } from './IDecorator';
import { GuardUuid } from './GuardUuid';
import { getAppsyncCtx } from '@shared/utils/test';
import { Envelope } from '@shared/core/Envelope';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

const controller = {
  async execute(): ExeResponse {
    return Envelope.ok();
  },
};

it(`doesn't return error when the id follows UUID format`, async () => {
  const decorated = new GuardUuid({ controller, uuids: ['userId'] });
  const result = await decorated.execute(
    getAppsyncCtx(
      {
        userId: chance.guid(),
      },
      {},
    ),
    Object(),
  );

  expect(result).toMatchObject({
    errorType: undefined,
    errorMessage: undefined,
    errors: undefined,
  });
});

describe('returns error', () => {
  it(`Format when an id doesn't follow UUID format`, async () => {
    const decorated = new GuardUuid({ controller, uuids: ['userId'] });
    const result = await decorated.execute(
      getAppsyncCtx(
        {
          userId: 'invalid uuid',
        },
        {},
      ),
      Object(),
    );

    expect(result).toMatchObject({
      errorType: 'GuardUuidErrors.Format',
      errorMessage: expect.stringContaining('userId'),
      errors: [
        {
          type: 'GuardUuidErrors.Format',
          message: expect.stringContaining('userId'),
          status: 400,
        },
      ],
    });
  });

  it(`Format when there are valid and invalid ids`, async () => {
    const decorated = new GuardUuid({
      controller,
      uuids: ['userId1', 'userId2', 'userId3'],
    });
    const result = await decorated.execute(
      getAppsyncCtx(
        {
          userId1: chance.guid(),
          userId2: chance.guid(),
          userId3: 'invalid uuid',
        },
        {},
      ),
      Object(),
    );

    expect(result).toMatchObject({
      errorType: 'GuardUuidErrors.Format',
      errorMessage: expect.stringContaining('userId3'),
      errors: [
        {
          type: 'GuardUuidErrors.Format',
          message: expect.stringContaining('userId3'),
          status: 400,
        },
      ],
    });
  });

  test(`NotString when id isn't found in the incoming event`, async () => {
    const decorated = new GuardUuid({ controller, uuids: ['userId'] });
    const result = await decorated.execute(
      getAppsyncCtx(
        {
          userId2: chance.guid(),
        },
        {},
      ),
      Object(),
    );

    expect(result).toMatchObject({
      errorType: 'GuardUuidErrors.NotString',
      errorMessage: expect.stringContaining('userId'),
      errors: [
        {
          type: 'GuardUuidErrors.NotString',
          message: expect.stringContaining('userId'),
          status: 400,
        },
      ],
    });

  });
});

describe('throws when', () => {
  test('uuids is empty', () => {
    expect(() => new GuardUuid({ controller, uuids: [] })).toThrow();
  });
});
