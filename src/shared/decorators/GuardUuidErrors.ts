import { BadRequest } from '@shared/core/AppError';
import { patch } from '@shared/core/utils';

export namespace GuardUuidErrors {
  export class Format extends BadRequest {
    public constructor(id: string) {
      // Cuando es llamado x src/modules/user/useCases/getCustomerUsers/GetCustomerUsers.ts, id se refiere al valor
      // ..cuando es llamado x src/shared/decorators/GuardUuid.ts, id se refiere al campo
      super({
        message: `El id "${id}" no cumple el formato UUID.`,
      });
    }
  }
  export class NotString extends BadRequest {
    public constructor(field: string) {
      super({
        message: `El campo de id "${field}" debe ser de tipo "string"`,
      });
    }
  }
}

patch({ GuardUuidErrors });
