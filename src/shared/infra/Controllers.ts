import { AppSyncResolverEvent, Context } from 'aws-lambda';
import { Envelope } from '../core/Envelope';
import { BaseError, isBaseErrorArray } from '../core/AppError';
import { ControllerResult } from '../core/ControllerResult';
import { possibleSuccesses } from '@shared/core/Status';
import { UnexpectedError } from '@shared/decorators/UnexpectedError';
import { Result2 } from '@shared/core/Result2';
import { IContextProvider } from '../context/IContextProvider';

// region synchronous requests

// AppSyncController is meant for frontend
export abstract class AppSyncController<Request, Response> {
  protected readonly contextProvider: IContextProvider;

  protected constructor(args: {
    contextProvider: IContextProvider;
  }) {
    const { contextProvider } = args;
    this.contextProvider = contextProvider;
  }

  protected abstract executeImpl(
    dto: Request
  ): ControllerResult<Response>;

  public async execute(
    event: AppSyncResolverEvent<Request>,
    context: Context,
  ): Promise<Envelope<BaseError | [BaseError, ...BaseError[]] | [UnexpectedError, ...UnexpectedError[]] | Response | undefined>> {
    console.log(`${this.constructor.name}.execute`);

    try {
      this.contextProvider.set(context);

      const implResult = await this.executeImpl(event.arguments);

      if (possibleSuccesses.includes(implResult.status))
        return Envelope.ok(implResult.result);

      if (isBaseErrorArray(implResult.result))
        return Envelope.errors(implResult.result);

      console.log('implResult', implResult);
      throw Error(
        `implResult isn't successful nor its result an array of BaseError`,
      );
    } finally {
      this.contextProvider.clear();
    }
  }
}

// FunctionController and FunctionController2 are meant for being called by a parent controller as simple functions. There is no AppSync or Lambda involved.
// FunctionController.executeImpl doesn't return errors, returns Response
export abstract class FunctionController<Request, Response> {
  public abstract executeImpl(dto: Request): Promise<Response>;
}
export type FCHandler<Req, Res> = FunctionController<Req, Res>['executeImpl'];  // (dto: Req) => Promise<Res>
// FunctionController2.executeImpl may return errors, returns Result2
export abstract class FunctionController2<Request, Response> {
  public abstract executeImpl(dto: Request): Promise<Result2<Response>>;
}
export type FC2Handler<Req, Res> = FunctionController2<Req, Res>['executeImpl'];  // (dto: Req) => Promise<Result2<Res>>

// endregion

// region asynchronous requests
// AsynchronousController is meant for controllers implemented as lambdas to subscribe to domain events or decouple from the parent controller called by.
export abstract class AsynchronousController<Request> {
  protected readonly contextProvider: IContextProvider;

  protected constructor(args: {
    contextProvider: IContextProvider;
  }) {
    const { contextProvider } = args;
    this.contextProvider = contextProvider;
  }

  public async execute(event: Request, context: Context): Promise<void> {
    // console.log(`${this.constructor.name}.execute`);

    try {
      this.contextProvider.set(context);

      await this.executeImpl(event);
    } finally {
      this.contextProvider.clear();
    }
  }

  protected abstract executeImpl(event: Request): Promise<void>;
}
// endregion