'use strict';
/* eslint-disable @typescript-eslint/no-var-requires */
const {DataTypes} = require('sequelize');

const attributes = {
  // Composite key
  userId: {
    type: DataTypes.UUID,
    allowNull: false,
    primaryKey: true,
  },
  index: {
    type: DataTypes.INTEGER,
    allowNull: false,
    primaryKey: true,
  },

  balance: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  movement: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  change: {
    type: DataTypes.INTEGER,
    allowNull: false,
  },
  reservationId: {
    type: DataTypes.UUID,
  },
  customerId: {
    type: DataTypes.UUID,
  },
  customerFullName: {
    type: DataTypes.TEXT,
  },
  srvName: {
    type: DataTypes.TEXT,
  },
  date: {
    type: DataTypes.DATE,
    allowNull: false,
  },

  // These timestamps aren't included in the BalanceRow domain model, they are handled by <PERSON>quelize. Configured in ../config/config.js
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  // Destroy deletes the row, otherwise it wouldn't allow the insertion of the next index
  /*deleted_at: {
    type: DataTypes.DATE,
  },*/
};

module.exports = {
  connect: (connection) => {
    return connection.define('balance_row', attributes,
      // Destroy deletes the row, otherwise it wouldn't allow the insertion of the next index
      /*{
        paranoid: true,
        deletedAt: 'deleted_at',
      }*/
    );
  },
  attributes,
};
