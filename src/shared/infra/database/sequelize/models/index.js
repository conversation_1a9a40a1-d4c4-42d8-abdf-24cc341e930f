/* eslint-disable @typescript-eslint/no-var-requires,no-undef */ // @ts-expect-error
const config = require('../config/config');

const sequelize = config.connection;

const ReservationOption = require('./reservationOption.js').connect(sequelize);
const CustomerUser = require('./customerUser.js').connect(sequelize);
const BusinessUser = require('./businessUser.js').connect(sequelize);
const ManuallyCreatedCustomer = require('./manuallyCreatedCustomer.js').connect(
  sequelize,
);
const Reservation = require('./reservation.js').connect(sequelize);
const Calendar = require('./calendar.js').connect(sequelize);
const GeneralSettingsForReservation = require('./gsForReservation.js').connect(
  sequelize,
);
const BalanceRow = require('./balanceRow.js').connect(sequelize);

module.exports = {
  BusinessUser,
  CustomerUser,
  ManuallyCreatedCustomer,
  ReservationOption,
  Reservation,
  Calendar,
  GeneralSettingsForReservation,
  BalanceRow,
};
