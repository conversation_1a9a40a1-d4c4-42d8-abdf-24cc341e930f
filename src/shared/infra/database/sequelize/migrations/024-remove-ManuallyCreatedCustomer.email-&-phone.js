'use strict';

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn('manually_created_customers', 'email', {
        transaction,
      });
      await queryInterface.removeColumn('manually_created_customers', 'phone', {
        transaction,
      });

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'manually_created_customers',
        'email',
        {
          type: DataTypes.TEXT,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'manually_created_customers',
        'phone',
        {
          type: DataTypes.TEXT,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
