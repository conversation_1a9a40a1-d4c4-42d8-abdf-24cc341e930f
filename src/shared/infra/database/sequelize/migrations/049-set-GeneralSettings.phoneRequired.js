'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      const records = await queryInterface.sequelize.query(
        `
        SELECT * FROM gs_for_reservations;
      `,
        { transaction },
      );
      for (const record of records[0]) {
        const { id } = record;

        await queryInterface.sequelize.query(
          `
          UPDATE gs_for_reservations SET "phoneRequired" = true WHERE id = '${id}';
        `,
          { transaction },
        );
      }

      await queryInterface.changeColumn(
        'gs_for_reservations',
        'phoneRequired',
        {
          type: DataTypes.BOOLEAN,
          allowNull: false,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.changeColumn(
        'gs_for_reservations',
        'phoneRequired',
        {
          type: DataTypes.BOOLEAN,
          allowNull: true,
        },
        { transaction },
      );

      const records = await queryInterface.sequelize.query(
        `
        SELECT * FROM gs_for_reservations;
      `,
        { transaction },
      );
      for (const record of records[0]) {
        const { id } = record;

        await queryInterface.sequelize.query(
          `
          UPDATE gs_for_reservations SET "phoneRequired" = null WHERE id = '${id}';
        `,
          { transaction },
        );
      }

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};