'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.changeColumn(
        'business_users',
        'firstName',
        {
          type: DataTypes.STRING,
          allowNull: true,
        },
        { transaction }
      );
      await queryInterface.changeColumn(
        'business_users',
        'lastName',
        {
          type: DataTypes.STRING,
          allowNull: true,
        },
        { transaction }
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.changeColumn(
        'business_users',
        'firstName',
        {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        { transaction }
      );
      await queryInterface.changeColumn(
        'business_users',
        'lastName',
        {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        { transaction }
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
