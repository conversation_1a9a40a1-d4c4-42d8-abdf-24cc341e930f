'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'manually_created_customers',
        'notes',
        {
          type: DataTypes.TEXT,
        },
        { transaction }
      );
      await queryInterface.addColumn(
        'manually_created_customers',
        'fullName',
        {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        { transaction }
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(
        'manually_created_customers',
        'notes',
        { transaction },
      );
      await queryInterface.removeColumn(
        'manually_created_customers',
        'fullName',
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
