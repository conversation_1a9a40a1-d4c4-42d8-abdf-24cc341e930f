'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'reservations',
        'cancelReservationEventError',
        {
          type: DataTypes.TEXT,
          defaultValue: null,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'reservations',
        'cancelReservationEventErrorFixable',
        {
          type: DataTypes.BOOLEAN,
          defaultValue: null,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'reservations',
        'cancelReservationEventResult',
        {
          type: DataTypes.JSON,
          defaultValue: null,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(
        'reservations',
        'cancelReservationEventError',
        {
          transaction,
        },
      );
      await queryInterface.removeColumn(
        'reservations',
        'cancelReservationEventErrorFixable',
        {
          transaction,
        },
      );
      await queryInterface.removeColumn(
        'reservations',
        'cancelReservationEventResult',
        {
          transaction,
        },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
