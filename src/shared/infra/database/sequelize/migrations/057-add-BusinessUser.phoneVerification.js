'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.addColumn(
        'business_users',
        'phoneVerificationAttempts',
        {
          type: DataTypes.ARRAY(DataTypes.DATE),
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'business_users',
        'phoneVerificationBlockedUntil',
        {
          type: DataTypes.DATE,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'business_users',
        'phoneVerificationCode',
        {
          type: DataTypes.INTEGER,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'business_users',
        'phoneVerificationCodeExpiresAt',
        {
          type: DataTypes.DATE,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.removeColumn(
        'business_users',
        'phoneVerificationAttempts',
        { transaction },
      );

      await queryInterface.removeColumn(
        'business_users',
        'phoneVerificationBlockedUntil',
        { transaction },
      );

      await queryInterface.removeColumn(
        'business_users',
        'phoneVerificationCode',
        { transaction },
      );

      await queryInterface.removeColumn(
        'business_users',
        'phoneVerificationCodeExpiresAt',
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
