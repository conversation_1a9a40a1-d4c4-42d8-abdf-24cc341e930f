'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.addColumn(
        'balance_rows',
        'reservationId',
        {
          type: DataTypes.UUID,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'balance_rows',
        'customerFullName',
        {
          type: DataTypes.TEXT,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'balance_rows',
        'srvName',
        {
          type: DataTypes.TEXT,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.removeColumn(
        'balance_rows',
        'reservationId',
        {
          transaction,
        },
      );
      await queryInterface.removeColumn(
        'balance_rows',
        'customerFullName',
        {
          transaction,
        },
      );
      await queryInterface.removeColumn(
        'balance_rows',
        'srvName',
        {
          transaction,
        },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};