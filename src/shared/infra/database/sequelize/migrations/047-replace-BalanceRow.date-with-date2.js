'use strict';

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.removeColumn(
        'balance_rows',
        'date',
        {
          transaction,
        },
      );

      await queryInterface.renameColumn(
        'balance_rows',
        'date2',
        'date',
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      // In the case of wanting to revert this migration, we need to create migrations similar to 046-add-BalanceRow.date2.js and this one
      console.log('One way migration, no down migration for this migration');

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};