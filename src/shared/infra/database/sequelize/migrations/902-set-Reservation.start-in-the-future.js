'use strict';

module.exports = {
  async up(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      // Modify reservations start time to the day after tomorrow to avoid ReservationErrors.ServiceStartTimeInPast when cancelling
      const tomorrow = new Date(new Date().getTime() + 1000 * 60 * 60 * 24 * 2).toJSON();

      await queryInterface.sequelize.query(
        `
        UPDATE reservations SET "start" = '${tomorrow}';
      `,
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      console.log('No down migration for this migration');

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};