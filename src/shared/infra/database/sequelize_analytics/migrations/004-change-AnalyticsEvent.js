'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.removeColumn(
        'analytics_events',
        'module',
        {
          transaction,
        },
      );
      await queryInterface.removeColumn(
        'analytics_events',
        'file',
        {
          transaction,
        },
      );
      await queryInterface.removeColumn(
        'analytics_events',
        'ref',
        {
          transaction,
        },
      );

      await queryInterface.addColumn(
        'analytics_events',
        'logStream',
        {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'analytics_events',
        'logGroup',
        {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'analytics_events',
        'requestId',
        {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'analytics_events',
        'cognitoIdentityId',
        {
          type: DataTypes.TEXT,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      console.log('No down migration created for this migration');

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
