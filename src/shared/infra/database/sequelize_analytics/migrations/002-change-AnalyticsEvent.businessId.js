'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.removeColumn(
        'analytics_events',
        'businessId',
        {
          transaction,
        },
      );
      await queryInterface.addColumn(
        'analytics_events',
        'businessId',
        {
          type: DataTypes.UUID,
          allowNull: true,
        },
        { transaction },
      );

      await queryInterface.removeColumn(
        'analytics_events',
        'customerId',
        {
          transaction,
        },
      );
      await queryInterface.addColumn(
        'analytics_events',
        'customerId',
        {
          type: DataTypes.UUID,
          allowNull: true,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      console.log('No down migration created for this migration');

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
