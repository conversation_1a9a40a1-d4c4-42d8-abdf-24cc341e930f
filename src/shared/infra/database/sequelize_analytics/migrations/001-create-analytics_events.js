'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    await queryInterface.createTable(
      'analytics_events',
      {
        id: {
          type: DataTypes.UUID,
          allowNull: false,
          primaryKey: true,
        },
        businessId: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        customerId: {
          type: DataTypes.TEXT,
        },
        module: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        logGroup: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        file: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        requestId: {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        data: {
          type: DataTypes.JSON,
          allowNull: false,
        },
        date: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        created_at: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        updated_at: {
          type: DataTypes.DATE,
          allowNull: false,
        },
        deleted_at: {
          type: DataTypes.DATE,
        },
      },
    );
  },
  async down(queryInterface) {
    await queryInterface.dropTable('analytics_events');
  },
};
