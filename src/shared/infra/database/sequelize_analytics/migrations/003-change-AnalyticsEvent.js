'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.removeColumn(
        'analytics_events',
        'businessId',
        {
          transaction,
        },
      );
      await queryInterface.removeColumn(
        'analytics_events',
        'customerId',
        {
          transaction,
        },
      );
      await queryInterface.removeColumn(
        'analytics_events',
        'logGroup',
        {
          transaction,
        },
      );
      await queryInterface.removeColumn(
        'analytics_events',
        'requestId',
        {
          transaction,
        },
      );
      await queryInterface.removeColumn(
        'analytics_events',
        'data',
        {
          transaction,
        },
      );

      await queryInterface.addColumn(
        'analytics_events',
        'data',
        {
          type: DataTypes.JSONB,
          allowNull: false,
        },
        { transaction },
      );

      await queryInterface.addColumn(
        'analytics_events',
        'ref',
        {
          type: DataTypes.TEXT,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      console.log('No down migration created for this migration');

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
