'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {
      await queryInterface.changeColumn(
        'analytics_fronts',
        'userId',
        {
          type: DataTypes.UUID,
          allowNull: true,
        },
        { transaction }
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.changeColumn(
        'analytics_fronts',
        'userId',
        {
          type: DataTypes.UUID,
          allowNull: false,
        },
        { transaction }
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
