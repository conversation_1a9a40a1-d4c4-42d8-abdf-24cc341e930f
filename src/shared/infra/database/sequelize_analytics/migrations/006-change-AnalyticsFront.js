'use strict';

module.exports = {
  async up(queryInterface, DataTypes) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      await queryInterface.removeColumn(
        'analytics_fronts',
        'data',
        {
          transaction,
        },
      );

      await queryInterface.addColumn(
        'analytics_fronts',
        'label',
        {
          type: DataTypes.TEXT,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'analytics_fronts',
        'appState',
        {
          type: DataTypes.JSONB,
          allowNull: false,
        },
        { transaction },
      );
      await queryInterface.addColumn(
        'analytics_fronts',
        'ua',
        {
          type: DataTypes.JSONB,
          allowNull: false,
        },
        { transaction },
      );

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
  async down(queryInterface) {
    const transaction = await queryInterface.sequelize.transaction();
    try {

      console.log('No down migration created for this migration');

      await transaction.commit();
    } catch (err) {
      await transaction.rollback();
      throw err;
    }
  },
};
