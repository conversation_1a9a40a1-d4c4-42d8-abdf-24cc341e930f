/* eslint-disable @typescript-eslint/no-var-requires,no-undef */
const pg = require('pg');
const { Sequelize } = require('sequelize');
require('dotenv').config();

// Workaround to fix DECIMALs being returned as strings instead of numbers (make sure that your data won't run into precision issues)
Sequelize.postgres.DECIMAL.parse = function (value) {
  return parseFloat(value);
};
pg.defaults.parseInt8 = true;

const {
  COCKROACH_ANALYTICS_username,
  COCKROACH_ANALYTICS_password,
  COCKROACH_ANALYTICS_database,
  COCKROACH_ANALYTICS_host,
  COCKROACH_ANALYTICS_dialect,
  COCKROACH_ANALYTICS_port,
  COCKROACH_ANALYTICS_cluster,
} = process.env;

if (!COCKROACH_ANALYTICS_username || !COCKROACH_ANALYTICS_password ||
  !COCKROACH_ANALYTICS_database || !COCKROACH_ANALYTICS_host || !COCKROACH_ANALYTICS_dialect ||
  !COCKROACH_ANALYTICS_port || !COCKROACH_ANALYTICS_cluster
) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}

const databaseCredentials = {
  username: COCKROACH_ANALYTICS_username,
  password: COCKROACH_ANALYTICS_password,
  database: COCKROACH_ANALYTICS_database,
  host: COCKROACH_ANALYTICS_host,
  dialect: COCKROACH_ANALYTICS_dialect,
  dialectModule: pg,
  port: COCKROACH_ANALYTICS_port,
  dialectOptions: {
    options: `--cluster=${COCKROACH_ANALYTICS_cluster}`,
    ssl: {},
  },
};

module.exports = databaseCredentials;

let connection = new Sequelize(
  databaseCredentials.database,
  databaseCredentials.username,
  databaseCredentials.password,
  {
    host: databaseCredentials.host,
    dialect: databaseCredentials.dialect,
    dialectModule: pg,
    port: databaseCredentials.port,
    dialectOptions: databaseCredentials.dialectOptions,
    // According to https://sequelize.org/docs/v6/other-topics/aws-lambda
    pool: {
      max: 2,
      min: 0,
      idle: 0,
      acquire: 3000,
      evict: 10000, // Default lambda timeout for Serverless Stack SST is 10s
    },
    define: {
      timestamps: true,
      createdAt: 'created_at',
      updatedAt: 'updated_at',
      underscored: false,
    },
  },
);

module.exports.connection = connection;
