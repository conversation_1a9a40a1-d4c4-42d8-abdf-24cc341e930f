'use strict';
/* eslint-disable @typescript-eslint/no-var-requires */
const { DataTypes } = require('sequelize');

const attributes = {
  id: {
    type: DataTypes.UUID,
    allowNull: false,
    primaryKey: true,
  },
  userId: {
    type: DataTypes.UUID,
  },
  uiLang: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  label: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  appState: {
    type: DataTypes.JSONB,
    allowNull: false,
  },
  ua: {
    type: DataTypes.JSONB,
    allowNull: false,
  },
  data: {
    type: DataTypes.JSONB,
    allowNull: false,
  },
  date: {
    type: DataTypes.DATE,
    allowNull: false,
  },

  // These timestamps aren't included in the AnalyticsEvent domain model, they are handled by Sequelize. Configured in ../config/config.js
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  deleted_at: {
    type: DataTypes.DATE,
  },
};

module.exports = {
  connect: (connection) => {
    return connection.define('analytics_front', attributes, {
      paranoid: true,
      deletedAt: 'deleted_at',
    });
  },
  attributes,
};
