'use strict';
/* eslint-disable @typescript-eslint/no-var-requires */
const { DataTypes } = require('sequelize');

const attributes = {
  id: {
    type: DataTypes.UUID,
    allowNull: false,
    primaryKey: true,
  },
  logStream: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  logGroup: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  requestId: {
    type: DataTypes.TEXT,
    allowNull: false,
  },
  cognitoIdentityId: {
    type: DataTypes.TEXT,
  },
  data: {
    type: DataTypes.JSONB,
    allowNull: false,
  },
  date: {
    type: DataTypes.DATE,
    allowNull: false,
  },

  // These timestamps aren't included in the AnalyticsEvent domain model, they are handled by Sequelize. Configured in ../config/config.js
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
  },
  deleted_at: {
    type: DataTypes.DATE,
  },
};

module.exports = {
  connect: (connection) => {
    return connection.define('analytics_event', attributes, {
      paranoid: true,
      deletedAt: 'deleted_at',
    });
  },
  attributes,
};
