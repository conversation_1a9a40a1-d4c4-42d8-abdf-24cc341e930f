import fetch from 'node-fetch';
import { DocumentNode } from 'graphql';
import { print } from 'graphql';

export interface IFeClient {
  send(args: { query: DocumentNode; variables: unknown }): void;
}

export class AppSyncClient implements IFeClient {
  private readonly url: string;
  private readonly key: string;

  public constructor() {
    // Add all process.env used:
    const { appsyncUrl, appsyncKey } = process.env;
    if (!appsyncUrl || !appsyncKey) {
      console.log('process.env', process.env);
      throw new Error(`Undefined env var!`);
    }
    this.url = appsyncUrl;
    this.key = appsyncKey;
  }

  public send<Request>({
    query,
    variables,
  }: {
    query: DocumentNode;
    variables: Request;
  }) {
    return fetch(this.url, {
      method: 'post',
      headers: {
        'x-api-key': this.key,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: print(query),
        variables,
      }),
    });
  }
}
