import { expect, vi, describe, it } from 'vitest';
import { LambdaInvoker } from './LambdaInvoker';
import { Lambda } from '@aws-sdk/client-lambda';
// eslint-disable-next-line @typescript-eslint/no-var-requires
const chance = require('chance').Chance();

describe('invokeSync', () => {

  it(`doesn't throw when invoke doesn't throw`, async () => {
    const lambdaClient = {
      invoke: () => undefined,
    };
    const spyInvoke = vi.spyOn(lambdaClient, 'invoke');
    const lambdaInvoker = new LambdaInvoker(lambdaClient as unknown as Lambda);

    await expect(lambdaInvoker.invokeSync({}, 'handler')).resolves.not.toThrow();
    expect(spyInvoke).toHaveBeenCalledTimes(1);
  });

  it('makes 8 attempts when receives always rate limit exceeded errors and throws in the end', async () => {
    const lambdaClient = {
      invoke: () => { throw Error('Rate') },
    };
    const spyInvoke = vi.spyOn(lambdaClient, 'invoke');
    const lambdaInvoker = new LambdaInvoker(lambdaClient as unknown as Lambda);

    await expect(lambdaInvoker.invokeSync({}, 'handler')).rejects.toThrow();
    expect(spyInvoke).toHaveBeenCalledTimes(8); // 8 attempts
  });

  it('throws if receives an error different from rate limit exceeded', async () => {
    const maxAttempts = chance.integer({ min: 1, max: 7 });
    let attempts = 0;
    const lambdaClient = {
      invoke: () => {
        attempts++;
        if (attempts < maxAttempts)
          throw Error('Rate');
        throw Error('unexpected');
      },
    };
    const spyInvoke = vi.spyOn(lambdaClient, 'invoke');
    const lambdaInvoker = new LambdaInvoker(lambdaClient as unknown as Lambda);

    await expect(lambdaInvoker.invokeSync({}, 'handler')).rejects.toThrow();
    expect(spyInvoke).toHaveBeenCalledTimes(maxAttempts);
  });
});