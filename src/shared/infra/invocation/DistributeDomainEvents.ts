import { IInvoker } from './IInvoker';
import { LambdaInvoker } from './LambdaInvoker';
import { DomainEventBase } from '../../events/DomainEventBase';
import { DomainEventTypes } from '../../events/DomainEventTypes';
import { Lambda } from '@aws-sdk/client-lambda';

// Add all process.env used:
const {
  dummySubscriber,
  createGSforReservation,
  createBalance,
  moveInvolvingReservation, // @balance
} =
  process.env;
if (
  !dummySubscriber ||
  !createGSforReservation ||
  !createBalance ||
  !moveInvolvingReservation
) {
  console.log('process.env', process.env);
  throw new Error(`Undefined env var!`);
}
const {
  BusinessUserCreatedEvent,
  CustomerUserCreatedEvent,
  UserCreatedEventExample,
  CustomerManuallyCreatedEvent,
  ReservationByBusinessEvent,
  ReservationByCustomerEvent,
  CancelReservationByCustomerEvent,
  CancelReservationByBusinessEvent,
} = DomainEventTypes;

const subscribers = {
  // Set up permissions in MyStack.ts:
  // Events/keys: allowEmittingDomainEvents
  // Subscribers/values: allowSubscribeToDomainEvents
  [BusinessUserCreatedEvent]: [
    dummySubscriber,
    createGSforReservation,
    createBalance,
  ],
  [CustomerUserCreatedEvent]: [dummySubscriber],
  [UserCreatedEventExample]: [dummySubscriber],
  [CustomerManuallyCreatedEvent]: [dummySubscriber],
  [ReservationByBusinessEvent]: [
    moveInvolvingReservation,
  ],
  [CancelReservationByCustomerEvent]: [
    moveInvolvingReservation,
  ],
  [ReservationByCustomerEvent]: [
    moveInvolvingReservation,
  ],
  [CancelReservationByBusinessEvent]: [
    moveInvolvingReservation,
  ],
};

class DistributeDomainEvents {
  private invoker: IInvoker;

  public constructor() {
    this.invoker = new LambdaInvoker(new Lambda({}));
  }

  public async execute(event: DomainEventBase) {
    console.log('event', event);
    console.log(`subscribers[${event.type}]`, subscribers[event.type]);

    await Promise.all(
      subscribers[event.type].map((lambda) => {
        return this.invoker.dispatch(event, lambda);
      }),
    );

    if (!subscribers[event.type] || !subscribers[event.type].length) {
      const msg = `Unexpected event`;
      console.log(msg, event, subscribers);
      throw new Error(msg);
    }
  }
}

const distributeDomainEvents = new DistributeDomainEvents();
export const handler = distributeDomainEvents.execute.bind(distributeDomainEvents);
