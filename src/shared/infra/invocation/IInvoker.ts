import { DomainEventBase } from '@shared/events/DomainEventBase';

export interface IInvoker {
  dispatch(event: DomainEventBase, handler: string): Promise<void>;
  invokeAsync<Req>(payload: Req, handler: string): Promise<void>;
  // Don't include invokeSync, since it's used for tests, not for the actual design. When a synchronous invocation is needed, since this is a monorepo, just call the use case as a function (use case extends FunctionController | FunctionController2)
}
