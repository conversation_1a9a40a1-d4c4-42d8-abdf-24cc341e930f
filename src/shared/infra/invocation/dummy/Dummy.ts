import { AsynchronousController } from '@shared/infra/Controllers';
import { Request } from './DummyDTOs';
import { IContextProvider } from '@shared/context/IContextProvider';

export class Dummy extends AsynchronousController<Request> {
  public constructor(args : {
    contextProvider: IContextProvider;
  }) {
    super(args);
  }

  public async executeImpl(event: Request) {
    console.log(`${this.constructor.name}.executeImpl`, event);
  }
}
