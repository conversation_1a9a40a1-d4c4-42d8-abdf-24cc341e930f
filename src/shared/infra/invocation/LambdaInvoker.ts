import { DomainEventBase } from '@shared/events/DomainEventBase';
import { Lambda } from '@aws-sdk/client-lambda';
import stringify from 'json-stringify-safe';
import { TextEncoder } from 'util';
import { InvocationType } from 'aws-cdk-lib/triggers';
import { IInvoker } from '@shared/infra/invocation/IInvoker';
import {
  commonBackOffOptions,
  isRateLimitExceeded,
} from '@shared/utils/utils';
import { backOff } from 'exponential-backoff';

export class LambdaInvoker implements IInvoker {
  private lambdaClient: Lambda;

  public constructor(lambdaClient: Lambda) {
    this.lambdaClient = lambdaClient;
  }

  // Synchronous invocation for e2e testing of AsynchronousControllers, to wait for the controller to finish before looking for expected side effects.
  // This isn't used for the design, but left here for convenience. It would be useful for the design in the case we have to communicate with another repo/microservice and need the response back (in which case we would need to add some code to return a Result2 response and a LambdaInvokerErrors.RateLimitExceeded error when attempts are exhausted).
  public async invokeSync<Req>(
    payload: Req,
    handler: string
  ) {
    const req = {
      FunctionName: handler,
      InvocationType: InvocationType.REQUEST_RESPONSE,
      Payload: JSON.stringify(payload),
    };

    console.log(`Invoking synchronously...`, {
      ...req,
      event: payload,
    });
    try {
      await backOff(() => this.lambdaClient.invoke(req),
        backOffOptions({
          handler,
          payload,
        })
      );
    } catch (e) {
      console.log(`Error when invoking lambda ${handler}`, e);
      console.log({payload});
      if (isRateLimitExceeded(e)) {
        const msg = 'Maximum attempts to recover from rate limit error exceeded';
        console.log(msg, e);
        // Send to analytics (as invokeSync is used only by tests, it's ok to skip sending to analytics). See note above invokeSync signature and in IInvoker.

        throw new Error(msg);
      } else {
        throw e;
      }
    }
  }

  // Asynchronous invocation meant for AsynchronousControllers
  public async invokeAsync<Req>(
    payload: Req,
    handler: string,
  ) {
    const req = {
      FunctionName: handler,
      InvocationType: InvocationType.EVENT,
      Payload: new TextEncoder().encode(stringify(payload)),
    };
    console.log(`Invoking asynchronously...`, {
      ...req,
      payload,
    });
    const result = await this.lambdaClient.invoke(req);
    console.log('Invocation complete', result);
  }

  // Asynchronous invocation meant for dispatching domain events
  public async dispatch(event: DomainEventBase, handler: string): Promise<void> {
    console.log('dispatch event');
    await this.invokeAsync(event, handler);
  }
}

const backOffOptions = (args: { handler: string; payload: unknown }) => {
  const {handler, payload} = args;
  return {
    ...commonBackOffOptions,
    retry: (e: unknown, attempt: number) => {
      // Send to analytics (as invokeSync is used only by tests, it's ok to skip sending to analytics). See note above invokeSync signature and in IInvoker.
      if (isRateLimitExceeded(e)) {
        console.log(`Rate limit exceeded error persists in LambdaInvoker.invokeSync for handler ${handler}, backOff attempt number ${attempt}`, e);
        console.log({ payload });
        return true;
      } else {
        console.log(`A different error from rate limit exceeded in LambdaInvoker.invokeSync for handler ${handler}, backOff stops at attempt number ${attempt}`, e);
        console.log({ payload });
        return false;
      }
    },
  }
};
