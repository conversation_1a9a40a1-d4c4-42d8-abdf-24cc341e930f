import { DomainEventBase } from '@shared/events/DomainEventBase';
import { IInvoker } from './IInvoker';

export class LambdaInvokerFake implements IInvoker {
  public async dispatch(event: DomainEventBase, handler: string) {
    console.log(`${this.constructor.name}.dispatch`, event, handler);
  }
  public async invokeAsync<Request>(
    payload: Request,
    handler: string,
  ) {
    console.log(`${this.constructor.name}.invokeSync`, {
      payload,
      handler,
    });
  }
}
