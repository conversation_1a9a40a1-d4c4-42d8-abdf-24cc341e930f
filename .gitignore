/node_modules/
/.idea/
/setAwsProfile.sh
/.env
/.sst/
/src/modules/user/useCases/getCustomerUser/notas.txt
/src/modules/reservation/useCases/createReservation/notas.txt
/src/modules/calendar/useCases/getAvailableTimes/notas.txt
/src/modules/reservation/useCases/createLocation/notas.txt
/src/modules/reservation/useCases/getLocations/notas.txt
/src/modules/reservation/useCases/updateLocation/notas.txt
/src/modules/reservation/useCases/createService/notas.txt
/prueba1--s4nt14godev-3528f2be01e5.json
/coverage
/pack/lib/
/pack/package/
**/s4nt14go-book-backend-*
/pack/dev/lib/
/winged-precept-448216-r7-31510f88d470.json
