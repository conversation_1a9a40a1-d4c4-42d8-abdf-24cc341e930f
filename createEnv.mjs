// Create .env for integration and e2e tests
// Run it with stack name: PROJECT=turnero STACK=MyStack zx createEnv.mjs
const stack = process.env.STACK;
const project = process.env.PROJECT;
const region = process.env.AWS_REGION;
const stage = process.env.STAGE;
if (!stack || !project || !region || !stage) {
  console.log(process.env);
  console.log(`stack: ${stack}, project: ${project}, region: ${region}, stage: ${stage}`);
  throw new Error(`Mandatory env var is missing`);  // They can be set with "export STAGE=dev;AWS_REGION=us-east-1
}

const envFile = `.env`;
await $`rm -f ${envFile}`;
await $`echo AWS_REGION=${region} >> ${envFile}`
// PROJECT & STAGE are used by e2e tests in calendar module
await $`echo PROJECT=${project} >> ${envFile}`
await $`echo STAGE=${stage} >> ${envFile}`

await $`aws cloudformation list-stack-resources --stack-name ${stage}-${project}-${stack} --query "StackResourceSummaries[?ResourceType=='AWS::Lambda::Function']" \
    > deployedLambdas.json`

const deployedNames = require('./deployedLambdas.json').map(obj => obj.PhysicalResourceId);

const lambdas = [
  'dummySubscriber',  // @shared/infra/invocation/dummy/Dummy.e2e.ts
  'createGSforReservation', // src/modules/reservation/useCases/createGSforReservation/CreateGSforReservation.e2e.ts
  'createBalance', // src/modules/balance/useCases/createBalance/CreateBalance.e2e.ts
  'moveInvolvingReservation', // src/modules/balance/useCases/moveInvolvingReservation/MoveInvolvingReservation.e2e.ts
  'sendAnalyticsEvent', // src/modules/analytics/useCases/send/Send.e2e.ts
  'analyticsFromFrontend', // src/modules/analytics/useCases/analyticsFromFrontend/AnalyticsFromFrontend.e2e.ts
];

lambdas.map(async l => {
    const find = `${stage}-${project}-${stack}-${l}`;
    const name = deployedNames.filter(d => d.startsWith(find))[0];
    if (name === undefined) {
      throw new Error(`Lambda not found: ${find}`);
    }
    await $`echo ${l}=${name} >> ${envFile}`
});
await $`rm deployedLambdas.json`

$.verbose = false // Don't log sensitive data

await $`aws appsync list-graphql-apis > appsync.json`
const graphqlApis = require(`./appsync.json`).graphqlApis;
await $`rm appsync.json`;
const appsyncName = 'AppSyncApi';
const appsync = graphqlApis.filter(g => g.name === `${stage}-${project}-${appsyncName}`)[0];
let appsyncId = appsync.apiId;
let appsyncUrl = appsync.uris.GRAPHQL;
await $`echo appsyncUrl=${appsyncUrl} >> ${envFile}`
await $`aws appsync list-api-keys --api-id ${appsyncId} > appsyncKeys.json`
let appsyncKey = require('./appsyncKeys.json').apiKeys[0].id;
await $`rm appsyncKeys.json`
await $`echo appsyncKey=${appsyncKey} >> ${envFile}`

await $`aws ssm get-parameter --name /${project}/${stage}/cockroach > cockroach.json`
let cockroach = require('./cockroach.json').Parameter.Value;
await $`rm cockroach.json`
const [ username, password, database, host, dialect, port, cluster ] = cockroach.split(',');
await $`echo COCKROACH_username=${username} >> ${envFile}`
await $`echo COCKROACH_password=${password} >> ${envFile}`
await $`echo COCKROACH_database=${database} >> ${envFile}`
await $`echo COCKROACH_host=${host} >> ${envFile}`
await $`echo COCKROACH_dialect=${dialect} >> ${envFile}`
await $`echo COCKROACH_port=${port} >> ${envFile}`
await $`echo COCKROACH_cluster=${cluster} >> ${envFile}`

await $`aws ssm get-parameter --name /${project}/${stage}/cockroach_analytics > cockroach_analytics.json`
let cockroach_analytics = require('./cockroach_analytics.json').Parameter.Value;
await $`rm cockroach_analytics.json`
const [ username_a, password_a, database_a, host_a, dialect_a, port_a, cluster_a ] = cockroach_analytics.split(',');
await $`echo COCKROACH_ANALYTICS_username=${username_a} >> ${envFile}`
await $`echo COCKROACH_ANALYTICS_password=${password_a} >> ${envFile}`
await $`echo COCKROACH_ANALYTICS_database=${database_a} >> ${envFile}`
await $`echo COCKROACH_ANALYTICS_host=${host_a} >> ${envFile}`
await $`echo COCKROACH_ANALYTICS_dialect=${dialect_a} >> ${envFile}`
await $`echo COCKROACH_ANALYTICS_port=${port_a} >> ${envFile}`
await $`echo COCKROACH_ANALYTICS_cluster=${cluster_a} >> ${envFile}`