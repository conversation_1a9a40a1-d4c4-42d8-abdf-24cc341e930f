// Este archivo es usado por pack/tsconfig.json para generar el paquete npm con los archivos de domain para ser usados en el frontend
// region shared
import {
  BaseErrorDto as _BaseErrorDto,
} from '@shared/core/AppError';
import {
  PositiveInt as _PositiveInt,
} from '@shared/core/PositiveInt';
import {
  PositiveFloat as _PositiveFloat,
} from '@shared/core/PositiveFloat';
import {
  PositiveFloatAndZero as _PositiveFloatAndZero,
} from '@shared/core/PositiveFloatAndZero';
import {
  N0 as _N0,
} from '@shared/core/N0';
import {
  Slug as _Slug,
} from '@shared/core/Slug';
import {
  possibleLngs,
  getLng,
  PossibleLngs,
  uuidFormat as _uuidFormat,
  areObjectsEqual as _areObjectsEqual,
} from '@shared/utils/utils';
import { Timestamps as _Timestamps } from '@shared/infra/database/sequelize/config/Timestamps';
import {
  Result2 as _Result2,
} from '@shared/core/Result2';
import {
  Result as _Result,
} from '@shared/core/Result';
import {
  Dat as _Dat,
} from '@shared/core/Dat';
import {
  isUnexpectedError as _isUnexpectedError,
} from '@shared/decorators/UnexpectedError';
import {
  TimeZone as _TimeZone,
} from '@shared/core/TimeZone';
import {
  Integer as _Integer,
} from '@shared/core/Integer';
// endregion
// region calendar
import { CalendarType as _CalendarType } from './src/modules/calendar/domain/Calendar';
import {
  Response as _CheckMakeChangesAndSaveResponse,
  Request as _CheckMakeChangesAndSaveRequest,
} from './src/modules/calendar/useCases/checkMakeChangesAndSave/CheckMakeChangesAndSaveDTOs';
import {
  Response as _GetCalendarsResponse,
  Request as _GetCalendarsRequest,
} from './src/modules/calendar/useCases/getCalendars/GetCalendarsDTOs';
import { NextEventDto as _NextEventDto } from './src/modules/calendar/domain/NextEvent';
import { Action as _Action } from './src/modules/calendar/useCases/checkMakeChangesAndSave/CheckMakeChangesAndSave';
import {
  Response as _DeleteCalendarResponse,
  Request as _DeleteCalendarRequest,
} from './src/modules/calendar/useCases/deleteCalendar/DeleteCalendarDTOs';
import {
  checkIdentifier as _checkIdentifier,
} from './src/modules/calendar/domain/Identifier';
// endregion
// region reservation
import {
  MaxDaysAhead as _MaxDaysAhead,
} from './src/modules/reservation/domain/MaxDaysAhead';
import {
  Response as _GetGSforReservationsResponse,
  Request as _GetGSforReservationsRequest,
} from './src/modules/reservation/useCases/getGSforReservations/GetGSforReservationsDTOs';
import {
  Response as _UpdateGSreservationsResponse,
  Request as _UpdateGSreservationsRequest,
} from './src/modules/reservation/useCases/updateGSreservations/UpdateGSreservationsDTOs';
import {
  validateReq as _validateUpdateGSreservationsRequest,
} from './src/modules/reservation/useCases/updateGSreservations/UpdateGSreservations';
import {
  Response as _UpdateGSstoreInfoResponse,
  Request as _UpdateGSstoreInfoRequest,
} from './src/modules/reservation/useCases/updateGSstoreInfo/UpdateGSstoreInfoDTOs';
import {
  Request as _GetReservationOptionsRequest,
} from './src/modules/reservation/useCases/getReservationOptions/GetReservationOptionsDTOs';
import { ReservationOptionDto as _ReservationOptionDto } from './src/modules/reservation/domain/ReservationOption';
import {
  Response as _SetActiveReservationOptionResponse,
  Request as _SetActiveReservationOptionRequest,
} from './src/modules/reservation/useCases/setActiveReservationOption/SetActiveReservationOptionDTOs';
import {
  Response as _CreateReservationOptionResponse,
  Request as _CreateReservationOptionRequest,
} from './src/modules/reservation/useCases/createReservationOption/CreateReservationOptionDTOs';
import {
  validateReq as _validateCreateReservationOptionRequest,
} from './src/modules/reservation/useCases/createReservationOption/CreateReservationOption';
import {
  Response as _UpdateReservationOptionResponse,
  Request as _UpdateReservationOptionRequest,
} from './src/modules/reservation/useCases/updateReservationOption/UpdateReservationOptionDTOs';
import {
  Response as _DeleteReservationOptionResponse,
  Request as _DeleteReservationOptionRequest,
} from './src/modules/reservation/useCases/deleteReservationOption/DeleteReservationOptionDTOs';
import {
  Response as _GetAvailabilityOfReservationOptionsResponse,
  Request as _GetAvailabilityOfReservationOptionsRequest,
  ReservationOptionWithAvailabilityDto as _ReservationOptionWithAvailabilityDto,
} from './src/modules/reservation/useCases/getAvailabilityOfReservationOptions/GetAvailabilityOfReservationOptionsDTOs';
import { ServiceDto as _ServiceDto } from './src/modules/reservation/domain/Service';
import { Every as _Every } from './src/modules/reservation/domain/Every';
import { LocationDto as _LocationDto } from './src/modules/reservation/domain/Location';
import { TimeReferenceDto as _TimeReferenceDto } from './src/modules/reservation/domain/TimeReference';
import {
  ExplanatoryNotesDto as _ExplanatoryNotesDto,
} from './src/modules/reservation/domain/ExplanatoryNotes';
import {
  Response as _CreateReservationResponse,
  Request as _CreateReservationRequest,
} from './src/modules/reservation/useCases/createReservation/CreateReservationDTOs';
import {
  Response as _CreateReservationByBusinessResponse,
  Request as _CreateReservationByBusinessRequest,
} from './src/modules/reservation/useCases/createReservationByBusiness/CreateReservationByBusinessDTOs';
import {
  Request as _GetReservationsRequest,
} from './src/modules/reservation/useCases/getReservations/GetReservationsDTOs';
import {
  CustomerType as _CustomerType,
  ReservationDto as _ReservationDto,
  CancellationActor as _CancellationActor,
} from './src/modules/reservation/domain/Reservation';
import {
  Response as _CancelReservationByBusinessResponse,
  Request as _CancelReservationByBusinessRequest,
} from './src/modules/reservation/useCases/cancelReservationByBusiness/CancelReservationByBusinessDTOs';
import {
  ReminderTemplate as _ReminderTemplate,
} from './src/modules/reservation/domain/ReminderTemplate';
import {
  Response as _SetReminderResponse,
  Request as _SetReminderRequest,
} from './src/modules/reservation/useCases/setReminder/SetReminderDTOs';
// endregion
// region user
import {
  Response as _GetBusinessUserResponse,
  Request as _GetBusinessUserRequest,
} from './src/modules/user/useCases/getBusinessUser/GetBusinessUserDTOs';
import {
  Response as _UpdateBusinessUserResponse,
  Request as _UpdateBusinessUserRequest,
} from './src/modules/user/useCases/updateBusinessUser/UpdateBusinessUserDTOs';
import {
  validateReq as _validateUpdateBusinessUserRequest,
} from './src/modules/user/useCases/updateBusinessUser/UpdateBusinessUser';
import {
  Request as _GetManuallyCreatedCustomersRequest,
} from './src/modules/user/useCases/getManuallyCreatedCustomers/GetManuallyCreatedCustomersDTOs';
import {
  ManuallyCreatedCustomerDto as _ManuallyCreatedCustomerDto,
} from './src/modules/user/domain/ManuallyCreatedCustomer';
import {
  Request as _CreateCustomerManuallyRequest,
  Response as _CreateCustomerManuallyResponse,
} from './src/modules/user/useCases/createCustomerManually/CreateCustomerManuallyDTOs';
import {
  validateReq as _validateCreateCustomerManuallyRequest,
} from './src/modules/user/useCases/createCustomerManually/CreateCustomerManually';
import {
  Request as _UpdateCustomerManuallyRequest,
  Response as _UpdateCustomerManuallyResponse,
} from './src/modules/user/useCases/updateCustomerManually/UpdateCustomerManuallyDTOs';
import {
  Request as _DeleteCustomerManuallyRequest,
  Response as _DeleteCustomerManuallyResponse,
} from './src/modules/user/useCases/deleteCustomerManually/DeleteCustomerManuallyDTOs';
import {
  GetAvailabilityOfReservationOptions,
} from './src/modules/reservation/useCases/getAvailabilityOfReservationOptions/GetAvailabilityOfReservationOptions';
import {
  Request as _ChangeBusinessPhoneSendCodeRequest,
  Response as _ChangeBusinessPhoneSendCodeResponse,
} from './src/modules/user/useCases/changeBusinessPhoneSendCode/ChangeBusinessPhoneSendCodeDTOs';
import {
  validateReq as _validateChangeBusinessPhoneSendCodeRequest,
} from './src/modules/user/useCases/changeBusinessPhoneSendCode/ChangeBusinessPhoneSendCode';
import {
  BusinessUserForFrontend as _BusinessUserForFrontend,
} from './src/modules/user/domain/BusinessUser';
import {
  BusinessUserErrors as _BusinessUserErrors,
} from './src/modules/user/domain/BusinessUserErrors';
import {
  VerificationStatus as _VerificationStatus,
} from './src/modules/user/domain/BusinessUser';
import {
  Request as _ChangeBusinessPhoneConfirmCodeRequest,
  Response as _ChangeBusinessPhoneConfirmCodeResponse,
} from './src/modules/user/useCases/changeBusinessPhoneConfirmCode/ChangeBusinessPhoneConfirmCodeDTOs';
// endregion
// region balance
import {
  Request as _GetBalanceRowsRequest,
} from './src/modules/balance/useCases/getBalanceRows/GetBalanceRowsDTOs';
import { BalanceRowDto } from './src/modules/balance/domain/BalanceRow';
import { Movement as _Movement } from './src/modules/balance/domain/BalanceRow';
import {
  Request as _PurchaseRequest,
  Response as _PurchaseResponse,
} from './src/modules/balance/useCases/purchase/PurchaseDTOs';
import {
  validateReq as _validatePurchaseRequest,
} from './src/modules/balance/useCases/purchase/Purchase';
// endregion
// region analytics
import {
  sendAnalyticsDummy as _sendAnalyticsDummy,
} from '@shared/utils/utils';
// endregion

export type BaseErrorDto = _BaseErrorDto;
export const isUnexpectedError = _isUnexpectedError;
export const uuidFormat = _uuidFormat;
export const possibleBackendLngs = possibleLngs;
export type PossibleBackendLngs = PossibleLngs;
export const getBackendLng = getLng;
export type Timestamps = _Timestamps;
export const PositiveInt = _PositiveInt;
export const PositiveFloat = _PositiveFloat;
export const PositiveFloatAndZero = _PositiveFloatAndZero;
export const N0 = _N0;
export const Result2 = _Result2;
export const Result = _Result;
export const areObjectsEqual = _areObjectsEqual;
export const Dat = _Dat;
export const Slug = _Slug;
export const TimeZone = _TimeZone
export const Integer = _Integer;

/*
En el frontend para importar el type de una variable siendo exportada (como por ejemplo BaseError):
import { <variable const> } from '@s4nt14go/book-backend';
type <type de variable const> = typeof <variable const>;
 */

export namespace calendar {
  export const CalendarType = _CalendarType;
  export type CheckMakeChangesAndSaveResponse = _CheckMakeChangesAndSaveResponse;
  export type CheckMakeChangesAndSaveRequest = _CheckMakeChangesAndSaveRequest;
  export type GetCalendarsResponse = _GetCalendarsResponse;
  export type GetCalendarsRequest = _GetCalendarsRequest;
  export type DeleteCalendarResponse = _DeleteCalendarResponse;
  export type DeleteCalendarRequest = _DeleteCalendarRequest;
  export type NextEventDto = _NextEventDto;
  export const Action = _Action;
  export const checkIdentifier = _checkIdentifier;

  /*
  En el frontend según sean variables o types se importan así:
  import { <namespace> } from '@s4nt14go/book-backend';
  const { <variable const> } = <namespace>;
  type <type> = <namespace>.<type>;
  */
}

export namespace user {
  export type GetBusinessUserResponse = _GetBusinessUserResponse;
  export type GetBusinessUserRequest = _GetBusinessUserRequest;
  export type UpdateBusinessUserResponse = _UpdateBusinessUserResponse;
  export type UpdateBusinessUserRequest = _UpdateBusinessUserRequest;
  export const validateUpdateBusinessUserRequest = _validateUpdateBusinessUserRequest;

  type ManuallyCreatedCustomerDto = _ManuallyCreatedCustomerDto;
  export type ManuallyCreatedCustomerDtoWithTimestamps = ManuallyCreatedCustomerDto & Timestamps;
  // Basándome en src/modules/user/useCases/getManuallyCreatedCustomers/GetManuallyCreatedCustomersDTOs.ts
  export type GetManuallyCreatedCustomersResponse = ManuallyCreatedCustomerDtoWithTimestamps[] | null;
  export type GetManuallyCreatedCustomersRequest = _GetManuallyCreatedCustomersRequest;

  export type CreateCustomerManuallyRequest = _CreateCustomerManuallyRequest;
  export type CreateCustomerManuallyResponse = _CreateCustomerManuallyResponse;
  export const validateCreateCustomerManuallyRequest = _validateCreateCustomerManuallyRequest;
  export type UpdateCustomerManuallyRequest = _UpdateCustomerManuallyRequest;
  export type UpdateCustomerManuallyResponse = _UpdateCustomerManuallyResponse;
  export type DeleteCustomerManuallyRequest = _DeleteCustomerManuallyRequest;
  export type DeleteCustomerManuallyResponse = _DeleteCustomerManuallyResponse;

  export type BusinessUserForFrontend = _BusinessUserForFrontend;
  export type ChangeBusinessPhoneSendCodeRequest = _ChangeBusinessPhoneSendCodeRequest;
  export type ChangeBusinessPhoneSendCodeResponse = _ChangeBusinessPhoneSendCodeResponse;
  export const validateChangeBusinessPhoneSendCodeRequest = _validateChangeBusinessPhoneSendCodeRequest;
  export const BusinessUserErrors = _BusinessUserErrors;
  export const VerificationStatus = _VerificationStatus;
  export type ChangeBusinessPhoneConfirmCodeRequest = _ChangeBusinessPhoneConfirmCodeRequest;
  export type ChangeBusinessPhoneConfirmCodeResponse = _ChangeBusinessPhoneConfirmCodeResponse;
}

export namespace reservation {
  export const MaxDaysAhead = _MaxDaysAhead;
  export type GetGSforReservationsResponse = _GetGSforReservationsResponse;
  export type GetGSforReservationsRequest = _GetGSforReservationsRequest;
  export type UpdateGSstoreInfoResponse = _UpdateGSstoreInfoResponse;
  export type UpdateGSstoreInfoRequest = _UpdateGSstoreInfoRequest;

  export type ReservationOptionDto = _ReservationOptionDto;

  // Had to look into GetReservationOptionsDTOs.Response to type GetReservationOptionsResponse with Timestamps;
  // export type GetReservationOptionsResponse = _GetReservationOptionsResponse;
  export type ReservationOptionDtoWithTimestamps = ReservationOptionDto & Timestamps;
  export type GetReservationOptionsResponse = ReservationOptionDtoWithTimestamps[] | null ;

  export type GetReservationOptionsRequest = _GetReservationOptionsRequest;
  export type SetActiveReservationOptionResponse = _SetActiveReservationOptionResponse;
  export type SetActiveReservationOptionRequest = _SetActiveReservationOptionRequest;
  export type CreateReservationOptionResponse = _CreateReservationOptionResponse;
  export type CreateReservationOptionRequest = _CreateReservationOptionRequest;
  export const validateCreateReservationOptionRequest = _validateCreateReservationOptionRequest;
  export type UpdateReservationOptionResponse = _UpdateReservationOptionResponse & Timestamps;
  export type UpdateReservationOptionRequest = _UpdateReservationOptionRequest;
  export type ServiceDto = _ServiceDto;
  export type LocationDto = _LocationDto;
  export type TimeReferenceDto = _TimeReferenceDto;
  export type ExplanatoryNotesDto = _ExplanatoryNotesDto;
  export type DeleteReservationOptionResponse = _DeleteReservationOptionResponse;
  export type DeleteReservationOptionRequest = _DeleteReservationOptionRequest;
  export type UpdateGSreservationsResponse = _UpdateGSreservationsResponse;
  export type UpdateGSreservationsRequest = _UpdateGSreservationsRequest;
  export const validateUpdateGSreservationsRequest = _validateUpdateGSreservationsRequest;
  export type GetAvailabilityOfReservationOptionsResponse = _GetAvailabilityOfReservationOptionsResponse;
  export type GetAvailabilityOfReservationOptionsRequest = _GetAvailabilityOfReservationOptionsRequest;
  export const MAX_ACTIVE_ROs_TIER1 = GetAvailabilityOfReservationOptions.MAX_ACTIVE_ROs_TIER1;
  export const MAX_TIMES_PER_RO_TIER1 = GetAvailabilityOfReservationOptions.MAX_TIMES_PER_RO_TIER1;
  export const MAX_ACTIVE_ROs_TIER2 = GetAvailabilityOfReservationOptions.MAX_ACTIVE_ROs_TIER2;
  export const MAX_TIMES_PER_RO_TIER2 = GetAvailabilityOfReservationOptions.MAX_TIMES_PER_RO_TIER2;
  export type ReservationOptionWithAvailabilityDto = _ReservationOptionWithAvailabilityDto;
  export const Every = _Every;
  export type CreateReservationResponse = _CreateReservationResponse;
  export type CreateReservationRequest = _CreateReservationRequest;
  export type CreateReservationByBusinessResponse = _CreateReservationByBusinessResponse;
  export type CreateReservationByBusinessRequest = _CreateReservationByBusinessRequest;
  export type ReservationDtoWithTimestamps = _ReservationDto & Timestamps;
  export type GetReservationsResponse = ReservationDtoWithTimestamps[] | null;  // Had to look into GetReservationsDTOs.Response to type GetReservationsResponse with Timestamps;
  export type GetReservationsRequest = _GetReservationsRequest;
  export const CustomerType = _CustomerType;
  export type CustomerTypeT =`${_CustomerType}`;
  export const CancellationActor = _CancellationActor;
  export type CancellationActorT =`${_CancellationActor}`;
  export type CancelReservationByBusinessResponse = _CancelReservationByBusinessResponse;
  export type CancelReservationByBusinessRequest = _CancelReservationByBusinessRequest;
  export const ReminderTemplate = _ReminderTemplate;
  export type SetReminderResponse = _SetReminderResponse;
  export type SetReminderRequest = _SetReminderRequest;
}

export namespace balance {
  export type BalanceRowDtoWithTimestamps = BalanceRowDto & Timestamps;
  export type GetBalanceRowsResponse = BalanceRowDtoWithTimestamps[];
  export type GetBalanceRowsRequest = _GetBalanceRowsRequest;
  export const Movement = _Movement;
  export type MovementT =`${_Movement}`;
  export type PurchaseResponse = _PurchaseResponse & Timestamps;
  export type PurchaseRequest = _PurchaseRequest;
  export const validatePurchaseRequest = _validatePurchaseRequest;
}

export namespace analytics {
  export const sendAnalyticsDummy = _sendAnalyticsDummy;
}
