<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="UpdateGSreservationLimits.e2e.ts" type="JavaScriptTestRunnerVitest" nameIsGenerated="true">
    <node-interpreter value="project" />
    <vitest-package value="$PROJECT_DIR$/node_modules/vitest" />
    <working-dir value="$PROJECT_DIR$" />
    <vitest-options value="--run" />
    <envs>
      <env name="TEST_MODE" value="e2e" />
      <env name="AWS_PROFILE" value="dev" />
    </envs>
    <scope-kind value="TEST_FILE" />
    <test-file value="$PROJECT_DIR$/src/modules/calendar/useCases/updateGSreservationLimits/UpdateGSreservationLimits.e2e.ts" />
    <method v="2" />
  </configuration>
</component>