<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="pack-dev" type="js.build_tools.npm">
    <package-json value="$PROJECT_DIR$/pack/dev/package.json" />
    <command value="run" />
    <scripts>
      <script value="pack" />
    </scripts>
    <node-interpreter value="project" />
    <envs>
      <env name="AWS_PROFILE" value="dev" />
    </envs>
    <method v="2" />
  </configuration>
</component>