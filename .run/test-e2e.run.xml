<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="test-e2e" type="js.build_tools.npm" nameIsGenerated="true">
    <package-json value="$PROJECT_DIR$/package.json" />
    <command value="run" />
    <scripts>
      <script value="test-e2e" />
    </scripts>
    <node-interpreter value="project" />
    <envs>
      <env name="TEST_MODE" value="e2e" />
      <env name="AWS_PROFILE" value="dev" />
    </envs>
    <method v="2" />
  </configuration>
</component>