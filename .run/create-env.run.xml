<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="create-env" type="js.build_tools.npm" nameIsGenerated="true">
    <package-json value="$PROJECT_DIR$/package.json" />
    <command value="run" />
    <scripts>
      <script value="create-env" />
    </scripts>
    <node-interpreter value="project" />
    <envs>
      <env name="AWS_PROFILE" value="dev" />
      <env name="STAGE" value="dev" />
      <env name="AWS_REGION" value="us-east-1" />
    </envs>
    <method v="2" />
  </configuration>
</component>