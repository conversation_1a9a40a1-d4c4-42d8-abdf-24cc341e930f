<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="migrate" type="js.build_tools.npm" nameIsGenerated="true">
    <package-json value="$PROJECT_DIR$/package.json" />
    <command value="run" />
    <scripts>
      <script value="migrate" />
    </scripts>
    <node-interpreter value="project" />
    <envs>
      <env name="AWS_PROFILE" value="dev" />
    </envs>
    <method v="2" />
  </configuration>
</component>