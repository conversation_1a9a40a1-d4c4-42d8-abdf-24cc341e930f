<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="test-unit-noBO" type="js.build_tools.npm">
    <package-json value="$PROJECT_DIR$/package.json" />
    <command value="run" />
    <scripts>
      <script value="test-unit-noBO" />
    </scripts>
    <node-interpreter value="project" />
    <envs>
      <env name="TEST_MODE" value="unitNoBO" />
    </envs>
    <method v="2" />
  </configuration>
</component>