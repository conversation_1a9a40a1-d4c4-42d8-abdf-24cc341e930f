const path = require('path')

// Default configuration for the main database
const defaultConfig = {
  'config':          path.resolve('src', 'shared', 'infra', 'database', 'sequelize', 'config', 'config.js'),
  'migrations-path': path.resolve('src', 'shared', 'infra', 'database', 'sequelize', 'migrations'),
  'models-path':     path.resolve('src', 'shared', 'infra', 'database', 'sequelize', 'models'),
}

// Configuration for the analytics database
const analyticsConfig = {
  'config':          path.resolve('src', 'shared', 'infra', 'database', 'sequelize_analytics', 'config', 'config.js'),
  'migrations-path': path.resolve('src', 'shared', 'infra', 'database', 'sequelize_analytics', 'migrations'),
  'models-path':     path.resolve('src', 'shared', 'infra', 'database', 'sequelize_analytics', 'models'),
}

// Use the DB_TYPE environment variable to determine which configuration to use
// If DB_TYPE=analytics, use the analytics configuration, otherwise use the default
module.exports = process.env.DB_TYPE === 'analytics' ? analyticsConfig : defaultConfig